#!/usr/bin/env python3
"""
Test script for the enhanced OpenShift Must-Gather Analyzer web interface.
Tests the new data browser, log viewer, resource explorer, and search functionality.
"""

import os
import sys
import time
import json
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_test_must_gather_data():
    """Create sample must-gather data for testing."""
    test_dir = tempfile.mkdtemp(prefix='test_must_gather_')
    test_path = Path(test_dir)
    
    # Create directory structure
    (test_path / 'namespaces' / 'default' / 'pods').mkdir(parents=True)
    (test_path / 'namespaces' / 'kube-system' / 'pods').mkdir(parents=True)
    (test_path / 'cluster-scoped-resources' / 'nodes').mkdir(parents=True)
    (test_path / 'logs').mkdir(parents=True)
    
    # Create sample pod YAML
    pod_yaml = """
apiVersion: v1
kind: Pod
metadata:
  name: test-pod
  namespace: default
  labels:
    app: test-app
spec:
  containers:
  - name: test-container
    image: nginx:latest
status:
  phase: Running
  conditions:
  - type: Ready
    status: "True"
    reason: ContainersReady
"""
    
    with open(test_path / 'namespaces' / 'default' / 'pods' / 'test-pod.yaml', 'w') as f:
        f.write(pod_yaml)
    
    # Create sample service YAML
    service_yaml = """
apiVersion: v1
kind: Service
metadata:
  name: test-service
  namespace: default
spec:
  selector:
    app: test-app
  ports:
  - port: 80
    targetPort: 8080
"""
    
    with open(test_path / 'namespaces' / 'default' / 'test-service.yaml', 'w') as f:
        f.write(service_yaml)
    
    # Create sample log file
    log_content = """
2024-01-15T10:30:00.123Z INFO Starting application
2024-01-15T10:30:01.456Z INFO Server listening on port 8080
2024-01-15T10:30:05.789Z WARN High memory usage detected
2024-01-15T10:30:10.012Z ERROR Failed to connect to database
2024-01-15T10:30:15.345Z INFO Retrying database connection
2024-01-15T10:30:20.678Z INFO Database connection established
"""
    
    with open(test_path / 'logs' / 'application.log', 'w') as f:
        f.write(log_content)
    
    # Create sample node YAML
    node_yaml = """
apiVersion: v1
kind: Node
metadata:
  name: worker-node-1
status:
  conditions:
  - type: Ready
    status: "True"
    reason: KubeletReady
  capacity:
    cpu: "4"
    memory: "8Gi"
"""
    
    with open(test_path / 'cluster-scoped-resources' / 'nodes' / 'worker-node-1.yaml', 'w') as f:
        f.write(node_yaml)
    
    return test_dir

def test_web_interface_routes():
    """Test that all new web interface routes are accessible."""
    print("Testing web interface routes...")
    
    try:
        from web.app import create_app
        
        app = create_app({'TESTING': True})
        client = app.test_client()
        
        # Test main routes
        routes_to_test = [
            ('/', 'Dashboard'),
            ('/data-browser', 'Data Browser'),
            ('/log-viewer', 'Log Viewer'),
            ('/resource-explorer', 'Resource Explorer'),
            ('/search', 'Advanced Search'),
            ('/upload', 'Upload'),
            ('/jobs', 'Jobs')
        ]
        
        for route, expected_title in routes_to_test:
            response = client.get(route)
            assert response.status_code == 200, f"Route {route} failed with status {response.status_code}"
            assert expected_title.encode() in response.data, f"Expected title '{expected_title}' not found in {route}"
            print(f"✓ Route {route} - {expected_title}")
        
        print("✓ All web interface routes are accessible")
        return True
        
    except Exception as e:
        print(f"✗ Web interface route test failed: {e}")
        return False

def test_api_endpoints():
    """Test API endpoints for the enhanced functionality."""
    print("Testing API endpoints...")
    
    try:
        from web.app import create_app
        
        app = create_app({'TESTING': True})
        client = app.test_client()
        
        # Test system health endpoint
        response = client.get('/api/system/health')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'status' in data
        assert data['status'] == 'healthy'
        print("✓ System health API")
        
        # Test jobs API
        response = client.get('/api/jobs')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert isinstance(data, dict)
        print("✓ Jobs API")
        
        # Test search API (should require POST)
        response = client.post('/api/search', 
                              json={'query': 'test', 'scope': 'all'},
                              content_type='application/json')
        # Should return results even if empty
        assert response.status_code == 200
        print("✓ Search API")
        
        print("✓ All API endpoints are functional")
        return True
        
    except Exception as e:
        print(f"✗ API endpoint test failed: {e}")
        return False

def test_data_browser_functionality():
    """Test data browser functionality with sample data."""
    print("Testing data browser functionality...")
    
    try:
        test_data_dir = create_test_must_gather_data()
        
        # Mock a completed job
        from core.models import AnalysisProgress, AnalysisStatus
        from datetime import datetime
        
        mock_job = AnalysisProgress(
            job_id='test-job-123',
            status=AnalysisStatus.COMPLETED,
            start_time=datetime.now(),
            end_time=datetime.now(),
            progress=100,
            current_step='Completed',
            must_gather_path=test_data_dir
        )
        
        from web.app import create_app, WebAnalyzer
        
        app = create_app({'TESTING': True})
        
        # Mock the web analyzer to return our test job
        with patch.object(WebAnalyzer, 'get_job_status', return_value=mock_job):
            client = app.test_client()
            
            # Test file listing API
            response = client.get('/api/jobs/test-job-123/files')
            assert response.status_code == 200
            data = json.loads(response.data)
            assert isinstance(data, dict)
            assert len(data) > 0  # Should have some files
            print("✓ File listing API")
            
            # Test file content API
            file_path = 'namespaces/default/pods/test-pod.yaml'
            response = client.get(f'/api/jobs/test-job-123/files/{file_path}')
            assert response.status_code == 200
            assert b'kind: Pod' in response.data
            print("✓ File content API")
        
        # Cleanup
        shutil.rmtree(test_data_dir)
        
        print("✓ Data browser functionality working")
        return True
        
    except Exception as e:
        print(f"✗ Data browser test failed: {e}")
        if 'test_data_dir' in locals():
            shutil.rmtree(test_data_dir)
        return False

def test_search_functionality():
    """Test search functionality with sample data."""
    print("Testing search functionality...")
    
    try:
        test_data_dir = create_test_must_gather_data()
        
        # Import search functions
        from web.app import _search_logs, _search_configs, _search_resources
        
        must_gather_root = Path(test_data_dir)
        
        # Test log search
        log_results = _search_logs(must_gather_root, 'ERROR', 'test-job', False, False, False)
        assert len(log_results) > 0, "Should find ERROR in logs"
        assert any('ERROR' in result['context'] for result in log_results)
        print("✓ Log search functionality")
        
        # Test config search
        config_results = _search_configs(must_gather_root, 'test-app', 'test-job', False, False, False)
        assert len(config_results) > 0, "Should find test-app in configs"
        print("✓ Config search functionality")
        
        # Test resource search
        resource_results = _search_resources(must_gather_root, 'Pod', 'test-job', False, False, False)
        assert len(resource_results) > 0, "Should find Pod resources"
        print("✓ Resource search functionality")
        
        # Cleanup
        shutil.rmtree(test_data_dir)
        
        print("✓ Search functionality working")
        return True
        
    except Exception as e:
        print(f"✗ Search functionality test failed: {e}")
        if 'test_data_dir' in locals():
            shutil.rmtree(test_data_dir)
        return False

def test_template_rendering():
    """Test that all templates render without errors."""
    print("Testing template rendering...")
    
    try:
        from web.app import create_app
        
        app = create_app({'TESTING': True})
        
        with app.app_context():
            from flask import render_template
            
            templates_to_test = [
                'index.html',
                'data_browser.html',
                'log_viewer.html',
                'resource_explorer.html',
                'search.html',
                'upload.html',
                'jobs.html'
            ]
            
            for template in templates_to_test:
                try:
                    rendered = render_template(template)
                    assert len(rendered) > 0, f"Template {template} rendered empty"
                    assert 'OpenShift Must-Gather Analyzer' in rendered, f"Template {template} missing title"
                    print(f"✓ Template {template}")
                except Exception as e:
                    print(f"✗ Template {template} failed: {e}")
                    return False
        
        print("✓ All templates render successfully")
        return True
        
    except Exception as e:
        print(f"✗ Template rendering test failed: {e}")
        return False

def run_all_tests():
    """Run all tests and report results."""
    print("=" * 60)
    print("OpenShift Must-Gather Analyzer - Enhanced Web Interface Tests")
    print("=" * 60)
    
    tests = [
        test_web_interface_routes,
        test_api_endpoints,
        test_template_rendering,
        test_data_browser_functionality,
        test_search_functionality
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        print(f"\n{test.__name__.replace('_', ' ').title()}:")
        print("-" * 40)
        
        try:
            if test():
                passed += 1
                print(f"✓ {test.__name__} PASSED")
            else:
                failed += 1
                print(f"✗ {test.__name__} FAILED")
        except Exception as e:
            failed += 1
            print(f"✗ {test.__name__} FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed} passed, {failed} failed")
    print("=" * 60)
    
    if failed == 0:
        print("🎉 All tests passed! Enhanced web interface is ready.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)
