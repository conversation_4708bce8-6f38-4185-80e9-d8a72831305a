---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2023-12-19T12:54:32Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2023-12-19T12:54:32Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:06:22Z"
  name: storagestates.v1alpha1.migration.k8s.io
  resourceVersion: "*********"
  uid: ********-e42f-4dd7-a5bf-9f867b86d78b
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 4
    requestCount: 4
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 4
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 13
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 19
    requestCount: 36
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 5
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 19
    requestCount: 39
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 6
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 13
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 18
    requestCount: 37
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 6
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 14
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 23
    requestCount: 44
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 11
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 15
    requestCount: 41
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 6
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 18
    requestCount: 41
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 12
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 16
    requestCount: 36
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 11
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 12
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 17
    requestCount: 40
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 12
          verb: list
        requestCount: 12
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 20
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 15
    requestCount: 38
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 12
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 19
    requestCount: 39
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 4
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 14
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 18
    requestCount: 36
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 10
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 3
          verb: watch
        requestCount: 4
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 22
    requestCount: 41
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 6
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 28
    requestCount: 43
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 5
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 29
    requestCount: 41
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 4
    requestCount: 4
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 14
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 20
    requestCount: 41
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 4
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 16
    requestCount: 37
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 1
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 22
    requestCount: 39
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 5
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 18
    requestCount: 38
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 14
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 17
    requestCount: 38
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 20
    requestCount: 38
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 13
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 17
    requestCount: 38
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 6
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 11
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 22
    requestCount: 39
  requestCount: 864
