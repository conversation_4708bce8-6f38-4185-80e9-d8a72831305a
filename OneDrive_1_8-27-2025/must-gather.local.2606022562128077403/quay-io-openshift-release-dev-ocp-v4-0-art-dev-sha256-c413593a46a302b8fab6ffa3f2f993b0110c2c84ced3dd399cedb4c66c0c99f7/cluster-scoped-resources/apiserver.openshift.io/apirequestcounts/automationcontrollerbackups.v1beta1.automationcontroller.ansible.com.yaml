---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-01-14T19:19:26Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-01-14T19:19:26Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:06:40Z"
  name: automationcontrollerbackups.v1beta1.automationcontroller.ansible.com
  resourceVersion: "*********"
  uid: e40ac43f-8499-4c4c-8698-0ede699d5cc2
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 36
    - byUser:
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 11
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: list
        requestCount: 43
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 46
    requestCount: 93
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 830
          verb: list
        requestCount: 830
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 2
          verb: watch
        requestCount: 2
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: J8683
      nodeName: 10.21.102.11
      requestCount: 835
    - byUser:
      - byVerb:
        - requestCount: 773
          verb: list
        requestCount: 773
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 781
    - byUser:
      - byVerb:
        - requestCount: 766
          verb: list
        requestCount: 766
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: Mozilla/5.0
        username: J8683
      nodeName: 10.21.102.9
      requestCount: 795
    requestCount: 2411
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 878
          verb: list
        requestCount: 878
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 878
    - byUser:
      - byVerb:
        - requestCount: 858
          verb: list
        requestCount: 858
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 865
    - byUser:
      - byVerb:
        - requestCount: 879
          verb: list
        requestCount: 879
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 908
    requestCount: 2651
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 878
          verb: list
        requestCount: 878
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 878
    - byUser:
      - byVerb:
        - requestCount: 836
          verb: list
        requestCount: 836
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 844
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 860
    requestCount: 2582
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1005
          verb: list
        requestCount: 1005
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 1005
    - byUser:
      - byVerb:
        - requestCount: 994
          verb: list
        requestCount: 994
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 1002
    - byUser:
      - byVerb:
        - requestCount: 970
          verb: list
        requestCount: 970
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 14
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 999
    requestCount: 3006
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 918
          verb: list
        requestCount: 918
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 918
    - byUser:
      - byVerb:
        - requestCount: 976
          verb: list
        requestCount: 976
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 985
    - byUser:
      - byVerb:
        - requestCount: 958
          verb: list
        requestCount: 958
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 985
    requestCount: 2888
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 983
          verb: list
        requestCount: 983
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 983
    - byUser:
      - byVerb:
        - requestCount: 976
          verb: list
        requestCount: 976
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 984
    - byUser:
      - byVerb:
        - requestCount: 989
          verb: list
        requestCount: 989
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1018
    requestCount: 2985
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 812
          verb: list
        requestCount: 812
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 812
    - byUser:
      - byVerb:
        - requestCount: 808
          verb: list
        requestCount: 808
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 816
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: list
        requestCount: 860
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 888
    requestCount: 2516
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 915
          verb: list
        requestCount: 915
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 915
    - byUser:
      - byVerb:
        - requestCount: 837
          verb: list
        requestCount: 837
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 845
    - byUser:
      - byVerb:
        - requestCount: 876
          verb: list
        requestCount: 876
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 903
    requestCount: 2663
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: list
        requestCount: 860
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 860
    - byUser:
      - byVerb:
        - requestCount: 817
          verb: list
        requestCount: 817
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 825
    - byUser:
      - byVerb:
        - requestCount: 846
          verb: list
        requestCount: 846
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 875
    requestCount: 2560
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 913
          verb: list
        requestCount: 913
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 913
    - byUser:
      - byVerb:
        - requestCount: 914
          verb: list
        requestCount: 914
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 923
    - byUser:
      - byVerb:
        - requestCount: 957
          verb: list
        requestCount: 957
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 985
    requestCount: 2821
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 769
          verb: list
        requestCount: 769
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 769
    - byUser:
      - byVerb:
        - requestCount: 815
          verb: list
        requestCount: 815
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 822
    - byUser:
      - byVerb:
        - requestCount: 746
          verb: list
        requestCount: 746
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 773
    requestCount: 2364
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 940
          verb: list
        requestCount: 940
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 940
    - byUser:
      - byVerb:
        - requestCount: 895
          verb: list
        requestCount: 895
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 899
    - byUser:
      - byVerb:
        - requestCount: 969
          verb: list
        requestCount: 969
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 3
          verb: watch
        requestCount: 4
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1001
    requestCount: 2840
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1111
          verb: list
        requestCount: 1111
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 1111
    - byUser:
      - byVerb:
        - requestCount: 1076
          verb: list
        requestCount: 1076
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 1076
    - byUser:
      - byVerb:
        - requestCount: 1115
          verb: list
        requestCount: 1115
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1151
    requestCount: 3338
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 836
          verb: list
        requestCount: 836
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 836
    - byUser:
      - byVerb:
        - requestCount: 836
          verb: list
        requestCount: 836
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 836
    - byUser:
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 861
    requestCount: 2533
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 36
    - byUser:
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 11
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: list
        requestCount: 43
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 46
    requestCount: 93
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 924
          verb: list
        requestCount: 924
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 924
    - byUser:
      - byVerb:
        - requestCount: 911
          verb: list
        requestCount: 911
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 2
          verb: watch
        requestCount: 2
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 920
    - byUser:
      - byVerb:
        - requestCount: 957
          verb: list
        requestCount: 957
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 985
    requestCount: 2829
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 751
          verb: list
        requestCount: 751
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 751
    - byUser:
      - byVerb:
        - requestCount: 747
          verb: list
        requestCount: 747
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 756
    - byUser:
      - byVerb:
        - requestCount: 715
          verb: list
        requestCount: 715
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 743
    requestCount: 2250
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 893
          verb: list
        requestCount: 893
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 893
    - byUser:
      - byVerb:
        - requestCount: 917
          verb: list
        requestCount: 917
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 924
    - byUser:
      - byVerb:
        - requestCount: 961
          verb: list
        requestCount: 961
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 991
    requestCount: 2808
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 833
    - byUser:
      - byVerb:
        - requestCount: 834
          verb: list
        requestCount: 834
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 842
    - byUser:
      - byVerb:
        - requestCount: 876
          verb: list
        requestCount: 876
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 904
    requestCount: 2579
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 872
          verb: list
        requestCount: 872
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 872
    - byUser:
      - byVerb:
        - requestCount: 865
          verb: list
        requestCount: 865
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 873
    - byUser:
      - byVerb:
        - requestCount: 906
          verb: list
        requestCount: 906
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 933
    requestCount: 2678
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 925
          verb: list
        requestCount: 925
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 925
    - byUser:
      - byVerb:
        - requestCount: 870
          verb: list
        requestCount: 870
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 877
    - byUser:
      - byVerb:
        - requestCount: 898
          verb: list
        requestCount: 898
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 927
    requestCount: 2729
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 821
          verb: list
        requestCount: 821
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 821
    - byUser:
      - byVerb:
        - requestCount: 831
          verb: list
        requestCount: 831
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 839
    - byUser:
      - byVerb:
        - requestCount: 812
          verb: list
        requestCount: 812
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 839
    requestCount: 2499
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 830
          verb: list
        requestCount: 830
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 5
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.11
      requestCount: 835
    - byUser:
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 834
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 858
    requestCount: 2527
  requestCount: 59150
