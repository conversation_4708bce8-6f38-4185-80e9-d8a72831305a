---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2023-12-19T12:51:54Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2023-12-19T12:51:54Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:05:54Z"
  name: oauthclients.v1.oauth.openshift.io
  resourceVersion: "*********"
  uid: b2926522-43cc-4992-a8d7-78d8ca05aa96
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: create
        - requestCount: 6
          verb: get
        requestCount: 12
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 5
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 6
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.21.102.11
      requestCount: 18
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 10
          verb: get
        requestCount: 10
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.21.102.9
      requestCount: 13
    requestCount: 31
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 138
          verb: create
        - requestCount: 138
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 285
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 81
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 88
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 376
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 13
          verb: get
        requestCount: 13
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 40
    requestCount: 416
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 138
          verb: create
        - requestCount: 138
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 284
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 81
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 89
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 376
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: get
        requestCount: 16
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 42
    requestCount: 418
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 138
          verb: create
        - requestCount: 138
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 284
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 80
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 89
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 376
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 46
    requestCount: 422
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 138
          verb: create
        - requestCount: 138
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 284
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 93
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.21.102.11
      requestCount: 377
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 7
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 14
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 29
    requestCount: 406
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 138
          verb: create
        - requestCount: 138
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 284
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 93
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 378
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 4
          verb: get
        requestCount: 4
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 30
    requestCount: 408
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 138
          verb: create
        - requestCount: 138
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 284
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 92
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 4
          verb: get
        requestCount: 4
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 380
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 27
    requestCount: 407
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 138
          verb: create
        - requestCount: 138
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 284
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 83
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 92
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.21.102.11
      requestCount: 376
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 26
    requestCount: 402
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 138
          verb: create
        - requestCount: 138
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 283
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 83
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 91
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 380
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 35
    requestCount: 415
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 138
          verb: create
        - requestCount: 138
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 284
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 81
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 90
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.21.102.11
      requestCount: 374
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 26
    requestCount: 400
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 138
          verb: create
        - requestCount: 138
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 285
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 83
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 91
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.21.102.11
      requestCount: 376
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 27
    requestCount: 403
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 138
          verb: create
        - requestCount: 138
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 285
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 82
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 90
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.21.102.11
      requestCount: 375
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 26
    requestCount: 401
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 138
          verb: create
        - requestCount: 138
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 284
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 93
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.21.102.11
      requestCount: 377
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 31
    requestCount: 408
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 138
          verb: create
        - requestCount: 138
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 284
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 83
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 91
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 393
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 27
          verb: get
        requestCount: 27
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 54
    requestCount: 447
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 138
          verb: create
        - requestCount: 138
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 284
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 80
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 87
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 4
          verb: get
        requestCount: 4
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-6mxgs:default
      nodeName: 10.21.102.11
      requestCount: 376
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 28
          verb: get
        requestCount: 28
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 54
    requestCount: 430
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: create
        - requestCount: 6
          verb: get
        requestCount: 12
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 5
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 6
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.21.102.11
      requestCount: 18
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 10
          verb: get
        requestCount: 10
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.21.102.9
      requestCount: 13
    requestCount: 31
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 138
          verb: create
        - requestCount: 138
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 284
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 91
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.21.102.11
      requestCount: 375
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 32
    requestCount: 407
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 141
          verb: create
        - requestCount: 141
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 291
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 93
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 396
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: get
        requestCount: 17
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 43
    requestCount: 439
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 138
          verb: create
        - requestCount: 138
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 284
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 83
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 92
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 4
          verb: get
        requestCount: 4
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 380
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: get
        requestCount: 30
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 57
    requestCount: 437
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 135
          verb: create
        - requestCount: 135
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 276
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 79
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 87
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 372
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 29
          verb: get
        requestCount: 29
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 55
    requestCount: 427
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 138
          verb: create
        - requestCount: 138
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 284
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 92
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.21.102.11
      requestCount: 376
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 32
    requestCount: 408
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 138
          verb: create
        - requestCount: 138
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 284
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 92
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.21.102.11
      requestCount: 376
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: get
        requestCount: 8
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 35
    requestCount: 411
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 138
          verb: create
        - requestCount: 138
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 283
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 82
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 90
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.21.102.11
      requestCount: 373
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 26
    requestCount: 399
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 138
          verb: create
        - requestCount: 138
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 284
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 92
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 4
          verb: get
        requestCount: 4
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 380
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 31
    requestCount: 411
  requestCount: 9153
