---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2023-12-19T12:54:26Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2023-12-19T12:54:26Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:06:38Z"
  name: storageclasses.v1.storage.k8s.io
  resourceVersion: "*********"
  uid: c50dadbd-0fd6-4279-9cdf-fb416ac63979
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: Z8186
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.21.102.11
      requestCount: 5
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.12
      requestCount: 2
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 8
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 7
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 2
          verb: watch
        requestCount: 2
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: M4108
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: Z8186
      nodeName: 10.21.102.9
      requestCount: 26
    requestCount: 33
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 90
          verb: list
        requestCount: 90
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 82
          verb: list
        requestCount: 82
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 73
          verb: list
        requestCount: 73
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 61
          verb: list
        requestCount: 61
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: trident_orchestrator/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.11
      requestCount: 360
    - byUser:
      - byVerb:
        - requestCount: 89
          verb: list
        requestCount: 89
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 87
          verb: list
        requestCount: 87
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 75
          verb: list
        requestCount: 75
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 57
          verb: list
        requestCount: 57
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: I9275
      nodeName: 10.21.102.12
      requestCount: 354
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 73
          verb: list
        requestCount: 73
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 63
          verb: list
        requestCount: 63
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 53
          verb: list
        requestCount: 53
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 52
          verb: list
        requestCount: 52
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 23
          verb: list
        requestCount: 23
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 479
    requestCount: 1193
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 88
          verb: list
        requestCount: 88
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 64
          verb: list
        requestCount: 64
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 18
          verb: list
        requestCount: 18
        userAgent: Mozilla/5.0
        username: Z6988
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: Mozilla/5.0
        username: Z8504
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 246
    - byUser:
      - byVerb:
        - requestCount: 95
          verb: list
        requestCount: 95
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 63
          verb: list
        requestCount: 63
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 33
          verb: list
        requestCount: 33
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 22
          verb: list
        requestCount: 22
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: Mozilla/5.0
        username: Z6988
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: Mozilla/5.0
        username: Z8504
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 286
    - byUser:
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 81
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 73
          verb: list
        requestCount: 73
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 59
          verb: list
        requestCount: 59
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 35
          verb: list
        requestCount: 35
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: Mozilla/5.0
        username: Z8504
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 414
    requestCount: 946
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 84
          verb: list
        requestCount: 84
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 57
          verb: list
        requestCount: 57
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: trident_orchestrator/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.21.102.11
      requestCount: 208
    - byUser:
      - byVerb:
        - requestCount: 73
          verb: list
        requestCount: 73
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 67
          verb: list
        requestCount: 67
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: Z2695
      nodeName: 10.21.102.12
      requestCount: 188
    - byUser:
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 68
          verb: list
        requestCount: 68
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 58
          verb: list
        requestCount: 58
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 12
          verb: list
        requestCount: 12
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      nodeName: 10.21.102.9
      requestCount: 372
    requestCount: 768
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 96
          verb: list
        requestCount: 96
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: trident_orchestrator/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: S0914
      nodeName: 10.21.102.11
      requestCount: 160
    - byUser:
      - byVerb:
        - requestCount: 53
          verb: list
        requestCount: 53
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 16
          verb: list
        requestCount: 16
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.12
      requestCount: 108
    - byUser:
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 81
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 71
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 68
          verb: list
        requestCount: 68
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 14
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.9
      requestCount: 324
    requestCount: 592
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 31
          verb: list
        requestCount: 31
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: trident_orchestrator/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: S0914
      nodeName: 10.21.102.11
      requestCount: 97
    - byUser:
      - byVerb:
        - requestCount: 25
          verb: list
        requestCount: 25
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 15
          verb: list
        requestCount: 15
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.12
      requestCount: 77
    - byUser:
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 82
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 40
          verb: list
        requestCount: 40
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 14
          verb: list
        requestCount: 14
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      nodeName: 10.21.102.9
      requestCount: 298
    requestCount: 472
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 13
          verb: list
        requestCount: 13
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: trident_orchestrator/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.11
      requestCount: 69
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 50
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 79
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 77
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 252
    requestCount: 371
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: trident_orchestrator/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      nodeName: 10.21.102.11
      requestCount: 63
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 12
          verb: list
        requestCount: 12
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 51
    - byUser:
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 82
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 77
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 252
    requestCount: 366
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: trident_orchestrator/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 64
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 13
          verb: list
        requestCount: 13
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: I6669
      nodeName: 10.21.102.12
      requestCount: 51
    - byUser:
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 81
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 15
          verb: list
        requestCount: 15
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 260
    requestCount: 375
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: trident_orchestrator/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.11
      requestCount: 65
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.12
      requestCount: 51
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 79
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 79
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 251
    requestCount: 367
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 12
          verb: list
        requestCount: 12
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: trident_orchestrator/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 2
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 3
        userAgent: Mozilla/5.0
        username: S0914
      nodeName: 10.21.102.11
      requestCount: 70
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 49
    - byUser:
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 80
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 79
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 251
    requestCount: 370
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 14
          verb: list
        requestCount: 14
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: trident_orchestrator/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: Mozilla/5.0
        username: S0914
      nodeName: 10.21.102.11
      requestCount: 72
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.12
      requestCount: 49
    - byUser:
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 79
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 253
    requestCount: 374
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: trident_orchestrator/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: Mozilla/5.0
        username: L9338
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: L9695
      nodeName: 10.21.102.11
      requestCount: 75
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 14
          verb: list
        requestCount: 14
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: Mozilla/5.0
        username: L9338
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 54
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 79
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 77
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 14
          verb: list
        requestCount: 14
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: Mozilla/5.0
        username: L9338
      nodeName: 10.21.102.9
      requestCount: 259
    requestCount: 388
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 15
          verb: list
        requestCount: 15
        userAgent: Mozilla/5.0
        username: Z2424
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: trident_orchestrator/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: Mozilla/5.0
        username: I5292
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: L9334
      nodeName: 10.21.102.11
      requestCount: 83
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 14
          verb: list
        requestCount: 14
        userAgent: Mozilla/5.0
        username: Z2424
      - byVerb:
        - requestCount: 13
          verb: list
        requestCount: 13
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: Mozilla/5.0
        username: I5292
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: Z3128
      nodeName: 10.21.102.12
      requestCount: 65
    - byUser:
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 76
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: Mozilla/5.0
        username: Z2424
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: Mozilla/5.0
        username: I5292
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 258
    requestCount: 406
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 80
          verb: list
        requestCount: 80
        userAgent: Mozilla/5.0
        username: I5292
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: trident_orchestrator/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-6mxgs:default
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: Mozilla/5.0
        username: V8131
      nodeName: 10.21.102.11
      requestCount: 145
    - byUser:
      - byVerb:
        - requestCount: 93
          verb: list
        requestCount: 93
        userAgent: Mozilla/5.0
        username: I5292
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 14
          verb: list
        requestCount: 14
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: Mozilla/5.0
        username: S0914
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: oc/4.16.0
        username: system:serviceaccount:openshift-must-gather-6mxgs:default
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: I2053
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: V8131
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: L9323
      nodeName: 10.21.102.12
      requestCount: 149
    - byUser:
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 81
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 41
          verb: list
        requestCount: 41
        userAgent: Mozilla/5.0
        username: I5292
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 12
          verb: list
        requestCount: 12
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 299
    requestCount: 593
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: Z8186
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.21.102.11
      requestCount: 5
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.12
      requestCount: 2
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 8
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 7
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 2
          verb: watch
        requestCount: 2
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: M4108
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: Z8186
      nodeName: 10.21.102.9
      requestCount: 26
    requestCount: 33
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 73
          verb: list
        requestCount: 73
        userAgent: Mozilla/5.0
        username: Z3128
      - byVerb:
        - requestCount: 66
          verb: list
        requestCount: 66
        userAgent: Mozilla/5.0
        username: L9360
      - byVerb:
        - requestCount: 37
          verb: list
        requestCount: 37
        userAgent: Mozilla/5.0
        username: Z9083
      - byVerb:
        - requestCount: 31
          verb: list
        requestCount: 31
        userAgent: Mozilla/5.0
        username: E2944
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 15
          verb: list
        requestCount: 15
        userAgent: Mozilla/5.0
        username: I8906
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: trident_orchestrator/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.11
      requestCount: 275
    - byUser:
      - byVerb:
        - requestCount: 85
          verb: list
        requestCount: 85
        userAgent: Mozilla/5.0
        username: L9360
      - byVerb:
        - requestCount: 62
          verb: list
        requestCount: 62
        userAgent: Mozilla/5.0
        username: Z3128
      - byVerb:
        - requestCount: 41
          verb: list
        requestCount: 41
        userAgent: Mozilla/5.0
        username: Z9083
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: Mozilla/5.0
        username: E2944
      - byVerb:
        - requestCount: 23
          verb: list
        requestCount: 23
        userAgent: Mozilla/5.0
        username: I8906
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: Mozilla/5.0
        username: Z6937
      nodeName: 10.21.102.12
      requestCount: 296
    - byUser:
      - byVerb:
        - requestCount: 71
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 80
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 71
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 79
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 73
          verb: list
        requestCount: 73
        userAgent: Mozilla/5.0
        username: L9360
      - byVerb:
        - requestCount: 62
          verb: list
        requestCount: 62
        userAgent: Mozilla/5.0
        username: Z3128
      - byVerb:
        - requestCount: 39
          verb: list
        requestCount: 39
        userAgent: Mozilla/5.0
        username: Z9083
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: Mozilla/5.0
        username: E2944
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: Mozilla/5.0
        username: I8906
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 12
          verb: list
        requestCount: 12
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 431
    requestCount: 1002
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 84
          verb: list
        requestCount: 84
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 84
          verb: list
        requestCount: 84
        userAgent: Mozilla/5.0
        username: L9360
      - byVerb:
        - requestCount: 39
          verb: list
        requestCount: 39
        userAgent: Mozilla/5.0
        username: E2944
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 24
          verb: list
        requestCount: 24
        userAgent: Mozilla/5.0
        username: I8906
      - byVerb:
        - requestCount: 23
          verb: list
        requestCount: 23
        userAgent: Mozilla/5.0
        username: Z9083
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 16
          verb: list
        requestCount: 16
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.11
      requestCount: 330
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: list
        requestCount: 69
        userAgent: Mozilla/5.0
        username: E2944
      - byVerb:
        - requestCount: 49
          verb: list
        requestCount: 49
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 47
          verb: list
        requestCount: 47
        userAgent: Mozilla/5.0
        username: L9360
      - byVerb:
        - requestCount: 23
          verb: list
        requestCount: 23
        userAgent: Mozilla/5.0
        username: Z9083
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: Mozilla/5.0
        username: I8906
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 265
    - byUser:
      - byVerb:
        - requestCount: 94
          verb: list
        requestCount: 94
        userAgent: Mozilla/5.0
        username: L9360
      - byVerb:
        - requestCount: 83
          verb: list
        requestCount: 83
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 81
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 40
          verb: list
        requestCount: 40
        userAgent: Mozilla/5.0
        username: Z9083
      - byVerb:
        - requestCount: 37
          verb: list
        requestCount: 37
        userAgent: Mozilla/5.0
        username: E2944
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 28
          verb: list
        requestCount: 28
        userAgent: Mozilla/5.0
        username: I8906
      - byVerb:
        - requestCount: 16
          verb: list
        requestCount: 16
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.21.102.9
      requestCount: 504
    requestCount: 1099
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 85
          verb: list
        requestCount: 85
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 84
          verb: list
        requestCount: 84
        userAgent: Mozilla/5.0
        username: L9360
      - byVerb:
        - requestCount: 65
          verb: list
        requestCount: 65
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 44
          verb: list
        requestCount: 44
        userAgent: Mozilla/5.0
        username: E2944
      - byVerb:
        - requestCount: 21
          verb: list
        requestCount: 21
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: trident_orchestrator/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      nodeName: 10.21.102.11
      requestCount: 347
    - byUser:
      - byVerb:
        - requestCount: 77
          verb: list
        requestCount: 77
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 74
          verb: list
        requestCount: 74
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 73
          verb: list
        requestCount: 73
        userAgent: Mozilla/5.0
        username: L9360
      - byVerb:
        - requestCount: 43
          verb: list
        requestCount: 43
        userAgent: Mozilla/5.0
        username: E2944
      - byVerb:
        - requestCount: 29
          verb: list
        requestCount: 29
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 342
    - byUser:
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 68
          verb: list
        requestCount: 68
        userAgent: Mozilla/5.0
        username: L9360
      - byVerb:
        - requestCount: 61
          verb: list
        requestCount: 61
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 54
          verb: list
        requestCount: 54
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 37
          verb: list
        requestCount: 37
        userAgent: Mozilla/5.0
        username: E2944
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 16
          verb: list
        requestCount: 16
        userAgent: Mozilla/5.0
        username: L9421
      nodeName: 10.21.102.9
      requestCount: 460
    requestCount: 1149
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 77
          verb: list
        requestCount: 77
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 69
          verb: list
        requestCount: 69
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 57
          verb: list
        requestCount: 57
        userAgent: Mozilla/5.0
        username: E2944
      - byVerb:
        - requestCount: 51
          verb: list
        requestCount: 51
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 46
          verb: list
        requestCount: 46
        userAgent: Mozilla/5.0
        username: L9360
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: trident_orchestrator/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.21.102.11
      requestCount: 350
    - byUser:
      - byVerb:
        - requestCount: 98
          verb: list
        requestCount: 98
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 77
          verb: list
        requestCount: 77
        userAgent: Mozilla/5.0
        username: E2944
      - byVerb:
        - requestCount: 71
          verb: list
        requestCount: 71
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 56
          verb: list
        requestCount: 56
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 39
          verb: list
        requestCount: 39
        userAgent: Mozilla/5.0
        username: L9360
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 391
    - byUser:
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 81
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 79
          verb: list
        requestCount: 79
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 47
          verb: list
        requestCount: 47
        userAgent: Mozilla/5.0
        username: E2944
      - byVerb:
        - requestCount: 46
          verb: list
        requestCount: 46
        userAgent: Mozilla/5.0
        username: L9360
      - byVerb:
        - requestCount: 42
          verb: list
        requestCount: 42
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 473
    requestCount: 1214
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 81
          verb: list
        requestCount: 81
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 68
          verb: list
        requestCount: 68
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 68
          verb: list
        requestCount: 68
        userAgent: Mozilla/5.0
        username: E2944
      - byVerb:
        - requestCount: 54
          verb: list
        requestCount: 54
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 53
          verb: list
        requestCount: 53
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 12
          verb: list
        requestCount: 12
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: trident_orchestrator/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 376
    - byUser:
      - byVerb:
        - requestCount: 96
          verb: list
        requestCount: 96
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 76
          verb: list
        requestCount: 76
        userAgent: Mozilla/5.0
        username: E2944
      - byVerb:
        - requestCount: 72
          verb: list
        requestCount: 72
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 59
          verb: list
        requestCount: 59
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 57
          verb: list
        requestCount: 57
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 405
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 79
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 77
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 67
          verb: list
        requestCount: 67
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 60
          verb: list
        requestCount: 60
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 49
          verb: list
        requestCount: 49
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 42
          verb: list
        requestCount: 42
        userAgent: Mozilla/5.0
        username: E2944
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 22
          verb: list
        requestCount: 22
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 18
          verb: list
        requestCount: 18
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.21.102.9
      requestCount: 462
    requestCount: 1243
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 81
          verb: list
        requestCount: 81
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 80
          verb: list
        requestCount: 80
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 68
          verb: list
        requestCount: 68
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 55
          verb: list
        requestCount: 55
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 12
          verb: list
        requestCount: 12
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: trident_orchestrator/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      nodeName: 10.21.102.11
      requestCount: 342
    - byUser:
      - byVerb:
        - requestCount: 81
          verb: list
        requestCount: 81
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 72
          verb: list
        requestCount: 72
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 61
          verb: list
        requestCount: 61
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 55
          verb: list
        requestCount: 55
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: Mozilla/5.0
        username: E2944
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 326
    - byUser:
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 82
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 81
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 76
          verb: list
        requestCount: 76
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 75
          verb: list
        requestCount: 75
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 72
          verb: list
        requestCount: 72
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 60
          verb: list
        requestCount: 60
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 13
          verb: list
        requestCount: 13
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 516
    requestCount: 1184
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 86
          verb: list
        requestCount: 86
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 82
          verb: list
        requestCount: 82
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 81
          verb: list
        requestCount: 81
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 62
          verb: list
        requestCount: 62
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 13
          verb: list
        requestCount: 13
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: trident_orchestrator/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.11
      requestCount: 372
    - byUser:
      - byVerb:
        - requestCount: 89
          verb: list
        requestCount: 89
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 84
          verb: list
        requestCount: 84
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 72
          verb: list
        requestCount: 72
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 66
          verb: list
        requestCount: 66
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 359
    - byUser:
      - byVerb:
        - requestCount: 71
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 80
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: list
        requestCount: 60
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 55
          verb: list
        requestCount: 55
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 54
          verb: list
        requestCount: 54
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: list
        requestCount: 16
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 443
    requestCount: 1174
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: list
        requestCount: 69
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 57
          verb: list
        requestCount: 57
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 57
          verb: list
        requestCount: 57
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 39
          verb: list
        requestCount: 39
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: trident_orchestrator/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.21.102.11
      requestCount: 282
    - byUser:
      - byVerb:
        - requestCount: 96
          verb: list
        requestCount: 96
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 92
          verb: list
        requestCount: 92
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 78
          verb: list
        requestCount: 78
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 71
          verb: list
        requestCount: 71
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.12
      requestCount: 394
    - byUser:
      - byVerb:
        - requestCount: 71
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 79
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 71
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 76
          verb: list
        requestCount: 76
        userAgent: Mozilla/5.0
        username: Z6937
      - byVerb:
        - requestCount: 76
          verb: list
        requestCount: 76
        userAgent: Mozilla/5.0
        username: E8324
      - byVerb:
        - requestCount: 72
          verb: list
        requestCount: 72
        userAgent: Mozilla/5.0
        username: L9421
      - byVerb:
        - requestCount: 47
          verb: list
        requestCount: 47
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 499
    requestCount: 1175
  requestCount: 16854
