---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2023-12-19T12:53:40Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2023-12-19T12:53:40Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:06:37Z"
  name: apirequestcounts.v1.apiserver.openshift.io
  resourceVersion: "770115801"
  uid: d341fb35-5dee-4a21-9e78-f5bcbb21b961
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 379
          verb: get
        - requestCount: 168
          verb: update
        requestCount: 547
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 547
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 738
          verb: get
        - requestCount: 352
          verb: update
        requestCount: 1090
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 1091
    requestCount: 1638
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8528
          verb: get
        - requestCount: 3845
          verb: update
        requestCount: 12373
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 12377
    - byUser:
      - byVerb:
        - requestCount: 8644
          verb: get
        - requestCount: 3976
          verb: update
        requestCount: 12620
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 12635
    - byUser:
      - byVerb:
        - requestCount: 8748
          verb: get
        - requestCount: 4076
          verb: update
        requestCount: 12824
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 12843
    requestCount: 37855
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8456
          verb: get
        - requestCount: 3789
          verb: update
        requestCount: 12245
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 12250
    - byUser:
      - byVerb:
        - requestCount: 8668
          verb: get
        - requestCount: 4000
          verb: update
        requestCount: 12668
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 12683
    - byUser:
      - byVerb:
        - requestCount: 8732
          verb: get
        - requestCount: 4065
          verb: update
        requestCount: 12797
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 12816
    requestCount: 37749
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8543
          verb: get
        - requestCount: 3869
          verb: update
        requestCount: 12412
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 12418
    - byUser:
      - byVerb:
        - requestCount: 8671
          verb: get
        - requestCount: 4002
          verb: update
        requestCount: 12673
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 12686
    - byUser:
      - byVerb:
        - requestCount: 8743
          verb: get
        - requestCount: 4070
          verb: update
        requestCount: 12813
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 12831
    requestCount: 37935
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8121
          verb: get
        - requestCount: 3652
          verb: update
        requestCount: 11773
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 11779
    - byUser:
      - byVerb:
        - requestCount: 8665
          verb: get
        - requestCount: 3995
          verb: update
        requestCount: 12660
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 12673
    - byUser:
      - byVerb:
        - requestCount: 8718
          verb: get
        - requestCount: 4054
          verb: update
        requestCount: 12772
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 13
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 12794
    requestCount: 37246
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8590
          verb: get
        - requestCount: 3916
          verb: update
        requestCount: 12506
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 12517
    - byUser:
      - byVerb:
        - requestCount: 8655
          verb: get
        - requestCount: 3987
          verb: update
        requestCount: 12642
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 12658
    - byUser:
      - byVerb:
        - requestCount: 8701
          verb: get
        - requestCount: 4029
          verb: update
        requestCount: 12730
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 12745
    requestCount: 37920
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8525
          verb: get
        - requestCount: 3864
          verb: update
        requestCount: 12389
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 12395
    - byUser:
      - byVerb:
        - requestCount: 8650
          verb: get
        - requestCount: 3982
          verb: update
        requestCount: 12632
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 12647
    - byUser:
      - byVerb:
        - requestCount: 8742
          verb: get
        - requestCount: 4069
          verb: update
        requestCount: 12811
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 12829
    requestCount: 37871
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8564
          verb: get
        - requestCount: 3881
          verb: update
        requestCount: 12445
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 12454
    - byUser:
      - byVerb:
        - requestCount: 8640
          verb: get
        - requestCount: 3972
          verb: update
        requestCount: 12612
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 12625
    - byUser:
      - byVerb:
        - requestCount: 8667
          verb: get
        - requestCount: 4002
          verb: update
        requestCount: 12669
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 12685
    requestCount: 37764
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8580
          verb: get
        - requestCount: 3930
          verb: update
        requestCount: 12510
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 12520
    - byUser:
      - byVerb:
        - requestCount: 8647
          verb: get
        - requestCount: 3978
          verb: update
        requestCount: 12625
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 12638
    - byUser:
      - byVerb:
        - requestCount: 8778
          verb: get
        - requestCount: 4109
          verb: update
        requestCount: 12887
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 12904
    requestCount: 38062
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8498
          verb: get
        - requestCount: 3813
          verb: update
        requestCount: 12311
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 12314
    - byUser:
      - byVerb:
        - requestCount: 8673
          verb: get
        - requestCount: 4005
          verb: update
        requestCount: 12678
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 12
          verb: list
        requestCount: 12
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 12698
    - byUser:
      - byVerb:
        - requestCount: 8706
          verb: get
        - requestCount: 4043
          verb: update
        requestCount: 12749
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 12764
    requestCount: 37776
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8448
          verb: get
        - requestCount: 3791
          verb: update
        requestCount: 12239
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 12247
    - byUser:
      - byVerb:
        - requestCount: 8621
          verb: get
        - requestCount: 3952
          verb: update
        requestCount: 12573
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 12586
    - byUser:
      - byVerb:
        - requestCount: 8739
          verb: get
        - requestCount: 4066
          verb: update
        requestCount: 12805
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 12824
    requestCount: 37657
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8484
          verb: get
        - requestCount: 3804
          verb: update
        requestCount: 12288
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 12292
    - byUser:
      - byVerb:
        - requestCount: 8623
          verb: get
        - requestCount: 3954
          verb: update
        requestCount: 12577
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 12592
    - byUser:
      - byVerb:
        - requestCount: 8733
          verb: get
        - requestCount: 4066
          verb: update
        requestCount: 12799
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 12817
    requestCount: 37701
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8562
          verb: get
        - requestCount: 3883
          verb: update
        requestCount: 12445
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 12454
    - byUser:
      - byVerb:
        - requestCount: 8563
          verb: get
        - requestCount: 3895
          verb: update
        requestCount: 12458
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 12467
    - byUser:
      - byVerb:
        - requestCount: 8749
          verb: get
        - requestCount: 4080
          verb: update
        requestCount: 12829
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 3
          verb: watch
        requestCount: 4
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 12851
    requestCount: 37772
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8475
          verb: get
        - requestCount: 3826
          verb: update
        requestCount: 12301
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 12307
    - byUser:
      - byVerb:
        - requestCount: 8496
          verb: get
        - requestCount: 3827
          verb: update
        requestCount: 12323
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 12332
    - byUser:
      - byVerb:
        - requestCount: 8806
          verb: get
        - requestCount: 4132
          verb: update
        requestCount: 12938
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 12966
    requestCount: 37605
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8401
          verb: get
        - requestCount: 3742
          verb: update
        requestCount: 12143
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 12148
    - byUser:
      - byVerb:
        - requestCount: 8427
          verb: get
        - requestCount: 3759
          verb: update
        requestCount: 12186
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 12193
    - byUser:
      - byVerb:
        - requestCount: 8795
          verb: get
        - requestCount: 4133
          verb: update
        requestCount: 12928
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 12956
    requestCount: 37297
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 379
          verb: get
        - requestCount: 168
          verb: update
        requestCount: 547
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 547
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 738
          verb: get
        - requestCount: 352
          verb: update
        requestCount: 1090
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 1091
    requestCount: 1638
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8500
          verb: get
        - requestCount: 3836
          verb: update
        requestCount: 12336
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 12343
    - byUser:
      - byVerb:
        - requestCount: 8616
          verb: get
        - requestCount: 3948
          verb: update
        requestCount: 12564
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 12577
    - byUser:
      - byVerb:
        - requestCount: 8711
          verb: get
        - requestCount: 4046
          verb: update
        requestCount: 12757
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 12777
    requestCount: 37697
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8484
          verb: get
        - requestCount: 3805
          verb: update
        requestCount: 12289
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 12293
    - byUser:
      - byVerb:
        - requestCount: 8646
          verb: get
        - requestCount: 3977
          verb: update
        requestCount: 12623
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 12639
    - byUser:
      - byVerb:
        - requestCount: 8713
          verb: get
        - requestCount: 4044
          verb: update
        requestCount: 12757
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 12773
    requestCount: 37705
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8416
          verb: get
        - requestCount: 3745
          verb: update
        requestCount: 12161
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 12162
    - byUser:
      - byVerb:
        - requestCount: 8683
          verb: get
        - requestCount: 4014
          verb: update
        requestCount: 12697
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 12715
    - byUser:
      - byVerb:
        - requestCount: 8744
          verb: get
        - requestCount: 4070
          verb: update
        requestCount: 12814
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 12836
    requestCount: 37713
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8508
          verb: get
        - requestCount: 3834
          verb: update
        requestCount: 12342
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 12347
    - byUser:
      - byVerb:
        - requestCount: 8645
          verb: get
        - requestCount: 3976
          verb: update
        requestCount: 12621
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 12635
    - byUser:
      - byVerb:
        - requestCount: 8737
          verb: get
        - requestCount: 4073
          verb: update
        requestCount: 12810
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 12828
    requestCount: 37810
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8555
          verb: get
        - requestCount: 3898
          verb: update
        requestCount: 12453
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 12460
    - byUser:
      - byVerb:
        - requestCount: 8654
          verb: get
        - requestCount: 3986
          verb: update
        requestCount: 12640
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 12655
    - byUser:
      - byVerb:
        - requestCount: 8735
          verb: get
        - requestCount: 4057
          verb: update
        requestCount: 12792
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 12809
    requestCount: 37924
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8476
          verb: get
        - requestCount: 3798
          verb: update
        requestCount: 12274
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 12277
    - byUser:
      - byVerb:
        - requestCount: 8697
          verb: get
        - requestCount: 4029
          verb: update
        requestCount: 12726
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 12742
    - byUser:
      - byVerb:
        - requestCount: 8734
          verb: get
        - requestCount: 4074
          verb: update
        requestCount: 12808
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 12828
    requestCount: 37847
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8582
          verb: get
        - requestCount: 3907
          verb: update
        requestCount: 12489
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 12497
    - byUser:
      - byVerb:
        - requestCount: 8656
          verb: get
        - requestCount: 3988
          verb: update
        requestCount: 12644
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 12656
    - byUser:
      - byVerb:
        - requestCount: 8766
          verb: get
        - requestCount: 4087
          verb: update
        requestCount: 12853
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 12870
    requestCount: 38023
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8489
          verb: get
        - requestCount: 3832
          verb: update
        requestCount: 12321
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 12327
    - byUser:
      - byVerb:
        - requestCount: 8607
          verb: get
        - requestCount: 3939
          verb: update
        requestCount: 12546
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 12556
    - byUser:
      - byVerb:
        - requestCount: 8724
          verb: get
        - requestCount: 4064
          verb: update
        requestCount: 12788
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 12810
    requestCount: 37693
  requestCount: 832260
