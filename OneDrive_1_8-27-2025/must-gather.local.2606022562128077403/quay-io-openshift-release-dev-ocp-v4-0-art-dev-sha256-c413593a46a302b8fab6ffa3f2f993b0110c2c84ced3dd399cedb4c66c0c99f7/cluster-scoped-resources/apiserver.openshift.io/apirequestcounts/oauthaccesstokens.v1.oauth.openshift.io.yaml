---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2023-12-19T16:51:18Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2023-12-19T16:51:18Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:05:10Z"
  name: oauthaccesstokens.v1.oauth.openshift.io
  resourceVersion: "770113050"
  uid: 94e48bcb-acd9-4567-904e-eef060c3fd8c
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    requestCount: 0
  last24h:
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 14
    requestCount: 14
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: create
        requestCount: 3
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 15
    requestCount: 15
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: delete
        requestCount: 1
        userAgent: bridge/v0.0.0
        username: Z8504
      nodeName: 10.21.102.11
      requestCount: 1
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 4
          verb: create
        requestCount: 4
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 16
    requestCount: 17
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 14
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 14
    requestCount: 14
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: delete
        requestCount: 1
        userAgent: bridge/v0.0.0
        username: L9334
      nodeName: 10.21.102.11
      requestCount: 1
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 13
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: create
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 1
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: delete
        requestCount: 1
        userAgent: oc/4.15.0
        username: z_unixauto
      nodeName: 10.21.102.9
      requestCount: 13
    requestCount: 14
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 12
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: create
        requestCount: 3
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 15
    requestCount: 15
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 12
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 12
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 12
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: create
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 13
    requestCount: 13
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: create
        requestCount: 7
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 19
    requestCount: 19
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: create
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 1
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: create
        requestCount: 5
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 17
    requestCount: 18
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    requestCount: 0
  - requestCount: 0
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: create
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 13
    requestCount: 13
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: create
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 1
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: create
        requestCount: 3
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 1
          verb: delete
        requestCount: 1
        userAgent: oc/4.15.0
        username: z_unixauto
      nodeName: 10.21.102.9
      requestCount: 16
    requestCount: 17
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: create
        requestCount: 6
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 18
    requestCount: 18
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: create
        requestCount: 6
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 18
    requestCount: 18
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 12
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: delete
        requestCount: 1
        userAgent: bridge/v0.0.0
        username: Z8504
      nodeName: 10.21.102.11
      requestCount: 1
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: create
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 13
    requestCount: 14
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 12
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: create
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 1
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: create
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 13
    requestCount: 14
  requestCount: 318
