---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2023-12-19T12:53:04Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2023-12-19T12:53:04Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:05:52Z"
  name: volumesnapshotclasses.v1.snapshot.storage.k8s.io
  resourceVersion: "*********"
  uid: c178515f-2a45-4115-8d59-ab58e3ffce2d
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.21.102.11
      requestCount: 2
    requestCount: 2
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 37
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 13
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      nodeName: 10.21.102.9
      requestCount: 146
    requestCount: 196
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 38
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 147
    requestCount: 200
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 39
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 13
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 145
    requestCount: 197
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 38
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 14
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 7
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 14
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      nodeName: 10.21.102.9
      requestCount: 150
    requestCount: 202
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      nodeName: 10.21.102.11
      requestCount: 46
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 144
    requestCount: 205
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 36
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 146
    requestCount: 197
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      nodeName: 10.21.102.11
      requestCount: 39
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 14
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 145
    requestCount: 198
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.21.102.11
      requestCount: 43
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 11
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 144
    requestCount: 198
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 33
    - byUser:
      - byVerb:
        - requestCount: 12
          verb: list
        requestCount: 12
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 20
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 143
    requestCount: 196
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      nodeName: 10.21.102.11
      requestCount: 40
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 13
    - byUser:
      - byVerb:
        - requestCount: 62
          verb: get
        - requestCount: 62
          verb: update
        requestCount: 124
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 151
    requestCount: 204
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 38
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 14
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 146
    requestCount: 198
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      nodeName: 10.21.102.11
      requestCount: 41
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 10
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 152
    requestCount: 203
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 39
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 155
    requestCount: 203
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: oc/4.16.0
        username: system:serviceaccount:openshift-must-gather-6mxgs:default
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-6mxgs:default
      nodeName: 10.21.102.11
      requestCount: 41
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      nodeName: 10.21.102.9
      requestCount: 156
    requestCount: 204
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.21.102.11
      requestCount: 2
    requestCount: 2
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 39
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 13
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 148
    requestCount: 200
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 35
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 144
    requestCount: 195
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 34
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      nodeName: 10.21.102.9
      requestCount: 150
    requestCount: 201
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 38
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 147
    requestCount: 200
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 41
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 14
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 144
    requestCount: 199
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 32
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 62
          verb: get
        - requestCount: 62
          verb: update
        requestCount: 124
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      nodeName: 10.21.102.9
      requestCount: 151
    requestCount: 199
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      nodeName: 10.21.102.11
      requestCount: 41
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 11
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 145
    requestCount: 197
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 39
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 12
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      nodeName: 10.21.102.9
      requestCount: 150
    requestCount: 201
  requestCount: 4395
