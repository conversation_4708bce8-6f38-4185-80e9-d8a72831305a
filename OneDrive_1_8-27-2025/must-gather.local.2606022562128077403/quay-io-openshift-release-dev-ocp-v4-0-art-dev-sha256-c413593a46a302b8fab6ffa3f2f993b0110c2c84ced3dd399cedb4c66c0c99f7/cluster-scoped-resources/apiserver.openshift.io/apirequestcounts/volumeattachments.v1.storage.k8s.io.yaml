---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2023-12-19T12:55:09Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2023-12-19T12:55:09Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:06:10Z"
  name: volumeattachments.v1.storage.k8s.io
  resourceVersion: "*********"
  uid: a92d6921-de29-40de-a3ae-9f0ff6af856f
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.21.102.11
      requestCount: 1
    - byUser:
      - byVerb:
        - requestCount: 24
          verb: patch
        - requestCount: 1
          verb: watch
        requestCount: 25
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 27
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: create
        - requestCount: 7
          verb: delete
        requestCount: 12
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-tssl4
      - byVerb:
        - requestCount: 2
          verb: watch
        requestCount: 2
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 30
    requestCount: 58
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      nodeName: 10.21.102.11
      requestCount: 34
    - byUser:
      - byVerb:
        - requestCount: 144
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 151
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 26
          verb: create
        - requestCount: 25
          verb: delete
        requestCount: 51
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      nodeName: 10.21.102.12
      requestCount: 240
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-tcmdz
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-dgkqt
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-sqqdl
      nodeName: 10.21.102.9
      requestCount: 100
    requestCount: 374
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 32
    - byUser:
      - byVerb:
        - requestCount: 19
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 27
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 5
          verb: create
        - requestCount: 2
          verb: delete
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      nodeName: 10.21.102.12
      requestCount: 58
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      nodeName: 10.21.102.9
      requestCount: 60
    requestCount: 150
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.21.102.11
      requestCount: 39
    - byUser:
      - byVerb:
        - requestCount: 39
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 48
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 5
          verb: create
        - requestCount: 8
          verb: delete
        requestCount: 13
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      nodeName: 10.21.102.12
      requestCount: 88
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      nodeName: 10.21.102.9
      requestCount: 46
    requestCount: 173
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      nodeName: 10.21.102.11
      requestCount: 36
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 15
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 2
          verb: create
        - requestCount: 1
          verb: delete
        requestCount: 3
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      nodeName: 10.21.102.12
      requestCount: 42
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 7
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 14
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 45
    requestCount: 123
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      nodeName: 10.21.102.11
      requestCount: 37
    - byUser:
      - byVerb:
        - requestCount: 48
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 56
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 9
          verb: create
        - requestCount: 9
          verb: delete
        requestCount: 18
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      nodeName: 10.21.102.12
      requestCount: 105
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-tssl4
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-7599c
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      nodeName: 10.21.102.9
      requestCount: 60
    requestCount: 202
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 42
          verb: get
        requestCount: 42
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.21.102.11
      requestCount: 75
    - byUser:
      - byVerb:
        - requestCount: 148
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 157
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 27
          verb: create
        - requestCount: 24
          verb: delete
        requestCount: 51
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 251
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      nodeName: 10.21.102.9
      requestCount: 65
    requestCount: 391
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 45
    - byUser:
      - byVerb:
        - requestCount: 76
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 85
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 12
          verb: create
        - requestCount: 15
          verb: delete
        requestCount: 27
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      nodeName: 10.21.102.12
      requestCount: 139
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 65
    requestCount: 249
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: get
        requestCount: 30
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 61
    - byUser:
      - byVerb:
        - requestCount: 248
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 255
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 44
          verb: create
        - requestCount: 43
          verb: delete
        - requestCount: 1
          verb: get
        requestCount: 88
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 30
          verb: get
        requestCount: 30
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 405
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-7599c
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-tssl4
      nodeName: 10.21.102.9
      requestCount: 107
    requestCount: 573
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.21.102.11
      requestCount: 41
    - byUser:
      - byVerb:
        - requestCount: 230
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 238
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 49
          verb: create
        - requestCount: 44
          verb: delete
        requestCount: 93
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 39
          verb: get
        requestCount: 39
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      nodeName: 10.21.102.12
      requestCount: 394
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: get
        requestCount: 33
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 21
          verb: get
        requestCount: 21
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-5wphs
      nodeName: 10.21.102.9
      requestCount: 136
    requestCount: 571
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      nodeName: 10.21.102.11
      requestCount: 35
    - byUser:
      - byVerb:
        - requestCount: 140
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 148
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 30
          verb: create
        - requestCount: 37
          verb: delete
        requestCount: 67
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 256
    - byUser:
      - byVerb:
        - requestCount: 27
          verb: get
        requestCount: 27
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 24
          verb: get
        requestCount: 24
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      nodeName: 10.21.102.9
      requestCount: 113
    requestCount: 404
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      nodeName: 10.21.102.11
      requestCount: 37
    - byUser:
      - byVerb:
        - requestCount: 108
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 117
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 31
          verb: create
        - requestCount: 22
          verb: delete
        requestCount: 53
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 27
          verb: get
        requestCount: 27
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      nodeName: 10.21.102.12
      requestCount: 223
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: get
        requestCount: 17
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-5wphs
      nodeName: 10.21.102.9
      requestCount: 103
    requestCount: 363
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-5wphs
      nodeName: 10.21.102.11
      requestCount: 46
    - byUser:
      - byVerb:
        - requestCount: 236
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 244
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 29
          verb: create
        - requestCount: 30
          verb: delete
        requestCount: 59
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      nodeName: 10.21.102.12
      requestCount: 357
    - byUser:
      - byVerb:
        - requestCount: 29
          verb: create
        - requestCount: 26
          verb: delete
        requestCount: 55
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 30
          verb: get
        requestCount: 30
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 24
          verb: get
        requestCount: 24
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 21
          verb: get
        requestCount: 21
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-f6gz2
      nodeName: 10.21.102.9
      requestCount: 197
    requestCount: 600
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 24
          verb: get
        requestCount: 24
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-5wphs
      nodeName: 10.21.102.11
      requestCount: 59
    - byUser:
      - byVerb:
        - requestCount: 253
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 261
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 27
          verb: get
        requestCount: 27
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      nodeName: 10.21.102.12
      requestCount: 310
    - byUser:
      - byVerb:
        - requestCount: 59
          verb: create
        - requestCount: 65
          verb: delete
        requestCount: 124
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 27
          verb: get
        requestCount: 27
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 24
          verb: get
        requestCount: 24
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 24
          verb: get
        requestCount: 24
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 275
    requestCount: 644
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 21
          verb: get
        requestCount: 21
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-5wphs
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-6mxgs:default
      nodeName: 10.21.102.11
      requestCount: 64
    - byUser:
      - byVerb:
        - requestCount: 319
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 327
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 21
          verb: get
        requestCount: 21
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      nodeName: 10.21.102.12
      requestCount: 382
    - byUser:
      - byVerb:
        - requestCount: 80
          verb: create
        - requestCount: 74
          verb: delete
        requestCount: 154
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 27
          verb: get
        requestCount: 27
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 21
          verb: get
        requestCount: 21
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 21
          verb: get
        requestCount: 21
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-tssl4
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 317
    requestCount: 763
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.21.102.11
      requestCount: 1
    - byUser:
      - byVerb:
        - requestCount: 24
          verb: patch
        - requestCount: 1
          verb: watch
        requestCount: 25
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 27
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: create
        - requestCount: 7
          verb: delete
        requestCount: 12
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-tssl4
      - byVerb:
        - requestCount: 2
          verb: watch
        requestCount: 2
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 30
    requestCount: 58
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.21.102.11
      requestCount: 48
    - byUser:
      - byVerb:
        - requestCount: 283
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 290
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 52
          verb: create
        - requestCount: 49
          verb: delete
        requestCount: 101
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      nodeName: 10.21.102.12
      requestCount: 427
    - byUser:
      - byVerb:
        - requestCount: 27
          verb: get
        requestCount: 27
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 24
          verb: get
        requestCount: 24
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 21
          verb: get
        requestCount: 21
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 21
          verb: get
        requestCount: 21
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-dgkqt
      nodeName: 10.21.102.9
      requestCount: 158
    requestCount: 633
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 42
    - byUser:
      - byVerb:
        - requestCount: 314
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 321
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 60
          verb: create
        - requestCount: 55
          verb: delete
        requestCount: 115
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      nodeName: 10.21.102.12
      requestCount: 473
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: get
        requestCount: 30
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 27
          verb: get
        requestCount: 27
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 27
          verb: get
        requestCount: 27
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 24
          verb: get
        requestCount: 24
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 21
          verb: get
        requestCount: 21
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 21
          verb: get
        requestCount: 21
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.21.102.9
      requestCount: 192
    requestCount: 707
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 21
          verb: get
        requestCount: 21
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.21.102.11
      requestCount: 53
    - byUser:
      - byVerb:
        - requestCount: 437
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 445
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 79
          verb: create
        - requestCount: 85
          verb: delete
        requestCount: 164
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 24
          verb: get
        requestCount: 24
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 657
    - byUser:
      - byVerb:
        - requestCount: 39
          verb: get
        requestCount: 39
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 30
          verb: get
        requestCount: 30
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 24
          verb: get
        requestCount: 24
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 21
          verb: get
        requestCount: 21
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 21
          verb: get
        requestCount: 21
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-dgkqt
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-24ggt
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-rwxsl
      nodeName: 10.21.102.9
      requestCount: 187
    requestCount: 897
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 21
          verb: get
        requestCount: 21
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.21.102.11
      requestCount: 54
    - byUser:
      - byVerb:
        - requestCount: 316
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 325
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 55
          verb: create
        - requestCount: 54
          verb: delete
        - requestCount: 1
          verb: get
        requestCount: 110
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 24
          verb: get
        requestCount: 24
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      nodeName: 10.21.102.12
      requestCount: 483
    - byUser:
      - byVerb:
        - requestCount: 27
          verb: get
        requestCount: 27
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 24
          verb: get
        requestCount: 24
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 142
    requestCount: 679
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: get
        requestCount: 36
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.21.102.11
      requestCount: 67
    - byUser:
      - byVerb:
        - requestCount: 425
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 432
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 81
          verb: create
        - requestCount: 78
          verb: delete
        requestCount: 159
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 630
    - byUser:
      - byVerb:
        - requestCount: 54
          verb: get
        requestCount: 54
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 30
          verb: get
        requestCount: 30
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 30
          verb: get
        requestCount: 30
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 24
          verb: get
        requestCount: 24
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-sqqdl
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      nodeName: 10.21.102.9
      requestCount: 235
    requestCount: 932
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      nodeName: 10.21.102.11
      requestCount: 36
    - byUser:
      - byVerb:
        - requestCount: 143
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 151
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 22
          verb: create
        - requestCount: 29
          verb: delete
        requestCount: 51
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 239
    - byUser:
      - byVerb:
        - requestCount: 21
          verb: get
        requestCount: 21
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-sqqdl
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      nodeName: 10.21.102.9
      requestCount: 93
    requestCount: 368
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.21.102.11
      requestCount: 33
    - byUser:
      - byVerb:
        - requestCount: 56
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 65
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 11
          verb: create
        - requestCount: 10
          verb: delete
        requestCount: 21
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      nodeName: 10.21.102.12
      requestCount: 112
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-sqqdl
      nodeName: 10.21.102.9
      requestCount: 77
    requestCount: 222
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.21.102.11
      requestCount: 30
    - byUser:
      - byVerb:
        - requestCount: 96
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 103
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 15
          verb: create
        - requestCount: 18
          verb: delete
        requestCount: 33
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:trident:trident-controller
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      nodeName: 10.21.102.12
      requestCount: 165
    - byUser:
      - byVerb:
        - requestCount: 24
          verb: get
        requestCount: 24
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-tcmdz
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-dgkqt
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-24ggt
      nodeName: 10.21.102.9
      requestCount: 92
    requestCount: 287
  requestCount: 10363
