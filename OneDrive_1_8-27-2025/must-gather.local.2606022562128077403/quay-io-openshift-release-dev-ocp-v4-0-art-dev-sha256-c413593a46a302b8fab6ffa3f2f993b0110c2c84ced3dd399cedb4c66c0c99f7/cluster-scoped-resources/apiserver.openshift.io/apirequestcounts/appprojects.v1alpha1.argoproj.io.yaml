---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2024-01-23T20:21:57Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2024-01-23T20:21:57Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:07:04Z"
  name: appprojects.v1alpha1.argoproj.io
  resourceVersion: "*********"
  uid: 45c680c4-a26c-465a-b99d-b3266e8a7b21
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 37
    - byUser:
      - byVerb:
        - requestCount: 65
          verb: list
        requestCount: 65
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 65
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: list
        requestCount: 43
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 2
          verb: watch
        requestCount: 3
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 50
    requestCount: 152
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 830
          verb: list
        requestCount: 830
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 846
    - byUser:
      - byVerb:
        - requestCount: 773
          verb: list
        requestCount: 773
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 781
    - byUser:
      - byVerb:
        - requestCount: 766
          verb: list
        requestCount: 766
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 820
    requestCount: 2447
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 879
          verb: list
        requestCount: 879
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 895
    - byUser:
      - byVerb:
        - requestCount: 858
          verb: list
        requestCount: 858
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 865
    - byUser:
      - byVerb:
        - requestCount: 879
          verb: list
        requestCount: 879
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 932
    requestCount: 2692
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 877
          verb: list
        requestCount: 877
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 892
    - byUser:
      - byVerb:
        - requestCount: 816
          verb: list
        requestCount: 816
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 824
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 31
          verb: list
        - requestCount: 25
          verb: watch
        requestCount: 56
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 917
    requestCount: 2633
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1005
          verb: list
        requestCount: 1005
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 1021
    - byUser:
      - byVerb:
        - requestCount: 994
          verb: list
        requestCount: 994
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 1001
    - byUser:
      - byVerb:
        - requestCount: 970
          verb: list
        requestCount: 970
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 7
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 14
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 1023
    requestCount: 3045
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 918
          verb: list
        requestCount: 918
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 935
    - byUser:
      - byVerb:
        - requestCount: 976
          verb: list
        requestCount: 976
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 984
    - byUser:
      - byVerb:
        - requestCount: 958
          verb: list
        requestCount: 958
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 1010
    requestCount: 2929
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 983
          verb: list
        requestCount: 983
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 998
    - byUser:
      - byVerb:
        - requestCount: 976
          verb: list
        requestCount: 976
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 985
    - byUser:
      - byVerb:
        - requestCount: 989
          verb: list
        requestCount: 989
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 1043
    requestCount: 3026
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 812
          verb: list
        requestCount: 812
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 829
    - byUser:
      - byVerb:
        - requestCount: 824
          verb: list
        requestCount: 824
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 833
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: list
        requestCount: 860
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 26
          verb: watch
        requestCount: 26
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 914
    requestCount: 2576
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 915
          verb: list
        requestCount: 915
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 929
    - byUser:
      - byVerb:
        - requestCount: 837
          verb: list
        requestCount: 837
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 845
    - byUser:
      - byVerb:
        - requestCount: 876
          verb: list
        requestCount: 876
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 928
    requestCount: 2702
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: list
        requestCount: 860
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 875
    - byUser:
      - byVerb:
        - requestCount: 815
          verb: list
        requestCount: 815
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 823
    - byUser:
      - byVerb:
        - requestCount: 846
          verb: list
        requestCount: 846
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 898
    requestCount: 2596
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 913
          verb: list
        requestCount: 913
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 928
    - byUser:
      - byVerb:
        - requestCount: 914
          verb: list
        requestCount: 914
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 923
    - byUser:
      - byVerb:
        - requestCount: 957
          verb: list
        requestCount: 957
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 1009
    requestCount: 2860
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 769
          verb: list
        requestCount: 769
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 786
    - byUser:
      - byVerb:
        - requestCount: 825
          verb: list
        requestCount: 825
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 834
    - byUser:
      - byVerb:
        - requestCount: 746
          verb: list
        requestCount: 746
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 799
    requestCount: 2419
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 902
          verb: list
        requestCount: 902
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 917
    - byUser:
      - byVerb:
        - requestCount: 911
          verb: list
        requestCount: 911
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 916
    - byUser:
      - byVerb:
        - requestCount: 969
          verb: list
        requestCount: 969
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 3
          verb: watch
        requestCount: 4
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1024
    requestCount: 2857
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1111
          verb: list
        requestCount: 1111
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 1126
    - byUser:
      - byVerb:
        - requestCount: 1087
          verb: list
        requestCount: 1087
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 1087
    - byUser:
      - byVerb:
        - requestCount: 1115
          verb: list
        requestCount: 1115
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 27
          verb: watch
        requestCount: 27
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 1178
    requestCount: 3391
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 806
          verb: list
        requestCount: 806
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 821
    - byUser:
      - byVerb:
        - requestCount: 825
          verb: list
        requestCount: 825
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 825
    - byUser:
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 24
          verb: watch
        requestCount: 25
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 887
    requestCount: 2533
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 37
    - byUser:
      - byVerb:
        - requestCount: 65
          verb: list
        requestCount: 65
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 65
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: list
        requestCount: 43
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 2
          verb: watch
        requestCount: 3
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 50
    requestCount: 152
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 924
          verb: list
        requestCount: 924
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 939
    - byUser:
      - byVerb:
        - requestCount: 912
          verb: list
        requestCount: 912
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 920
    - byUser:
      - byVerb:
        - requestCount: 957
          verb: list
        requestCount: 957
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 1008
    requestCount: 2867
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 751
          verb: list
        requestCount: 751
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 766
    - byUser:
      - byVerb:
        - requestCount: 746
          verb: list
        requestCount: 746
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 754
    - byUser:
      - byVerb:
        - requestCount: 715
          verb: list
        requestCount: 715
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 23
          verb: watch
        requestCount: 24
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 767
    requestCount: 2287
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 893
          verb: list
        requestCount: 893
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 910
    - byUser:
      - byVerb:
        - requestCount: 926
          verb: list
        requestCount: 926
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 934
    - byUser:
      - byVerb:
        - requestCount: 961
          verb: list
        requestCount: 961
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 1012
    requestCount: 2856
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 850
    - byUser:
      - byVerb:
        - requestCount: 829
          verb: list
        requestCount: 829
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 837
    - byUser:
      - byVerb:
        - requestCount: 876
          verb: list
        requestCount: 876
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 3
          verb: list
        - requestCount: 24
          verb: watch
        requestCount: 27
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 932
    requestCount: 2619
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 872
          verb: list
        requestCount: 872
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 887
    - byUser:
      - byVerb:
        - requestCount: 865
          verb: list
        requestCount: 865
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 874
    - byUser:
      - byVerb:
        - requestCount: 906
          verb: list
        requestCount: 906
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 957
    requestCount: 2718
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 925
          verb: list
        requestCount: 925
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 940
    - byUser:
      - byVerb:
        - requestCount: 870
          verb: list
        requestCount: 870
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 879
    - byUser:
      - byVerb:
        - requestCount: 898
          verb: list
        requestCount: 898
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 953
    requestCount: 2772
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 821
          verb: list
        requestCount: 821
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 836
    - byUser:
      - byVerb:
        - requestCount: 831
          verb: list
        requestCount: 831
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 838
    - byUser:
      - byVerb:
        - requestCount: 812
          verb: list
        requestCount: 812
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 863
    requestCount: 2537
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 830
          verb: list
        requestCount: 830
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 846
    - byUser:
      - byVerb:
        - requestCount: 827
          verb: list
        requestCount: 827
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 835
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 2
          verb: list
        - requestCount: 23
          verb: watch
        requestCount: 25
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 885
    requestCount: 2566
  requestCount: 60080
