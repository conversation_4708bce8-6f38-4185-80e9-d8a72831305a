---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2024-02-04T21:17:03Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2024-02-04T21:17:03Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:07:04Z"
  name: appqoepolicies.v1.citrix.com
  resourceVersion: "*********"
  uid: 6fe0e2ef-c277-4027-9287-cf7e64222615
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 37
    - byUser:
      - byVerb:
        - requestCount: 65
          verb: list
        requestCount: 65
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.12
      requestCount: 69
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: list
        requestCount: 43
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 46
    requestCount: 152
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 830
          verb: list
        requestCount: 830
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 852
    - byUser:
      - byVerb:
        - requestCount: 773
          verb: list
        requestCount: 773
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 797
    - byUser:
      - byVerb:
        - requestCount: 766
          verb: list
        requestCount: 766
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 799
    requestCount: 2448
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 879
          verb: list
        requestCount: 879
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 902
    - byUser:
      - byVerb:
        - requestCount: 838
          verb: list
        requestCount: 838
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 860
    - byUser:
      - byVerb:
        - requestCount: 879
          verb: list
        requestCount: 879
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 915
    requestCount: 2677
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 877
          verb: list
        requestCount: 877
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 897
    - byUser:
      - byVerb:
        - requestCount: 836
          verb: list
        requestCount: 836
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 860
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 871
    requestCount: 2628
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1005
          verb: list
        requestCount: 1005
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 1025
    - byUser:
      - byVerb:
        - requestCount: 994
          verb: list
        requestCount: 994
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 1016
    - byUser:
      - byVerb:
        - requestCount: 970
          verb: list
        requestCount: 970
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 7
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 14
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1013
    requestCount: 3054
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 918
          verb: list
        requestCount: 918
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 934
    - byUser:
      - byVerb:
        - requestCount: 976
          verb: list
        requestCount: 976
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 997
    - byUser:
      - byVerb:
        - requestCount: 958
          verb: list
        requestCount: 958
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1001
    requestCount: 2932
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 983
          verb: list
        requestCount: 983
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 1001
    - byUser:
      - byVerb:
        - requestCount: 970
          verb: list
        requestCount: 970
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 19
          verb: watch
        requestCount: 19
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 997
    - byUser:
      - byVerb:
        - requestCount: 989
          verb: list
        requestCount: 989
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1025
    requestCount: 3023
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 812
          verb: list
        requestCount: 812
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 833
    - byUser:
      - byVerb:
        - requestCount: 824
          verb: list
        requestCount: 824
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 849
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: list
        requestCount: 860
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 896
    requestCount: 2578
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 915
          verb: list
        requestCount: 915
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 932
    - byUser:
      - byVerb:
        - requestCount: 837
          verb: list
        requestCount: 837
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 861
    - byUser:
      - byVerb:
        - requestCount: 876
          verb: list
        requestCount: 876
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 19
          verb: watch
        requestCount: 19
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 917
    requestCount: 2710
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: list
        requestCount: 860
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 878
    - byUser:
      - byVerb:
        - requestCount: 815
          verb: list
        requestCount: 815
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 19
          verb: watch
        requestCount: 19
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 841
    - byUser:
      - byVerb:
        - requestCount: 846
          verb: list
        requestCount: 846
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 882
    requestCount: 2601
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 913
          verb: list
        requestCount: 913
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 933
    - byUser:
      - byVerb:
        - requestCount: 914
          verb: list
        requestCount: 914
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 941
    - byUser:
      - byVerb:
        - requestCount: 957
          verb: list
        requestCount: 957
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 13
          verb: watch
        requestCount: 13
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 990
    requestCount: 2864
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 769
          verb: list
        requestCount: 769
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 790
    - byUser:
      - byVerb:
        - requestCount: 825
          verb: list
        requestCount: 825
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 855
    - byUser:
      - byVerb:
        - requestCount: 746
          verb: list
        requestCount: 746
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 12
          verb: watch
        requestCount: 12
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 779
    requestCount: 2424
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 940
          verb: list
        requestCount: 940
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 960
    - byUser:
      - byVerb:
        - requestCount: 895
          verb: list
        requestCount: 895
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 917
    - byUser:
      - byVerb:
        - requestCount: 969
          verb: list
        requestCount: 969
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1008
    requestCount: 2885
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1111
          verb: list
        requestCount: 1111
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 1129
    - byUser:
      - byVerb:
        - requestCount: 1087
          verb: list
        requestCount: 1087
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.12
      requestCount: 1112
    - byUser:
      - byVerb:
        - requestCount: 1115
          verb: list
        requestCount: 1115
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1152
    requestCount: 3393
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 836
          verb: list
        requestCount: 836
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 860
    - byUser:
      - byVerb:
        - requestCount: 825
          verb: list
        requestCount: 825
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 12
          verb: watch
        requestCount: 12
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.12
      requestCount: 837
    - byUser:
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 871
    requestCount: 2568
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 37
    - byUser:
      - byVerb:
        - requestCount: 65
          verb: list
        requestCount: 65
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.12
      requestCount: 69
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: list
        requestCount: 43
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 46
    requestCount: 152
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 924
          verb: list
        requestCount: 924
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 946
    - byUser:
      - byVerb:
        - requestCount: 912
          verb: list
        requestCount: 912
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 936
    - byUser:
      - byVerb:
        - requestCount: 957
          verb: list
        requestCount: 957
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 991
    requestCount: 2873
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 751
          verb: list
        requestCount: 751
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 772
    - byUser:
      - byVerb:
        - requestCount: 711
          verb: list
        requestCount: 711
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 735
    - byUser:
      - byVerb:
        - requestCount: 715
          verb: list
        requestCount: 715
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 750
    requestCount: 2257
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 893
          verb: list
        requestCount: 893
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 916
    - byUser:
      - byVerb:
        - requestCount: 926
          verb: list
        requestCount: 926
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 19
          verb: watch
        requestCount: 19
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 953
    - byUser:
      - byVerb:
        - requestCount: 961
          verb: list
        requestCount: 961
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 11
          verb: watch
        requestCount: 11
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 991
    requestCount: 2860
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 849
    - byUser:
      - byVerb:
        - requestCount: 834
          verb: list
        requestCount: 834
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 859
    - byUser:
      - byVerb:
        - requestCount: 876
          verb: list
        requestCount: 876
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 919
    requestCount: 2627
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 872
          verb: list
        requestCount: 872
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 893
    - byUser:
      - byVerb:
        - requestCount: 865
          verb: list
        requestCount: 865
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 889
    - byUser:
      - byVerb:
        - requestCount: 906
          verb: list
        requestCount: 906
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 940
    requestCount: 2722
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 925
          verb: list
        requestCount: 925
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 945
    - byUser:
      - byVerb:
        - requestCount: 870
          verb: list
        requestCount: 870
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 897
    - byUser:
      - byVerb:
        - requestCount: 898
          verb: list
        requestCount: 898
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 932
    requestCount: 2774
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 821
          verb: list
        requestCount: 821
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 837
    - byUser:
      - byVerb:
        - requestCount: 831
          verb: list
        requestCount: 831
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 19
          verb: watch
        requestCount: 19
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 858
    - byUser:
      - byVerb:
        - requestCount: 812
          verb: list
        requestCount: 812
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 850
    requestCount: 2545
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 830
          verb: list
        requestCount: 830
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 19
          verb: watch
        requestCount: 19
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 849
    - byUser:
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 852
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 871
    requestCount: 2572
  requestCount: 60167
