---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-07-15T12:32:42Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-07-15T12:32:42Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:07:14Z"
  name: prioritylevelconfigurations.v1.flowcontrol.apiserver.k8s.io
  resourceVersion: "770116794"
  uid: 37c29258-a8ed-48ac-912d-0caa07037ce0
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 1
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 1
    requestCount: 2
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 34
    requestCount: 58
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 35
    requestCount: 59
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 34
    requestCount: 58
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 7
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 14
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 36
    requestCount: 59
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 34
    requestCount: 59
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 34
    requestCount: 57
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 35
    requestCount: 59
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 33
    requestCount: 57
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 33
    requestCount: 57
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 35
    requestCount: 59
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 34
    requestCount: 58
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 12
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 3
          verb: watch
        requestCount: 4
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 38
    requestCount: 59
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 43
    requestCount: 59
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 41
    requestCount: 57
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 1
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 1
    requestCount: 2
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 34
    requestCount: 56
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 33
    requestCount: 58
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 35
    requestCount: 58
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 10
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 33
    requestCount: 59
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 34
    requestCount: 58
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 37
    requestCount: 59
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 33
    requestCount: 56
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.11
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 35
    requestCount: 60
  requestCount: 1281
