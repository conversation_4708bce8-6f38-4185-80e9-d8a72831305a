---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2024-01-23T20:21:18Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2024-01-23T20:21:18Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:06:34Z"
  name: applications.v1alpha1.argoproj.io
  resourceVersion: "*********"
  uid: eb7222cd-24fa-49bc-b058-a9555af75772
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 10
          verb: get
        - requestCount: 643
          verb: patch
        - requestCount: 5
          verb: update
        - requestCount: 1
          verb: watch
        requestCount: 659
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 695
    - byUser:
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 11
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: list
        requestCount: 43
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 11
          verb: list
        - requestCount: 3
          verb: watch
        requestCount: 14
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 60
    requestCount: 766
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 162
          verb: get
        - requestCount: 10331
          verb: patch
        - requestCount: 81
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 10583
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 830
          verb: list
        requestCount: 830
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 2
          verb: patch
        requestCount: 6
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 11426
    - byUser:
      - byVerb:
        - requestCount: 763
          verb: list
        requestCount: 763
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: get
        - requestCount: 7
          verb: patch
        requestCount: 21
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 793
    - byUser:
      - byVerb:
        - requestCount: 766
          verb: list
        requestCount: 766
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 9
          verb: list
        - requestCount: 24
          verb: watch
        requestCount: 34
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 3
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 21
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 837
    requestCount: 13056
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 10309
          verb: patch
        - requestCount: 84
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10569
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 879
          verb: list
        requestCount: 879
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 10
          verb: get
        - requestCount: 5
          verb: patch
        requestCount: 15
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 11471
    - byUser:
      - byVerb:
        - requestCount: 858
          verb: list
        requestCount: 858
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: get
        - requestCount: 4
          verb: patch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 878
    - byUser:
      - byVerb:
        - requestCount: 879
          verb: list
        requestCount: 879
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: list
        - requestCount: 26
          verb: watch
        requestCount: 44
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 3
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 21
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 959
    requestCount: 13308
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 174
          verb: get
        - requestCount: 10391
          verb: patch
        - requestCount: 84
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10657
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 877
          verb: list
        requestCount: 877
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 6
          verb: patch
        requestCount: 18
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      nodeName: 10.21.102.11
      requestCount: 11565
    - byUser:
      - byVerb:
        - requestCount: 836
          verb: list
        requestCount: 836
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: get
        - requestCount: 3
          verb: patch
        requestCount: 9
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: get
        requestCount: 4
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      nodeName: 10.21.102.12
      requestCount: 857
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 269
          verb: get
        - requestCount: 14
          verb: list
        - requestCount: 1
          verb: patch
        - requestCount: 6
          verb: update
        - requestCount: 24
          verb: watch
        requestCount: 314
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 3
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 21
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1184
    requestCount: 13606
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 10306
          verb: patch
        - requestCount: 84
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 10565
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1005
          verb: list
        requestCount: 1005
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 2
          verb: patch
        requestCount: 6
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 11584
    - byUser:
      - byVerb:
        - requestCount: 994
          verb: list
        requestCount: 994
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 16
          verb: get
        - requestCount: 8
          verb: patch
        requestCount: 24
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 1026
    - byUser:
      - byVerb:
        - requestCount: 970
          verb: list
        requestCount: 970
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 22
          verb: get
        - requestCount: 11
          verb: list
        - requestCount: 4
          verb: patch
        - requestCount: 22
          verb: watch
        requestCount: 59
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 2
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 18
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1063
    requestCount: 13673
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 160
          verb: get
        - requestCount: 10299
          verb: patch
        - requestCount: 80
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10547
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 918
          verb: list
        requestCount: 918
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 1
          verb: patch
        requestCount: 3
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 11476
    - byUser:
      - byVerb:
        - requestCount: 976
          verb: list
        requestCount: 976
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 9
          verb: patch
        requestCount: 27
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 1012
    - byUser:
      - byVerb:
        - requestCount: 958
          verb: list
        requestCount: 958
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 11
          verb: list
        - requestCount: 26
          verb: watch
        requestCount: 37
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 2
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 18
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1029
    requestCount: 13517
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 164
          verb: get
        - requestCount: 10300
          verb: patch
        - requestCount: 82
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10554
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 983
          verb: list
        requestCount: 983
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: get
        - requestCount: 4
          verb: patch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 11557
    - byUser:
      - byVerb:
        - requestCount: 970
          verb: list
        requestCount: 970
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: get
        - requestCount: 4
          verb: patch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 989
    - byUser:
      - byVerb:
        - requestCount: 989
          verb: list
        requestCount: 989
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 29
          verb: list
        - requestCount: 23
          verb: watch
        requestCount: 52
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 3
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 21
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1078
    requestCount: 13624
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 10288
          verb: patch
        - requestCount: 84
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 10547
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 812
          verb: list
        requestCount: 812
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 1
          verb: patch
        requestCount: 3
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 11370
    - byUser:
      - byVerb:
        - requestCount: 808
          verb: list
        requestCount: 808
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: get
        - requestCount: 7
          verb: patch
        requestCount: 21
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 836
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: list
        requestCount: 860
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 12
          verb: list
        - requestCount: 26
          verb: watch
        requestCount: 42
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 8
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 4
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 24
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 942
    requestCount: 13148
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 10329
          verb: patch
        - requestCount: 84
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 10588
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 915
          verb: list
        requestCount: 915
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: get
        - requestCount: 4
          verb: patch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 11522
    - byUser:
      - byVerb:
        - requestCount: 857
          verb: list
        requestCount: 857
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: get
        - requestCount: 3
          verb: patch
        requestCount: 9
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 875
    - byUser:
      - byVerb:
        - requestCount: 876
          verb: list
        requestCount: 876
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 33
          verb: list
        - requestCount: 23
          verb: watch
        requestCount: 56
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 10
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 5
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 27
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 977
    requestCount: 13374
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 162
          verb: get
        - requestCount: 10266
          verb: patch
        - requestCount: 81
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 10518
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 860
          verb: list
        requestCount: 860
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 10
          verb: get
        - requestCount: 5
          verb: patch
        requestCount: 15
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 11401
    - byUser:
      - byVerb:
        - requestCount: 815
          verb: list
        requestCount: 815
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 6
          verb: patch
        requestCount: 18
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 841
    - byUser:
      - byVerb:
        - requestCount: 846
          verb: list
        requestCount: 846
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 57
          verb: list
        - requestCount: 26
          verb: watch
        requestCount: 87
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 1
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 15
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 964
    requestCount: 13206
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 164
          verb: get
        - requestCount: 10277
          verb: patch
        - requestCount: 82
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 10530
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 913
          verb: list
        requestCount: 913
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: get
        - requestCount: 4
          verb: patch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 11462
    - byUser:
      - byVerb:
        - requestCount: 914
          verb: list
        requestCount: 914
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 16
          verb: get
        - requestCount: 8
          verb: patch
        requestCount: 24
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 946
    - byUser:
      - byVerb:
        - requestCount: 957
          verb: list
        requestCount: 957
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 36
          verb: list
        - requestCount: 22
          verb: watch
        requestCount: 58
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 1044
    requestCount: 13452
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 166
          verb: get
        - requestCount: 10290
          verb: patch
        - requestCount: 83
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 10546
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 769
          verb: list
        requestCount: 769
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 6
          verb: patch
        requestCount: 18
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 11341
    - byUser:
      - byVerb:
        - requestCount: 825
          verb: list
        requestCount: 825
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 10
          verb: get
        - requestCount: 5
          verb: patch
        requestCount: 15
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 847
    - byUser:
      - byVerb:
        - requestCount: 746
          verb: list
        requestCount: 746
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 38
          verb: list
        - requestCount: 26
          verb: watch
        requestCount: 64
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 1
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 15
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 841
    requestCount: 13029
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 10205
          verb: patch
        - requestCount: 84
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10465
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 940
          verb: list
        requestCount: 940
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 2
          verb: patch
        requestCount: 6
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 11418
    - byUser:
      - byVerb:
        - requestCount: 895
          verb: list
        requestCount: 895
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: get
        - requestCount: 7
          verb: patch
        requestCount: 21
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 921
    - byUser:
      - byVerb:
        - requestCount: 969
          verb: list
        requestCount: 969
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 79
          verb: list
        - requestCount: 25
          verb: watch
        requestCount: 106
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 3
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 21
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1117
    requestCount: 13456
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 198
          verb: get
        - requestCount: 10464
          verb: patch
        - requestCount: 90
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10760
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1111
          verb: list
        requestCount: 1111
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 2
          verb: patch
        requestCount: 6
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 11885
    - byUser:
      - byVerb:
        - requestCount: 1076
          verb: list
        requestCount: 1076
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: create
        - requestCount: 34
          verb: get
        - requestCount: 11
          verb: patch
        requestCount: 51
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.12
      requestCount: 1127
    - byUser:
      - byVerb:
        - requestCount: 1115
          verb: list
        requestCount: 1115
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 146
          verb: list
        - requestCount: 160
          verb: patch
        - requestCount: 24
          verb: watch
        requestCount: 330
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 8
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 4
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 24
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 1492
    requestCount: 14504
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 162
          verb: get
        - requestCount: 10289
          verb: patch
        - requestCount: 81
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 10539
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 836
          verb: list
        requestCount: 836
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: get
        - requestCount: 3
          verb: patch
        requestCount: 9
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 11392
    - byUser:
      - byVerb:
        - requestCount: 836
          verb: list
        requestCount: 836
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: get
        - requestCount: 7
          verb: patch
        requestCount: 21
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.12
      requestCount: 857
    - byUser:
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: get
        - requestCount: 152
          verb: list
        - requestCount: 24
          verb: watch
        requestCount: 183
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 2
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 18
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1051
    requestCount: 13300
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 10
          verb: get
        - requestCount: 643
          verb: patch
        - requestCount: 5
          verb: update
        - requestCount: 1
          verb: watch
        requestCount: 659
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 695
    - byUser:
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 11
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: list
        requestCount: 43
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 11
          verb: list
        - requestCount: 3
          verb: watch
        requestCount: 14
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 60
    requestCount: 766
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 10293
          verb: patch
        - requestCount: 84
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10553
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 924
          verb: list
        requestCount: 924
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 2
          verb: patch
        requestCount: 6
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 11491
    - byUser:
      - byVerb:
        - requestCount: 912
          verb: list
        requestCount: 912
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: get
        - requestCount: 7
          verb: patch
        requestCount: 21
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 941
    - byUser:
      - byVerb:
        - requestCount: 957
          verb: list
        requestCount: 957
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 98
          verb: list
        - requestCount: 25
          verb: watch
        requestCount: 123
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 3
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 21
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1116
    requestCount: 13548
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 166
          verb: get
        - requestCount: 10311
          verb: patch
        - requestCount: 83
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 10567
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 751
          verb: list
        requestCount: 751
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 10
          verb: get
        - requestCount: 5
          verb: patch
        requestCount: 15
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 11341
    - byUser:
      - byVerb:
        - requestCount: 746
          verb: list
        requestCount: 746
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: get
        - requestCount: 4
          verb: patch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 766
    - byUser:
      - byVerb:
        - requestCount: 715
          verb: list
        requestCount: 715
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 157
          verb: list
        - requestCount: 6
          verb: patch
        - requestCount: 24
          verb: watch
        requestCount: 187
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 2
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 18
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 937
    requestCount: 13044
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 10321
          verb: patch
        - requestCount: 84
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10581
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 893
          verb: list
        requestCount: 893
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 6
          verb: patch
        requestCount: 18
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 11501
    - byUser:
      - byVerb:
        - requestCount: 926
          verb: list
        requestCount: 926
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: get
        - requestCount: 3
          verb: patch
        requestCount: 9
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.12
      requestCount: 944
    - byUser:
      - byVerb:
        - requestCount: 961
          verb: list
        requestCount: 961
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 189
          verb: list
        - requestCount: 6
          verb: patch
        - requestCount: 25
          verb: watch
        requestCount: 220
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 3
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 21
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1218
    requestCount: 13663
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 158
          verb: get
        - requestCount: 10258
          verb: patch
        - requestCount: 79
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10503
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: get
        - requestCount: 4
          verb: patch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 11357
    - byUser:
      - byVerb:
        - requestCount: 834
          verb: list
        requestCount: 834
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: get
        - requestCount: 7
          verb: patch
        requestCount: 21
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 864
    - byUser:
      - byVerb:
        - requestCount: 876
          verb: list
        requestCount: 876
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: get
        - requestCount: 110
          verb: list
        - requestCount: 1
          verb: patch
        - requestCount: 23
          verb: watch
        requestCount: 140
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 1
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 15
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 1047
    requestCount: 13268
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 166
          verb: get
        - requestCount: 10260
          verb: patch
        - requestCount: 83
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10517
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 872
          verb: list
        requestCount: 872
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 10
          verb: get
        - requestCount: 5
          verb: patch
        requestCount: 15
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 11411
    - byUser:
      - byVerb:
        - requestCount: 865
          verb: list
        requestCount: 865
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: get
        - requestCount: 3
          verb: patch
        requestCount: 9
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 883
    - byUser:
      - byVerb:
        - requestCount: 906
          verb: list
        requestCount: 906
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 149
          verb: list
        - requestCount: 25
          verb: watch
        requestCount: 174
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 8
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 4
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 24
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 1120
    requestCount: 13414
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 10335
          verb: patch
        - requestCount: 84
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10595
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 925
          verb: list
        requestCount: 925
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 2
          verb: patch
        requestCount: 6
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.11
      requestCount: 11535
    - byUser:
      - byVerb:
        - requestCount: 870
          verb: list
        requestCount: 870
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: get
        - requestCount: 7
          verb: patch
        requestCount: 21
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 899
    - byUser:
      - byVerb:
        - requestCount: 898
          verb: list
        requestCount: 898
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 38
          verb: list
        - requestCount: 26
          verb: watch
        requestCount: 64
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 3
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 21
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 999
    requestCount: 13433
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 10276
          verb: patch
        - requestCount: 84
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10536
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 821
          verb: list
        requestCount: 821
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: get
        - requestCount: 3
          verb: patch
        requestCount: 9
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 11374
    - byUser:
      - byVerb:
        - requestCount: 831
          verb: list
        requestCount: 831
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 6
          verb: patch
        requestCount: 18
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 858
    - byUser:
      - byVerb:
        - requestCount: 812
          verb: list
        requestCount: 812
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: get
        - requestCount: 25
          verb: list
        - requestCount: 1
          verb: patch
        - requestCount: 22
          verb: watch
        requestCount: 57
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 6
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 3
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 21
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 908
    requestCount: 13140
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 166
          verb: get
        - requestCount: 10263
          verb: patch
        - requestCount: 82
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10519
        userAgent: argocd-application-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 830
          verb: list
        requestCount: 830
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 10
          verb: get
        - requestCount: 5
          verb: patch
        requestCount: 15
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: argocd-notifications-controller/v3.0.12+ed1e239
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-notifications-controller
      nodeName: 10.21.102.11
      requestCount: 11373
    - byUser:
      - byVerb:
        - requestCount: 827
          verb: list
        requestCount: 827
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 6
          verb: patch
        requestCount: 18
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 853
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 30
          verb: get
        - requestCount: 14
          verb: list
        - requestCount: 2
          verb: update
        - requestCount: 24
          verb: watch
        requestCount: 70
        userAgent: argocd-server/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-server
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 1
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 15
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: argocd-applicationset-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-applicationset-controller
      nodeName: 10.21.102.9
      requestCount: 934
    requestCount: 13160
  requestCount: 295689
