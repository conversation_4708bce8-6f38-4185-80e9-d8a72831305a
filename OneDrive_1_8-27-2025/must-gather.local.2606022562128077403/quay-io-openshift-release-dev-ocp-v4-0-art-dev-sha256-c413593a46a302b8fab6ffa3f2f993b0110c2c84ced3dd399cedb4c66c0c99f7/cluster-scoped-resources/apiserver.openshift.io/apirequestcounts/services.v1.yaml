---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2023-12-19T12:53:23Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2023-12-19T12:53:23Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:05:16Z"
  name: services.v1
  resourceVersion: "*********"
  uid: cf55a502-4fb6-445b-8aec-fccb1daa5e3b
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 71
          verb: list
        requestCount: 71
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 39
          verb: watch
        requestCount: 39
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 36
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 37
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 19
          verb: get
        - requestCount: 2
          verb: watch
        requestCount: 21
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 5
          verb: delete
        - requestCount: 10
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 16
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 10
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 11
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 10
          verb: list
        requestCount: 11
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-6mxgs:default
      - byVerb:
        - requestCount: 8
          verb: get
        requestCount: 8
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 5
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 6
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      nodeName: 10.21.102.11
      requestCount: 255
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 28
          verb: list
        requestCount: 29
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: ocpazp001-4zlm9-worker-eastus3-gprvs/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: ocpazp001-4zlm9-worker-eastus3-xxg89/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-xxg89
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: ocpazp001-4zlm9-worker-eastus1-24ggt/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus1-24ggt
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: ocpazp001-4zlm9-worker-eastus2-chx4q/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: ocpazp001-4zlm9-worker-eastus3-7599c/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-7599c
      nodeName: 10.21.102.12
      requestCount: 34
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 111
          verb: list
        requestCount: 115
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 49
          verb: get
        - requestCount: 3
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 53
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 43
          verb: get
        requestCount: 43
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 13
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 14
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 7
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 8
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 5
          verb: get
        - requestCount: 2
          verb: watch
        requestCount: 7
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 5
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 6
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      nodeName: 10.21.102.9
      requestCount: 309
    requestCount: 598
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 38
          verb: get
        - requestCount: 1743
          verb: list
        requestCount: 1781
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 744
          verb: watch
        requestCount: 744
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 555
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 572
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 388
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 414
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 84
          verb: delete
        - requestCount: 168
          verb: get
        requestCount: 252
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 241
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 127
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 153
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 109
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 118
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 100
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 109
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 75
          verb: watch
        requestCount: 75
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      nodeName: 10.21.102.11
      requestCount: 4459
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: get
        - requestCount: 1829
          verb: list
        requestCount: 1869
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 108
          verb: patch
        - requestCount: 32
          verb: watch
        requestCount: 140
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 25
          verb: patch
        requestCount: 25
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: ocpazp001-4zlm9-worker-eastus3-tssl4/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-tssl4
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazp001-4zlm9-worker-eastus3-mnznv/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 4
          verb: delete
        - requestCount: 4
          verb: get
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:generic-garbage-collector
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus2-59559/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 2140
    - byUser:
      - byVerb:
        - requestCount: 42
          verb: get
        - requestCount: 1758
          verb: list
        requestCount: 1800
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 632
          verb: get
        requestCount: 632
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 475
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 499
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 177
          verb: watch
        requestCount: 177
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 159
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 168
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 91
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 77
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 85
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 76
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 38
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 46
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 3660
    requestCount: 10259
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 47
          verb: get
        - requestCount: 1566
          verb: list
        requestCount: 1613
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 744
          verb: watch
        requestCount: 744
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 429
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 445
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 374
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 398
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 90
          verb: delete
        - requestCount: 180
          verb: get
        requestCount: 270
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 110
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 133
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 97
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 105
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 87
          verb: watch
        requestCount: 87
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 77
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 85
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      nodeName: 10.21.102.11
      requestCount: 4122
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: get
        - requestCount: 1603
          verb: list
        requestCount: 1643
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 102
          verb: patch
        - requestCount: 32
          verb: watch
        requestCount: 134
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 13
          verb: watch
        requestCount: 13
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazp001-4zlm9-worker-eastus3-xxg89/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-xxg89
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus3-cmsnk/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-master-rhg6x-2/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-master-rhg6x-2
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazp001-4zlm9-worker-eastus1-24ggt/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus1-24ggt
      nodeName: 10.21.102.12
      requestCount: 1885
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: get
        - requestCount: 1453
          verb: list
        requestCount: 1485
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 590
          verb: get
        requestCount: 590
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 482
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 506
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 180
          verb: watch
        requestCount: 180
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 134
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 142
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 93
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 87
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 75
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 47
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 3291
    requestCount: 9298
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: get
        - requestCount: 1643
          verb: list
        requestCount: 1683
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 767
          verb: watch
        requestCount: 767
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 435
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 451
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 374
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 397
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 72
          verb: delete
        - requestCount: 144
          verb: get
        requestCount: 216
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 108
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 133
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 95
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 102
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 87
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 84
          verb: watch
        requestCount: 84
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      nodeName: 10.21.102.11
      requestCount: 4162
    - byUser:
      - byVerb:
        - requestCount: 48
          verb: get
        - requestCount: 1624
          verb: list
        requestCount: 1672
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 108
          verb: patch
        - requestCount: 30
          verb: watch
        requestCount: 138
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazp001-4zlm9-worker-eastus3-tssl4/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-tssl4
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus2-59559/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus3-xxg89/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-xxg89
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazp001-4zlm9-worker-eastus3-7599c/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-7599c
      nodeName: 10.21.102.12
      requestCount: 1924
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: get
        - requestCount: 1624
          verb: list
        requestCount: 1656
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 592
          verb: get
        requestCount: 592
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 485
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 509
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 181
          verb: watch
        requestCount: 181
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 137
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 145
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 92
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 85
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 85
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 76
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 3469
    requestCount: 9555
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 37
          verb: get
        - requestCount: 1604
          verb: list
        requestCount: 1641
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 741
          verb: watch
        requestCount: 741
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 471
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 488
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 374
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 400
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 90
          verb: delete
        - requestCount: 180
          verb: get
        requestCount: 270
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 112
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 134
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 100
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 109
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 89
          verb: watch
        requestCount: 89
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      nodeName: 10.21.102.11
      requestCount: 4200
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: get
        - requestCount: 1562
          verb: list
        requestCount: 1598
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 108
          verb: patch
        - requestCount: 31
          verb: watch
        requestCount: 139
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 49
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus3-2x82d/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazp001-4zlm9-worker-eastus3-mnznv/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-mnznv
      nodeName: 10.21.102.12
      requestCount: 1854
    - byUser:
      - byVerb:
        - requestCount: 47
          verb: get
        - requestCount: 1535
          verb: list
        requestCount: 1582
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 630
          verb: get
        requestCount: 630
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 468
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 492
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 179
          verb: watch
        requestCount: 179
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 142
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 149
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 83
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 91
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 85
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 77
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 48
          verb: watch
        requestCount: 48
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      nodeName: 10.21.102.9
      requestCount: 3419
    requestCount: 9473
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 49
          verb: get
        - requestCount: 1742
          verb: list
        requestCount: 1791
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 754
          verb: watch
        requestCount: 754
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 459
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 473
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 377
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 403
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 241
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 72
          verb: delete
        - requestCount: 144
          verb: get
        requestCount: 216
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 108
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 132
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 105
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 88
          verb: watch
        requestCount: 88
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      nodeName: 10.21.102.11
      requestCount: 4289
    - byUser:
      - byVerb:
        - requestCount: 38
          verb: get
        - requestCount: 1792
          verb: list
        requestCount: 1830
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 102
          verb: patch
        - requestCount: 31
          verb: watch
        requestCount: 133
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 10
        userAgent: ocpazp001-4zlm9-infra-eastus2-zjfm5/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus3-2x82d/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-infra-eastus3-lcgzj/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-infra-eastus3-lcgzj
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      nodeName: 10.21.102.12
      requestCount: 2075
    - byUser:
      - byVerb:
        - requestCount: 34
          verb: get
        - requestCount: 1693
          verb: list
        requestCount: 1727
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 590
          verb: get
        requestCount: 590
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 478
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 502
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 175
          verb: watch
        requestCount: 175
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 136
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 144
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 92
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 87
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 87
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 75
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 47
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 3526
    requestCount: 9890
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: get
        - requestCount: 1418
          verb: list
        requestCount: 1461
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 757
          verb: watch
        requestCount: 757
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 453
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 469
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 375
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 400
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 84
          verb: delete
        - requestCount: 168
          verb: get
        requestCount: 252
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 110
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 135
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 95
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 103
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 88
          verb: watch
        requestCount: 88
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      nodeName: 10.21.102.11
      requestCount: 3993
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: get
        - requestCount: 1299
          verb: list
        requestCount: 1329
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 102
          verb: patch
        - requestCount: 32
          verb: watch
        requestCount: 134
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 36
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 43
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazp001-4zlm9-worker-eastus2-59559/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-infra-eastus3-lcgzj/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-infra-eastus3-lcgzj
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazp001-4zlm9-infra-eastus2-zjfm5/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      nodeName: 10.21.102.12
      requestCount: 1571
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: get
        - requestCount: 1471
          verb: list
        requestCount: 1514
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 604
          verb: get
        requestCount: 604
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 472
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 496
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 172
          verb: watch
        requestCount: 172
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 130
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 138
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 92
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 85
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 76
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 3311
    requestCount: 8875
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: get
        - requestCount: 1563
          verb: list
        requestCount: 1606
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 747
          verb: watch
        requestCount: 747
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 411
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 427
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 374
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 398
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 241
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        requestCount: 234
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 111
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 135
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 95
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 103
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 88
          verb: watch
        requestCount: 88
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      nodeName: 10.21.102.11
      requestCount: 4065
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: get
        - requestCount: 1617
          verb: list
        requestCount: 1653
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 104
          verb: patch
        - requestCount: 29
          verb: watch
        requestCount: 133
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 49
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 19
          verb: watch
        requestCount: 19
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus3-7599c/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-7599c
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus2-59559/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus1-24ggt/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus1-24ggt
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus2-chx4q/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      nodeName: 10.21.102.12
      requestCount: 1902
    - byUser:
      - byVerb:
        - requestCount: 39
          verb: get
        - requestCount: 1546
          verb: list
        requestCount: 1585
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 618
          verb: get
        requestCount: 618
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 473
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 497
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 178
          verb: watch
        requestCount: 178
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 131
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 138
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 83
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 92
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 88
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 87
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 76
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 50
          verb: watch
        requestCount: 50
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      nodeName: 10.21.102.9
      requestCount: 3409
    requestCount: 9376
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 38
          verb: get
        - requestCount: 1515
          verb: list
        requestCount: 1553
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 750
          verb: watch
        requestCount: 750
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 477
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 492
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 384
          verb: get
        - requestCount: 27
          verb: watch
        requestCount: 411
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 90
          verb: delete
        - requestCount: 180
          verb: get
        requestCount: 270
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 110
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 134
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 106
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 91
          verb: watch
        requestCount: 91
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 87
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      nodeName: 10.21.102.11
      requestCount: 4136
    - byUser:
      - byVerb:
        - requestCount: 37
          verb: get
        - requestCount: 1490
          verb: list
        requestCount: 1527
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 106
          verb: patch
        - requestCount: 34
          verb: watch
        requestCount: 140
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 47
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazp001-4zlm9-worker-eastus1-24ggt/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus1-24ggt
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazp001-4zlm9-worker-eastus3-m8kc9/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus3-tssl4/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-tssl4
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus3-gprvs/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-master-rhg6x-2/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-master-rhg6x-2
      nodeName: 10.21.102.12
      requestCount: 1773
    - byUser:
      - byVerb:
        - requestCount: 44
          verb: get
        - requestCount: 1493
          verb: list
        requestCount: 1537
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 590
          verb: get
        requestCount: 590
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 476
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 500
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 174
          verb: watch
        requestCount: 174
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 141
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 150
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 91
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 85
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 75
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 3336
    requestCount: 9245
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: get
        - requestCount: 1323
          verb: list
        requestCount: 1355
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 757
          verb: watch
        requestCount: 757
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 450
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 466
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 380
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 404
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 84
          verb: delete
        - requestCount: 168
          verb: get
        requestCount: 252
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 112
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 137
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 106
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 90
          verb: watch
        requestCount: 90
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 79
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 86
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      nodeName: 10.21.102.11
      requestCount: 3895
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: get
        - requestCount: 1278
          verb: list
        requestCount: 1321
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 108
          verb: patch
        - requestCount: 34
          verb: watch
        requestCount: 142
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazp001-4zlm9-worker-eastus2-59559/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazp001-4zlm9-worker-eastus3-mnznv/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazp001-4zlm9-master-rhg6x-2/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-master-rhg6x-2
      nodeName: 10.21.102.12
      requestCount: 1574
    - byUser:
      - byVerb:
        - requestCount: 44
          verb: get
        - requestCount: 1369
          verb: list
        requestCount: 1413
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 600
          verb: get
        requestCount: 600
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 473
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 497
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 178
          verb: watch
        requestCount: 178
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 135
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 144
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 92
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 77
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 49
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 3222
    requestCount: 8691
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: get
        - requestCount: 1477
          verb: list
        requestCount: 1513
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 751
          verb: watch
        requestCount: 751
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 447
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 462
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 378
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 400
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 90
          verb: delete
        - requestCount: 180
          verb: get
        requestCount: 270
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 109
          verb: get
        - requestCount: 27
          verb: watch
        requestCount: 136
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 97
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 105
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 87
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 86
          verb: watch
        requestCount: 86
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      nodeName: 10.21.102.11
      requestCount: 4052
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: get
        - requestCount: 1389
          verb: list
        requestCount: 1429
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 102
          verb: patch
        - requestCount: 31
          verb: watch
        requestCount: 133
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 49
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 13
          verb: watch
        requestCount: 13
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazp001-4zlm9-worker-eastus3-f6gz2/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-f6gz2
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazp001-4zlm9-worker-eastus2-chx4q/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazp001-4zlm9-worker-eastus3-gprvs/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-infra-eastus2-zjfm5/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus3-m8kc9/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      nodeName: 10.21.102.12
      requestCount: 1675
    - byUser:
      - byVerb:
        - requestCount: 42
          verb: get
        - requestCount: 1410
          verb: list
        requestCount: 1452
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 622
          verb: get
        requestCount: 622
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 473
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 497
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 174
          verb: watch
        requestCount: 174
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 135
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 143
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 85
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 94
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 80
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 88
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 61
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 78
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 46
          verb: watch
        requestCount: 46
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      nodeName: 10.21.102.9
      requestCount: 3280
    requestCount: 9007
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 39
          verb: get
        - requestCount: 1662
          verb: list
        requestCount: 1701
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 760
          verb: watch
        requestCount: 760
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 450
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 466
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 377
          verb: get
        - requestCount: 27
          verb: watch
        requestCount: 404
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 72
          verb: delete
        - requestCount: 144
          verb: get
        requestCount: 216
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 107
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 130
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 95
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 103
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 89
          verb: watch
        requestCount: 89
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      nodeName: 10.21.102.11
      requestCount: 4197
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: get
        - requestCount: 1660
          verb: list
        requestCount: 1696
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 108
          verb: patch
        - requestCount: 33
          verb: watch
        requestCount: 141
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus3-gprvs/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazp001-4zlm9-worker-eastus3-m8kc9/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazp001-4zlm9-infra-eastus2-zjfm5/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazp001-4zlm9-infra-eastus3-lcgzj/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-infra-eastus3-lcgzj
      nodeName: 10.21.102.12
      requestCount: 1949
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: get
        - requestCount: 1722
          verb: list
        requestCount: 1765
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 590
          verb: get
        requestCount: 590
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 473
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 497
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 181
          verb: watch
        requestCount: 181
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 133
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 141
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 91
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 77
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 50
          verb: watch
        requestCount: 50
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      nodeName: 10.21.102.9
      requestCount: 3564
    requestCount: 9710
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 34
          verb: get
        - requestCount: 1730
          verb: list
        requestCount: 1764
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 751
          verb: watch
        requestCount: 751
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 411
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 428
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 384
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 407
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 84
          verb: delete
        - requestCount: 168
          verb: get
        requestCount: 252
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 243
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 106
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 128
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 92
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 100
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 91
          verb: watch
        requestCount: 91
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      nodeName: 10.21.102.11
      requestCount: 4250
    - byUser:
      - byVerb:
        - requestCount: 37
          verb: get
        - requestCount: 1701
          verb: list
        requestCount: 1738
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 104
          verb: patch
        - requestCount: 32
          verb: watch
        requestCount: 136
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 49
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 19
          verb: watch
        requestCount: 19
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazp001-4zlm9-worker-eastus3-7599c/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-7599c
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazp001-4zlm9-master-rhg6x-2/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-master-rhg6x-2
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-infra-eastus2-zjfm5/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazp001-4zlm9-worker-eastus2-chx4q/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus2-chx4q
      nodeName: 10.21.102.12
      requestCount: 1992
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: get
        - requestCount: 1771
          verb: list
        requestCount: 1814
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1056
          verb: get
        requestCount: 1056
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:generic-garbage-collector
      - byVerb:
        - requestCount: 594
          verb: get
        requestCount: 594
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 463
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 487
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 180
          verb: watch
        requestCount: 180
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 128
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 136
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 92
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 76
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.21.102.9
      requestCount: 4607
    requestCount: 10849
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: get
        - requestCount: 1211
          verb: list
        requestCount: 1241
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 754
          verb: watch
        requestCount: 754
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 414
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 429
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 378
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 401
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 90
          verb: delete
        - requestCount: 180
          verb: get
        requestCount: 270
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 243
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 111
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 133
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 97
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 105
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 86
          verb: watch
        requestCount: 86
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 77
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 84
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      nodeName: 10.21.102.11
      requestCount: 3746
    - byUser:
      - byVerb:
        - requestCount: 41
          verb: get
        - requestCount: 1137
          verb: list
        requestCount: 1178
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 106
          verb: patch
        - requestCount: 28
          verb: watch
        requestCount: 134
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazp001-4zlm9-worker-eastus3-7599c/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-7599c
      nodeName: 10.21.102.12
      requestCount: 1427
    - byUser:
      - byVerb:
        - requestCount: 37
          verb: get
        - requestCount: 1152
          verb: list
        requestCount: 1189
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 627
          verb: get
        requestCount: 627
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 478
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 502
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 174
          verb: watch
        requestCount: 174
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 133
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 140
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 92
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 77
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 85
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 76
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 3019
    requestCount: 8192
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 45
          verb: get
        - requestCount: 1721
          verb: list
        requestCount: 1766
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 753
          verb: watch
        requestCount: 753
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 444
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 460
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 376
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 400
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 77
          verb: delete
        - requestCount: 153
          verb: get
        requestCount: 230
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 112
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 137
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 107
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 88
          verb: watch
        requestCount: 88
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 79
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 86
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      nodeName: 10.21.102.11
      requestCount: 4289
    - byUser:
      - byVerb:
        - requestCount: 39
          verb: get
        - requestCount: 1730
          verb: list
        requestCount: 1769
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 108
          verb: patch
        - requestCount: 31
          verb: watch
        requestCount: 139
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 19
          verb: watch
        requestCount: 19
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazp001-4zlm9-worker-eastus3-2x82d/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus1-24ggt/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus1-24ggt
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-infra-eastus3-lcgzj/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-infra-eastus3-lcgzj
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-infra-eastus2-zjfm5/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      nodeName: 10.21.102.12
      requestCount: 2037
    - byUser:
      - byVerb:
        - requestCount: 42
          verb: get
        - requestCount: 1687
          verb: list
        requestCount: 1729
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 590
          verb: get
        requestCount: 590
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 467
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 491
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 177
          verb: watch
        requestCount: 177
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 136
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 144
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 93
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 76
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 84
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 75
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 3517
    requestCount: 9843
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 71
          verb: list
        requestCount: 71
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 39
          verb: watch
        requestCount: 39
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 36
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 37
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 19
          verb: get
        - requestCount: 2
          verb: watch
        requestCount: 21
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 5
          verb: delete
        - requestCount: 10
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 16
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 10
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 11
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 10
          verb: list
        requestCount: 11
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-6mxgs:default
      - byVerb:
        - requestCount: 8
          verb: get
        requestCount: 8
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 5
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 6
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      nodeName: 10.21.102.11
      requestCount: 255
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 28
          verb: list
        requestCount: 29
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: ocpazp001-4zlm9-worker-eastus3-gprvs/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: ocpazp001-4zlm9-worker-eastus3-xxg89/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-xxg89
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: ocpazp001-4zlm9-worker-eastus1-24ggt/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus1-24ggt
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: ocpazp001-4zlm9-worker-eastus2-chx4q/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: ocpazp001-4zlm9-worker-eastus3-7599c/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-7599c
      nodeName: 10.21.102.12
      requestCount: 34
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 111
          verb: list
        requestCount: 115
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 49
          verb: get
        - requestCount: 3
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 53
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 43
          verb: get
        requestCount: 43
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 13
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 14
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 7
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 8
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 5
          verb: get
        - requestCount: 2
          verb: watch
        requestCount: 7
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 5
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 6
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      nodeName: 10.21.102.9
      requestCount: 309
    requestCount: 598
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1523
          verb: list
        requestCount: 1523
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 744
          verb: watch
        requestCount: 744
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 468
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 486
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 380
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 406
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        requestCount: 234
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 111
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 133
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 101
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 110
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 83
          verb: watch
        requestCount: 83
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      nodeName: 10.21.102.11
      requestCount: 4047
    - byUser:
      - byVerb:
        - requestCount: 1494
          verb: list
        requestCount: 1494
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 102
          verb: patch
        - requestCount: 35
          verb: watch
        requestCount: 137
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 47
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazp001-4zlm9-worker-eastus3-cmsnk/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-infra-eastus2-zjfm5/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus1-km2pd/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazp001-4zlm9-worker-eastus3-7599c/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-7599c
      nodeName: 10.21.102.12
      requestCount: 1742
    - byUser:
      - byVerb:
        - requestCount: 1538
          verb: list
        requestCount: 1538
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 590
          verb: get
        requestCount: 590
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 476
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 500
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 175
          verb: watch
        requestCount: 175
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 151
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 83
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 93
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 76
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 3343
    requestCount: 9132
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 28
          verb: get
        - requestCount: 1678
          verb: list
        requestCount: 1706
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 762
          verb: watch
        requestCount: 762
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 450
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 466
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 379
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 404
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 84
          verb: delete
        - requestCount: 168
          verb: get
        requestCount: 252
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 106
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 130
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 99
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 107
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 77
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 86
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 84
          verb: watch
        requestCount: 84
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      nodeName: 10.21.102.11
      requestCount: 4239
    - byUser:
      - byVerb:
        - requestCount: 27
          verb: get
        - requestCount: 1727
          verb: list
        requestCount: 1754
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 108
          verb: patch
        - requestCount: 33
          verb: watch
        requestCount: 141
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazp001-4zlm9-worker-eastus3-7599c/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-7599c
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-hub-operator-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus3-m8kc9/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-infra-eastus3-lcgzj/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-infra-eastus3-lcgzj
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus2-tbwst/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus2-tbwst
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazp001-4zlm9-worker-eastus1-xdbqq/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      nodeName: 10.21.102.12
      requestCount: 2012
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: get
        - requestCount: 1613
          verb: list
        requestCount: 1646
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 590
          verb: get
        requestCount: 590
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 484
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 508
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 168
          verb: watch
        requestCount: 168
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 130
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 138
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 83
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 90
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 77
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 85
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 75
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 50
          verb: watch
        requestCount: 50
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      nodeName: 10.21.102.9
      requestCount: 3436
    requestCount: 9687
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 35
          verb: get
        - requestCount: 1575
          verb: list
        requestCount: 1610
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 747
          verb: watch
        requestCount: 747
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 444
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 460
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 376
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 398
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 84
          verb: delete
        - requestCount: 168
          verb: get
        requestCount: 252
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 111
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 135
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 94
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 102
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 92
          verb: watch
        requestCount: 92
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 85
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      nodeName: 10.21.102.11
      requestCount: 4123
    - byUser:
      - byVerb:
        - requestCount: 38
          verb: get
        - requestCount: 1527
          verb: list
        requestCount: 1565
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 102
          verb: patch
        - requestCount: 33
          verb: watch
        requestCount: 135
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 49
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 11
          verb: watch
        requestCount: 11
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazp001-4zlm9-worker-eastus3-tssl4/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-tssl4
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus3-xxg89/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-xxg89
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-infra-eastus3-lcgzj/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-infra-eastus3-lcgzj
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazp001-4zlm9-worker-eastus1-xdbqq/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      nodeName: 10.21.102.12
      requestCount: 1807
    - byUser:
      - byVerb:
        - requestCount: 47
          verb: get
        - requestCount: 1538
          verb: list
        requestCount: 1585
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 632
          verb: get
        requestCount: 632
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 471
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 495
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 180
          verb: watch
        requestCount: 180
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 133
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 140
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 91
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 76
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 49
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 3420
    requestCount: 9350
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 41
          verb: get
        - requestCount: 1315
          verb: list
        requestCount: 1356
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 764
          verb: watch
        requestCount: 764
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 426
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 441
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 377
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 399
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        requestCount: 234
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 108
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 132
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 95
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 102
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 77
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 85
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 83
          verb: watch
        requestCount: 83
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      nodeName: 10.21.102.11
      requestCount: 3838
    - byUser:
      - byVerb:
        - requestCount: 38
          verb: get
        - requestCount: 1265
          verb: list
        requestCount: 1303
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 108
          verb: patch
        - requestCount: 33
          verb: watch
        requestCount: 141
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazp001-4zlm9-infra-eastus3-lcgzj/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-infra-eastus3-lcgzj
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-hub-operator-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus3-tssl4/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-tssl4
      - byVerb:
        - requestCount: 7
          verb: get
        requestCount: 7
        userAgent: rhacs-operator/v4.8.2
        username: system:serviceaccount:rhacs-operator:rhacs-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazp001-4zlm9-worker-eastus3-m8kc9/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      nodeName: 10.21.102.12
      requestCount: 1556
    - byUser:
      - byVerb:
        - requestCount: 41
          verb: get
        - requestCount: 1327
          verb: list
        requestCount: 1368
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 590
          verb: get
        requestCount: 590
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 478
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 502
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 173
          verb: watch
        requestCount: 173
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 129
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 138
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 93
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 87
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 76
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 85
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 76
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 48
          verb: watch
        requestCount: 48
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      nodeName: 10.21.102.9
      requestCount: 3160
    requestCount: 8554
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 47
          verb: get
        - requestCount: 1835
          verb: list
        requestCount: 1882
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 750
          verb: watch
        requestCount: 750
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 453
          verb: get
        - requestCount: 19
          verb: watch
        requestCount: 472
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 377
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 403
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 84
          verb: delete
        - requestCount: 168
          verb: get
        requestCount: 252
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 112
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 137
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 105
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 91
          verb: watch
        requestCount: 91
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 87
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      nodeName: 10.21.102.11
      requestCount: 4421
    - byUser:
      - byVerb:
        - requestCount: 45
          verb: get
        - requestCount: 1848
          verb: list
        requestCount: 1893
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 102
          verb: patch
        - requestCount: 33
          verb: watch
        requestCount: 135
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus1-xdbqq/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus3-f6gz2/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-f6gz2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus3-7599c/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-7599c
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      nodeName: 10.21.102.12
      requestCount: 2138
    - byUser:
      - byVerb:
        - requestCount: 46
          verb: get
        - requestCount: 1753
          verb: list
        requestCount: 1799
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 590
          verb: get
        requestCount: 590
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 465
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 489
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 180
          verb: watch
        requestCount: 180
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 141
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 149
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 92
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 77
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 85
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 77
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 49
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 3596
    requestCount: 10155
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: get
        - requestCount: 1548
          verb: list
        requestCount: 1580
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 720
          verb: watch
        requestCount: 720
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 420
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 434
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 355
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 378
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 90
          verb: delete
        - requestCount: 180
          verb: get
        requestCount: 270
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 73
          verb: delete
        - requestCount: 146
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 226
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 102
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 125
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 89
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 96
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 84
          verb: watch
        requestCount: 84
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 71
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 78
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      nodeName: 10.21.102.11
      requestCount: 3991
    - byUser:
      - byVerb:
        - requestCount: 51
          verb: get
        - requestCount: 1501
          verb: list
        requestCount: 1552
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 102
          verb: patch
        - requestCount: 32
          verb: watch
        requestCount: 134
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 12
          verb: watch
        requestCount: 12
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazp001-4zlm9-worker-eastus3-m8kc9/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazp001-4zlm9-worker-eastus3-tssl4/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-tssl4
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-hub-operator-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazp001-4zlm9-infra-eastus3-lcgzj/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-infra-eastus3-lcgzj
      nodeName: 10.21.102.12
      requestCount: 1798
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: get
        - requestCount: 1566
          verb: list
        requestCount: 1609
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 631
          verb: get
        requestCount: 631
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 476
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 500
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 181
          verb: watch
        requestCount: 181
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 141
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 149
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 85
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 93
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 80
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 89
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 61
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 76
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 49
          verb: watch
        requestCount: 49
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      nodeName: 10.21.102.9
      requestCount: 3463
    requestCount: 9252
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 42
          verb: get
        - requestCount: 1686
          verb: list
        requestCount: 1728
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 758
          verb: watch
        requestCount: 758
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 435
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 450
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 379
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 403
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 243
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        requestCount: 234
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 107
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 130
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 106
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 89
          verb: watch
        requestCount: 89
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 76
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 84
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      nodeName: 10.21.102.11
      requestCount: 4225
    - byUser:
      - byVerb:
        - requestCount: 39
          verb: get
        - requestCount: 1691
          verb: list
        requestCount: 1730
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 108
          verb: patch
        - requestCount: 30
          verb: watch
        requestCount: 138
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazp001-4zlm9-worker-eastus2-59559/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazp001-4zlm9-worker-eastus3-2x82d/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazp001-4zlm9-master-rhg6x-2/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-master-rhg6x-2
      nodeName: 10.21.102.12
      requestCount: 1984
    - byUser:
      - byVerb:
        - requestCount: 39
          verb: get
        - requestCount: 1721
          verb: list
        requestCount: 1760
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 590
          verb: get
        requestCount: 590
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 465
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 489
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 174
          verb: watch
        requestCount: 174
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 136
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 145
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 92
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 77
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 86
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 76
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 50
          verb: watch
        requestCount: 50
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      nodeName: 10.21.102.9
      requestCount: 3548
    requestCount: 9757
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 44
          verb: get
        - requestCount: 1791
          verb: list
        requestCount: 1835
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 759
          verb: watch
        requestCount: 759
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 435
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 450
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 375
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 400
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 84
          verb: delete
        - requestCount: 168
          verb: get
        requestCount: 252
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 243
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 108
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 132
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 94
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 101
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 89
          verb: watch
        requestCount: 89
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      nodeName: 10.21.102.11
      requestCount: 4347
    - byUser:
      - byVerb:
        - requestCount: 38
          verb: get
        - requestCount: 1882
          verb: list
        requestCount: 1920
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 102
          verb: patch
        - requestCount: 32
          verb: watch
        requestCount: 134
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 28
          verb: watch
        requestCount: 28
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 2
          verb: create
        - requestCount: 2
          verb: delete
        - requestCount: 6
          verb: list
        requestCount: 10
        userAgent: OpenAPI-Generator/26.1.0/python
        username: system:serviceaccount:aap:aap-gateway-eda
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: ocpazp001-4zlm9-worker-eastus3-gprvs/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-worker-eastus3-xxg89/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-xxg89
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazp001-4zlm9-infra-eastus3-lcgzj/ovnkube@51b0ffcbc273
        username: system:ovn-node:ocpazp001-4zlm9-infra-eastus3-lcgzj
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      nodeName: 10.21.102.12
      requestCount: 2181
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: get
        - requestCount: 1795
          verb: list
        requestCount: 1835
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 590
          verb: get
        requestCount: 590
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 473
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 497
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 179
          verb: watch
        requestCount: 179
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 132
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 140
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 92
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 86
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 85
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 77
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.9
      requestCount: 3629
    requestCount: 10157
  requestCount: 208905
