---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2023-12-19T12:54:30Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2023-12-19T12:54:30Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:06:38Z"
  name: projecthelmchartrepositories.v1beta1.helm.openshift.io
  resourceVersion: "*********"
  uid: 60c7c415-6a36-4f00-be57-d3738679102e
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: ""
        username: V8131
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: Z8186
      nodeName: 10.21.102.11
      requestCount: 43
    - byUser:
      - byVerb:
        - requestCount: 13
          verb: list
        requestCount: 13
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: V8131
      nodeName: 10.21.102.12
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: list
        requestCount: 43
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: Z7010
      nodeName: 10.21.102.9
      requestCount: 52
    requestCount: 110
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 831
          verb: list
        requestCount: 831
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 33
          verb: list
        requestCount: 33
        userAgent: ""
        username: J8683
      - byVerb:
        - requestCount: 30
          verb: list
        requestCount: 30
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 14
          verb: list
        requestCount: 14
        userAgent: ""
        username: Z7003
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: S0914
      nodeName: 10.21.102.11
      requestCount: 911
    - byUser:
      - byVerb:
        - requestCount: 761
          verb: list
        requestCount: 761
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 42
          verb: list
        requestCount: 42
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 25
          verb: list
        requestCount: 25
        userAgent: ""
        username: J8683
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: ""
        username: Z7003
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: S0914
      nodeName: 10.21.102.12
      requestCount: 856
    - byUser:
      - byVerb:
        - requestCount: 767
          verb: list
        requestCount: 767
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 29
          verb: list
        requestCount: 29
        userAgent: ""
        username: J8683
      - byVerb:
        - requestCount: 24
          verb: list
        requestCount: 24
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 15
          verb: list
        requestCount: 15
        userAgent: ""
        username: Z7003
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: S0914
      nodeName: 10.21.102.9
      requestCount: 856
    requestCount: 2623
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 878
          verb: list
        requestCount: 878
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 195
          verb: list
        requestCount: 195
        userAgent: ""
        username: Z8504
      - byVerb:
        - requestCount: 117
          verb: list
        requestCount: 117
        userAgent: ""
        username: Z6988
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: ""
        username: S0914
      nodeName: 10.21.102.11
      requestCount: 1201
    - byUser:
      - byVerb:
        - requestCount: 838
          verb: list
        requestCount: 838
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 204
          verb: list
        requestCount: 204
        userAgent: ""
        username: Z8504
      - byVerb:
        - requestCount: 154
          verb: list
        requestCount: 154
        userAgent: ""
        username: Z6988
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: ""
        username: S0914
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 1229
    - byUser:
      - byVerb:
        - requestCount: 879
          verb: list
        requestCount: 879
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 171
          verb: list
        requestCount: 171
        userAgent: ""
        username: Z6988
      - byVerb:
        - requestCount: 163
          verb: list
        requestCount: 163
        userAgent: ""
        username: Z8504
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: ""
        username: S0914
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1260
    requestCount: 3690
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 878
          verb: list
        requestCount: 878
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 125
          verb: list
        requestCount: 125
        userAgent: ""
        username: Z8504
      - byVerb:
        - requestCount: 59
          verb: list
        requestCount: 59
        userAgent: ""
        username: Z6988
      - byVerb:
        - requestCount: 18
          verb: list
        requestCount: 18
        userAgent: ""
        username: S0914
      nodeName: 10.21.102.11
      requestCount: 1080
    - byUser:
      - byVerb:
        - requestCount: 815
          verb: list
        requestCount: 815
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 136
          verb: list
        requestCount: 136
        userAgent: ""
        username: Z8504
      - byVerb:
        - requestCount: 86
          verb: list
        requestCount: 86
        userAgent: ""
        username: Z6988
      - byVerb:
        - requestCount: 18
          verb: list
        requestCount: 18
        userAgent: ""
        username: S0914
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 1063
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 83
          verb: list
        requestCount: 83
        userAgent: ""
        username: Z8504
      - byVerb:
        - requestCount: 70
          verb: list
        requestCount: 70
        userAgent: ""
        username: Z6988
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: ""
        username: S0914
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1018
    requestCount: 3161
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1005
          verb: list
        requestCount: 1005
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 94
          verb: list
        requestCount: 94
        userAgent: ""
        username: Z6988
      nodeName: 10.21.102.11
      requestCount: 1099
    - byUser:
      - byVerb:
        - requestCount: 996
          verb: list
        requestCount: 996
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 72
          verb: list
        requestCount: 72
        userAgent: ""
        username: Z6988
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 1076
    - byUser:
      - byVerb:
        - requestCount: 968
          verb: list
        requestCount: 968
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 102
          verb: list
        requestCount: 102
        userAgent: ""
        username: Z6988
      - byVerb:
        - requestCount: 7
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 14
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1092
    requestCount: 3267
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 918
          verb: list
        requestCount: 918
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 56
          verb: list
        requestCount: 56
        userAgent: ""
        username: Z6988
      nodeName: 10.21.102.11
      requestCount: 974
    - byUser:
      - byVerb:
        - requestCount: 954
          verb: list
        requestCount: 954
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 49
          verb: list
        requestCount: 49
        userAgent: ""
        username: Z6988
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: S0914
      nodeName: 10.21.102.12
      requestCount: 1013
    - byUser:
      - byVerb:
        - requestCount: 960
          verb: list
        requestCount: 960
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 55
          verb: list
        requestCount: 55
        userAgent: ""
        username: Z6988
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: L9334
      nodeName: 10.21.102.9
      requestCount: 1036
    requestCount: 3023
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 983
          verb: list
        requestCount: 983
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: ""
        username: Z6988
      nodeName: 10.21.102.11
      requestCount: 990
    - byUser:
      - byVerb:
        - requestCount: 977
          verb: list
        requestCount: 977
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: Z6988
      nodeName: 10.21.102.12
      requestCount: 987
    - byUser:
      - byVerb:
        - requestCount: 989
          verb: list
        requestCount: 989
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: ""
        username: Z6988
      nodeName: 10.21.102.9
      requestCount: 1015
    requestCount: 2992
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 812
          verb: list
        requestCount: 812
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: list
        requestCount: 15
        userAgent: ""
        username: Z6988
      nodeName: 10.21.102.11
      requestCount: 827
    - byUser:
      - byVerb:
        - requestCount: 809
          verb: list
        requestCount: 809
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 12
          verb: list
        requestCount: 12
        userAgent: ""
        username: Z6988
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 828
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: list
        requestCount: 860
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 27
          verb: list
        requestCount: 27
        userAgent: ""
        username: Z6988
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 906
    requestCount: 2561
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 915
          verb: list
        requestCount: 915
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 32
          verb: list
        requestCount: 32
        userAgent: ""
        username: I6669
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: Z6988
      nodeName: 10.21.102.11
      requestCount: 948
    - byUser:
      - byVerb:
        - requestCount: 857
          verb: list
        requestCount: 857
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: ""
        username: I6669
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 892
    - byUser:
      - byVerb:
        - requestCount: 876
          verb: list
        requestCount: 876
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: ""
        username: I6669
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: Z6988
      nodeName: 10.21.102.9
      requestCount: 917
    requestCount: 2757
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: list
        requestCount: 860
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 860
    - byUser:
      - byVerb:
        - requestCount: 816
          verb: list
        requestCount: 816
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: S0914
      nodeName: 10.21.102.12
      requestCount: 825
    - byUser:
      - byVerb:
        - requestCount: 845
          verb: list
        requestCount: 845
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 865
    requestCount: 2550
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 913
          verb: list
        requestCount: 913
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: ""
        username: Z6988
      nodeName: 10.21.102.11
      requestCount: 918
    - byUser:
      - byVerb:
        - requestCount: 900
          verb: list
        requestCount: 900
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 12
          verb: list
        requestCount: 12
        userAgent: ""
        username: Z6988
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 918
    - byUser:
      - byVerb:
        - requestCount: 958
          verb: list
        requestCount: 958
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 13
          verb: list
        requestCount: 13
        userAgent: ""
        username: Z6988
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: S0914
      nodeName: 10.21.102.9
      requestCount: 992
    requestCount: 2828
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 769
          verb: list
        requestCount: 769
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: ""
        username: Z6988
      nodeName: 10.21.102.11
      requestCount: 779
    - byUser:
      - byVerb:
        - requestCount: 825
          verb: list
        requestCount: 825
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: ""
        username: Z6988
      nodeName: 10.21.102.12
      requestCount: 838
    - byUser:
      - byVerb:
        - requestCount: 746
          verb: list
        requestCount: 746
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 12
          verb: list
        requestCount: 12
        userAgent: ""
        username: Z6988
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 778
    requestCount: 2395
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 902
          verb: list
        requestCount: 902
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: ""
        username: L9338
      nodeName: 10.21.102.11
      requestCount: 906
    - byUser:
      - byVerb:
        - requestCount: 896
          verb: list
        requestCount: 896
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: L9338
      nodeName: 10.21.102.12
      requestCount: 903
    - byUser:
      - byVerb:
        - requestCount: 969
          verb: list
        requestCount: 969
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: ""
        username: L9338
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1003
    requestCount: 2812
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1111
          verb: list
        requestCount: 1111
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 33
          verb: list
        requestCount: 33
        userAgent: ""
        username: Z7040
      - byVerb:
        - requestCount: 27
          verb: list
        requestCount: 27
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 22
          verb: list
        requestCount: 22
        userAgent: ""
        username: L9323
      nodeName: 10.21.102.11
      requestCount: 1193
    - byUser:
      - byVerb:
        - requestCount: 1087
          verb: list
        requestCount: 1087
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 41
          verb: list
        requestCount: 41
        userAgent: ""
        username: L9323
      - byVerb:
        - requestCount: 21
          verb: list
        requestCount: 21
        userAgent: ""
        username: Z7040
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: ""
        username: L9334
      nodeName: 10.21.102.12
      requestCount: 1166
    - byUser:
      - byVerb:
        - requestCount: 1115
          verb: list
        requestCount: 1115
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: ""
        username: Z7040
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: ""
        username: L9323
      - byVerb:
        - requestCount: 14
          verb: list
        requestCount: 14
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1190
    requestCount: 3549
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 837
          verb: list
        requestCount: 837
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 66
          verb: list
        requestCount: 66
        userAgent: ""
        username: L9323
      - byVerb:
        - requestCount: 21
          verb: list
        requestCount: 21
        userAgent: ""
        username: L9334
      nodeName: 10.21.102.11
      requestCount: 924
    - byUser:
      - byVerb:
        - requestCount: 823
          verb: list
        requestCount: 823
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 66
          verb: list
        requestCount: 66
        userAgent: ""
        username: L9323
      - byVerb:
        - requestCount: 22
          verb: list
        requestCount: 22
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: L9338
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: V8131
      nodeName: 10.21.102.12
      requestCount: 914
    - byUser:
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 41
          verb: list
        requestCount: 41
        userAgent: ""
        username: L9323
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: L9338
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: V8131
      nodeName: 10.21.102.9
      requestCount: 916
    requestCount: 2754
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: ""
        username: V8131
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: Z8186
      nodeName: 10.21.102.11
      requestCount: 43
    - byUser:
      - byVerb:
        - requestCount: 13
          verb: list
        requestCount: 13
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: V8131
      nodeName: 10.21.102.12
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: list
        requestCount: 43
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: Z7010
      nodeName: 10.21.102.9
      requestCount: 52
    requestCount: 110
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 922
          verb: list
        requestCount: 922
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 21
          verb: list
        requestCount: 21
        userAgent: ""
        username: Z8249
      - byVerb:
        - requestCount: 13
          verb: list
        requestCount: 13
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: ""
        username: Z7010
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: Z6988
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: Z7040
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: S0914
      nodeName: 10.21.102.11
      requestCount: 965
    - byUser:
      - byVerb:
        - requestCount: 910
          verb: list
        requestCount: 910
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 19
          verb: list
        requestCount: 19
        userAgent: ""
        username: Z8249
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: ""
        username: Z7010
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: Z7040
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: S0914
      nodeName: 10.21.102.12
      requestCount: 966
    - byUser:
      - byVerb:
        - requestCount: 954
          verb: list
        requestCount: 954
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 23
          verb: list
        requestCount: 23
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: ""
        username: Z8249
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: ""
        username: Z6988
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: ""
        username: S0914
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: Z7040
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: Z7010
      nodeName: 10.21.102.9
      requestCount: 1029
    requestCount: 2960
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 753
          verb: list
        requestCount: 753
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 66
          verb: list
        requestCount: 66
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 28
          verb: list
        requestCount: 28
        userAgent: ""
        username: Z8249
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: ""
        username: S0914
      - byVerb:
        - requestCount: 13
          verb: list
        requestCount: 13
        userAgent: ""
        username: L9334
      nodeName: 10.21.102.11
      requestCount: 880
    - byUser:
      - byVerb:
        - requestCount: 749
          verb: list
        requestCount: 749
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 56
          verb: list
        requestCount: 56
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 21
          verb: list
        requestCount: 21
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 19
          verb: list
        requestCount: 19
        userAgent: ""
        username: Z8249
      - byVerb:
        - requestCount: 14
          verb: list
        requestCount: 14
        userAgent: ""
        username: S0914
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 868
    - byUser:
      - byVerb:
        - requestCount: 718
          verb: list
        requestCount: 718
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 87
          verb: list
        requestCount: 87
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: ""
        username: S0914
      - byVerb:
        - requestCount: 13
          verb: list
        requestCount: 13
        userAgent: ""
        username: Z8249
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 868
    requestCount: 2616
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 893
          verb: list
        requestCount: 893
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 68
          verb: list
        requestCount: 68
        userAgent: ""
        username: T4494
      - byVerb:
        - requestCount: 50
          verb: list
        requestCount: 50
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 40
          verb: list
        requestCount: 40
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: ""
        username: Z7003
      - byVerb:
        - requestCount: 27
          verb: list
        requestCount: 27
        userAgent: ""
        username: Z6981
      - byVerb:
        - requestCount: 27
          verb: list
        requestCount: 27
        userAgent: ""
        username: L9323
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: ""
        username: Z8249
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: S0914
      nodeName: 10.21.102.11
      requestCount: 1168
    - byUser:
      - byVerb:
        - requestCount: 926
          verb: list
        requestCount: 926
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 74
          verb: list
        requestCount: 74
        userAgent: ""
        username: T4494
      - byVerb:
        - requestCount: 51
          verb: list
        requestCount: 51
        userAgent: ""
        username: Z6981
      - byVerb:
        - requestCount: 42
          verb: list
        requestCount: 42
        userAgent: ""
        username: Z7003
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 30
          verb: list
        requestCount: 30
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 27
          verb: list
        requestCount: 27
        userAgent: ""
        username: L9323
      - byVerb:
        - requestCount: 19
          verb: list
        requestCount: 19
        userAgent: ""
        username: Z8249
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: S0914
      nodeName: 10.21.102.12
      requestCount: 1214
    - byUser:
      - byVerb:
        - requestCount: 961
          verb: list
        requestCount: 961
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 39
          verb: list
        requestCount: 39
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 33
          verb: list
        requestCount: 33
        userAgent: ""
        username: Z6981
      - byVerb:
        - requestCount: 29
          verb: list
        requestCount: 29
        userAgent: ""
        username: T4494
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: ""
        username: Z7003
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: ""
        username: L9323
      - byVerb:
        - requestCount: 15
          verb: list
        requestCount: 15
        userAgent: ""
        username: Z8249
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1160
    requestCount: 3542
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 74
          verb: list
        requestCount: 74
        userAgent: ""
        username: Z7003
      - byVerb:
        - requestCount: 31
          verb: list
        requestCount: 31
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 27
          verb: list
        requestCount: 27
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: ""
        username: Z8249
      - byVerb:
        - requestCount: 12
          verb: list
        requestCount: 12
        userAgent: ""
        username: S0914
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: ""
        username: I2642
      nodeName: 10.21.102.11
      requestCount: 1012
    - byUser:
      - byVerb:
        - requestCount: 834
          verb: list
        requestCount: 834
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 57
          verb: list
        requestCount: 57
        userAgent: ""
        username: Z7003
      - byVerb:
        - requestCount: 31
          verb: list
        requestCount: 31
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 21
          verb: list
        requestCount: 21
        userAgent: ""
        username: Z8249
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 16
          verb: list
        requestCount: 16
        userAgent: ""
        username: S0914
      - byVerb:
        - requestCount: 12
          verb: list
        requestCount: 12
        userAgent: ""
        username: I2642
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 999
    - byUser:
      - byVerb:
        - requestCount: 876
          verb: list
        requestCount: 876
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 91
          verb: list
        requestCount: 91
        userAgent: ""
        username: Z7003
      - byVerb:
        - requestCount: 21
          verb: list
        requestCount: 21
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 19
          verb: list
        requestCount: 19
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 13
          verb: list
        requestCount: 13
        userAgent: ""
        username: Z8249
      - byVerb:
        - requestCount: 12
          verb: list
        requestCount: 12
        userAgent: ""
        username: S0914
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: I2642
      nodeName: 10.21.102.9
      requestCount: 1054
    requestCount: 3065
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 872
          verb: list
        requestCount: 872
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 63
          verb: list
        requestCount: 63
        userAgent: ""
        username: Z7003
      - byVerb:
        - requestCount: 28
          verb: list
        requestCount: 28
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 27
          verb: list
        requestCount: 27
        userAgent: ""
        username: S0914
      - byVerb:
        - requestCount: 27
          verb: list
        requestCount: 27
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 23
          verb: list
        requestCount: 23
        userAgent: ""
        username: I2642
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: ""
        username: Z8249
      nodeName: 10.21.102.11
      requestCount: 1049
    - byUser:
      - byVerb:
        - requestCount: 864
          verb: list
        requestCount: 864
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 44
          verb: list
        requestCount: 44
        userAgent: ""
        username: Z7003
      - byVerb:
        - requestCount: 35
          verb: list
        requestCount: 35
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 18
          verb: list
        requestCount: 18
        userAgent: ""
        username: S0914
      - byVerb:
        - requestCount: 18
          verb: list
        requestCount: 18
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: ""
        username: Z8249
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: ""
        username: I2642
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 1006
    - byUser:
      - byVerb:
        - requestCount: 905
          verb: list
        requestCount: 905
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 68
          verb: list
        requestCount: 68
        userAgent: ""
        username: Z7003
      - byVerb:
        - requestCount: 21
          verb: list
        requestCount: 21
        userAgent: ""
        username: I2642
      - byVerb:
        - requestCount: 16
          verb: list
        requestCount: 16
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 15
          verb: list
        requestCount: 15
        userAgent: ""
        username: S0914
      - byVerb:
        - requestCount: 14
          verb: list
        requestCount: 14
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: ""
        username: Z8249
      nodeName: 10.21.102.9
      requestCount: 1067
    requestCount: 3122
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 925
          verb: list
        requestCount: 925
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 32
          verb: list
        requestCount: 32
        userAgent: ""
        username: Z7003
      - byVerb:
        - requestCount: 23
          verb: list
        requestCount: 23
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: ""
        username: S0914
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: Z8504
      nodeName: 10.21.102.11
      requestCount: 1005
    - byUser:
      - byVerb:
        - requestCount: 859
          verb: list
        requestCount: 859
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 42
          verb: list
        requestCount: 42
        userAgent: ""
        username: Z7003
      - byVerb:
        - requestCount: 41
          verb: list
        requestCount: 41
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 23
          verb: list
        requestCount: 23
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: ""
        username: Z8504
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: ""
        username: S0914
      nodeName: 10.21.102.12
      requestCount: 984
    - byUser:
      - byVerb:
        - requestCount: 898
          verb: list
        requestCount: 898
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 46
          verb: list
        requestCount: 46
        userAgent: ""
        username: Z7003
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 19
          verb: list
        requestCount: 19
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: ""
        username: S0914
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: Z8504
      nodeName: 10.21.102.9
      requestCount: 1011
    requestCount: 3000
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 821
          verb: list
        requestCount: 821
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 43
          verb: list
        requestCount: 43
        userAgent: ""
        username: Z7003
      - byVerb:
        - requestCount: 25
          verb: list
        requestCount: 25
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: S0914
      nodeName: 10.21.102.11
      requestCount: 896
    - byUser:
      - byVerb:
        - requestCount: 834
          verb: list
        requestCount: 834
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 52
          verb: list
        requestCount: 52
        userAgent: ""
        username: Z7003
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: ""
        username: Z8186
      nodeName: 10.21.102.12
      requestCount: 937
    - byUser:
      - byVerb:
        - requestCount: 813
          verb: list
        requestCount: 813
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 41
          verb: list
        requestCount: 41
        userAgent: ""
        username: Z7003
      - byVerb:
        - requestCount: 25
          verb: list
        requestCount: 25
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 12
          verb: list
        requestCount: 12
        userAgent: ""
        username: Z8186
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 912
    requestCount: 2745
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 829
          verb: list
        requestCount: 829
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 70
          verb: list
        requestCount: 70
        userAgent: ""
        username: Z7003
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 13
          verb: list
        requestCount: 13
        userAgent: ""
        username: S0914
      nodeName: 10.21.102.11
      requestCount: 938
    - byUser:
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 41
          verb: list
        requestCount: 41
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: ""
        username: Z7003
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: ""
        username: S0914
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 920
    - byUser:
      - byVerb:
        - requestCount: 832
          verb: list
        requestCount: 832
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 70
          verb: list
        requestCount: 70
        userAgent: ""
        username: Z7003
      - byVerb:
        - requestCount: 25
          verb: list
        requestCount: 25
        userAgent: ""
        username: L9334
      - byVerb:
        - requestCount: 15
          verb: list
        requestCount: 15
        userAgent: ""
        username: S0914
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 962
    requestCount: 2820
  requestCount: 64942
