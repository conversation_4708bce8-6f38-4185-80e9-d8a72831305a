---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2023-12-19T16:54:06Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2023-12-19T16:54:06Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:05:27Z"
  name: oauthauthorizetokens.v1.oauth.openshift.io
  resourceVersion: "*********"
  uid: 49e7a08d-6834-4ad5-a14a-42519b24e59b
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    requestCount: 0
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: create
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 1
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: create
        - requestCount: 2
          verb: delete
        - requestCount: 2
          verb: get
        requestCount: 7
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 19
    requestCount: 20
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: create
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 1
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: create
        - requestCount: 3
          verb: delete
        - requestCount: 3
          verb: get
        requestCount: 9
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 21
    requestCount: 22
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: create
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 1
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: create
        - requestCount: 4
          verb: delete
        - requestCount: 4
          verb: get
        requestCount: 11
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 23
    requestCount: 24
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 14
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 14
    requestCount: 14
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 12
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: create
        - requestCount: 1
          verb: delete
        - requestCount: 1
          verb: get
        requestCount: 3
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 3
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 15
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 12
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 2
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: create
        - requestCount: 3
          verb: delete
        - requestCount: 3
          verb: get
        requestCount: 7
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 19
    requestCount: 21
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 12
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 12
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 12
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: create
        - requestCount: 1
          verb: delete
        - requestCount: 1
          verb: get
        requestCount: 3
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 15
    requestCount: 15
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: create
        requestCount: 5
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 5
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 7
          verb: delete
        - requestCount: 7
          verb: get
        requestCount: 18
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 30
    requestCount: 35
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: create
        - requestCount: 1
          verb: delete
        - requestCount: 1
          verb: get
        requestCount: 3
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 3
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: create
        - requestCount: 5
          verb: delete
        - requestCount: 5
          verb: get
        requestCount: 15
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 27
    requestCount: 30
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    requestCount: 0
  - requestCount: 0
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: create
        - requestCount: 1
          verb: delete
        - requestCount: 1
          verb: get
        requestCount: 3
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 15
    requestCount: 15
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: create
        - requestCount: 1
          verb: delete
        - requestCount: 1
          verb: get
        requestCount: 5
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 5
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: create
        - requestCount: 3
          verb: delete
        - requestCount: 3
          verb: get
        requestCount: 7
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 19
    requestCount: 24
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: create
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 1
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: create
        - requestCount: 6
          verb: delete
        - requestCount: 6
          verb: get
        requestCount: 17
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 29
    requestCount: 30
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: create
        requestCount: 3
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 3
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: create
        - requestCount: 6
          verb: delete
        - requestCount: 6
          verb: get
        requestCount: 17
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 29
    requestCount: 32
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 12
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: create
        - requestCount: 1
          verb: delete
        - requestCount: 1
          verb: get
        requestCount: 3
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 15
    requestCount: 15
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 12
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: create
        - requestCount: 1
          verb: delete
        - requestCount: 1
          verb: get
        requestCount: 3
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 3
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: create
        - requestCount: 1
          verb: delete
        - requestCount: 1
          verb: get
        requestCount: 3
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 15
    requestCount: 18
  requestCount: 414
