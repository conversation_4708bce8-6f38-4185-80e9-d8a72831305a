---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2023-12-19T12:51:51Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2023-12-19T12:51:51Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:06:06Z"
  name: serviceaccounts.v1
  resourceVersion: "*********"
  uid: fbef5318-7bba-4198-94ae-55b7566f9720
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 71
          verb: list
        requestCount: 71
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: delete
        - requestCount: 34
          verb: get
        requestCount: 42
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 25
          verb: get
        requestCount: 25
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 24
          verb: get
        requestCount: 24
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 22
          verb: get
        requestCount: 22
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 5
          verb: create
        - requestCount: 10
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 16
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 12
          verb: create
        requestCount: 12
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-5wphs
      - byVerb:
        - requestCount: 6
          verb: create
        requestCount: 6
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 2
          verb: patch
        requestCount: 6
        userAgent: trident-operator/v0.0.0
        username: system:serviceaccount:openshift-operators:trident-operator
      nodeName: 10.21.102.11
      requestCount: 258
    - byUser:
      - byVerb:
        - requestCount: 222
          verb: get
        requestCount: 222
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 111
          verb: list
        requestCount: 111
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 96
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 97
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 41
          verb: get
        requestCount: 41
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 31
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 32
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 29
          verb: get
        - requestCount: 2
          verb: watch
        requestCount: 31
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 21
          verb: create
        requestCount: 21
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-sqqdl
      - byVerb:
        - requestCount: 21
          verb: create
        requestCount: 21
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-w959c
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 2
          verb: watch
        requestCount: 19
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 16
          verb: create
        requestCount: 16
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      nodeName: 10.21.102.9
      requestCount: 857
    requestCount: 1115
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1743
          verb: list
        requestCount: 1743
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 368
          verb: create
        - requestCount: 985
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 1373
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 109
          verb: delete
        - requestCount: 496
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 628
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 388
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 404
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 392
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 399
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 370
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 394
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 314
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 324
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 154
          verb: get
        requestCount: 210
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 104
          verb: create
        requestCount: 104
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 66
          verb: create
        requestCount: 66
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      nodeName: 10.21.102.11
      requestCount: 5645
    - byUser:
      - byVerb:
        - requestCount: 1829
          verb: list
        requestCount: 1829
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 131
          verb: create
        requestCount: 131
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 126
          verb: create
        requestCount: 126
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 122
          verb: create
        requestCount: 122
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 109
          verb: create
        requestCount: 109
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 101
          verb: create
        requestCount: 101
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 16
          verb: create
        - requestCount: 16
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 40
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: delete
        - requestCount: 11
          verb: get
        - requestCount: 6
          verb: patch
        requestCount: 18
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:generic-garbage-collector
      nodeName: 10.21.102.12
      requestCount: 2777
    - byUser:
      - byVerb:
        - requestCount: 2821
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 2830
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1948
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 16
          verb: watch
        requestCount: 1976
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1758
          verb: list
        requestCount: 1758
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 615
          verb: get
        requestCount: 615
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 415
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 423
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 302
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 320
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 206
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 221
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 205
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 220
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 135
          verb: create
        requestCount: 135
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 132
          verb: create
        requestCount: 132
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-w959c
      nodeName: 10.21.102.9
      requestCount: 8630
    requestCount: 17052
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1566
          verb: list
        requestCount: 1566
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 349
          verb: create
        - requestCount: 858
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 1215
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 97
          verb: delete
        - requestCount: 435
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 555
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 374
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 389
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 319
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 328
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 286
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 310
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 272
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 280
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 60
          verb: delete
        - requestCount: 165
          verb: get
        requestCount: 225
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 89
          verb: create
        requestCount: 89
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 48
          verb: list
        - requestCount: 24
          verb: patch
        requestCount: 72
        userAgent: trident-operator/v0.0.0
        username: system:serviceaccount:openshift-operators:trident-operator
      nodeName: 10.21.102.11
      requestCount: 5029
    - byUser:
      - byVerb:
        - requestCount: 1603
          verb: list
        requestCount: 1603
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 170
          verb: patch
        requestCount: 170
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 131
          verb: create
        requestCount: 131
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 116
          verb: create
        requestCount: 116
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 115
          verb: create
        requestCount: 115
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 114
          verb: create
        requestCount: 114
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 114
          verb: create
        requestCount: 114
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 112
          verb: create
        requestCount: 112
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 22
          verb: create
        - requestCount: 22
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 52
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      nodeName: 10.21.102.12
      requestCount: 2535
    - byUser:
      - byVerb:
        - requestCount: 2803
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 2810
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1812
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 1828
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1453
          verb: list
        requestCount: 1453
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 574
          verb: get
        requestCount: 574
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 340
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 349
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 250
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 268
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 188
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 204
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 188
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 203
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 135
          verb: create
        requestCount: 135
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 131
          verb: create
        requestCount: 131
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-f6gz2
      nodeName: 10.21.102.9
      requestCount: 7955
    requestCount: 15519
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1643
          verb: list
        requestCount: 1643
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 342
          verb: create
        - requestCount: 804
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 1154
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 95
          verb: delete
        - requestCount: 429
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 550
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 374
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 392
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 323
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 331
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 290
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 314
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 270
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 278
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 48
          verb: delete
        - requestCount: 132
          verb: get
        requestCount: 180
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 72
          verb: create
        requestCount: 72
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 48
          verb: list
        - requestCount: 24
          verb: patch
        requestCount: 72
        userAgent: trident-operator/v0.0.0
        username: system:serviceaccount:openshift-operators:trident-operator
      nodeName: 10.21.102.11
      requestCount: 4986
    - byUser:
      - byVerb:
        - requestCount: 1624
          verb: list
        requestCount: 1624
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: create
        requestCount: 144
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 120
          verb: create
        requestCount: 120
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 111
          verb: create
        requestCount: 111
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 111
          verb: create
        requestCount: 111
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 104
          verb: create
        requestCount: 104
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 103
          verb: create
        requestCount: 103
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 13
          verb: create
        - requestCount: 13
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 34
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 2539
    - byUser:
      - byVerb:
        - requestCount: 2840
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 2849
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1848
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 1863
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1624
          verb: list
        requestCount: 1624
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 577
          verb: get
        requestCount: 577
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 346
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 354
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 256
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 272
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 190
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 207
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 189
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 205
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 176
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 138
          verb: create
        requestCount: 138
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-w959c
      nodeName: 10.21.102.9
      requestCount: 8265
    requestCount: 15790
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1604
          verb: list
        requestCount: 1604
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 304
          verb: create
        - requestCount: 768
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 1080
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 100
          verb: delete
        - requestCount: 444
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 568
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 374
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 391
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 314
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 338
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 319
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 327
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 273
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 282
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 60
          verb: delete
        - requestCount: 165
          verb: get
        requestCount: 225
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 91
          verb: create
        requestCount: 91
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 48
          verb: list
        - requestCount: 24
          verb: patch
        requestCount: 72
        userAgent: trident-operator/v0.0.0
        username: system:serviceaccount:openshift-operators:trident-operator
      nodeName: 10.21.102.11
      requestCount: 4978
    - byUser:
      - byVerb:
        - requestCount: 1588
          verb: list
        requestCount: 1588
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 142
          verb: create
        requestCount: 142
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 112
          verb: create
        requestCount: 112
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 112
          verb: create
        requestCount: 112
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 110
          verb: create
        requestCount: 110
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 102
          verb: create
        requestCount: 102
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 94
          verb: create
        requestCount: 94
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 13
          verb: create
        - requestCount: 13
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 34
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 2483
    - byUser:
      - byVerb:
        - requestCount: 2781
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 2788
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1888
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 1906
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1535
          verb: list
        requestCount: 1535
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 612
          verb: get
        requestCount: 612
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 356
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 364
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 258
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 274
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 191
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 207
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 188
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 204
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 166
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 174
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 141
          verb: create
        requestCount: 141
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      nodeName: 10.21.102.9
      requestCount: 8205
    requestCount: 15666
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1742
          verb: list
        requestCount: 1742
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 339
          verb: create
        - requestCount: 798
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 1145
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 98
          verb: delete
        - requestCount: 438
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 559
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 377
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 392
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 322
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 330
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 306
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 330
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 270
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 278
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 48
          verb: delete
        - requestCount: 132
          verb: get
        requestCount: 180
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 102
          verb: create
        requestCount: 102
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 48
          verb: list
        - requestCount: 24
          verb: patch
        requestCount: 72
        userAgent: trident-operator/v0.0.0
        username: system:serviceaccount:openshift-operators:trident-operator
      nodeName: 10.21.102.11
      requestCount: 5130
    - byUser:
      - byVerb:
        - requestCount: 1806
          verb: list
        requestCount: 1806
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 170
          verb: patch
        requestCount: 170
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 126
          verb: create
        requestCount: 126
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 123
          verb: create
        requestCount: 123
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 118
          verb: create
        requestCount: 118
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 106
          verb: create
        requestCount: 106
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 94
          verb: create
        requestCount: 94
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 12
          verb: create
        - requestCount: 12
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 32
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 2703
    - byUser:
      - byVerb:
        - requestCount: 2861
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 2870
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1902
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 1920
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1693
          verb: list
        requestCount: 1693
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 574
          verb: get
        requestCount: 574
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 344
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 353
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 253
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 270
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 189
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 206
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 186
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 201
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 140
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 145
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 131
          verb: create
        requestCount: 131
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-tssl4
      nodeName: 10.21.102.9
      requestCount: 8363
    requestCount: 16196
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1418
          verb: list
        requestCount: 1418
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 333
          verb: create
        - requestCount: 826
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 1168
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 95
          verb: delete
        - requestCount: 429
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 548
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 375
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 391
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 319
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 328
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 302
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 326
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 272
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 280
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 154
          verb: get
        requestCount: 210
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 98
          verb: create
        requestCount: 98
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 48
          verb: list
        - requestCount: 24
          verb: patch
        requestCount: 72
        userAgent: trident-operator/v0.0.0
        username: system:serviceaccount:openshift-operators:trident-operator
      nodeName: 10.21.102.11
      requestCount: 4839
    - byUser:
      - byVerb:
        - requestCount: 1421
          verb: list
        requestCount: 1421
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 131
          verb: create
        requestCount: 131
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 124
          verb: create
        requestCount: 124
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 122
          verb: create
        requestCount: 122
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 116
          verb: create
        requestCount: 116
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 116
          verb: create
        requestCount: 116
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 111
          verb: create
        requestCount: 111
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 18
          verb: create
        - requestCount: 18
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 45
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      nodeName: 10.21.102.12
      requestCount: 2373
    - byUser:
      - byVerb:
        - requestCount: 2857
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 2865
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1814
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 1828
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1471
          verb: list
        requestCount: 1471
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 589
          verb: get
        requestCount: 589
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 332
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 339
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 260
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 275
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 189
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 206
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 190
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 204
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 140
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 147
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 140
          verb: create
        requestCount: 140
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      nodeName: 10.21.102.9
      requestCount: 8064
    requestCount: 15276
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1563
          verb: list
        requestCount: 1563
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 350
          verb: create
        - requestCount: 860
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 1217
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 95
          verb: delete
        - requestCount: 429
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 546
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 374
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 390
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 320
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 328
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 274
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 297
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 272
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 279
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 52
          verb: delete
        - requestCount: 143
          verb: get
        requestCount: 195
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 79
          verb: create
        requestCount: 79
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 48
          verb: list
        - requestCount: 24
          verb: patch
        requestCount: 72
        userAgent: trident-operator/v0.0.0
        username: system:serviceaccount:openshift-operators:trident-operator
      nodeName: 10.21.102.11
      requestCount: 4966
    - byUser:
      - byVerb:
        - requestCount: 1637
          verb: list
        requestCount: 1637
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 175
          verb: patch
        requestCount: 175
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: create
        requestCount: 144
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 113
          verb: create
        requestCount: 113
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 109
          verb: create
        requestCount: 109
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 107
          verb: create
        requestCount: 107
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 105
          verb: create
        requestCount: 105
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 102
          verb: create
        requestCount: 102
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 11
          verb: create
        - requestCount: 11
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 30
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 2531
    - byUser:
      - byVerb:
        - requestCount: 2839
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 2846
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1766
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 1782
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1546
          verb: list
        requestCount: 1546
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 600
          verb: get
        requestCount: 600
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 334
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 343
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 252
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 268
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 189
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 205
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 188
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 204
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 166
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 174
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 137
          verb: create
        requestCount: 137
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-w959c
      nodeName: 10.21.102.9
      requestCount: 8105
    requestCount: 15602
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1515
          verb: list
        requestCount: 1515
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 316
          verb: create
        - requestCount: 752
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 1077
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 98
          verb: delete
        - requestCount: 438
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 560
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 384
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 400
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 318
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 344
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 323
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 331
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 271
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 280
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 60
          verb: delete
        - requestCount: 165
          verb: get
        requestCount: 225
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 94
          verb: create
        requestCount: 94
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 48
          verb: list
        - requestCount: 24
          verb: patch
        requestCount: 72
        userAgent: trident-operator/v0.0.0
        username: system:serviceaccount:openshift-operators:trident-operator
      nodeName: 10.21.102.11
      requestCount: 4898
    - byUser:
      - byVerb:
        - requestCount: 1470
          verb: list
        requestCount: 1470
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 170
          verb: patch
        requestCount: 170
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 141
          verb: create
        requestCount: 141
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 116
          verb: create
        requestCount: 116
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 112
          verb: create
        requestCount: 112
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 110
          verb: create
        requestCount: 110
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 103
          verb: create
        requestCount: 103
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 100
          verb: create
        requestCount: 100
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 18
          verb: create
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 44
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 2373
    - byUser:
      - byVerb:
        - requestCount: 2832
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 2840
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1830
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 1845
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1493
          verb: list
        requestCount: 1493
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 574
          verb: get
        requestCount: 574
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 362
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 259
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 275
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 189
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 205
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 189
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 205
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 176
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 126
          verb: create
        requestCount: 126
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      nodeName: 10.21.102.9
      requestCount: 8101
    requestCount: 15372
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1323
          verb: list
        requestCount: 1323
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 330
          verb: create
        - requestCount: 820
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 1157
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 98
          verb: delete
        - requestCount: 441
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 563
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 380
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 396
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 327
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 334
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 300
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 322
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 276
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 284
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 154
          verb: get
        requestCount: 210
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 99
          verb: create
        requestCount: 99
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 48
          verb: list
        - requestCount: 24
          verb: patch
        requestCount: 72
        userAgent: trident-operator/v0.0.0
        username: system:serviceaccount:openshift-operators:trident-operator
      nodeName: 10.21.102.11
      requestCount: 4760
    - byUser:
      - byVerb:
        - requestCount: 1278
          verb: list
        requestCount: 1278
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 127
          verb: create
        requestCount: 127
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 120
          verb: create
        requestCount: 120
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 120
          verb: create
        requestCount: 120
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 117
          verb: create
        requestCount: 117
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 115
          verb: create
        requestCount: 115
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 93
          verb: create
        requestCount: 93
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 15
          verb: create
        - requestCount: 15
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 38
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 2196
    - byUser:
      - byVerb:
        - requestCount: 2788
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 2797
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 2202
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 2216
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1369
          verb: list
        requestCount: 1369
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 584
          verb: get
        requestCount: 584
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 342
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 349
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 261
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 277
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 189
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 205
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 187
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 204
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 177
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 136
          verb: create
        requestCount: 136
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-w959c
      nodeName: 10.21.102.9
      requestCount: 8314
    requestCount: 15270
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1477
          verb: list
        requestCount: 1477
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 305
          verb: create
        - requestCount: 730
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 1044
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 97
          verb: delete
        - requestCount: 435
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 557
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 378
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 395
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 322
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 330
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 298
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 324
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 271
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 279
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 60
          verb: delete
        - requestCount: 165
          verb: get
        requestCount: 225
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 99
          verb: create
        requestCount: 99
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 48
          verb: list
        - requestCount: 24
          verb: patch
        requestCount: 72
        userAgent: trident-operator/v0.0.0
        username: system:serviceaccount:openshift-operators:trident-operator
      nodeName: 10.21.102.11
      requestCount: 4802
    - byUser:
      - byVerb:
        - requestCount: 1419
          verb: list
        requestCount: 1419
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 170
          verb: patch
        requestCount: 170
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 131
          verb: create
        requestCount: 131
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 123
          verb: create
        requestCount: 123
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 122
          verb: create
        requestCount: 122
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 115
          verb: create
        requestCount: 115
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 113
          verb: create
        requestCount: 113
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 109
          verb: create
        requestCount: 109
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 19
          verb: create
        - requestCount: 19
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 45
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 2357
    - byUser:
      - byVerb:
        - requestCount: 2797
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 2806
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1818
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 1835
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1410
          verb: list
        requestCount: 1410
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 605
          verb: get
        requestCount: 605
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 343
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 351
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 248
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 263
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 194
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 210
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 190
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 207
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 178
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 138
          verb: create
        requestCount: 138
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      nodeName: 10.21.102.9
      requestCount: 8003
    requestCount: 15162
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1662
          verb: list
        requestCount: 1662
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 329
          verb: create
        - requestCount: 818
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 1155
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 95
          verb: delete
        - requestCount: 429
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 546
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 377
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 391
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 322
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 329
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 300
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 324
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 268
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 276
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 48
          verb: delete
        - requestCount: 132
          verb: get
        requestCount: 180
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 75
          verb: create
        requestCount: 75
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 48
          verb: list
        - requestCount: 24
          verb: patch
        requestCount: 72
        userAgent: trident-operator/v0.0.0
        username: system:serviceaccount:openshift-operators:trident-operator
      nodeName: 10.21.102.11
      requestCount: 5010
    - byUser:
      - byVerb:
        - requestCount: 1660
          verb: list
        requestCount: 1660
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 138
          verb: create
        requestCount: 138
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 117
          verb: create
        requestCount: 117
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 112
          verb: create
        requestCount: 112
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 109
          verb: create
        requestCount: 109
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 107
          verb: create
        requestCount: 107
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 104
          verb: create
        requestCount: 104
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 14
          verb: create
        - requestCount: 14
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 37
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      nodeName: 10.21.102.12
      requestCount: 2572
    - byUser:
      - byVerb:
        - requestCount: 2835
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 2842
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1880
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 1895
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1722
          verb: list
        requestCount: 1722
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 574
          verb: get
        requestCount: 574
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 338
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 346
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 255
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 272
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 190
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 206
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 190
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 205
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 176
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 135
          verb: create
        requestCount: 135
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      nodeName: 10.21.102.9
      requestCount: 8373
    requestCount: 15955
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1730
          verb: list
        requestCount: 1730
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 319
          verb: create
        - requestCount: 798
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 1125
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 92
          verb: delete
        - requestCount: 420
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 538
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 384
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 399
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 322
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 331
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 274
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 298
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 266
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 274
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 154
          verb: get
        requestCount: 210
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 89
          verb: create
        requestCount: 89
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 88
          verb: create
        requestCount: 88
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-5wphs
      nodeName: 10.21.102.11
      requestCount: 5082
    - byUser:
      - byVerb:
        - requestCount: 1701
          verb: list
        requestCount: 1701
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 177
          verb: patch
        requestCount: 177
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 147
          verb: create
        requestCount: 147
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 122
          verb: create
        requestCount: 122
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 113
          verb: create
        requestCount: 113
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 110
          verb: create
        requestCount: 110
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 108
          verb: create
        requestCount: 108
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 105
          verb: create
        requestCount: 105
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 10
          verb: create
        - requestCount: 10
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 25
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 2615
    - byUser:
      - byVerb:
        - requestCount: 2829
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 2837
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1866
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 1884
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1771
          verb: list
        requestCount: 1771
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 581
          verb: get
        requestCount: 581
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 328
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 336
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 247
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 264
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 186
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 203
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 186
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 202
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 177
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 130
          verb: create
        requestCount: 130
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-w959c
      nodeName: 10.21.102.9
      requestCount: 8385
    requestCount: 16082
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1211
          verb: list
        requestCount: 1211
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 336
          verb: create
        - requestCount: 792
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 1136
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 97
          verb: delete
        - requestCount: 435
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 556
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 378
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 394
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 319
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 326
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 276
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 300
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 272
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 280
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 60
          verb: delete
        - requestCount: 165
          verb: get
        requestCount: 225
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 129
          verb: create
        requestCount: 129
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-5wphs
      - byVerb:
        - requestCount: 96
          verb: create
        requestCount: 96
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      nodeName: 10.21.102.11
      requestCount: 4653
    - byUser:
      - byVerb:
        - requestCount: 1098
          verb: list
        requestCount: 1098
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 170
          verb: patch
        requestCount: 170
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 126
          verb: create
        requestCount: 126
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 119
          verb: create
        requestCount: 119
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 117
          verb: create
        requestCount: 117
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 114
          verb: create
        requestCount: 114
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 105
          verb: create
        requestCount: 105
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 93
          verb: create
        requestCount: 93
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 1958
    - byUser:
      - byVerb:
        - requestCount: 2869
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 2877
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1922
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 1936
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1152
          verb: list
        requestCount: 1152
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 608
          verb: get
        requestCount: 608
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 338
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 346
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 253
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 269
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 189
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 205
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 186
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 201
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 176
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 145
          verb: create
        requestCount: 145
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      nodeName: 10.21.102.9
      requestCount: 7915
    requestCount: 14526
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 1721
          verb: list
        requestCount: 1722
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 326
          verb: create
        - requestCount: 812
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 1146
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 98
          verb: delete
        - requestCount: 439
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 562
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 376
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 392
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 323
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 331
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 296
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 321
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 272
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 280
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 52
          verb: delete
        - requestCount: 141
          verb: get
        requestCount: 193
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 129
          verb: create
        requestCount: 129
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-5wphs
      - byVerb:
        - requestCount: 109
          verb: create
        requestCount: 109
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      nodeName: 10.21.102.11
      requestCount: 5191
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 1782
          verb: list
        requestCount: 1783
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 129
          verb: create
        requestCount: 129
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 122
          verb: create
        requestCount: 122
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 120
          verb: create
        requestCount: 120
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 117
          verb: create
        requestCount: 117
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 105
          verb: create
        requestCount: 105
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 2695
    - byUser:
      - byVerb:
        - requestCount: 2849
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 2858
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 2004
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 2020
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 1687
          verb: list
        requestCount: 1689
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 574
          verb: get
        requestCount: 574
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 345
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 354
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 264
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 278
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 192
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 207
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 189
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 206
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 140
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 147
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 137
          verb: create
        requestCount: 137
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      nodeName: 10.21.102.9
      requestCount: 8471
    requestCount: 16357
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 71
          verb: list
        requestCount: 71
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: delete
        - requestCount: 34
          verb: get
        requestCount: 42
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 25
          verb: get
        requestCount: 25
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 24
          verb: get
        requestCount: 24
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 22
          verb: get
        requestCount: 22
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 5
          verb: create
        - requestCount: 10
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 16
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 12
          verb: create
        requestCount: 12
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-5wphs
      - byVerb:
        - requestCount: 6
          verb: create
        requestCount: 6
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 2
          verb: patch
        requestCount: 6
        userAgent: trident-operator/v0.0.0
        username: system:serviceaccount:openshift-operators:trident-operator
      nodeName: 10.21.102.11
      requestCount: 258
    - byUser:
      - byVerb:
        - requestCount: 222
          verb: get
        requestCount: 222
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 111
          verb: list
        requestCount: 111
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 96
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 97
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 41
          verb: get
        requestCount: 41
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 31
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 32
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 29
          verb: get
        - requestCount: 2
          verb: watch
        requestCount: 31
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 21
          verb: create
        requestCount: 21
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-sqqdl
      - byVerb:
        - requestCount: 21
          verb: create
        requestCount: 21
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-w959c
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 2
          verb: watch
        requestCount: 19
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 16
          verb: create
        requestCount: 16
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      nodeName: 10.21.102.9
      requestCount: 857
    requestCount: 1115
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1523
          verb: list
        requestCount: 1523
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 293
          verb: create
        - requestCount: 738
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 1039
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 101
          verb: delete
        - requestCount: 447
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 572
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 379
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 394
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 337
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 319
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 326
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 273
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 281
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 52
          verb: delete
        - requestCount: 143
          verb: get
        requestCount: 195
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 103
          verb: create
        requestCount: 103
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 48
          verb: list
        - requestCount: 24
          verb: patch
        requestCount: 72
        userAgent: trident-operator/v0.0.0
        username: system:serviceaccount:openshift-operators:trident-operator
      nodeName: 10.21.102.11
      requestCount: 4842
    - byUser:
      - byVerb:
        - requestCount: 1514
          verb: list
        requestCount: 1514
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 131
          verb: create
        requestCount: 131
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 122
          verb: create
        requestCount: 122
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 117
          verb: create
        requestCount: 117
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 112
          verb: create
        requestCount: 112
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 14
          verb: create
        - requestCount: 14
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 37
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      nodeName: 10.21.102.12
      requestCount: 2350
    - byUser:
      - byVerb:
        - requestCount: 2812
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 2820
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1922
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 1939
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1538
          verb: list
        requestCount: 1538
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 574
          verb: get
        requestCount: 574
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 369
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 259
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 276
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 190
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 206
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 185
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 201
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 138
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 145
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 143
          verb: create
        requestCount: 143
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      nodeName: 10.21.102.9
      requestCount: 8211
    requestCount: 15403
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1678
          verb: list
        requestCount: 1678
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 355
          verb: create
        - requestCount: 870
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 1233
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 99
          verb: delete
        - requestCount: 441
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 565
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 380
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 396
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 318
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 326
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 300
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 323
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 268
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 275
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 154
          verb: get
        requestCount: 210
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 86
          verb: create
        requestCount: 86
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 48
          verb: list
        - requestCount: 24
          verb: patch
        requestCount: 72
        userAgent: trident-operator/v0.0.0
        username: system:serviceaccount:openshift-operators:trident-operator
      nodeName: 10.21.102.11
      requestCount: 5164
    - byUser:
      - byVerb:
        - requestCount: 1716
          verb: list
        requestCount: 1716
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 178
          verb: patch
        requestCount: 178
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 138
          verb: create
        requestCount: 138
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 110
          verb: create
        requestCount: 110
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 109
          verb: create
        requestCount: 109
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 106
          verb: create
        requestCount: 106
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 103
          verb: create
        requestCount: 103
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 18
          verb: create
        - requestCount: 18
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 43
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-hub-operator-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      nodeName: 10.21.102.12
      requestCount: 2519
    - byUser:
      - byVerb:
        - requestCount: 2814
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 2823
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1984
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 2000
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1613
          verb: list
        requestCount: 1613
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 574
          verb: get
        requestCount: 574
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 332
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 340
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 250
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 265
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 189
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 206
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 186
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 203
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 166
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 173
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 138
          verb: create
        requestCount: 138
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-w959c
      nodeName: 10.21.102.9
      requestCount: 8335
    requestCount: 16018
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1575
          verb: list
        requestCount: 1575
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 275
          verb: create
        - requestCount: 670
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 953
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 94
          verb: delete
        - requestCount: 426
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 544
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 376
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 392
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 320
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 329
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 296
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 321
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 272
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 279
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 154
          verb: get
        requestCount: 210
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 90
          verb: create
        requestCount: 90
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 48
          verb: list
        - requestCount: 24
          verb: patch
        requestCount: 72
        userAgent: trident-operator/v0.0.0
        username: system:serviceaccount:openshift-operators:trident-operator
      nodeName: 10.21.102.11
      requestCount: 4765
    - byUser:
      - byVerb:
        - requestCount: 1527
          verb: list
        requestCount: 1527
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 172
          verb: patch
        requestCount: 172
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 150
          verb: create
        requestCount: 150
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 118
          verb: create
        requestCount: 118
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 112
          verb: create
        requestCount: 112
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 110
          verb: create
        requestCount: 110
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 109
          verb: create
        requestCount: 109
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 13
          verb: create
        - requestCount: 13
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 33
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-hub-operator-sa
      nodeName: 10.21.102.12
      requestCount: 2348
    - byUser:
      - byVerb:
        - requestCount: 2855
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 2863
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1860
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 1876
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1538
          verb: list
        requestCount: 1538
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 615
          verb: get
        requestCount: 615
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 338
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 346
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 256
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 271
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 189
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 205
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 190
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 204
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 177
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 135
          verb: create
        requestCount: 135
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      nodeName: 10.21.102.9
      requestCount: 8230
    requestCount: 15343
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1315
          verb: list
        requestCount: 1315
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 308
          verb: create
        - requestCount: 776
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 1093
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 95
          verb: delete
        - requestCount: 429
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 547
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 377
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 393
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 320
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 328
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 284
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 308
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 269
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 277
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 52
          verb: delete
        - requestCount: 143
          verb: get
        requestCount: 195
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 100
          verb: create
        requestCount: 100
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 48
          verb: list
        - requestCount: 24
          verb: patch
        requestCount: 72
        userAgent: trident-operator/v0.0.0
        username: system:serviceaccount:openshift-operators:trident-operator
      nodeName: 10.21.102.11
      requestCount: 4628
    - byUser:
      - byVerb:
        - requestCount: 1265
          verb: list
        requestCount: 1265
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 127
          verb: create
        requestCount: 127
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 126
          verb: create
        requestCount: 126
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 118
          verb: create
        requestCount: 118
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 118
          verb: create
        requestCount: 118
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 109
          verb: create
        requestCount: 109
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 16
          verb: create
        - requestCount: 16
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 41
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 10
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-hub-operator-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 2101
    - byUser:
      - byVerb:
        - requestCount: 2862
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 2870
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1898
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 1915
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1327
          verb: list
        requestCount: 1327
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 574
          verb: get
        requestCount: 574
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 330
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 339
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 251
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 267
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 188
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 204
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 186
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 201
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 141
          verb: create
        requestCount: 141
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-f6gz2
      - byVerb:
        - requestCount: 138
          verb: create
        requestCount: 138
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-w959c
      nodeName: 10.21.102.9
      requestCount: 7976
    requestCount: 14705
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 1835
          verb: list
        requestCount: 1839
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 354
          verb: create
        - requestCount: 868
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 1230
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 98
          verb: delete
        - requestCount: 441
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 564
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 377
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 394
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 323
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 331
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 302
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 326
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 276
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 285
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 154
          verb: get
        requestCount: 210
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 108
          verb: create
        requestCount: 108
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 48
          verb: list
        - requestCount: 24
          verb: patch
        requestCount: 72
        userAgent: trident-operator/v0.0.0
        username: system:serviceaccount:openshift-operators:trident-operator
      nodeName: 10.21.102.11
      requestCount: 5359
    - byUser:
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 1848
          verb: list
        requestCount: 1850
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 170
          verb: patch
        requestCount: 170
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 132
          verb: create
        requestCount: 132
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 118
          verb: create
        requestCount: 118
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 117
          verb: create
        requestCount: 117
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 111
          verb: create
        requestCount: 111
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 57
          verb: create
        requestCount: 57
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 10
          verb: create
        - requestCount: 10
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 27
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-hub-operator-sa
      nodeName: 10.21.102.12
      requestCount: 2710
    - byUser:
      - byVerb:
        - requestCount: 2863
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 2870
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1812
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 1827
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 1753
          verb: list
        requestCount: 1757
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 574
          verb: get
        requestCount: 574
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 362
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 261
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 278
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 192
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 207
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 190
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 206
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 140
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 147
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 147
          verb: create
        requestCount: 147
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      nodeName: 10.21.102.9
      requestCount: 8375
    requestCount: 16444
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1613
          verb: list
        requestCount: 1613
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 343
          verb: create
        - requestCount: 806
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 1157
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 96
          verb: delete
        - requestCount: 433
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 553
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 375
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 390
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 320
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 328
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 296
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 319
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 274
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 281
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 60
          verb: delete
        - requestCount: 165
          verb: get
        requestCount: 225
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 72
          verb: create
        requestCount: 72
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 48
          verb: list
        - requestCount: 24
          verb: patch
        requestCount: 72
        userAgent: trident-operator/v0.0.0
        username: system:serviceaccount:openshift-operators:trident-operator
      nodeName: 10.21.102.11
      requestCount: 5010
    - byUser:
      - byVerb:
        - requestCount: 1501
          verb: list
        requestCount: 1501
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 170
          verb: patch
        requestCount: 170
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 132
          verb: create
        requestCount: 132
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 111
          verb: create
        requestCount: 111
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 110
          verb: create
        requestCount: 110
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 109
          verb: create
        requestCount: 109
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 108
          verb: create
        requestCount: 108
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 107
          verb: create
        requestCount: 107
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 16
          verb: create
        - requestCount: 16
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 39
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.29.11+148a389
        username: system:apiserver
      nodeName: 10.21.102.12
      requestCount: 2394
    - byUser:
      - byVerb:
        - requestCount: 2867
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 2875
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1860
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 1876
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1566
          verb: list
        requestCount: 1566
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 615
          verb: get
        requestCount: 615
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 355
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 362
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 260
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 275
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 193
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 207
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 187
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 201
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 178
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 135
          verb: create
        requestCount: 135
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-f6gz2
      nodeName: 10.21.102.9
      requestCount: 8290
    requestCount: 15694
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1562
          verb: list
        requestCount: 1562
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 246
          verb: create
        - requestCount: 652
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 905
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 93
          verb: delete
        - requestCount: 413
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 529
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 359
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 374
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 282
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 304
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 290
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 297
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 246
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 253
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 52
          verb: delete
        - requestCount: 143
          verb: get
        requestCount: 195
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 80
          verb: create
        requestCount: 80
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 44
          verb: list
        - requestCount: 22
          verb: patch
        requestCount: 66
        userAgent: trident-operator/v0.0.0
        username: system:serviceaccount:openshift-operators:trident-operator
      nodeName: 10.21.102.11
      requestCount: 4565
    - byUser:
      - byVerb:
        - requestCount: 1663
          verb: list
        requestCount: 1663
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 146
          verb: create
        requestCount: 146
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 118
          verb: create
        requestCount: 118
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 108
          verb: create
        requestCount: 108
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 104
          verb: create
        requestCount: 104
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 103
          verb: create
        requestCount: 103
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 102
          verb: create
        requestCount: 102
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 12
          verb: create
        - requestCount: 12
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 33
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-hub-operator-sa
      nodeName: 10.21.102.12
      requestCount: 2565
    - byUser:
      - byVerb:
        - requestCount: 2784
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 2791
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1952
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 1969
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1721
          verb: list
        requestCount: 1721
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 574
          verb: get
        requestCount: 574
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 344
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 352
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 256
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 272
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 188
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 204
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 186
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 202
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 140
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 147
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 130
          verb: create
        requestCount: 130
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-w959c
      nodeName: 10.21.102.9
      requestCount: 8362
    requestCount: 15492
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1791
          verb: list
        requestCount: 1791
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 341
          verb: create
        - requestCount: 801
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 1151
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 94
          verb: delete
        - requestCount: 426
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 543
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 375
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 392
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 320
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 330
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 290
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 316
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 268
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 276
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 154
          verb: get
        requestCount: 210
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 90
          verb: create
        requestCount: 90
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 48
          verb: list
        - requestCount: 24
          verb: patch
        requestCount: 72
        userAgent: trident-operator/v0.0.0
        username: system:serviceaccount:openshift-operators:trident-operator
      nodeName: 10.21.102.11
      requestCount: 5171
    - byUser:
      - byVerb:
        - requestCount: 1895
          verb: list
        requestCount: 1895
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 170
          verb: patch
        requestCount: 170
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 133
          verb: create
        requestCount: 133
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 115
          verb: create
        requestCount: 115
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 114
          verb: create
        requestCount: 114
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 110
          verb: create
        requestCount: 110
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 100
          verb: create
        requestCount: 100
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 99
          verb: create
        requestCount: 99
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 19
          verb: create
        - requestCount: 19
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 45
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      nodeName: 10.21.102.12
      requestCount: 2788
    - byUser:
      - byVerb:
        - requestCount: 2819
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 2827
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1908
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 1924
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1795
          verb: list
        requestCount: 1795
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 574
          verb: get
        requestCount: 574
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 336
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 344
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 255
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 270
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 189
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 205
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 187
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 204
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 140
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 147
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 132
          verb: create
        requestCount: 132
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-w959c
      nodeName: 10.21.102.9
      requestCount: 8422
    requestCount: 16381
  requestCount: 346420
