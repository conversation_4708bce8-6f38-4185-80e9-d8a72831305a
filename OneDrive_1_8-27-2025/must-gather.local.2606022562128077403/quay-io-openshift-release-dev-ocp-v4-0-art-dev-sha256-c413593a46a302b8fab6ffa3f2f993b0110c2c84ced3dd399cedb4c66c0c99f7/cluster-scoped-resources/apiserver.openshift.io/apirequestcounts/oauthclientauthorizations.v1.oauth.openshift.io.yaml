---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2023-12-19T16:51:51Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2023-12-19T16:51:51Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:06:25Z"
  name: oauthclientauthorizations.v1.oauth.openshift.io
  resourceVersion: "*********"
  uid: f48f7125-4ecc-4cca-af0e-e4e04eec107f
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: get
        requestCount: 2
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 4
    requestCount: 4
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 1
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 15
    requestCount: 16
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 1
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 15
    requestCount: 16
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 1
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 15
    requestCount: 16
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 14
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 14
    requestCount: 14
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 12
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 1
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 13
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 12
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2
          verb: get
        requestCount: 2
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 2
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 13
    requestCount: 15
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 12
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 12
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 12
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 13
    requestCount: 13
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 5
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 4
          verb: get
        requestCount: 4
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 16
    requestCount: 21
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 1
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 17
    requestCount: 18
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: get
        requestCount: 2
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 4
    requestCount: 4
  - requestCount: 0
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 13
    requestCount: 13
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 3
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 13
    requestCount: 16
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 1
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 17
    requestCount: 18
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 3
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 17
    requestCount: 20
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 12
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 13
    requestCount: 13
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.21.102.9
      requestCount: 12
    requestCount: 12
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.11
      requestCount: 1
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.21.102.9
      requestCount: 13
    requestCount: 14
  requestCount: 324
