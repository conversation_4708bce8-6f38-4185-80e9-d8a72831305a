---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-07-15T12:47:39Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-07-15T12:47:39Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:06:45Z"
  name: openstackdataplanenodesets.v1beta1.dataplane.openstack.org
  resourceVersion: "770116095"
  uid: 8816c86a-30db-4b7d-9f67-983968f09dc7
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    requestCount: 0
  last24h:
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - nodeName: 10.21.102.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 1
    requestCount: 1
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - nodeName: 10.21.102.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 1
    requestCount: 1
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - nodeName: 10.21.102.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 1
    requestCount: 1
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - nodeName: 10.21.102.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 1
    requestCount: 1
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - nodeName: 10.21.102.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 1
    requestCount: 1
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - nodeName: 10.21.102.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 1
    requestCount: 1
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - nodeName: 10.21.102.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 1
    requestCount: 1
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    requestCount: 0
  - requestCount: 0
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - nodeName: 10.21.102.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 1
    requestCount: 1
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - nodeName: 10.21.102.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 1
    requestCount: 1
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - nodeName: 10.21.102.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 1
    requestCount: 1
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - nodeName: 10.21.102.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    - nodeName: 10.21.102.12
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 1
    requestCount: 1
  requestCount: 11
