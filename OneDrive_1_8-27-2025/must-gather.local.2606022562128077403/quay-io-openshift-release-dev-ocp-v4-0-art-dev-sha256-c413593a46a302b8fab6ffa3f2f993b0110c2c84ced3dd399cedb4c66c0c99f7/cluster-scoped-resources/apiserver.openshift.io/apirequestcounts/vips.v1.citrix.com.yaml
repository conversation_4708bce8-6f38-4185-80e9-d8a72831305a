---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2024-02-04T21:19:06Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2024-02-04T21:19:06Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:06:32Z"
  name: vips.v1.citrix.com
  resourceVersion: "*********"
  uid: 82e3a6c2-2c13-4b41-b9b8-************
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 37
    - byUser:
      - byVerb:
        - requestCount: 13
          verb: list
        requestCount: 13
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: ************
      requestCount: 13
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: list
        requestCount: 43
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 47
    requestCount: 97
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 831
          verb: list
        requestCount: 831
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 19
          verb: watch
        requestCount: 19
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 850
    - byUser:
      - byVerb:
        - requestCount: 771
          verb: list
        requestCount: 771
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: ************
      requestCount: 795
    - byUser:
      - byVerb:
        - requestCount: 766
          verb: list
        requestCount: 766
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 805
    requestCount: 2450
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 878
          verb: list
        requestCount: 878
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 903
    - byUser:
      - byVerb:
        - requestCount: 838
          verb: list
        requestCount: 838
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 13
          verb: watch
        requestCount: 13
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: ************
      requestCount: 859
    - byUser:
      - byVerb:
        - requestCount: 880
          verb: list
        requestCount: 880
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 916
    requestCount: 2678
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 878
          verb: list
        requestCount: 878
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 19
          verb: watch
        requestCount: 19
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 897
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: ************
      requestCount: 855
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 873
    requestCount: 2625
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1005
          verb: list
        requestCount: 1005
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 1023
    - byUser:
      - byVerb:
        - requestCount: 996
          verb: list
        requestCount: 996
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: ************
      requestCount: 1019
    - byUser:
      - byVerb:
        - requestCount: 968
          verb: list
        requestCount: 968
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 7
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 14
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1011
    requestCount: 3053
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 918
          verb: list
        requestCount: 918
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 940
    - byUser:
      - byVerb:
        - requestCount: 976
          verb: list
        requestCount: 976
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: ************
      requestCount: 1002
    - byUser:
      - byVerb:
        - requestCount: 960
          verb: list
        requestCount: 960
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 13
          verb: watch
        requestCount: 13
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 994
    requestCount: 2936
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 983
          verb: list
        requestCount: 983
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 1005
    - byUser:
      - byVerb:
        - requestCount: 968
          verb: list
        requestCount: 968
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 12
          verb: watch
        requestCount: 12
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: ************
      requestCount: 988
    - byUser:
      - byVerb:
        - requestCount: 989
          verb: list
        requestCount: 989
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1029
    requestCount: 3022
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 811
          verb: list
        requestCount: 811
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 825
    - byUser:
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: ************
      requestCount: 849
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: list
        requestCount: 860
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 904
    requestCount: 2578
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 916
          verb: list
        requestCount: 916
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 937
    - byUser:
      - byVerb:
        - requestCount: 837
          verb: list
        requestCount: 837
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: ************
      requestCount: 865
    - byUser:
      - byVerb:
        - requestCount: 876
          verb: list
        requestCount: 876
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 12
          verb: watch
        requestCount: 12
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 908
    requestCount: 2710
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: list
        requestCount: 860
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 878
    - byUser:
      - byVerb:
        - requestCount: 816
          verb: list
        requestCount: 816
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 19
          verb: watch
        requestCount: 19
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: ************
      requestCount: 843
    - byUser:
      - byVerb:
        - requestCount: 845
          verb: list
        requestCount: 845
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 883
    requestCount: 2604
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 861
          verb: list
        requestCount: 861
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 876
    - byUser:
      - byVerb:
        - requestCount: 915
          verb: list
        requestCount: 915
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 13
          verb: watch
        requestCount: 13
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: ************
      requestCount: 937
    - byUser:
      - byVerb:
        - requestCount: 958
          verb: list
        requestCount: 958
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1002
    requestCount: 2815
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 769
          verb: list
        requestCount: 769
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 19
          verb: watch
        requestCount: 19
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 788
    - byUser:
      - byVerb:
        - requestCount: 824
          verb: list
        requestCount: 824
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: ************
      requestCount: 849
    - byUser:
      - byVerb:
        - requestCount: 746
          verb: list
        requestCount: 746
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 784
    requestCount: 2421
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 940
          verb: list
        requestCount: 940
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 27
          verb: watch
        requestCount: 27
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 967
    - byUser:
      - byVerb:
        - requestCount: 897
          verb: list
        requestCount: 897
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 12
          verb: watch
        requestCount: 12
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: ************
      requestCount: 913
    - byUser:
      - byVerb:
        - requestCount: 968
          verb: list
        requestCount: 968
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 3
          verb: watch
        requestCount: 4
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1006
    requestCount: 2886
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1111
          verb: list
        requestCount: 1111
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 1129
    - byUser:
      - byVerb:
        - requestCount: 1076
          verb: list
        requestCount: 1076
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 1094
    - byUser:
      - byVerb:
        - requestCount: 1116
          verb: list
        requestCount: 1116
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1162
    requestCount: 3385
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 837
          verb: list
        requestCount: 837
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 857
    - byUser:
      - byVerb:
        - requestCount: 834
          verb: list
        requestCount: 834
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 849
    - byUser:
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 871
    requestCount: 2577
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 37
    - byUser:
      - byVerb:
        - requestCount: 13
          verb: list
        requestCount: 13
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: ************
      requestCount: 13
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: list
        requestCount: 43
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 47
    requestCount: 97
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 922
          verb: list
        requestCount: 922
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 939
    - byUser:
      - byVerb:
        - requestCount: 910
          verb: list
        requestCount: 910
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: ************
      requestCount: 939
    - byUser:
      - byVerb:
        - requestCount: 955
          verb: list
        requestCount: 955
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 990
    requestCount: 2868
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 753
          verb: list
        requestCount: 753
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 768
    - byUser:
      - byVerb:
        - requestCount: 749
          verb: list
        requestCount: 749
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: ************
      requestCount: 779
    - byUser:
      - byVerb:
        - requestCount: 718
          verb: list
        requestCount: 718
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 754
    requestCount: 2301
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 893
          verb: list
        requestCount: 893
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 917
    - byUser:
      - byVerb:
        - requestCount: 926
          verb: list
        requestCount: 926
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 12
          verb: watch
        requestCount: 12
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: ************
      requestCount: 946
    - byUser:
      - byVerb:
        - requestCount: 961
          verb: list
        requestCount: 961
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 997
    requestCount: 2860
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 858
    - byUser:
      - byVerb:
        - requestCount: 829
          verb: list
        requestCount: 829
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 13
          verb: watch
        requestCount: 13
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: ************
      requestCount: 850
    - byUser:
      - byVerb:
        - requestCount: 876
          verb: list
        requestCount: 876
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 911
    requestCount: 2619
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 872
          verb: list
        requestCount: 872
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 27
          verb: watch
        requestCount: 27
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 899
    - byUser:
      - byVerb:
        - requestCount: 864
          verb: list
        requestCount: 864
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 11
          verb: watch
        requestCount: 11
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: ************
      requestCount: 883
    - byUser:
      - byVerb:
        - requestCount: 905
          verb: list
        requestCount: 905
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 941
    requestCount: 2723
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 924
          verb: list
        requestCount: 924
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 947
    - byUser:
      - byVerb:
        - requestCount: 867
          verb: list
        requestCount: 867
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: ************
      requestCount: 892
    - byUser:
      - byVerb:
        - requestCount: 897
          verb: list
        requestCount: 897
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 931
    requestCount: 2770
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 822
          verb: list
        requestCount: 822
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 844
    - byUser:
      - byVerb:
        - requestCount: 835
          verb: list
        requestCount: 835
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: ************
      requestCount: 853
    - byUser:
      - byVerb:
        - requestCount: 814
          verb: list
        requestCount: 814
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 856
    requestCount: 2553
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 829
          verb: list
        requestCount: 829
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: ************
      requestCount: 846
    - byUser:
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: ************
      requestCount: 850
    - byUser:
      - byVerb:
        - requestCount: 832
          verb: list
        requestCount: 832
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 873
    requestCount: 2569
  requestCount: 60100
