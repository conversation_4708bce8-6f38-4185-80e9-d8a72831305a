---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2024-02-04T21:17:58Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2024-02-04T21:17:58Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:03:41Z"
  name: bots.v1.citrix.com
  resourceVersion: "*********"
  uid: 1724a174-184f-4269-8954-454505f3d1ca
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 836
          verb: list
        requestCount: 836
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 858
    - byUser:
      - byVerb:
        - requestCount: 770
          verb: list
        requestCount: 770
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.12
      requestCount: 787
    - byUser:
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 869
    requestCount: 2514
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 830
          verb: list
        requestCount: 830
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 850
    - byUser:
      - byVerb:
        - requestCount: 772
          verb: list
        requestCount: 772
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 797
    - byUser:
      - byVerb:
        - requestCount: 766
          verb: list
        requestCount: 766
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 802
    requestCount: 2449
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 878
          verb: list
        requestCount: 878
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 899
    - byUser:
      - byVerb:
        - requestCount: 859
          verb: list
        requestCount: 859
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 882
    - byUser:
      - byVerb:
        - requestCount: 879
          verb: list
        requestCount: 879
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 917
    requestCount: 2698
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 878
          verb: list
        requestCount: 878
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 899
    - byUser:
      - byVerb:
        - requestCount: 836
          verb: list
        requestCount: 836
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 862
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 867
    requestCount: 2628
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1005
          verb: list
        requestCount: 1005
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 1023
    - byUser:
      - byVerb:
        - requestCount: 994
          verb: list
        requestCount: 994
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 1021
    - byUser:
      - byVerb:
        - requestCount: 969
          verb: list
        requestCount: 969
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 7
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 14
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1009
    requestCount: 3053
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 918
          verb: list
        requestCount: 918
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 935
    - byUser:
      - byVerb:
        - requestCount: 955
          verb: list
        requestCount: 955
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 984
    - byUser:
      - byVerb:
        - requestCount: 959
          verb: list
        requestCount: 959
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 992
    requestCount: 2911
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 935
          verb: list
        requestCount: 935
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 956
    - byUser:
      - byVerb:
        - requestCount: 976
          verb: list
        requestCount: 976
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 1002
    - byUser:
      - byVerb:
        - requestCount: 989
          verb: list
        requestCount: 989
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1023
    requestCount: 2981
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 812
          verb: list
        requestCount: 812
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 837
    - byUser:
      - byVerb:
        - requestCount: 824
          verb: list
        requestCount: 824
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 849
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: list
        requestCount: 860
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 893
    requestCount: 2579
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 915
          verb: list
        requestCount: 915
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 932
    - byUser:
      - byVerb:
        - requestCount: 857
          verb: list
        requestCount: 857
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 882
    - byUser:
      - byVerb:
        - requestCount: 876
          verb: list
        requestCount: 876
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 19
          verb: watch
        requestCount: 19
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 916
    requestCount: 2730
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: list
        requestCount: 860
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 19
          verb: watch
        requestCount: 19
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 879
    - byUser:
      - byVerb:
        - requestCount: 817
          verb: list
        requestCount: 817
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 840
    - byUser:
      - byVerb:
        - requestCount: 846
          verb: list
        requestCount: 846
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 19
          verb: watch
        requestCount: 19
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 886
    requestCount: 2605
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 913
          verb: list
        requestCount: 913
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 933
    - byUser:
      - byVerb:
        - requestCount: 914
          verb: list
        requestCount: 914
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 942
    - byUser:
      - byVerb:
        - requestCount: 957
          verb: list
        requestCount: 957
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 991
    requestCount: 2866
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 769
          verb: list
        requestCount: 769
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 26
          verb: watch
        requestCount: 26
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 795
    - byUser:
      - byVerb:
        - requestCount: 825
          verb: list
        requestCount: 825
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 847
    - byUser:
      - byVerb:
        - requestCount: 746
          verb: list
        requestCount: 746
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 13
          verb: watch
        requestCount: 13
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 779
    requestCount: 2421
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 940
          verb: list
        requestCount: 940
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 19
          verb: watch
        requestCount: 19
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 959
    - byUser:
      - byVerb:
        - requestCount: 895
          verb: list
        requestCount: 895
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 13
          verb: watch
        requestCount: 13
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 912
    - byUser:
      - byVerb:
        - requestCount: 969
          verb: list
        requestCount: 969
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 3
          verb: watch
        requestCount: 4
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1014
    requestCount: 2885
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1111
          verb: list
        requestCount: 1111
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 1129
    - byUser:
      - byVerb:
        - requestCount: 1087
          verb: list
        requestCount: 1087
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.12
      requestCount: 1102
    - byUser:
      - byVerb:
        - requestCount: 1115
          verb: list
        requestCount: 1115
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1165
    requestCount: 3396
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 836
          verb: list
        requestCount: 836
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 858
    - byUser:
      - byVerb:
        - requestCount: 770
          verb: list
        requestCount: 770
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.12
      requestCount: 787
    - byUser:
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 869
    requestCount: 2514
  - requestCount: 0
  - byNode:
    - nodeName: 10.21.102.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 924
          verb: list
        requestCount: 924
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 941
    - byUser:
      - byVerb:
        - requestCount: 911
          verb: list
        requestCount: 911
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 936
    - byUser:
      - byVerb:
        - requestCount: 955
          verb: list
        requestCount: 955
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 992
    requestCount: 2869
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 751
          verb: list
        requestCount: 751
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 766
    - byUser:
      - byVerb:
        - requestCount: 711
          verb: list
        requestCount: 711
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 740
    - byUser:
      - byVerb:
        - requestCount: 717
          verb: list
        requestCount: 717
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 755
    requestCount: 2261
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 893
          verb: list
        requestCount: 893
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 914
    - byUser:
      - byVerb:
        - requestCount: 926
          verb: list
        requestCount: 926
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 949
    - byUser:
      - byVerb:
        - requestCount: 961
          verb: list
        requestCount: 961
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 997
    requestCount: 2860
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 850
    - byUser:
      - byVerb:
        - requestCount: 829
          verb: list
        requestCount: 829
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 858
    - byUser:
      - byVerb:
        - requestCount: 876
          verb: list
        requestCount: 876
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 911
    requestCount: 2619
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 872
          verb: list
        requestCount: 872
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 19
          verb: watch
        requestCount: 19
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 891
    - byUser:
      - byVerb:
        - requestCount: 865
          verb: list
        requestCount: 865
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 889
    - byUser:
      - byVerb:
        - requestCount: 906
          verb: list
        requestCount: 906
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 19
          verb: watch
        requestCount: 19
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 946
    requestCount: 2726
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 925
          verb: list
        requestCount: 925
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 940
    - byUser:
      - byVerb:
        - requestCount: 861
          verb: list
        requestCount: 861
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 886
    - byUser:
      - byVerb:
        - requestCount: 898
          verb: list
        requestCount: 898
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 939
    requestCount: 2765
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 821
          verb: list
        requestCount: 821
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 845
    - byUser:
      - byVerb:
        - requestCount: 832
          verb: list
        requestCount: 832
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 854
    - byUser:
      - byVerb:
        - requestCount: 812
          verb: list
        requestCount: 812
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 848
    requestCount: 2547
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 830
          verb: list
        requestCount: 830
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      nodeName: 10.21.102.11
      requestCount: 854
    - byUser:
      - byVerb:
        - requestCount: 827
          verb: list
        requestCount: 827
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 850
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: python-requests/2.32.0
        username: system:serviceaccount:citrix-cic:citrix-cic-netscaler-ingress-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 866
    requestCount: 2570
  requestCount: 59933
