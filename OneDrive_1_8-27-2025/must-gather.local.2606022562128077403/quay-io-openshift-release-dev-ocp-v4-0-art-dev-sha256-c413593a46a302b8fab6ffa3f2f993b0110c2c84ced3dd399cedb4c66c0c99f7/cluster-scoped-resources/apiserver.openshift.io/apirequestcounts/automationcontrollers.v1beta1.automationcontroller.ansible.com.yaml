---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-01-14T19:18:08Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-01-14T19:18:08Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:06:32Z"
  name: automationcontrollers.v1beta1.automationcontroller.ansible.com
  resourceVersion: "*********"
  uid: 2858d233-c29e-450c-aaa5-afe378d5f91d
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 36
    - byUser:
      - byVerb:
        - requestCount: 54
          verb: list
        requestCount: 54
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 54
    requestCount: 90
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 830
          verb: list
        requestCount: 830
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 6
          verb: get
        - requestCount: 1
          verb: list
        - requestCount: 4
          verb: update
        - requestCount: 5
          verb: watch
        requestCount: 16
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 5
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 4
          verb: patch
        requestCount: 4
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: Mozilla/5.0
        username: J8683
      nodeName: 10.21.102.11
      requestCount: 858
    - byUser:
      - byVerb:
        - requestCount: 773
          verb: list
        requestCount: 773
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 10
          verb: watch
        requestCount: 11
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 10
          verb: patch
        requestCount: 10
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 801
    - byUser:
      - byVerb:
        - requestCount: 766
          verb: list
        requestCount: 766
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 57
          verb: get
        - requestCount: 1
          verb: list
        - requestCount: 38
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 103
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 38
          verb: patch
        requestCount: 38
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 15
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 10
          verb: watch
        requestCount: 11
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: J8683
      nodeName: 10.21.102.9
      requestCount: 934
    requestCount: 2593
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 878
          verb: list
        requestCount: 878
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 878
    - byUser:
      - byVerb:
        - requestCount: 858
          verb: list
        requestCount: 858
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 873
    - byUser:
      - byVerb:
        - requestCount: 879
          verb: list
        requestCount: 879
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: get
        - requestCount: 6
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 22
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: patch
        requestCount: 6
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 926
    requestCount: 2677
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 878
          verb: list
        requestCount: 878
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 878
    - byUser:
      - byVerb:
        - requestCount: 836
          verb: list
        requestCount: 836
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 852
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 8
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 28
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: patch
        requestCount: 8
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 889
    requestCount: 2619
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1005
          verb: list
        requestCount: 1005
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 1005
    - byUser:
      - byVerb:
        - requestCount: 994
          verb: list
        requestCount: 994
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 1009
    - byUser:
      - byVerb:
        - requestCount: 970
          verb: list
        requestCount: 970
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: get
        - requestCount: 6
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 22
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: patch
        requestCount: 6
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1018
    requestCount: 3032
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 918
          verb: list
        requestCount: 918
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 918
    - byUser:
      - byVerb:
        - requestCount: 955
          verb: list
        requestCount: 955
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 970
    - byUser:
      - byVerb:
        - requestCount: 958
          verb: list
        requestCount: 958
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 8
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 29
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: patch
        requestCount: 8
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1014
    requestCount: 2902
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 983
          verb: list
        requestCount: 983
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 983
    - byUser:
      - byVerb:
        - requestCount: 976
          verb: list
        requestCount: 976
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 991
    - byUser:
      - byVerb:
        - requestCount: 989
          verb: list
        requestCount: 989
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: get
        - requestCount: 6
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 22
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: patch
        requestCount: 6
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1037
    requestCount: 3011
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 812
          verb: list
        requestCount: 812
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 812
    - byUser:
      - byVerb:
        - requestCount: 824
          verb: list
        requestCount: 824
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 841
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: list
        requestCount: 860
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: get
        - requestCount: 6
          verb: update
        - requestCount: 10
          verb: watch
        requestCount: 25
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: patch
        requestCount: 6
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 912
    requestCount: 2565
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 915
          verb: list
        requestCount: 915
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 915
    - byUser:
      - byVerb:
        - requestCount: 837
          verb: list
        requestCount: 837
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 852
    - byUser:
      - byVerb:
        - requestCount: 876
          verb: list
        requestCount: 876
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 8
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 27
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: patch
        requestCount: 8
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 930
    requestCount: 2697
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: list
        requestCount: 860
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 860
    - byUser:
      - byVerb:
        - requestCount: 817
          verb: list
        requestCount: 817
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 831
    - byUser:
      - byVerb:
        - requestCount: 846
          verb: list
        requestCount: 846
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: get
        - requestCount: 6
          verb: update
        - requestCount: 10
          verb: watch
        requestCount: 25
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: patch
        requestCount: 6
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 898
    requestCount: 2589
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 913
          verb: list
        requestCount: 913
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 913
    - byUser:
      - byVerb:
        - requestCount: 898
          verb: list
        requestCount: 898
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 912
    - byUser:
      - byVerb:
        - requestCount: 957
          verb: list
        requestCount: 957
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 8
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 28
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: patch
        requestCount: 8
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1013
    requestCount: 2838
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 769
          verb: list
        requestCount: 769
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 2
          verb: patch
        requestCount: 2
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.11
      requestCount: 778
    - byUser:
      - byVerb:
        - requestCount: 825
          verb: list
        requestCount: 825
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 3
          verb: watch
        requestCount: 3
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 2
          verb: patch
        requestCount: 2
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 839
    - byUser:
      - byVerb:
        - requestCount: 746
          verb: list
        requestCount: 746
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: get
        - requestCount: 6
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 23
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: patch
        requestCount: 6
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 795
    requestCount: 2412
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 940
          verb: list
        requestCount: 940
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.11
      requestCount: 945
    - byUser:
      - byVerb:
        - requestCount: 895
          verb: list
        requestCount: 895
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 5
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 2
          verb: patch
        requestCount: 2
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 907
    - byUser:
      - byVerb:
        - requestCount: 969
          verb: list
        requestCount: 969
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 10
          verb: get
        - requestCount: 7
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 25
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: patch
        requestCount: 8
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 2
          verb: get
        requestCount: 2
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:generic-garbage-collector
      nodeName: 10.21.102.9
      requestCount: 1030
    requestCount: 2882
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1111
          verb: list
        requestCount: 1111
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 1111
    - byUser:
      - byVerb:
        - requestCount: 1087
          verb: list
        requestCount: 1087
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 1095
    - byUser:
      - byVerb:
        - requestCount: 1115
          verb: list
        requestCount: 1115
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 11
          verb: get
        - requestCount: 7
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 25
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: patch
        requestCount: 6
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1174
    requestCount: 3380
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 836
          verb: list
        requestCount: 836
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 836
    - byUser:
      - byVerb:
        - requestCount: 836
          verb: list
        requestCount: 836
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 844
    - byUser:
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: get
        - requestCount: 6
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 24
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: patch
        requestCount: 6
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 882
    requestCount: 2562
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 36
    - byUser:
      - byVerb:
        - requestCount: 54
          verb: list
        requestCount: 54
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 54
    requestCount: 90
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 924
          verb: list
        requestCount: 924
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.11
      requestCount: 925
    - byUser:
      - byVerb:
        - requestCount: 911
          verb: list
        requestCount: 911
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 2
          verb: update
        - requestCount: 2
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 2
          verb: patch
        requestCount: 2
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 929
    - byUser:
      - byVerb:
        - requestCount: 957
          verb: list
        requestCount: 957
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 57
          verb: get
        - requestCount: 1
          verb: list
        - requestCount: 38
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 103
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 38
          verb: patch
        requestCount: 38
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 2
          verb: patch
        requestCount: 2
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1129
    requestCount: 2983
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 751
          verb: list
        requestCount: 751
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 751
    - byUser:
      - byVerb:
        - requestCount: 747
          verb: list
        requestCount: 747
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 755
    - byUser:
      - byVerb:
        - requestCount: 715
          verb: list
        requestCount: 715
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: get
        - requestCount: 6
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 22
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: patch
        requestCount: 6
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 770
    requestCount: 2276
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 893
          verb: list
        requestCount: 893
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 893
    - byUser:
      - byVerb:
        - requestCount: 926
          verb: list
        requestCount: 926
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 934
    - byUser:
      - byVerb:
        - requestCount: 961
          verb: list
        requestCount: 961
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 8
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 29
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: patch
        requestCount: 8
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1025
    requestCount: 2852
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 833
    - byUser:
      - byVerb:
        - requestCount: 829
          verb: list
        requestCount: 829
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 836
    - byUser:
      - byVerb:
        - requestCount: 876
          verb: list
        requestCount: 876
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: get
        - requestCount: 6
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 23
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: patch
        requestCount: 6
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 934
    requestCount: 2603
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 872
          verb: list
        requestCount: 872
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 872
    - byUser:
      - byVerb:
        - requestCount: 865
          verb: list
        requestCount: 865
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 873
    - byUser:
      - byVerb:
        - requestCount: 906
          verb: list
        requestCount: 906
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: get
        - requestCount: 6
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 24
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: patch
        requestCount: 6
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 963
    requestCount: 2708
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 925
          verb: list
        requestCount: 925
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 925
    - byUser:
      - byVerb:
        - requestCount: 869
          verb: list
        requestCount: 869
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 876
    - byUser:
      - byVerb:
        - requestCount: 898
          verb: list
        requestCount: 898
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 8
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 28
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: patch
        requestCount: 8
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 963
    requestCount: 2764
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 821
          verb: list
        requestCount: 821
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 821
    - byUser:
      - byVerb:
        - requestCount: 832
          verb: list
        requestCount: 832
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 839
    - byUser:
      - byVerb:
        - requestCount: 812
          verb: list
        requestCount: 812
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: get
        - requestCount: 6
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 23
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: patch
        requestCount: 6
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 869
    requestCount: 2529
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 830
          verb: list
        requestCount: 830
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 51
          verb: get
        - requestCount: 1
          verb: list
        - requestCount: 34
          verb: update
        - requestCount: 3
          verb: watch
        requestCount: 89
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 34
          verb: patch
        requestCount: 34
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 3
          verb: watch
        requestCount: 4
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 2
          verb: patch
        requestCount: 2
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.11
      requestCount: 959
    - byUser:
      - byVerb:
        - requestCount: 827
          verb: list
        requestCount: 827
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 836
    - byUser:
      - byVerb:
        - requestCount: 833
          verb: list
        requestCount: 833
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: get
        - requestCount: 5
          verb: update
        - requestCount: 6
          verb: watch
        requestCount: 18
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: patch
        requestCount: 6
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:automation-controller-operator-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 882
    requestCount: 2677
  requestCount: 60241
