---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2023-12-19T12:53:17Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2023-12-19T12:53:17Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:07:13Z"
  name: nodes.v1
  resourceVersion: "*********"
  uid: 915e2ff6-a545-40d1-a947-533fbd6d2bd7
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 729
          verb: get
        requestCount: 729
        userAgent: oc/4.16.0
        username: system:serviceaccount:openshift-must-gather-6mxgs:default
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 2
          verb: patch
        requestCount: 20
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 19
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 19
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 1
          verb: patch
        requestCount: 18
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-5wphs
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 3
          verb: watch
        requestCount: 3
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 2
          verb: watch
        requestCount: 2
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      nodeName: 10.21.102.11
      requestCount: 836
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 4
          verb: get
        requestCount: 4
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: ovnkube-identity/v0.29.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      nodeName: 10.21.102.12
      requestCount: 37
    - byUser:
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 63
          verb: watch
        requestCount: 65
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 31
          verb: get
        - requestCount: 5
          verb: patch
        requestCount: 36
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 30
          verb: get
        - requestCount: 2
          verb: patch
        - requestCount: 1
          verb: watch
        requestCount: 33
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-tssl4
      - byVerb:
        - requestCount: 30
          verb: get
        - requestCount: 2
          verb: patch
        requestCount: 32
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 30
          verb: get
        - requestCount: 1
          verb: patch
        - requestCount: 1
          verb: watch
        requestCount: 32
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus3-lcgzj
      - byVerb:
        - requestCount: 29
          verb: get
        - requestCount: 1
          verb: patch
        - requestCount: 1
          verb: watch
        requestCount: 31
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-dgkqt
      - byVerb:
        - requestCount: 29
          verb: get
        - requestCount: 1
          verb: patch
        - requestCount: 1
          verb: watch
        requestCount: 31
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-f6gz2
      - byVerb:
        - requestCount: 29
          verb: get
        - requestCount: 1
          verb: patch
        - requestCount: 1
          verb: watch
        requestCount: 31
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-xxg89
      - byVerb:
        - requestCount: 29
          verb: get
        - requestCount: 1
          verb: patch
        - requestCount: 1
          verb: watch
        requestCount: 31
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-tcmdz
      - byVerb:
        - requestCount: 29
          verb: get
        - requestCount: 1
          verb: patch
        - requestCount: 1
          verb: watch
        requestCount: 31
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-rwxsl
      nodeName: 10.21.102.9
      requestCount: 880
    requestCount: 1753
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 376
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 120
          verb: watch
        requestCount: 120
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 81
          verb: watch
        requestCount: 81
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 71
          verb: list
        requestCount: 71
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 59
          verb: watch
        requestCount: 59
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 27
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.21.102.11
      requestCount: 1512
    - byUser:
      - byVerb:
        - requestCount: 355
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 376
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 376
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 132
          verb: list
        - requestCount: 16
          verb: watch
        requestCount: 148
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 25
          verb: get
        - requestCount: 51
          verb: patch
        requestCount: 76
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 54
          verb: list
        - requestCount: 15
          verb: watch
        requestCount: 69
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.21.102.12
      requestCount: 2599
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 767
          verb: watch
        requestCount: 785
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 357
          verb: get
        - requestCount: 16
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 381
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 357
          verb: get
        - requestCount: 15
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 380
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 355
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 377
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-tcmdz
      - byVerb:
        - requestCount: 355
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 377
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-dgkqt
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-tssl4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-sqqdl
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-f6gz2
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-lsxxm
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus3-lcgzj
      nodeName: 10.21.102.9
      requestCount: 4171
    requestCount: 8282
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 112
          verb: watch
        requestCount: 112
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 77
          verb: watch
        requestCount: 77
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 56
          verb: watch
        requestCount: 56
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 53
          verb: list
        requestCount: 53
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 29
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.21.102.11
      requestCount: 1477
    - byUser:
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 370
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 132
          verb: list
        - requestCount: 15
          verb: watch
        requestCount: 147
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 73
          verb: watch
        requestCount: 73
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 51
          verb: list
        - requestCount: 17
          verb: watch
        requestCount: 68
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 62
          verb: watch
        requestCount: 62
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      nodeName: 10.21.102.12
      requestCount: 2584
    - byUser:
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 765
          verb: watch
        requestCount: 789
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-tcmdz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-5wphs
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-w959c
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-xxg89
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-tbwst
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      nodeName: 10.21.102.9
      requestCount: 4143
    requestCount: 8204
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 355
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 122
          verb: watch
        requestCount: 122
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 79
          verb: watch
        requestCount: 79
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 68
          verb: list
        requestCount: 68
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 53
          verb: watch
        requestCount: 54
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 27
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.21.102.11
      requestCount: 1508
    - byUser:
      - byVerb:
        - requestCount: 349
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 370
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 369
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 369
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 368
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 367
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 366
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 130
          verb: list
        - requestCount: 17
          verb: watch
        requestCount: 147
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 69
          verb: watch
        requestCount: 69
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 51
          verb: list
        - requestCount: 16
          verb: watch
        requestCount: 67
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 63
          verb: watch
        requestCount: 63
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      nodeName: 10.21.102.12
      requestCount: 2555
    - byUser:
      - byVerb:
        - requestCount: 22
          verb: get
        - requestCount: 768
          verb: watch
        requestCount: 790
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 377
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tvs4l-0
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-24ggt
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-rhg6x-2
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-tcmdz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-w959c
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-5wphs
      nodeName: 10.21.102.9
      requestCount: 4151
    requestCount: 8214
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 355
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 377
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 118
          verb: watch
        requestCount: 118
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 77
          verb: watch
        requestCount: 77
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 62
          verb: watch
        requestCount: 62
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 62
          verb: list
        requestCount: 62
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 29
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.21.102.11
      requestCount: 1504
    - byUser:
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 367
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 367
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 347
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 367
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 367
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 366
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 366
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 130
          verb: list
        - requestCount: 15
          verb: watch
        requestCount: 145
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 54
          verb: list
        - requestCount: 15
          verb: watch
        requestCount: 69
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 58
          verb: list
        requestCount: 58
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.21.102.12
      requestCount: 2537
    - byUser:
      - byVerb:
        - requestCount: 21
          verb: get
        - requestCount: 769
          verb: watch
        requestCount: 790
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-tcmdz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tvs4l-0
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-tbwst
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus1-bf5c6
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-tssl4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-24ggt
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus3-lcgzj
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      nodeName: 10.21.102.9
      requestCount: 4148
    requestCount: 8189
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 355
          verb: get
        - requestCount: 15
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 378
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 120
          verb: watch
        requestCount: 120
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 79
          verb: watch
        requestCount: 79
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 55
          verb: list
        requestCount: 55
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 29
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.21.102.11
      requestCount: 1506
    - byUser:
      - byVerb:
        - requestCount: 349
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 370
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 368
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 368
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 368
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 368
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 347
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 365
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 130
          verb: list
        - requestCount: 18
          verb: watch
        requestCount: 148
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 51
          verb: list
        - requestCount: 15
          verb: watch
        requestCount: 66
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 66
          verb: watch
        requestCount: 66
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 61
          verb: watch
        requestCount: 62
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      nodeName: 10.21.102.12
      requestCount: 2549
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 768
          verb: watch
        requestCount: 785
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 376
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-tssl4
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-24ggt
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-7599c
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-sqqdl
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-f6gz2
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-tcmdz
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 370
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-w959c
      nodeName: 10.21.102.9
      requestCount: 4145
    requestCount: 8200
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 366
          verb: get
        - requestCount: 21
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 395
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 121
          verb: watch
        requestCount: 121
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 81
          verb: watch
        requestCount: 81
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 68
          verb: list
        requestCount: 68
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 64
          verb: watch
        requestCount: 64
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 29
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 19
          verb: watch
        requestCount: 19
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.21.102.11
      requestCount: 1542
    - byUser:
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 18
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 379
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 368
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 367
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 347
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 367
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 367
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 347
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 366
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 130
          verb: list
        - requestCount: 15
          verb: watch
        requestCount: 145
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 51
          verb: patch
        requestCount: 75
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 71
          verb: watch
        requestCount: 71
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 51
          verb: list
        - requestCount: 17
          verb: watch
        requestCount: 68
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.21.102.12
      requestCount: 2573
    - byUser:
      - byVerb:
        - requestCount: 23
          verb: get
        - requestCount: 770
          verb: watch
        requestCount: 793
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 355
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 378
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-f6gz2
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-sqqdl
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus1-bf5c6
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-24ggt
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-tssl4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-rwxsl
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus3-lcgzj
      nodeName: 10.21.102.9
      requestCount: 4156
    requestCount: 8271
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 357
          verb: get
        - requestCount: 16
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 381
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 117
          verb: watch
        requestCount: 117
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 81
          verb: watch
        requestCount: 82
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 68
          verb: list
        requestCount: 68
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 28
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.21.102.11
      requestCount: 1516
    - byUser:
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 132
          verb: list
        - requestCount: 17
          verb: watch
        requestCount: 149
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 72
          verb: watch
        requestCount: 72
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 54
          verb: list
        - requestCount: 14
          verb: watch
        requestCount: 68
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.21.102.12
      requestCount: 2588
    - byUser:
      - byVerb:
        - requestCount: 22
          verb: get
        - requestCount: 769
          verb: watch
        requestCount: 791
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 356
          verb: get
        - requestCount: 15
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 379
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 357
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 378
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-24ggt
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tvs4l-0
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-t9l52
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus1-bf5c6
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-dgkqt
      nodeName: 10.21.102.9
      requestCount: 4155
    requestCount: 8259
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 363
          verb: get
        - requestCount: 18
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 390
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 119
          verb: watch
        requestCount: 119
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 80
          verb: watch
        requestCount: 80
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 74
          verb: list
        requestCount: 74
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 28
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.21.102.11
      requestCount: 1538
    - byUser:
      - byVerb:
        - requestCount: 363
          verb: get
        - requestCount: 17
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 387
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 355
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 377
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 132
          verb: list
        - requestCount: 16
          verb: watch
        requestCount: 148
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 43
          verb: get
        - requestCount: 85
          verb: patch
        requestCount: 128
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 76
          verb: watch
        requestCount: 76
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 51
          verb: list
        - requestCount: 17
          verb: watch
        requestCount: 68
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.21.102.12
      requestCount: 2673
    - byUser:
      - byVerb:
        - requestCount: 27
          verb: get
        - requestCount: 767
          verb: watch
        requestCount: 794
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 358
          verb: get
        - requestCount: 15
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 382
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 356
          verb: get
        - requestCount: 18
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 382
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 356
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 378
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-7599c
      - byVerb:
        - requestCount: 355
          verb: get
        - requestCount: 15
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 377
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-tbwst
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-tcmdz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus1-bf5c6
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tvs4l-0
      nodeName: 10.21.102.9
      requestCount: 4176
    requestCount: 8387
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 356
          verb: get
        - requestCount: 15
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 379
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 119
          verb: watch
        requestCount: 119
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 80
          verb: watch
        requestCount: 80
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 66
          verb: list
        requestCount: 66
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 29
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.21.102.11
      requestCount: 1521
    - byUser:
      - byVerb:
        - requestCount: 365
          verb: get
        - requestCount: 20
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 393
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 132
          verb: list
        - requestCount: 17
          verb: watch
        requestCount: 149
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 44
          verb: get
        - requestCount: 89
          verb: patch
        requestCount: 133
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 71
          verb: watch
        requestCount: 71
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 54
          verb: list
        - requestCount: 16
          verb: watch
        requestCount: 70
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.21.102.12
      requestCount: 2679
    - byUser:
      - byVerb:
        - requestCount: 22
          verb: get
        - requestCount: 770
          verb: watch
        requestCount: 792
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 364
          verb: get
        - requestCount: 16
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 387
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 16
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 385
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 359
          verb: get
        - requestCount: 16
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 382
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 356
          verb: get
        - requestCount: 16
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 380
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 356
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 378
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-tcmdz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-tssl4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-lsxxm
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-t9l52
      nodeName: 10.21.102.9
      requestCount: 4198
    requestCount: 8398
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 120
          verb: watch
        requestCount: 120
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 78
          verb: watch
        requestCount: 78
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 63
          verb: watch
        requestCount: 63
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 63
          verb: list
        requestCount: 63
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 29
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.21.102.11
      requestCount: 1509
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 16
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 378
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 367
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 347
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 367
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 366
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 347
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 365
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 347
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 364
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 130
          verb: list
        - requestCount: 17
          verb: watch
        requestCount: 147
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 36
          verb: get
        - requestCount: 65
          verb: patch
        requestCount: 101
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 70
          verb: watch
        requestCount: 70
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 51
          verb: list
        - requestCount: 17
          verb: watch
        requestCount: 68
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.21.102.12
      requestCount: 2593
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        - requestCount: 770
          verb: watch
        requestCount: 790
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 362
          verb: get
        - requestCount: 17
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 387
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 361
          verb: get
        - requestCount: 16
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 386
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-7599c
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus1-bf5c6
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-w959c
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-tbwst
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-xxg89
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-lsxxm
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 370
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-tssl4
      nodeName: 10.21.102.9
      requestCount: 4170
    requestCount: 8272
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 120
          verb: watch
        requestCount: 120
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 79
          verb: watch
        requestCount: 79
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 65
          verb: list
        requestCount: 65
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 56
          verb: watch
        requestCount: 56
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 28
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.21.102.11
      requestCount: 1503
    - byUser:
      - byVerb:
        - requestCount: 362
          verb: get
        - requestCount: 17
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 387
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 132
          verb: list
        - requestCount: 16
          verb: watch
        requestCount: 148
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 22
          verb: get
        - requestCount: 52
          verb: patch
        requestCount: 74
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 72
          verb: watch
        requestCount: 72
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 54
          verb: list
        - requestCount: 17
          verb: watch
        requestCount: 71
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.21.102.12
      requestCount: 2617
    - byUser:
      - byVerb:
        - requestCount: 23
          verb: get
        - requestCount: 772
          verb: watch
        requestCount: 795
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 357
          verb: get
        - requestCount: 15
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 381
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 15
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 378
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 355
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 377
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-5wphs
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-tcmdz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-dgkqt
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus1-bf5c6
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-xxg89
      nodeName: 10.21.102.9
      requestCount: 4170
    requestCount: 8290
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 355
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 376
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 10
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 248
          verb: get
        - requestCount: 10
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 263
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-5wphs
      - byVerb:
        - requestCount: 124
          verb: watch
        requestCount: 124
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 78
          verb: watch
        requestCount: 78
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 63
          verb: list
        requestCount: 63
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 56
          verb: watch
        requestCount: 56
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 27
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.21.102.11
      requestCount: 1750
    - byUser:
      - byVerb:
        - requestCount: 359
          verb: get
        - requestCount: 18
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 385
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 132
          verb: list
        - requestCount: 15
          verb: watch
        requestCount: 147
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 30
          verb: get
        - requestCount: 58
          verb: patch
        requestCount: 88
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 54
          verb: list
        - requestCount: 16
          verb: watch
        requestCount: 70
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 66
          verb: list
        requestCount: 66
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.21.102.12
      requestCount: 2626
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: get
        - requestCount: 766
          verb: watch
        requestCount: 799
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 362
          verb: get
        - requestCount: 18
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 387
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 18
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 386
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 18
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 386
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 357
          verb: get
        - requestCount: 16
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 380
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 355
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 377
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tvs4l-0
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-lsxxm
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-w959c
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus1-bf5c6
      nodeName: 10.21.102.9
      requestCount: 4208
    requestCount: 8584
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 361
          verb: get
        - requestCount: 19
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 389
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-5wphs
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 118
          verb: watch
        requestCount: 118
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 77
          verb: watch
        requestCount: 77
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 59
          verb: list
        requestCount: 59
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 52
          verb: watch
        requestCount: 52
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 27
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      nodeName: 10.21.102.11
      requestCount: 1861
    - byUser:
      - byVerb:
        - requestCount: 362
          verb: get
        - requestCount: 20
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 391
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 377
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 376
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 132
          verb: list
        - requestCount: 16
          verb: watch
        requestCount: 148
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 71
          verb: watch
        requestCount: 71
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 51
          verb: list
        - requestCount: 18
          verb: watch
        requestCount: 69
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 66
          verb: watch
        requestCount: 66
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.21.102.12
      requestCount: 2615
    - byUser:
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 768
          verb: watch
        requestCount: 794
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 361
          verb: get
        - requestCount: 20
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 388
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 361
          verb: get
        - requestCount: 17
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 387
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 357
          verb: get
        - requestCount: 17
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 382
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 357
          verb: get
        - requestCount: 16
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 381
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-tcmdz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-lsxxm
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-t9l52
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus1-bf5c6
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-xxg89
      nodeName: 10.21.102.9
      requestCount: 4200
    requestCount: 8676
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 654
          verb: get
        - requestCount: 3
          verb: list
        requestCount: 657
        userAgent: oc/4.16.0
        username: system:serviceaccount:openshift-must-gather-6mxgs:default
      - byVerb:
        - requestCount: 359
          verb: get
        - requestCount: 19
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 386
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 356
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 378
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-5wphs
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 123
          verb: watch
        requestCount: 123
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 81
          verb: watch
        requestCount: 81
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 59
          verb: list
        requestCount: 59
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 56
          verb: watch
        requestCount: 56
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 28
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      nodeName: 10.21.102.11
      requestCount: 2523
    - byUser:
      - byVerb:
        - requestCount: 355
          verb: get
        - requestCount: 20
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 383
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 351
          verb: get
        - requestCount: 15
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 350
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 349
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 369
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 368
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 367
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 130
          verb: list
        - requestCount: 17
          verb: watch
        requestCount: 147
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 74
          verb: watch
        requestCount: 74
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 54
          verb: list
        - requestCount: 15
          verb: watch
        requestCount: 69
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 67
          verb: watch
        requestCount: 67
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.21.102.12
      requestCount: 2637
    - byUser:
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 768
          verb: watch
        requestCount: 794
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 362
          verb: get
        - requestCount: 19
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 389
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 359
          verb: get
        - requestCount: 17
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 385
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 359
          verb: get
        - requestCount: 17
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 385
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 17
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 385
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 359
          verb: get
        - requestCount: 15
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 382
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 358
          verb: get
        - requestCount: 17
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 382
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 358
          verb: get
        - requestCount: 15
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 380
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-tssl4
      - byVerb:
        - requestCount: 356
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 378
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-24ggt
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-rwxsl
      nodeName: 10.21.102.9
      requestCount: 4241
    requestCount: 9401
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 729
          verb: get
        requestCount: 729
        userAgent: oc/4.16.0
        username: system:serviceaccount:openshift-must-gather-6mxgs:default
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 2
          verb: patch
        requestCount: 20
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 19
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 19
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 1
          verb: patch
        requestCount: 18
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-5wphs
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 3
          verb: watch
        requestCount: 3
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 2
          verb: watch
        requestCount: 2
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      nodeName: 10.21.102.11
      requestCount: 836
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 4
          verb: get
        requestCount: 4
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: ovnkube-identity/v0.29.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      nodeName: 10.21.102.12
      requestCount: 37
    - byUser:
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 63
          verb: watch
        requestCount: 65
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 31
          verb: get
        - requestCount: 5
          verb: patch
        requestCount: 36
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 30
          verb: get
        - requestCount: 2
          verb: patch
        - requestCount: 1
          verb: watch
        requestCount: 33
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-tssl4
      - byVerb:
        - requestCount: 30
          verb: get
        - requestCount: 2
          verb: patch
        requestCount: 32
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 30
          verb: get
        - requestCount: 1
          verb: patch
        - requestCount: 1
          verb: watch
        requestCount: 32
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus3-lcgzj
      - byVerb:
        - requestCount: 29
          verb: get
        - requestCount: 1
          verb: patch
        - requestCount: 1
          verb: watch
        requestCount: 31
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-dgkqt
      - byVerb:
        - requestCount: 29
          verb: get
        - requestCount: 1
          verb: patch
        - requestCount: 1
          verb: watch
        requestCount: 31
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-f6gz2
      - byVerb:
        - requestCount: 29
          verb: get
        - requestCount: 1
          verb: patch
        - requestCount: 1
          verb: watch
        requestCount: 31
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-xxg89
      - byVerb:
        - requestCount: 29
          verb: get
        - requestCount: 1
          verb: patch
        - requestCount: 1
          verb: watch
        requestCount: 31
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-tcmdz
      - byVerb:
        - requestCount: 29
          verb: get
        - requestCount: 1
          verb: patch
        - requestCount: 1
          verb: watch
        requestCount: 31
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-rwxsl
      nodeName: 10.21.102.9
      requestCount: 880
    requestCount: 1753
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 358
          verb: get
        - requestCount: 16
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 382
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 119
          verb: watch
        requestCount: 119
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 76
          verb: watch
        requestCount: 76
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 62
          verb: watch
        requestCount: 62
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 58
          verb: list
        requestCount: 58
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 28
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.21.102.11
      requestCount: 1506
    - byUser:
      - byVerb:
        - requestCount: 351
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 349
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 347
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 368
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 347
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 366
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 347
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 365
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 48
          verb: get
        - requestCount: 100
          verb: patch
        requestCount: 148
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 130
          verb: list
        - requestCount: 15
          verb: watch
        requestCount: 145
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 72
          verb: watch
        requestCount: 73
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 51
          verb: list
        - requestCount: 15
          verb: watch
        requestCount: 66
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      nodeName: 10.21.102.12
      requestCount: 2340
    - byUser:
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 769
          verb: watch
        requestCount: 793
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 362
          verb: get
        - requestCount: 18
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 388
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 19
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 387
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 18
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 387
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 357
          verb: get
        - requestCount: 16
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 381
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 356
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 378
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 355
          verb: get
        - requestCount: 15
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 378
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-dgkqt
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-rwxsl
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-tssl4
      nodeName: 10.21.102.9
      requestCount: 4215
    requestCount: 8061
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 355
          verb: get
        - requestCount: 15
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 377
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 120
          verb: watch
        requestCount: 120
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 72
          verb: watch
        requestCount: 72
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 57
          verb: watch
        requestCount: 57
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 28
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.21.102.11
      requestCount: 1472
    - byUser:
      - byVerb:
        - requestCount: 356
          verb: get
        - requestCount: 17
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 381
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 55
          verb: get
        - requestCount: 114
          verb: patch
        requestCount: 169
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 132
          verb: list
        - requestCount: 16
          verb: watch
        requestCount: 148
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 71
          verb: watch
        requestCount: 71
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 54
          verb: list
        - requestCount: 17
          verb: watch
        requestCount: 71
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 60
          verb: list
        requestCount: 60
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.21.102.12
      requestCount: 2392
    - byUser:
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 768
          verb: watch
        requestCount: 794
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 361
          verb: get
        - requestCount: 19
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 388
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 362
          verb: get
        - requestCount: 19
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 388
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 19
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 387
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 18
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 385
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-dgkqt
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-7599c
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-f6gz2
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-24ggt
      nodeName: 10.21.102.9
      requestCount: 4205
    requestCount: 8069
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 19
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 387
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 119
          verb: watch
        requestCount: 119
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 70
          verb: watch
        requestCount: 70
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 57
          verb: watch
        requestCount: 58
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 52
          verb: list
        requestCount: 52
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 29
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.21.102.11
      requestCount: 1498
    - byUser:
      - byVerb:
        - requestCount: 361
          verb: get
        - requestCount: 19
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 388
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 85
          verb: get
        - requestCount: 161
          verb: patch
        requestCount: 246
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 132
          verb: list
        - requestCount: 17
          verb: watch
        requestCount: 149
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 71
          verb: watch
        requestCount: 72
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 51
          verb: list
        - requestCount: 15
          verb: watch
        requestCount: 66
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 64
          verb: list
        requestCount: 64
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.21.102.12
      requestCount: 2472
    - byUser:
      - byVerb:
        - requestCount: 22
          verb: get
        - requestCount: 766
          verb: watch
        requestCount: 788
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 366
          verb: get
        - requestCount: 21
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 395
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 363
          verb: get
        - requestCount: 19
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 391
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 359
          verb: get
        - requestCount: 21
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 388
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 361
          verb: get
        - requestCount: 19
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 387
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 18
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 387
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 356
          verb: get
        - requestCount: 16
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 380
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-t9l52
      - byVerb:
        - requestCount: 355
          verb: get
        - requestCount: 15
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 379
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 357
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 379
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-dgkqt
      - byVerb:
        - requestCount: 356
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 376
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-rwxsl
      nodeName: 10.21.102.9
      requestCount: 4250
    requestCount: 8220
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 359
          verb: get
        - requestCount: 23
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 389
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 118
          verb: watch
        requestCount: 118
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 70
          verb: watch
        requestCount: 70
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 59
          verb: list
        requestCount: 59
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 58
          verb: watch
        requestCount: 58
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 27
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.21.102.11
      requestCount: 1501
    - byUser:
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 23
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 391
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 54
          verb: get
        - requestCount: 108
          verb: patch
        requestCount: 162
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 132
          verb: list
        - requestCount: 15
          verb: watch
        requestCount: 147
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 73
          verb: watch
        requestCount: 73
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 54
          verb: list
        - requestCount: 15
          verb: watch
        requestCount: 69
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      nodeName: 10.21.102.12
      requestCount: 2400
    - byUser:
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 768
          verb: watch
        requestCount: 794
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 361
          verb: get
        - requestCount: 18
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 387
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 362
          verb: get
        - requestCount: 18
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 386
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 359
          verb: get
        - requestCount: 17
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 384
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 357
          verb: get
        - requestCount: 17
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 382
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 356
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 379
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 356
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 378
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-dgkqt
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-f6gz2
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tvs4l-0
      nodeName: 10.21.102.9
      requestCount: 4210
    requestCount: 8111
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 365
          verb: get
        - requestCount: 17
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 390
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 124
          verb: watch
        requestCount: 124
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 76
          verb: watch
        requestCount: 76
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 58
          verb: watch
        requestCount: 58
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 55
          verb: list
        requestCount: 55
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 29
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.21.102.11
      requestCount: 1515
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 15
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 376
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 368
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 347
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 368
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 367
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 348
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 367
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 224
          verb: get
        - requestCount: 7
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 236
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 152
          verb: patch
        requestCount: 230
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 130
          verb: list
        - requestCount: 15
          verb: watch
        requestCount: 145
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 72
          verb: watch
        requestCount: 72
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 51
          verb: list
        - requestCount: 16
          verb: watch
        requestCount: 67
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.21.102.12
      requestCount: 2596
    - byUser:
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 772
          verb: watch
        requestCount: 796
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 369
          verb: get
        - requestCount: 23
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 400
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 370
          verb: get
        - requestCount: 20
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 399
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 363
          verb: get
        - requestCount: 19
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 390
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 362
          verb: get
        - requestCount: 19
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 389
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 15
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 384
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-sqqdl
      - byVerb:
        - requestCount: 355
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 376
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-7599c
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-24ggt
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-t9l52
      nodeName: 10.21.102.9
      requestCount: 4252
    requestCount: 8363
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 117
          verb: watch
        requestCount: 117
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 68
          verb: watch
        requestCount: 68
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 54
          verb: watch
        requestCount: 54
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 52
          verb: list
        requestCount: 52
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 27
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: Prometheus/2.52.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.21.102.11
      requestCount: 1475
    - byUser:
      - byVerb:
        - requestCount: 357
          verb: get
        - requestCount: 15
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 380
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 132
          verb: list
        - requestCount: 16
          verb: watch
        requestCount: 148
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 29
          verb: get
        - requestCount: 50
          verb: patch
        requestCount: 79
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:attachdetach-controller
      - byVerb:
        - requestCount: 73
          verb: watch
        requestCount: 73
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 51
          verb: list
        - requestCount: 15
          verb: watch
        requestCount: 66
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.21.102.12
      requestCount: 2606
    - byUser:
      - byVerb:
        - requestCount: 19
          verb: get
        - requestCount: 768
          verb: watch
        requestCount: 787
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 15
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 384
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 356
          verb: get
        - requestCount: 15
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 379
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-sqqdl
      - byVerb:
        - requestCount: 355
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 376
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 10
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-7599c
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus3-lcgzj
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus1-bf5c6
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-rhg6x-2
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 370
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-f6gz2
      nodeName: 10.21.102.9
      requestCount: 4160
    requestCount: 8241
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 122
          verb: watch
        requestCount: 122
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 77
          verb: watch
        requestCount: 78
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 59
          verb: list
        requestCount: 59
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 54
          verb: watch
        requestCount: 54
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 27
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.21.102.11
      requestCount: 1491
    - byUser:
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 132
          verb: list
        - requestCount: 15
          verb: watch
        requestCount: 147
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 76
          verb: watch
        requestCount: 76
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 71
          verb: watch
        requestCount: 71
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 54
          verb: list
        - requestCount: 16
          verb: watch
        requestCount: 70
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.21.102.12
      requestCount: 2599
    - byUser:
      - byVerb:
        - requestCount: 21
          verb: get
        - requestCount: 767
          verb: watch
        requestCount: 788
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 358
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 381
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-ct87b
      - byVerb:
        - requestCount: 357
          verb: get
        - requestCount: 15
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 380
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 355
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 10
          verb: watch
        requestCount: 379
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-sqqdl
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-5wphs
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-rhg6x-2
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-hrpw8
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-24ggt
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-w959c
      nodeName: 10.21.102.9
      requestCount: 4168
    requestCount: 8258
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-master-tqldl-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-infra-eastus2-zjfm5
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-km2pd
      - byVerb:
        - requestCount: 120
          verb: watch
        requestCount: 120
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:filebeat:filebeat
      - byVerb:
        - requestCount: 78
          verb: watch
        requestCount: 78
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 58
          verb: watch
        requestCount: 58
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 50
          verb: list
        requestCount: 50
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 29
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.21.102.11
      requestCount: 1489
    - byUser:
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 376
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-gprvs
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-xdbqq
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-9bm8m
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-hrvmc
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-59559
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-2x82d
      - byVerb:
        - requestCount: 132
          verb: list
        - requestCount: 17
          verb: watch
        requestCount: 149
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 73
          verb: watch
        requestCount: 73
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 67
          verb: list
        requestCount: 67
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 61
          verb: watch
        requestCount: 61
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      nodeName: 10.21.102.12
      requestCount: 2590
    - byUser:
      - byVerb:
        - requestCount: 23
          verb: get
        - requestCount: 767
          verb: watch
        requestCount: 790
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 356
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 376
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-chx4q
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 13
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-cmsnk
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-dgkqt
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-f6gz2
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-xxg89
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-m8kc9
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus1-sqqdl
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus2-w959c
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.29.11+148a389
        username: system:node:ocpazp001-4zlm9-worker-eastus3-mnznv
      nodeName: 10.21.102.9
      requestCount: 4150
    requestCount: 8229
  requestCount: 184932
