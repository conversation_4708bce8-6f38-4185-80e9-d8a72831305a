---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2023-12-19T12:52:27Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2023-12-19T12:52:27Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:06:18Z"
  name: statefulsets.v1.apps
  resourceVersion: "*********"
  uid: e14c2e7a-4755-4037-8ee8-2827880a181e
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 95
          verb: list
        requestCount: 95
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      nodeName: 10.21.102.12
      requestCount: 96
    requestCount: 96
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1743
          verb: list
        requestCount: 1743
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 64
          verb: watch
        requestCount: 64
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 2
          verb: watch
        requestCount: 2
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.11
      requestCount: 3168
    - byUser:
      - byVerb:
        - requestCount: 1829
          verb: list
        requestCount: 1829
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 885
          verb: list
        requestCount: 885
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: update
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:statefulset-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      - byVerb:
        - requestCount: 5
          verb: patch
        requestCount: 5
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 2774
    - byUser:
      - byVerb:
        - requestCount: 1758
          verb: list
        requestCount: 1758
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1055
          verb: list
        requestCount: 1055
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 58
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 70
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 34
          verb: watch
        requestCount: 35
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.9
      requestCount: 2952
    requestCount: 8894
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1566
          verb: list
        requestCount: 1566
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 944
          verb: list
        requestCount: 944
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 63
          verb: watch
        requestCount: 63
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 2
          verb: list
        - requestCount: 3
          verb: watch
        requestCount: 5
        userAgent: Mozilla/5.0
        username: Z8504
      nodeName: 10.21.102.11
      requestCount: 2640
    - byUser:
      - byVerb:
        - requestCount: 1583
          verb: list
        requestCount: 1583
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 5
        userAgent: Mozilla/5.0
        username: I0455
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: Mozilla/5.0
        username: Z8504
      nodeName: 10.21.102.12
      requestCount: 2756
    - byUser:
      - byVerb:
        - requestCount: 1453
          verb: list
        requestCount: 1453
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1239
          verb: list
        requestCount: 1239
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 56
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 68
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 3
          verb: watch
        requestCount: 8
        userAgent: Mozilla/5.0
        username: I0455
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: Mozilla/5.0
        username: Z8504
      nodeName: 10.21.102.9
      requestCount: 2834
    requestCount: 8230
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1643
          verb: list
        requestCount: 1643
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1003
          verb: list
        requestCount: 1003
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 67
          verb: watch
        requestCount: 69
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 3
          verb: list
        - requestCount: 2
          verb: watch
        requestCount: 6
        userAgent: Mozilla/5.0
        username: Z8504
      nodeName: 10.21.102.11
      requestCount: 2786
    - byUser:
      - byVerb:
        - requestCount: 1624
          verb: list
        requestCount: 1624
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 871
          verb: list
        requestCount: 871
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 24
          verb: update
        requestCount: 24
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:statefulset-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 2
          verb: watch
        requestCount: 2
        userAgent: Mozilla/5.0
        username: Z8504
      nodeName: 10.21.102.12
      requestCount: 2568
    - byUser:
      - byVerb:
        - requestCount: 1624
          verb: list
        requestCount: 1624
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1534
          verb: list
        requestCount: 1534
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 58
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 70
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 30
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 2
          verb: patch
        - requestCount: 1
          verb: watch
        requestCount: 4
        userAgent: Mozilla/5.0
        username: Z8504
      nodeName: 10.21.102.9
      requestCount: 3297
    requestCount: 8651
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1604
          verb: list
        requestCount: 1604
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 64
          verb: watch
        requestCount: 64
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.11
      requestCount: 2797
    - byUser:
      - byVerb:
        - requestCount: 1588
          verb: list
        requestCount: 1588
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.12
      requestCount: 2933
    - byUser:
      - byVerb:
        - requestCount: 1535
          verb: list
        requestCount: 1535
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 53
          verb: get
        - requestCount: 7
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 67
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 2790
    requestCount: 8520
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1645
          verb: list
        requestCount: 1645
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 63
          verb: watch
        requestCount: 63
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 20
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 27
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: Mozilla/5.0
        username: L9334
      nodeName: 10.21.102.11
      requestCount: 2616
    - byUser:
      - byVerb:
        - requestCount: 1806
          verb: list
        requestCount: 1806
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 2
          verb: update
        requestCount: 2
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:statefulset-controller
      nodeName: 10.21.102.12
      requestCount: 3156
    - byUser:
      - byVerb:
        - requestCount: 1693
          verb: list
        requestCount: 1693
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 885
          verb: list
        requestCount: 885
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 56
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 68
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.9
      requestCount: 2712
    requestCount: 8484
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1418
          verb: list
        requestCount: 1418
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 64
          verb: watch
        requestCount: 64
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 22
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 31
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.21.102.11
      requestCount: 2867
    - byUser:
      - byVerb:
        - requestCount: 1421
          verb: list
        requestCount: 1421
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1357
          verb: list
        requestCount: 1357
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.21.102.12
      requestCount: 2826
    - byUser:
      - byVerb:
        - requestCount: 1471
          verb: list
        requestCount: 1471
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 885
          verb: list
        requestCount: 885
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 56
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 68
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 2489
    requestCount: 8182
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1563
          verb: list
        requestCount: 1563
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 944
          verb: list
        requestCount: 944
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 63
          verb: watch
        requestCount: 63
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.21.102.11
      requestCount: 2632
    - byUser:
      - byVerb:
        - requestCount: 1637
          verb: list
        requestCount: 1637
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: update
        requestCount: 4
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:statefulset-controller
      nodeName: 10.21.102.12
      requestCount: 2812
    - byUser:
      - byVerb:
        - requestCount: 1546
          verb: list
        requestCount: 1546
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1475
          verb: list
        requestCount: 1475
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 54
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 66
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 3152
    requestCount: 8596
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1515
          verb: list
        requestCount: 1515
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 767
          verb: list
        requestCount: 767
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 62
          verb: watch
        requestCount: 62
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 10
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 18
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.21.102.11
      requestCount: 2419
    - byUser:
      - byVerb:
        - requestCount: 1593
          verb: list
        requestCount: 1593
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1490
          verb: list
        requestCount: 1490
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 3128
    - byUser:
      - byVerb:
        - requestCount: 1493
          verb: list
        requestCount: 1493
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 58
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 70
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 2691
    requestCount: 8238
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1323
          verb: list
        requestCount: 1323
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 885
          verb: list
        requestCount: 885
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.11
      requestCount: 2338
    - byUser:
      - byVerb:
        - requestCount: 1475
          verb: list
        requestCount: 1475
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1278
          verb: list
        requestCount: 1278
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      nodeName: 10.21.102.12
      requestCount: 2801
    - byUser:
      - byVerb:
        - requestCount: 1369
          verb: list
        requestCount: 1369
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 57
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 69
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.9
      requestCount: 2567
    requestCount: 7706
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1477
          verb: list
        requestCount: 1477
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1180
          verb: list
        requestCount: 1180
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 62
          verb: watch
        requestCount: 62
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.21.102.11
      requestCount: 2782
    - byUser:
      - byVerb:
        - requestCount: 1389
          verb: list
        requestCount: 1389
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 944
          verb: list
        requestCount: 944
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.12
      requestCount: 2380
    - byUser:
      - byVerb:
        - requestCount: 1410
          verb: list
        requestCount: 1410
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 55
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 67
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 2838
    requestCount: 8000
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1662
          verb: list
        requestCount: 1662
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1475
          verb: list
        requestCount: 1475
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 61
          verb: watch
        requestCount: 61
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 6
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.11
      requestCount: 3271
    - byUser:
      - byVerb:
        - requestCount: 1636
          verb: list
        requestCount: 1636
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1357
          verb: list
        requestCount: 1357
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      - byVerb:
        - requestCount: 2
          verb: watch
        requestCount: 2
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 1
          verb: patch
        requestCount: 1
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 3034
    - byUser:
      - byVerb:
        - requestCount: 1722
          verb: list
        requestCount: 1722
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 708
          verb: list
        requestCount: 708
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 59
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 71
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 2564
    requestCount: 8869
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1730
          verb: list
        requestCount: 1730
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 64
          verb: watch
        requestCount: 64
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.11
      requestCount: 2688
    - byUser:
      - byVerb:
        - requestCount: 1675
          verb: list
        requestCount: 1675
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 5
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: patch
        requestCount: 1
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 2779
    - byUser:
      - byVerb:
        - requestCount: 1771
          verb: list
        requestCount: 1771
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1652
          verb: list
        requestCount: 1652
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 55
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 67
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 3
          verb: watch
        requestCount: 4
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 3556
    requestCount: 9023
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1211
          verb: list
        requestCount: 1211
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1180
          verb: list
        requestCount: 1180
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 67
          verb: watch
        requestCount: 67
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: Mozilla/5.0
        username: L9334
      nodeName: 10.21.102.11
      requestCount: 2524
    - byUser:
      - byVerb:
        - requestCount: 1137
          verb: list
        requestCount: 1137
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: L9334
      nodeName: 10.21.102.12
      requestCount: 2238
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1152
          verb: list
        requestCount: 1152
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 59
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 71
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 2593
    requestCount: 7355
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1721
          verb: list
        requestCount: 1721
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 944
          verb: list
        requestCount: 944
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 62
          verb: watch
        requestCount: 62
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 16
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 23
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 23
          verb: list
        requestCount: 23
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-6mxgs:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.21.102.11
      requestCount: 2828
    - byUser:
      - byVerb:
        - requestCount: 1730
          verb: list
        requestCount: 1730
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1317
          verb: list
        requestCount: 1317
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      nodeName: 10.21.102.12
      requestCount: 3086
    - byUser:
      - byVerb:
        - requestCount: 1682
          verb: list
        requestCount: 1682
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 59
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 71
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 2946
    requestCount: 8860
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 95
          verb: list
        requestCount: 95
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      nodeName: 10.21.102.12
      requestCount: 96
    requestCount: 96
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1523
          verb: list
        requestCount: 1523
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 885
          verb: list
        requestCount: 885
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 64
          verb: watch
        requestCount: 64
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 2
          verb: watch
        requestCount: 2
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.11
      requestCount: 2539
    - byUser:
      - byVerb:
        - requestCount: 1514
          verb: list
        requestCount: 1514
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 2607
    - byUser:
      - byVerb:
        - requestCount: 1538
          verb: list
        requestCount: 1538
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 58
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 70
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      - byVerb:
        - requestCount: 1
          verb: patch
        requestCount: 1
        userAgent: OpenAPI-Generator/25.3.0/python
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 2753
    requestCount: 7899
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1678
          verb: list
        requestCount: 1678
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: Mozilla/5.0
        username: L9334
      nodeName: 10.21.102.11
      requestCount: 2635
    - byUser:
      - byVerb:
        - requestCount: 1727
          verb: list
        requestCount: 1727
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1091
          verb: list
        requestCount: 1091
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 2
          verb: update
        requestCount: 2
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:statefulset-controller
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: Mozilla/5.0
        username: Y8269
      nodeName: 10.21.102.12
      requestCount: 2854
    - byUser:
      - byVerb:
        - requestCount: 1613
          verb: list
        requestCount: 1613
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1003
          verb: list
        requestCount: 1003
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 55
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 67
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: Mozilla/5.0
        username: Z1741
      nodeName: 10.21.102.9
      requestCount: 2765
    requestCount: 8254
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1575
          verb: list
        requestCount: 1575
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1049
          verb: list
        requestCount: 1049
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 62
          verb: watch
        requestCount: 62
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.11
      requestCount: 2750
    - byUser:
      - byVerb:
        - requestCount: 1526
          verb: list
        requestCount: 1526
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 926
          verb: list
        requestCount: 926
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      nodeName: 10.21.102.12
      requestCount: 2484
    - byUser:
      - byVerb:
        - requestCount: 1538
          verb: list
        requestCount: 1538
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 55
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 67
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      nodeName: 10.21.102.9
      requestCount: 2985
    requestCount: 8219
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1315
          verb: list
        requestCount: 1315
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1003
          verb: list
        requestCount: 1003
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: L9334
      nodeName: 10.21.102.11
      requestCount: 2448
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1266
          verb: list
        requestCount: 1266
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: Mozilla/5.0
        username: L9334
      nodeName: 10.21.102.12
      requestCount: 2598
    - byUser:
      - byVerb:
        - requestCount: 1327
          verb: list
        requestCount: 1327
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 767
          verb: list
        requestCount: 767
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 57
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 69
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.9
      requestCount: 2243
    requestCount: 7289
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1835
          verb: list
        requestCount: 1835
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1239
          verb: list
        requestCount: 1239
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 64
          verb: watch
        requestCount: 64
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.11
      requestCount: 3201
    - byUser:
      - byVerb:
        - requestCount: 1848
          verb: list
        requestCount: 1848
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 801
          verb: list
        requestCount: 801
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      nodeName: 10.21.102.12
      requestCount: 2682
    - byUser:
      - byVerb:
        - requestCount: 1753
          verb: list
        requestCount: 1753
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1336
          verb: list
        requestCount: 1336
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 57
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 69
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.9
      requestCount: 3236
    requestCount: 9119
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1613
          verb: list
        requestCount: 1613
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 914
          verb: list
        requestCount: 914
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 62
          verb: watch
        requestCount: 62
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: Z8504
      nodeName: 10.21.102.11
      requestCount: 2654
    - byUser:
      - byVerb:
        - requestCount: 1517
          verb: list
        requestCount: 1517
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 885
          verb: list
        requestCount: 885
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: Mozilla/5.0
        username: L9334
      nodeName: 10.21.102.12
      requestCount: 2436
    - byUser:
      - byVerb:
        - requestCount: 1566
          verb: list
        requestCount: 1566
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1475
          verb: list
        requestCount: 1475
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 55
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 67
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      nodeName: 10.21.102.9
      requestCount: 3188
    requestCount: 8278
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1686
          verb: list
        requestCount: 1686
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 67
          verb: watch
        requestCount: 67
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.21.102.11
      requestCount: 2642
    - byUser:
      - byVerb:
        - requestCount: 1691
          verb: list
        requestCount: 1691
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 6
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      nodeName: 10.21.102.12
      requestCount: 2790
    - byUser:
      - byVerb:
        - requestCount: 1721
          verb: list
        requestCount: 1721
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1357
          verb: list
        requestCount: 1357
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 54
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 66
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      nodeName: 10.21.102.9
      requestCount: 3223
    requestCount: 8655
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1791
          verb: list
        requestCount: 1791
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 63
          verb: watch
        requestCount: 63
        userAgent: manager/v0.0.0
        username: system:serviceaccount:confluent-operator:confluent-for-kubernetes
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v2.12.0
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 5
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      nodeName: 10.21.102.11
      requestCount: 2750
    - byUser:
      - byVerb:
        - requestCount: 1895
          verb: list
        requestCount: 1895
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1239
          verb: list
        requestCount: 1239
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: update
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:statefulset-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kubernetes-sensor/v0.0.0
        username: system:serviceaccount:stackrox:sensor
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:github-arc:github-arc-actions-runner-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: oc/4.17.0
        username: J8683
      nodeName: 10.21.102.12
      requestCount: 3183
    - byUser:
      - byVerb:
        - requestCount: 1795
          verb: list
        requestCount: 1795
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1236
          verb: list
        requestCount: 1236
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 72
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 29
          verb: watch
        requestCount: 30
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: service-controller/v0.0.0
        username: system:serviceaccount:ptc-tunnel-skupper:skupper-service-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.29.11+148a389
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: ansible-operator/v0.0.0
        username: system:serviceaccount:aap:aap-gateway-operator-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Mozilla/5.0
        username: L9334
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: oc/4.17.0
        username: J8683
      nodeName: 10.21.102.9
      requestCount: 3171
    requestCount: 9104
  requestCount: 184521
