---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2023-12-19T12:55:17Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2023-12-19T12:55:17Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:03:50Z"
  name: openshiftapiservers.v1.operator.openshift.io
  resourceVersion: "*********"
  uid: d7cb7d35-267a-41ab-bd40-7092e1949733
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    requestCount: 0
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 4
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 21
    - byUser:
      - byVerb:
        - requestCount: 994
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 1010
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 1044
    requestCount: 1069
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 5
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 22
    - byUser:
      - byVerb:
        - requestCount: 887
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 902
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 936
    requestCount: 963
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 6
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 23
    - byUser:
      - byVerb:
        - requestCount: 905
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 922
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 954
    requestCount: 983
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 6
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 22
    - byUser:
      - byVerb:
        - requestCount: 886
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 902
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 7
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 14
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 941
    requestCount: 969
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 11
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.21.102.12
      requestCount: 22
    - byUser:
      - byVerb:
        - requestCount: 938
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 956
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 985
    requestCount: 1018
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 6
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 24
    - byUser:
      - byVerb:
        - requestCount: 1000
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 1016
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 1049
    requestCount: 1079
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 21
    - byUser:
      - byVerb:
        - requestCount: 934
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 951
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 982
    requestCount: 1011
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 11
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 20
    - byUser:
      - byVerb:
        - requestCount: 1068
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 1084
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 1116
    requestCount: 1147
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 12
          verb: list
        requestCount: 12
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.21.102.12
      requestCount: 28
    - byUser:
      - byVerb:
        - requestCount: 1066
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 1081
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 1110
    requestCount: 1141
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 20
    - byUser:
      - byVerb:
        - requestCount: 993
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 1011
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 1046
    requestCount: 1074
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 4
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 24
    - byUser:
      - byVerb:
        - requestCount: 984
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 1001
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 1033
    requestCount: 1061
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 18
    - byUser:
      - byVerb:
        - requestCount: 1146
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 1162
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 3
          verb: watch
        requestCount: 4
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 1199
    requestCount: 1226
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 6
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.21.102.12
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 1210
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 1224
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 1267
    requestCount: 1289
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 5
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 1266
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 1283
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 1325
    requestCount: 1345
  - byNode:
    - nodeName: 10.21.102.11
      requestCount: 0
    requestCount: 0
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 20
    - byUser:
      - byVerb:
        - requestCount: 1148
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 1166
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 1200
    requestCount: 1227
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 4
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 24
    - byUser:
      - byVerb:
        - requestCount: 1142
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 1160
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 1191
    requestCount: 1219
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 1
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: list
        requestCount: 9
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.21.102.12
      requestCount: 24
    - byUser:
      - byVerb:
        - requestCount: 1278
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 1294
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 1331
    requestCount: 1356
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 5
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 25
    - byUser:
      - byVerb:
        - requestCount: 1176
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 1192
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 1225
    requestCount: 1255
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 23
    - byUser:
      - byVerb:
        - requestCount: 1199
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 1216
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 1247
    requestCount: 1277
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 24
    - byUser:
      - byVerb:
        - requestCount: 983
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 998
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 1034
    requestCount: 1061
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 19
    - byUser:
      - byVerb:
        - requestCount: 938
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 954
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.9
      requestCount: 985
    requestCount: 1012
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.11
      requestCount: 6
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      nodeName: 10.21.102.12
      requestCount: 20
    - byUser:
      - byVerb:
        - requestCount: 944
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 960
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.21.102.9
      requestCount: 997
    requestCount: 1023
  requestCount: 24805
