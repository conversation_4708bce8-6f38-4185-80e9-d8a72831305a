---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2023-12-19T12:54:42Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2023-12-19T12:54:42Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:05:55Z"
  name: servicemonitors.v1.monitoring.coreos.com
  resourceVersion: "*********"
  uid: 00e9230b-1ba2-4b35-b8d2-dc5008d882db
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: get
        - requestCount: 54
          verb: list
        requestCount: 59
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 5
          verb: patch
        requestCount: 5
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.21.102.12
      requestCount: 64
    - byUser:
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 43
          verb: list
        requestCount: 45
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 42
          verb: get
        requestCount: 42
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 26
          verb: get
        requestCount: 26
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 7
          verb: get
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 9
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: get
        requestCount: 7
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      nodeName: 10.21.102.9
      requestCount: 137
    requestCount: 201
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 39
          verb: get
        - requestCount: 831
          verb: list
        requestCount: 870
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 84
          verb: delete
        - requestCount: 168
          verb: get
        - requestCount: 14
          verb: list
        - requestCount: 168
          verb: update
        requestCount: 434
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      nodeName: 10.21.102.11
      requestCount: 1320
    - byUser:
      - byVerb:
        - requestCount: 45
          verb: get
        - requestCount: 761
          verb: list
        requestCount: 806
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 90
          verb: patch
        requestCount: 90
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 904
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: get
        - requestCount: 766
          verb: list
        requestCount: 802
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 630
          verb: get
        requestCount: 630
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 318
          verb: get
        requestCount: 318
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 77
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 89
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 77
          verb: get
        requestCount: 77
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      nodeName: 10.21.102.9
      requestCount: 2018
    requestCount: 4242
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: get
        - requestCount: 878
          verb: list
        requestCount: 921
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 90
          verb: delete
        - requestCount: 180
          verb: get
        - requestCount: 15
          verb: list
        - requestCount: 180
          verb: update
        requestCount: 465
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: dns-operator/v0.0.0
        username: system:serviceaccount:openshift-dns-operator:dns-operator
      nodeName: 10.21.102.11
      requestCount: 1404
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: get
        - requestCount: 838
          verb: list
        requestCount: 871
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 85
          verb: patch
        requestCount: 85
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 964
    - byUser:
      - byVerb:
        - requestCount: 42
          verb: get
        - requestCount: 880
          verb: list
        requestCount: 922
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 588
          verb: get
        requestCount: 588
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 268
          verb: get
        requestCount: 268
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 76
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 88
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 2046
    requestCount: 4414
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: get
        - requestCount: 878
          verb: list
        requestCount: 911
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 72
          verb: delete
        - requestCount: 144
          verb: get
        - requestCount: 12
          verb: list
        - requestCount: 144
          verb: update
        requestCount: 372
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 10
          verb: get
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 2
          verb: get
        requestCount: 2
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      nodeName: 10.21.102.11
      requestCount: 1310
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: get
        - requestCount: 833
          verb: list
        requestCount: 876
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 90
          verb: patch
        requestCount: 90
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 974
    - byUser:
      - byVerb:
        - requestCount: 44
          verb: get
        - requestCount: 833
          verb: list
        requestCount: 877
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 592
          verb: get
        requestCount: 592
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 274
          verb: get
        requestCount: 274
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 76
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 88
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 2012
    requestCount: 4296
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 34
          verb: get
        - requestCount: 1005
          verb: list
        requestCount: 1039
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 90
          verb: delete
        - requestCount: 180
          verb: get
        - requestCount: 15
          verb: list
        - requestCount: 180
          verb: update
        requestCount: 465
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      nodeName: 10.21.102.11
      requestCount: 1520
    - byUser:
      - byVerb:
        - requestCount: 46
          verb: get
        - requestCount: 969
          verb: list
        requestCount: 1015
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 90
          verb: patch
        requestCount: 90
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 1112
    - byUser:
      - byVerb:
        - requestCount: 39
          verb: get
        - requestCount: 968
          verb: list
        requestCount: 1007
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 626
          verb: get
        requestCount: 626
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 284
          verb: get
        requestCount: 284
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 77
          verb: get
        - requestCount: 7
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 91
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      nodeName: 10.21.102.9
      requestCount: 2187
    requestCount: 4819
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 42
          verb: get
        - requestCount: 918
          verb: list
        requestCount: 960
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 72
          verb: delete
        - requestCount: 144
          verb: get
        - requestCount: 12
          verb: list
        - requestCount: 144
          verb: update
        requestCount: 372
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: dns-operator/v0.0.0
        username: system:serviceaccount:openshift-dns-operator:dns-operator
      nodeName: 10.21.102.11
      requestCount: 1350
    - byUser:
      - byVerb:
        - requestCount: 35
          verb: get
        - requestCount: 975
          verb: list
        requestCount: 1010
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 85
          verb: patch
        requestCount: 85
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 1104
    - byUser:
      - byVerb:
        - requestCount: 44
          verb: get
        - requestCount: 960
          verb: list
        requestCount: 1004
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 588
          verb: get
        requestCount: 588
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 272
          verb: get
        requestCount: 272
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 79
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 91
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      nodeName: 10.21.102.9
      requestCount: 2135
    requestCount: 4589
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: get
        - requestCount: 983
          verb: list
        requestCount: 1023
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 84
          verb: delete
        - requestCount: 168
          verb: get
        - requestCount: 14
          verb: list
        - requestCount: 168
          verb: update
        requestCount: 434
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      nodeName: 10.21.102.11
      requestCount: 1475
    - byUser:
      - byVerb:
        - requestCount: 34
          verb: get
        - requestCount: 976
          verb: list
        requestCount: 1010
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 90
          verb: patch
        requestCount: 90
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 1108
    - byUser:
      - byVerb:
        - requestCount: 46
          verb: get
        - requestCount: 989
          verb: list
        requestCount: 1035
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 605
          verb: get
        requestCount: 605
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 260
          verb: get
        requestCount: 260
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 79
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 91
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 2173
    requestCount: 4756
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: get
        - requestCount: 811
          verb: list
        requestCount: 847
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 13
          verb: list
        - requestCount: 156
          verb: update
        requestCount: 403
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: dns-operator/v0.0.0
        username: system:serviceaccount:openshift-dns-operator:dns-operator
      nodeName: 10.21.102.11
      requestCount: 1268
    - byUser:
      - byVerb:
        - requestCount: 44
          verb: get
        - requestCount: 825
          verb: list
        requestCount: 869
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 86
          verb: patch
        requestCount: 86
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 962
    - byUser:
      - byVerb:
        - requestCount: 39
          verb: get
        - requestCount: 860
          verb: list
        requestCount: 899
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 613
          verb: get
        requestCount: 613
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 262
          verb: get
        requestCount: 262
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 77
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 89
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      nodeName: 10.21.102.9
      requestCount: 2042
    requestCount: 4272
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: get
        - requestCount: 916
          verb: list
        requestCount: 948
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 90
          verb: delete
        - requestCount: 180
          verb: get
        - requestCount: 15
          verb: list
        - requestCount: 180
          verb: update
        requestCount: 465
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.21.102.11
      requestCount: 1429
    - byUser:
      - byVerb:
        - requestCount: 45
          verb: get
        - requestCount: 857
          verb: list
        requestCount: 902
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 89
          verb: patch
        requestCount: 89
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 999
    - byUser:
      - byVerb:
        - requestCount: 42
          verb: get
        - requestCount: 876
          verb: list
        requestCount: 918
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 588
          verb: get
        requestCount: 588
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 282
          verb: get
        requestCount: 282
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 79
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 91
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      nodeName: 10.21.102.9
      requestCount: 2059
    requestCount: 4487
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: get
        - requestCount: 860
          verb: list
        requestCount: 903
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 84
          verb: delete
        - requestCount: 168
          verb: get
        - requestCount: 14
          verb: list
        - requestCount: 168
          verb: update
        requestCount: 434
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      nodeName: 10.21.102.11
      requestCount: 1352
    - byUser:
      - byVerb:
        - requestCount: 41
          verb: get
        - requestCount: 816
          verb: list
        requestCount: 857
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 90
          verb: patch
        requestCount: 90
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 955
    - byUser:
      - byVerb:
        - requestCount: 35
          verb: get
        - requestCount: 845
          verb: list
        requestCount: 880
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 602
          verb: get
        requestCount: 602
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 270
          verb: get
        requestCount: 270
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 75
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 87
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 2018
    requestCount: 4325
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 41
          verb: get
        - requestCount: 912
          verb: list
        requestCount: 953
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 90
          verb: delete
        - requestCount: 180
          verb: get
        - requestCount: 15
          verb: list
        - requestCount: 180
          verb: update
        requestCount: 465
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 2
          verb: get
        requestCount: 2
        userAgent: dns-operator/v0.0.0
        username: system:serviceaccount:openshift-dns-operator:dns-operator
      nodeName: 10.21.102.11
      requestCount: 1436
    - byUser:
      - byVerb:
        - requestCount: 46
          verb: get
        - requestCount: 900
          verb: list
        requestCount: 946
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 85
          verb: patch
        requestCount: 85
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 1039
    - byUser:
      - byVerb:
        - requestCount: 31
          verb: get
        - requestCount: 958
          verb: list
        requestCount: 989
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 616
          verb: get
        requestCount: 616
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 270
          verb: get
        requestCount: 270
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 75
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 87
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 80
          verb: get
        requestCount: 80
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 2146
    requestCount: 4621
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 25
          verb: get
        - requestCount: 770
          verb: list
        requestCount: 795
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 72
          verb: delete
        - requestCount: 144
          verb: get
        - requestCount: 12
          verb: list
        - requestCount: 144
          verb: update
        requestCount: 372
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 2
          verb: get
        requestCount: 2
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: dns-operator/v0.0.0
        username: system:serviceaccount:openshift-dns-operator:dns-operator
      nodeName: 10.21.102.11
      requestCount: 1188
    - byUser:
      - byVerb:
        - requestCount: 41
          verb: get
        - requestCount: 825
          verb: list
        requestCount: 866
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 90
          verb: patch
        requestCount: 90
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 965
    - byUser:
      - byVerb:
        - requestCount: 52
          verb: get
        - requestCount: 746
          verb: list
        requestCount: 798
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 588
          verb: get
        requestCount: 588
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 266
          verb: get
        requestCount: 266
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 90
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      nodeName: 10.21.102.9
      requestCount: 1923
    requestCount: 4076
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 39
          verb: get
        - requestCount: 939
          verb: list
        requestCount: 978
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 84
          verb: delete
        - requestCount: 168
          verb: get
        - requestCount: 14
          verb: list
        - requestCount: 168
          verb: update
        requestCount: 434
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 2
          verb: get
        requestCount: 2
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      nodeName: 10.21.102.11
      requestCount: 1429
    - byUser:
      - byVerb:
        - requestCount: 37
          verb: get
        - requestCount: 911
          verb: list
        requestCount: 948
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 87
          verb: patch
        requestCount: 87
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 1040
    - byUser:
      - byVerb:
        - requestCount: 38
          verb: get
        - requestCount: 969
          verb: list
        requestCount: 1007
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 594
          verb: get
        requestCount: 594
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 256
          verb: get
        requestCount: 256
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 84
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:serviceaccount:kube-system:generic-garbage-collector
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      nodeName: 10.21.102.9
      requestCount: 2169
    requestCount: 4638
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 37
          verb: get
        - requestCount: 1111
          verb: list
        requestCount: 1148
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 90
          verb: delete
        - requestCount: 180
          verb: get
        - requestCount: 15
          verb: list
        - requestCount: 180
          verb: update
        requestCount: 465
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      nodeName: 10.21.102.11
      requestCount: 1630
    - byUser:
      - byVerb:
        - requestCount: 34
          verb: get
        - requestCount: 1076
          verb: list
        requestCount: 1110
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 85
          verb: patch
        requestCount: 85
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.21.102.12
      requestCount: 1195
    - byUser:
      - byVerb:
        - requestCount: 37
          verb: get
        - requestCount: 1115
          verb: list
        requestCount: 1152
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 624
          verb: get
        requestCount: 624
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 266
          verb: get
        requestCount: 266
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 81
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 93
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 77
          verb: get
        requestCount: 77
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 2322
    requestCount: 5147
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 44
          verb: get
        - requestCount: 837
          verb: list
        requestCount: 881
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 77
          verb: delete
        - requestCount: 150
          verb: get
        - requestCount: 13
          verb: list
        - requestCount: 150
          verb: update
        requestCount: 390
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 23
          verb: list
        requestCount: 23
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-6mxgs:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 2
          verb: get
        requestCount: 2
        userAgent: dns-operator/v0.0.0
        username: system:serviceaccount:openshift-dns-operator:dns-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      nodeName: 10.21.102.11
      requestCount: 1312
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: get
        - requestCount: 834
          verb: list
        requestCount: 867
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 90
          verb: patch
        requestCount: 90
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 2
          verb: get
        requestCount: 2
        userAgent: rhacs-operator/v4.8.2
        username: system:serviceaccount:rhacs-operator:rhacs-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 959
    - byUser:
      - byVerb:
        - requestCount: 41
          verb: get
        - requestCount: 826
          verb: list
        requestCount: 867
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 588
          verb: get
        requestCount: 588
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 272
          verb: get
        requestCount: 272
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 76
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 88
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 76
          verb: get
        requestCount: 76
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 2001
    requestCount: 4272
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: get
        - requestCount: 54
          verb: list
        requestCount: 59
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 5
          verb: patch
        requestCount: 5
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.21.102.12
      requestCount: 64
    - byUser:
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 43
          verb: list
        requestCount: 45
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 42
          verb: get
        requestCount: 42
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 26
          verb: get
        requestCount: 26
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 7
          verb: get
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 9
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: get
        requestCount: 7
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      nodeName: 10.21.102.9
      requestCount: 137
    requestCount: 201
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 922
          verb: list
        requestCount: 922
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 13
          verb: list
        - requestCount: 156
          verb: update
        requestCount: 403
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: dns-operator/v0.0.0
        username: system:serviceaccount:openshift-dns-operator:dns-operator
      nodeName: 10.21.102.11
      requestCount: 1343
    - byUser:
      - byVerb:
        - requestCount: 889
          verb: list
        requestCount: 889
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 85
          verb: patch
        requestCount: 85
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 982
    - byUser:
      - byVerb:
        - requestCount: 954
          verb: list
        requestCount: 954
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 588
          verb: get
        requestCount: 588
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 288
          verb: get
        requestCount: 288
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 90
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      nodeName: 10.21.102.9
      requestCount: 2101
    requestCount: 4426
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 753
          verb: list
        requestCount: 777
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 84
          verb: delete
        - requestCount: 168
          verb: get
        - requestCount: 14
          verb: list
        - requestCount: 168
          verb: update
        requestCount: 434
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      nodeName: 10.21.102.11
      requestCount: 1232
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: get
        - requestCount: 749
          verb: list
        requestCount: 781
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 90
          verb: patch
        requestCount: 90
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 879
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: get
        - requestCount: 718
          verb: list
        requestCount: 748
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 588
          verb: get
        requestCount: 588
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 260
          verb: get
        requestCount: 260
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 76
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 88
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 77
          verb: get
        requestCount: 77
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      nodeName: 10.21.102.9
      requestCount: 1863
    requestCount: 3974
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 48
          verb: get
        - requestCount: 893
          verb: list
        requestCount: 941
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 84
          verb: delete
        - requestCount: 168
          verb: get
        - requestCount: 14
          verb: list
        - requestCount: 168
          verb: update
        requestCount: 434
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: dns-operator/v0.0.0
        username: system:serviceaccount:openshift-dns-operator:dns-operator
      nodeName: 10.21.102.11
      requestCount: 1394
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: get
        - requestCount: 917
          verb: list
        requestCount: 957
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 85
          verb: patch
        requestCount: 85
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 1051
    - byUser:
      - byVerb:
        - requestCount: 31
          verb: get
        - requestCount: 961
          verb: list
        requestCount: 992
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 630
          verb: get
        requestCount: 630
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 266
          verb: get
        requestCount: 266
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 77
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 89
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      nodeName: 10.21.102.9
      requestCount: 2157
    requestCount: 4602
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 35
          verb: get
        - requestCount: 833
          verb: list
        requestCount: 868
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 13
          verb: list
        - requestCount: 156
          verb: update
        requestCount: 403
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: dns-operator/v0.0.0
        username: system:serviceaccount:openshift-dns-operator:dns-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      nodeName: 10.21.102.11
      requestCount: 1290
    - byUser:
      - byVerb:
        - requestCount: 46
          verb: get
        - requestCount: 829
          verb: list
        requestCount: 875
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 90
          verb: patch
        requestCount: 90
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 2
          verb: get
        requestCount: 2
        userAgent: rhacs-operator/v4.8.2
        username: system:serviceaccount:rhacs-operator:rhacs-operator-controller-manager
      nodeName: 10.21.102.12
      requestCount: 975
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: get
        - requestCount: 812
          verb: list
        requestCount: 848
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 504
          verb: get
        requestCount: 504
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 238
          verb: get
        requestCount: 238
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 66
          verb: get
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 76
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 73
          verb: get
        requestCount: 73
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 68
          verb: get
        requestCount: 68
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      nodeName: 10.21.102.9
      requestCount: 1826
    requestCount: 4091
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 41
          verb: get
        - requestCount: 872
          verb: list
        requestCount: 913
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 84
          verb: delete
        - requestCount: 168
          verb: get
        - requestCount: 14
          verb: list
        - requestCount: 168
          verb: update
        requestCount: 434
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      nodeName: 10.21.102.11
      requestCount: 1364
    - byUser:
      - byVerb:
        - requestCount: 47
          verb: get
        - requestCount: 861
          verb: list
        requestCount: 908
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 85
          verb: patch
        requestCount: 85
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 1002
    - byUser:
      - byVerb:
        - requestCount: 41
          verb: get
        - requestCount: 905
          verb: list
        requestCount: 946
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 588
          verb: get
        requestCount: 588
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 282
          verb: get
        requestCount: 282
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 79
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 91
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 77
          verb: get
        requestCount: 77
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      nodeName: 10.21.102.9
      requestCount: 2085
    requestCount: 4451
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 49
          verb: get
        - requestCount: 924
          verb: list
        requestCount: 973
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 90
          verb: delete
        - requestCount: 180
          verb: get
        - requestCount: 15
          verb: list
        - requestCount: 180
          verb: update
        requestCount: 465
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.21.102.11
      requestCount: 1454
    - byUser:
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 859
          verb: list
        requestCount: 885
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 85
          verb: patch
        requestCount: 85
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 977
    - byUser:
      - byVerb:
        - requestCount: 53
          verb: get
        - requestCount: 898
          verb: list
        requestCount: 951
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 630
          verb: get
        requestCount: 630
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 282
          verb: get
        requestCount: 282
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 90
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 80
          verb: get
        requestCount: 80
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 2134
    requestCount: 4565
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 35
          verb: get
        - requestCount: 822
          verb: list
        requestCount: 857
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 13
          verb: list
        - requestCount: 156
          verb: update
        requestCount: 403
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: dns-operator/v0.0.0
        username: system:serviceaccount:openshift-dns-operator:dns-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      nodeName: 10.21.102.11
      requestCount: 1277
    - byUser:
      - byVerb:
        - requestCount: 46
          verb: get
        - requestCount: 834
          verb: list
        requestCount: 880
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 90
          verb: patch
        requestCount: 90
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 978
    - byUser:
      - byVerb:
        - requestCount: 39
          verb: get
        - requestCount: 813
          verb: list
        requestCount: 852
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 588
          verb: get
        requestCount: 588
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 272
          verb: get
        requestCount: 272
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 75
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 87
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 77
          verb: get
        requestCount: 77
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      nodeName: 10.21.102.9
      requestCount: 1976
    requestCount: 4231
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: get
        - requestCount: 823
          verb: list
        requestCount: 866
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 84
          verb: delete
        - requestCount: 168
          verb: get
        - requestCount: 14
          verb: list
        - requestCount: 168
          verb: update
        requestCount: 434
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: PrometheusOperator/0.73.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      nodeName: 10.21.102.11
      requestCount: 1318
    - byUser:
      - byVerb:
        - requestCount: 41
          verb: get
        - requestCount: 826
          verb: list
        requestCount: 867
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 85
          verb: patch
        requestCount: 85
        userAgent: network-operator/4.16.0-************.p0.gb5eb4ae.assembly.stream.el9-b5eb4ae
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.29.11+148a389
        username: system:kube-controller-manager
      nodeName: 10.21.102.12
      requestCount: 960
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: get
        - requestCount: 832
          verb: list
        requestCount: 868
        userAgent: kubectl/v1.18.10
        username: system:serviceaccount:default:z-devops
      - byVerb:
        - requestCount: 588
          verb: get
        requestCount: 588
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 264
          verb: get
        requestCount: 264
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 85
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.21.102.9
      requestCount: 1986
    requestCount: 4264
  requestCount: 97759
