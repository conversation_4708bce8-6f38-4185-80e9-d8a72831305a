---
apiVersion: certificates.k8s.io/v1
kind: CertificateSigningRequest
metadata:
  creationTimestamp: "2025-08-22T12:43:44Z"
  generateName: csr-
  managedFields:
  - apiVersion: certificates.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:generateName: {}
    manager: ocpazp001-4zlm9-worker-eastus1-km2pd
    operation: Update
    time: "2025-08-22T12:43:44Z"
  - apiVersion: certificates.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:certificate: {}
    manager: kube-controller-manager
    operation: Update
    subresource: status
    time: "2025-08-22T12:43:45Z"
  - apiVersion: certificates.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:conditions:
          .: {}
          k:{"type":"Approved"}:
            .: {}
            f:lastTransitionTime: {}
            f:lastUpdateTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: ovnkube-identity
    operation: Update
    subresource: approval
    time: "2025-08-22T12:43:45Z"
  name: csr-c99w8
  resourceVersion: "769986640"
  uid: 893d5319-e998-4240-a5de-0c99c593d122
spec:
  expirationSeconds: 86400
  groups:
  - system:ovn-nodes
  - system:authenticated
  request: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURSBSRVFVRVNULS0tLS0KTUlJQkZUQ0J2QUlCQURCYU1Sa3dGd1lEVlFRS0V4QnplWE4wWlcwNmIzWnVMVzV2WkdWek1UMHdPd1lEVlFRRApFelJ6ZVhOMFpXMDZiM1p1TFc1dlpHVTZiMk53WVhwd01EQXhMVFI2YkcwNUxYZHZjbXRsY2kxbFlYTjBkWE14CkxXdHRNbkJrTUZrd0V3WUhLb1pJemowQ0FRWUlLb1pJemowREFRY0RRZ0FFNmRwbE82Um1tR2hYTk05YXhQalgKeXFrWEVQWCtBMlRoN0pieU9tUTRpcmNiWkRKVTQ2SHJJSlRneTlPTVF4UkpBMHErZitLTjRFV1l2Nk5vVVhVOQpWS0FBTUFvR0NDcUdTTTQ5QkFNQ0EwZ0FNRVVDSUVnaCtDMHpWRWhKS3V0ZlY2L2cwZDh0bm9Bam1VK2h6THlXClEwWmZQNkd3QWlFQWhydU1lb3RwNUhGNTNucHMvUXlaMkFiaTl3MlUwMm5odjFhdXdoelYxems9Ci0tLS0tRU5EIENFUlRJRklDQVRFIFJFUVVFU1QtLS0tLQo=
  signerName: kubernetes.io/kube-apiserver-client
  usages:
  - digital signature
  - client auth
  username: system:ovn-node:ocpazp001-4zlm9-worker-eastus1-km2pd
status:
  certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUNsVENDQVgyZ0F3SUJBZ0lRRXM5RHRpaEZPUjJwL09JYU5NWVdsakFOQmdrcWhraUc5dzBCQVFzRkFEQW0KTVNRd0lnWURWUVFEREJ0cmRXSmxMV056Y2kxemFXZHVaWEpmUURFM05UVXdNamc0T1Rjd0hoY05NalV3T0RJeQpNVEl6T0RRMVdoY05NalV3T0RJek1USXpPRFExV2pCYU1Sa3dGd1lEVlFRS0V4QnplWE4wWlcwNmIzWnVMVzV2ClpHVnpNVDB3T3dZRFZRUURFelJ6ZVhOMFpXMDZiM1p1TFc1dlpHVTZiMk53WVhwd01EQXhMVFI2YkcwNUxYZHYKY210bGNpMWxZWE4wZFhNeExXdHRNbkJrTUZrd0V3WUhLb1pJemowQ0FRWUlLb1pJemowREFRY0RRZ0FFNmRwbApPNlJtbUdoWE5NOWF4UGpYeXFrWEVQWCtBMlRoN0pieU9tUTRpcmNiWkRKVTQ2SHJJSlRneTlPTVF4UkpBMHErCmYrS040RVdZdjZOb1VYVTlWS05XTUZRd0RnWURWUjBQQVFIL0JBUURBZ2VBTUJNR0ExVWRKUVFNTUFvR0NDc0cKQVFVRkJ3TUNNQXdHQTFVZEV3RUIvd1FDTUFBd0h3WURWUjBqQkJnd0ZvQVVZaDNsSW5MNzBoVWhuLzR3Q2c5WApHVnN1cDNFd0RRWUpLb1pJaHZjTkFRRUxCUUFEZ2dFQkFEbGtIN1dyb0ZHUEpNVEdtVkswbFBPSGpySnJhNm1HCkxXV3YzdGs4dDRmZkE3OHF1ZVMwQXIwY0JWNmNGcjJaTnErMXI5VE9KZkRUamZUcXBZc29JUlMxa0tVeWY1VVUKYTNOMU9sK1lLdEsyaEp1WTJlL1VDbTZsQjNHMWUyZXFFN1pNUXBvK3ZZcUdqNXMwYTJad08vdTdSaXNBZkkvbwpNa0VWcWhlNXZVaEJGNm0wOUlaYzIzMmt6RTAvZ2lPQTdmZkt1WkhsT2F5ME5wQWJ3RnhER1Y2ZmpQV05QRnh2ClU4Wng3SWlaLzNmbHV3YUJlN2tERGRNTVY1Z2VvU2ZnbE0wSUJwRW1rdTlpMWtMS3hRaUMrc2tRV3Z1WDMzblgKMGM5RmlvdmVkVEtlR2FQVlZFSC8ydDIrNUFneE96QWM4MktEazRibk5HUHExK0FhZXhUbGxHVT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
  conditions:
  - lastTransitionTime: "2025-08-22T12:43:45Z"
    lastUpdateTime: "2025-08-22T12:43:45Z"
    message: Auto-approved CSR "csr-c99w8"
    reason: AutoApproved
    status: "True"
    type: Approved
