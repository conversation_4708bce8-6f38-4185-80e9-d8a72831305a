---
apiVersion: certificates.k8s.io/v1
kind: CertificateSigningRequest
metadata:
  creationTimestamp: "2025-08-22T12:49:31Z"
  generateName: csr-
  managedFields:
  - apiVersion: certificates.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:certificate: {}
    manager: kube-controller-manager
    operation: Update
    subresource: status
    time: "2025-08-22T12:49:31Z"
  - apiVersion: certificates.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:generateName: {}
    manager: multus-daemon
    operation: Update
    time: "2025-08-22T12:49:31Z"
  - apiVersion: certificates.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:conditions:
          .: {}
          k:{"type":"Approved"}:
            .: {}
            f:lastTransitionTime: {}
            f:lastUpdateTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: ovnkube-identity
    operation: Update
    subresource: approval
    time: "2025-08-22T12:49:31Z"
  name: csr-7ltsf
  resourceVersion: "769995352"
  uid: 3a848e4f-a693-410b-aa22-431f614e2df9
spec:
  expirationSeconds: 86400
  groups:
  - system:multus
  - system:authenticated
  request: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURSBSRVFVRVNULS0tLS0KTUlJQkR6Q0J0d0lCQURCVk1SWXdGQVlEVlFRS0V3MXplWE4wWlcwNmJYVnNkSFZ6TVRzd09RWURWUVFERXpKegplWE4wWlcwNmJYVnNkSFZ6T205amNHRjZjREF3TVMwMGVteHRPUzEzYjNKclpYSXRaV0Z6ZEhWek15MTRlR2M0Ck9UQlpNQk1HQnlxR1NNNDlBZ0VHQ0NxR1NNNDlBd0VIQTBJQUJNVjZic1NybDVHOG9DVnhxK2ZSNFVZQVRVOTEKVUlicEhWZG94eVZzWXJWbVJlNWJEWTVxMGJ6UHpwb2xhazZOQzE5R25iMXFJdGx6T2J3V3dFdCtzNXVnQURBSwpCZ2dxaGtqT1BRUURBZ05IQURCRUFpQm5EL2ZDbmpyYUF3MHNrUEp2Zmk1S242eGIwTkZYMVJOUUpTTjBwaWwyClJ3SWdGc3hpQzIvK3NuRGxYMGg5U0NIZWFKSW5RL3NsVTlLQk5BRnhsV1dnZ0Y0PQotLS0tLUVORCBDRVJUSUZJQ0FURSBSRVFVRVNULS0tLS0K
  signerName: kubernetes.io/kube-apiserver-client
  usages:
  - digital signature
  - client auth
  username: system:multus:ocpazp001-4zlm9-worker-eastus3-xxg89
status:
  certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUNrVENDQVhtZ0F3SUJBZ0lSQVBpQVVkcDVEUS9Md3FuSU9pc2dtQTB3RFFZSktvWklodmNOQVFFTEJRQXcKSmpFa01DSUdBMVVFQXd3YmEzVmlaUzFqYzNJdGMybG5ibVZ5WDBBeE56VTFNREk0T0RrM01CNFhEVEkxTURneQpNakV5TkRRek1Wb1hEVEkxTURneU16RXlORFF6TVZvd1ZURVdNQlFHQTFVRUNoTU5jM2x6ZEdWdE9tMTFiSFIxCmN6RTdNRGtHQTFVRUF4TXljM2x6ZEdWdE9tMTFiSFIxY3pwdlkzQmhlbkF3TURFdE5IcHNiVGt0ZDI5eWEyVnkKTFdWaGMzUjFjek10ZUhobk9Ea3dXVEFUQmdjcWhrak9QUUlCQmdncWhrak9QUU1CQndOQ0FBVEZlbTdFcTVlUgp2S0FsY2F2bjBlRkdBRTFQZFZDRzZSMVhhTWNsYkdLMVprWHVXdzJPYXRHOHo4NmFKV3BPalF0ZlJwMjlhaUxaCmN6bThGc0JMZnJPYm8xWXdWREFPQmdOVkhROEJBZjhFQkFNQ0I0QXdFd1lEVlIwbEJBd3dDZ1lJS3dZQkJRVUgKQXdJd0RBWURWUjBUQVFIL0JBSXdBREFmQmdOVkhTTUVHREFXZ0JSaUhlVWljdnZTRlNHZi9qQUtEMWNaV3k2bgpjVEFOQmdrcWhraUc5dzBCQVFzRkFBT0NBUUVBajNVN2w0TUx3M2ZKWUdENE1oTWR2YWFDb1k2ZEJPUzRXZUUrCnRMd2pQMGtva0FMbUNWMTVEZlJCWXlpSlpOV2RVMUJJai9oRkRwR1EyNnFLbDdZWUt1dXFjeXJZNWk2OEx5cVkKOEtNOWN2bEZLaUpsSlFXWlZaMEZZc09OVDNjWkhaVEg0SWFDb0FOTDZBZUNncWE0cHdxODZwRkRhQWlpZk5wUQphR0NLc29EaFp1MlJJOWhHNVhIeGVrSkFoZ1paaDBhZEpMU2ZkbnRLMnFTL0x1bzQ4SiswR2NIVWROaytiaTBLCm9uQzZ3UWM5UkhMTTVjaVp1V0RFNFRWQXdCcnJzWXV5WnNzeDR3MC9jSE4vYWNnQWg5VGRwUjMrSmgrV0RRS1IKejZsajMzZGxGSk5VRXUyRkpnQSt1cjNGUTBkYW5pd3FRdld0Ti85VU15MWlpL1ZzeFE9PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
  conditions:
  - lastTransitionTime: "2025-08-22T12:49:31Z"
    lastUpdateTime: "2025-08-22T12:49:31Z"
    message: Auto-approved CSR "csr-7ltsf"
    reason: AutoApproved
    status: "True"
    type: Approved
