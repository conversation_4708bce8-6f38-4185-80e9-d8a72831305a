---
apiVersion: certificates.k8s.io/v1
kind: CertificateSigningRequest
metadata:
  creationTimestamp: "2025-08-22T13:16:39Z"
  generateName: csr-
  managedFields:
  - apiVersion: certificates.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:certificate: {}
    manager: kube-controller-manager
    operation: Update
    subresource: status
    time: "2025-08-22T13:16:39Z"
  - apiVersion: certificates.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:generateName: {}
    manager: ocpazp001-4zlm9-worker-eastus3-xxg89
    operation: Update
    time: "2025-08-22T13:16:39Z"
  - apiVersion: certificates.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:conditions:
          .: {}
          k:{"type":"Approved"}:
            .: {}
            f:lastTransitionTime: {}
            f:lastUpdateTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: ovnkube-identity
    operation: Update
    subresource: approval
    time: "2025-08-22T13:16:39Z"
  name: csr-k8msb
  resourceVersion: "770037233"
  uid: 5bfba00f-a631-4d27-968d-def0414b8e7e
spec:
  expirationSeconds: 86400
  groups:
  - system:ovn-nodes
  - system:authenticated
  request: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURSBSRVFVRVNULS0tLS0KTUlJQkZqQ0J2QUlCQURCYU1Sa3dGd1lEVlFRS0V4QnplWE4wWlcwNmIzWnVMVzV2WkdWek1UMHdPd1lEVlFRRApFelJ6ZVhOMFpXMDZiM1p1TFc1dlpHVTZiMk53WVhwd01EQXhMVFI2YkcwNUxYZHZjbXRsY2kxbFlYTjBkWE16CkxYaDRaemc1TUZrd0V3WUhLb1pJemowQ0FRWUlLb1pJemowREFRY0RRZ0FFWWMxNVV6WERVK2VmUU80eUtwdnMKejdudFJuTStZcW1tMmZOU1Q4aUVkdkFVekluTm83RWFPQ0lDUnY4UzB5Q0lRbUhPUWJKWGk0TU5QOStiWDc0TgpicUFBTUFvR0NDcUdTTTQ5QkFNQ0Ewa0FNRVlDSVFEQXU2Q256VDNQZnpCTTkwR2xFYjlKdVYzU2phVGJvNjMzCkgyaG9xTTF4VHdJaEFKa1Jnd0lZRWFNKzExdnh6NmFZaG5Pb3BHYWF2M1ZLNk1GVWJ6TDl0ZjZ1Ci0tLS0tRU5EIENFUlRJRklDQVRFIFJFUVVFU1QtLS0tLQo=
  signerName: kubernetes.io/kube-apiserver-client
  usages:
  - digital signature
  - client auth
  username: system:ovn-node:ocpazp001-4zlm9-worker-eastus3-xxg89
status:
  certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUNsVENDQVgyZ0F3SUJBZ0lRRHpxa3FlMHRocVdDdlZsSnN3RHNFekFOQmdrcWhraUc5dzBCQVFzRkFEQW0KTVNRd0lnWURWUVFEREJ0cmRXSmxMV056Y2kxemFXZHVaWEpmUURFM05UVXdNamc0T1Rjd0hoY05NalV3T0RJeQpNVE14TVRNNVdoY05NalV3T0RJek1UTXhNVE01V2pCYU1Sa3dGd1lEVlFRS0V4QnplWE4wWlcwNmIzWnVMVzV2ClpHVnpNVDB3T3dZRFZRUURFelJ6ZVhOMFpXMDZiM1p1TFc1dlpHVTZiMk53WVhwd01EQXhMVFI2YkcwNUxYZHYKY210bGNpMWxZWE4wZFhNekxYaDRaemc1TUZrd0V3WUhLb1pJemowQ0FRWUlLb1pJemowREFRY0RRZ0FFWWMxNQpVelhEVStlZlFPNHlLcHZzejdudFJuTStZcW1tMmZOU1Q4aUVkdkFVekluTm83RWFPQ0lDUnY4UzB5Q0lRbUhPClFiSlhpNE1OUDkrYlg3NE5icU5XTUZRd0RnWURWUjBQQVFIL0JBUURBZ2VBTUJNR0ExVWRKUVFNTUFvR0NDc0cKQVFVRkJ3TUNNQXdHQTFVZEV3RUIvd1FDTUFBd0h3WURWUjBqQkJnd0ZvQVVZaDNsSW5MNzBoVWhuLzR3Q2c5WApHVnN1cDNFd0RRWUpLb1pJaHZjTkFRRUxCUUFEZ2dFQkFDUHh2S2lBZFlhQnd0Szc1MklMNlg5NGRCczF5WmsyCkRMT3BoUjR3RzBkT0VhZGgrK3phcDE1MEZSSHlvNS83VXRnU0h3RWZHTW9RYWExY1ZDK0RjVXJicUQycTJqdkYKZVROSkFlYWhyMUtJbUdhcXFGUnEyT2FrczMrd0kyY3gvdGhYa3lOU3Y2TDBNNmRURDZlU2U4Umg0U0hON2FFdApOaVpsbW1XU2MrdU4rN1EzWmdMcGhKcXI0MjdadWc0bFZQeTd2ZVRaR0VENGZkZ2t3ZkNWMTFnYWQwUTBZNDlSCkdEQThRWnptMmZMN2paWmN6aEd3UzJMbGF6K284VDg1THNLanpvM2NhMnF3UnFqS1dXVndDaitOUFNyRFRTWkEKaUlEa1ZveGI5NW5aTkZLOWN0OU9pSDg3WXVOYitacm5PRFI0cDU5ei82Q2haWmNqVlVrdE8zOD0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
  conditions:
  - lastTransitionTime: "2025-08-22T13:16:39Z"
    lastUpdateTime: "2025-08-22T13:16:39Z"
    message: Auto-approved CSR "csr-k8msb"
    reason: AutoApproved
    status: "True"
    type: Approved
