---
apiVersion: certificates.k8s.io/v1
kind: CertificateSigningRequest
metadata:
  creationTimestamp: "2025-08-22T13:05:03Z"
  generateName: csr-
  managedFields:
  - apiVersion: certificates.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:certificate: {}
    manager: kube-controller-manager
    operation: Update
    subresource: status
    time: "2025-08-22T13:05:03Z"
  - apiVersion: certificates.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:generateName: {}
    manager: kubelet
    operation: Update
    time: "2025-08-22T13:05:03Z"
  - apiVersion: certificates.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:conditions:
          .: {}
          k:{"type":"Approved"}:
            .: {}
            f:lastTransitionTime: {}
            f:lastUpdateTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: machine-approver
    operation: Update
    subresource: approval
    time: "2025-08-22T13:05:03Z"
  name: csr-pqfvf
  resourceVersion: "770019301"
  uid: 77f8bfb3-da0a-4bc6-9fb1-81fd8819f7ea
spec:
  groups:
  - system:nodes
  - system:authenticated
  request: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURSBSRVFVRVNULS0tLS0KTUlJQlZEQ0IvQUlCQURCU01SVXdFd1lEVlFRS0V3eHplWE4wWlcwNmJtOWtaWE14T1RBM0JnTlZCQU1UTUhONQpjM1JsYlRwdWIyUmxPbTlqY0dGNmNEQXdNUzAwZW14dE9TMTNiM0pyWlhJdFpXRnpkSFZ6TVMxa1oydHhkREJaCk1CTUdCeXFHU000OUFnRUdDQ3FHU000OUF3RUhBMElBQk5iME5NVzFGaFF3dEpwdVNyTmpWNldHaCs0MU1nZ3IKeE15NDhPUXBhOUFaL1BVNW1ZWXJaWURLYks1OStkaDdNeG8zTkdmZENMOFVuU0I5elBWS3lNZWdTREJHQmdrcQpoa2lHOXcwQkNRNHhPVEEzTURVR0ExVWRFUVF1TUN5Q0pHOWpjR0Y2Y0RBd01TMDBlbXh0T1MxM2IzSnJaWEl0ClpXRnpkSFZ6TVMxa1oydHhkSWNFQ2hWbVhEQUtCZ2dxaGtqT1BRUURBZ05IQURCRUFpQldnV0FjaXI0K1R6aEoKeTIwZGIzL0srZU9oRm9yQ2prSDFFVjJEbVdveXl3SWdKdU1TYU8wMG5xWEh1N0QrRVQ3a0o0QXU2cytYZVpFbwpEYmllejh0OTJoND0KLS0tLS1FTkQgQ0VSVElGSUNBVEUgUkVRVUVTVC0tLS0tCg==
  signerName: kubernetes.io/kubelet-serving
  usages:
  - digital signature
  - server auth
  username: system:node:ocpazp001-4zlm9-worker-eastus1-dgkqt
status:
  certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUN4ekNDQWErZ0F3SUJBZ0lSQU9XTE5neXhqM1VIelI2cUl5NndxTEl3RFFZSktvWklodmNOQVFFTEJRQXcKSmpFa01DSUdBMVVFQXd3YmEzVmlaUzFqYzNJdGMybG5ibVZ5WDBBeE56VTFNREk0T0RrM01CNFhEVEkxTURneQpNakV6TURBd00xb1hEVEkxTURreE1USXdNREV6TjFvd1VqRVZNQk1HQTFVRUNoTU1jM2x6ZEdWdE9tNXZaR1Z6Ck1Ua3dOd1lEVlFRREV6QnplWE4wWlcwNmJtOWtaVHB2WTNCaGVuQXdNREV0Tkhwc2JUa3RkMjl5YTJWeUxXVmgKYzNSMWN6RXRaR2RyY1hRd1dUQVRCZ2NxaGtqT1BRSUJCZ2dxaGtqT1BRTUJCd05DQUFUVzlEVEZ0UllVTUxTYQpia3F6WTFlbGhvZnVOVElJSzhUTXVQRGtLV3ZRR2Z6MU9abUdLMldBeW15dWZmblllek1hTnpSbjNRaS9GSjBnCmZjejFTc2pIbzRHT01JR0xNQTRHQTFVZER3RUIvd1FFQXdJSGdEQVRCZ05WSFNVRUREQUtCZ2dyQmdFRkJRY0QKQVRBTUJnTlZIUk1CQWY4RUFqQUFNQjhHQTFVZEl3UVlNQmFBRkdJZDVTSnkrOUlWSVovK01Bb1BWeGxiTHFkeApNRFVHQTFVZEVRUXVNQ3lDSkc5amNHRjZjREF3TVMwMGVteHRPUzEzYjNKclpYSXRaV0Z6ZEhWek1TMWtaMnR4CmRJY0VDaFZtWERBTkJna3Foa2lHOXcwQkFRc0ZBQU9DQVFFQWtEbEZOaFRLNGU1RGkvNHFSVzFicnQxaHJkcUQKSGV2akRaZEJCbGd6L0szVy9JWDdPUlBRcVo5TDZMSUhuR1p5ME0rR1BUMm01anljdHVnY0k3ZFZadWloamNLdAozbVBwODc3enhhVUF1MVFjcG9nV1NaWE9tOHdWOTN2ZWFoQjJmeU52TDM0cVhpQ0ZZMU9GTjVBWmVjYStsbitwClRQTWVucjZPUHh1aWFETDBOU0lPQXd4OENvSmczYWl1WHAyRGthaW0yWXUxd0dTS2RiQmRQbUNwQTRpVFg0U0QKeHM0M2ordjlIUHVHZ1F5Z2tPcVQ0WTlFYXRlblBXR1VKb0V1NU1WMlQzdmZka3AwaWJMNS9RRHk5OFc1NmFPawpRRlJ2cGdwWFNJclpBZGs4eERiWTVQRExLNHdYeFlpMW8xVU15bi9CMDVDZzA2QmYzaENLZ0Q3SzJ3PT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
  conditions:
  - lastTransitionTime: "2025-08-22T13:05:03Z"
    lastUpdateTime: "2025-08-22T13:05:03Z"
    message: This CSR was approved by the Node CSR Approver (cluster-machine-approver)
    reason: NodeCSRApprove
    status: "True"
    type: Approved
