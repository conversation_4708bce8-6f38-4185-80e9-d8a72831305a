---
apiVersion: certificates.k8s.io/v1
kind: CertificateSigningRequest
metadata:
  creationTimestamp: "2025-08-22T13:39:08Z"
  generateName: csr-
  managedFields:
  - apiVersion: certificates.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:certificate: {}
    manager: kube-controller-manager
    operation: Update
    subresource: status
    time: "2025-08-22T13:39:08Z"
  - apiVersion: certificates.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:generateName: {}
    manager: multus-daemon
    operation: Update
    time: "2025-08-22T13:39:08Z"
  - apiVersion: certificates.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:conditions:
          .: {}
          k:{"type":"Approved"}:
            .: {}
            f:lastTransitionTime: {}
            f:lastUpdateTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: ovnkube-identity
    operation: Update
    subresource: approval
    time: "2025-08-22T13:39:08Z"
  name: csr-4sbvv
  resourceVersion: "770071999"
  uid: c19597ba-e06e-4496-b27c-14bfc1d8de8b
spec:
  expirationSeconds: 86400
  groups:
  - system:multus
  - system:authenticated
  request: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURSBSRVFVRVNULS0tLS0KTUlJQkVEQ0J0d0lCQURCVk1SWXdGQVlEVlFRS0V3MXplWE4wWlcwNmJYVnNkSFZ6TVRzd09RWURWUVFERXpKegplWE4wWlcwNmJYVnNkSFZ6T205amNHRjZjREF3TVMwMGVteHRPUzEzYjNKclpYSXRaV0Z6ZEhWek15MXRibnB1CmRqQlpNQk1HQnlxR1NNNDlBZ0VHQ0NxR1NNNDlBd0VIQTBJQUJDVFBCNG5UZFprcW9xVWdNb3k4dGlzYlMxamwKSGprbk5ZV1pIcktSSmoxMTZVVHp5czdhMlhwL2lpTTVXd0R1Nm8wNkR5MWtxTHZmTFl4VmVsd21OSXVnQURBSwpCZ2dxaGtqT1BRUURBZ05JQURCRkFpRUEvcnhrRklmbjB6S1hyLzNhSzRBRHdmYWliNnBuTjlXRHIxeEd2alJqCmY3QUNJRmxRV3BVNVhGTVR1NGVqeTFvVTlKQ3d5Zm9CSFhtaUJjYXRjbmZCZ2diNAotLS0tLUVORCBDRVJUSUZJQ0FURSBSRVFVRVNULS0tLS0K
  signerName: kubernetes.io/kube-apiserver-client
  usages:
  - digital signature
  - client auth
  username: system:multus:ocpazp001-4zlm9-worker-eastus3-mnznv
status:
  certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUNrVENDQVhtZ0F3SUJBZ0lSQU5LWWcxSnlkY1IrYyswbVcxQ0E4SnN3RFFZSktvWklodmNOQVFFTEJRQXcKSmpFa01DSUdBMVVFQXd3YmEzVmlaUzFqYzNJdGMybG5ibVZ5WDBBeE56VTFNREk0T0RrM01CNFhEVEkxTURneQpNakV6TXpRd09Gb1hEVEkxTURneU16RXpNelF3T0Zvd1ZURVdNQlFHQTFVRUNoTU5jM2x6ZEdWdE9tMTFiSFIxCmN6RTdNRGtHQTFVRUF4TXljM2x6ZEdWdE9tMTFiSFIxY3pwdlkzQmhlbkF3TURFdE5IcHNiVGt0ZDI5eWEyVnkKTFdWaGMzUjFjek10Ylc1NmJuWXdXVEFUQmdjcWhrak9QUUlCQmdncWhrak9QUU1CQndOQ0FBUWt6d2VKMDNXWgpLcUtsSURLTXZMWXJHMHRZNVI0NUp6V0ZtUjZ5a1NZOWRlbEU4OHJPMnRsNmY0b2pPVnNBN3VxTk9nOHRaS2k3CjN5Mk1WWHBjSmpTTG8xWXdWREFPQmdOVkhROEJBZjhFQkFNQ0I0QXdFd1lEVlIwbEJBd3dDZ1lJS3dZQkJRVUgKQXdJd0RBWURWUjBUQVFIL0JBSXdBREFmQmdOVkhTTUVHREFXZ0JSaUhlVWljdnZTRlNHZi9qQUtEMWNaV3k2bgpjVEFOQmdrcWhraUc5dzBCQVFzRkFBT0NBUUVBWnVyVTNWeWtDVjAwL0V4TDBacHpVK2RvenQ4MXB6YlEzRG9WCjJWSDlXc0t6MUVGU1UreHlRUk9xcnVidk9EL3J6VlhKdkZmZVFUL1ZEVTA1R0hwMEJyOGtNaEcxY24reHYrYm0KY1JxVFRwcmhMUlVGU05QQkMvSHdUc2RERGtVQ1dvUENWRGhNSk9JNkNyWmcxazhlMVVzY24ySzljbnp6a2FvYwpkblE2M0tUelp5blc0TDBwaWdXaFEvK200RTVaaWorVVAxNVhMdmFnMnJCaVNvNWp6RFdTZGR4bmgyWHRGVFVJCk9RbGpUY1hpc1c3bzlqdFdicHpKV3cvM2FBMnZoVGpKYkxFMTZiYWsxNWpsOEVhait0YmJ6djZqWUFOaTRIcmwKbjF4U3A2Q29ZSTFRRFhzTWZmNXZ1VHBzMjhWVkVoTy9WSm50UDhNdGlabERJQVBMQXc9PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
  conditions:
  - lastTransitionTime: "2025-08-22T13:39:08Z"
    lastUpdateTime: "2025-08-22T13:39:08Z"
    message: Auto-approved CSR "csr-4sbvv"
    reason: AutoApproved
    status: "True"
    type: Approved
