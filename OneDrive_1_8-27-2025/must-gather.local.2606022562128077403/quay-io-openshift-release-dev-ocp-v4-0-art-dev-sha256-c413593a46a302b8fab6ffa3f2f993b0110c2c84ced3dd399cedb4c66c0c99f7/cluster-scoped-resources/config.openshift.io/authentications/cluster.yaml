---
apiVersion: config.openshift.io/v1
kind: Authentication
metadata:
  annotations:
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    release.openshift.io/create-only: "true"
  creationTimestamp: "2023-12-19T12:47:23Z"
  generation: 2
  managedFields:
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
          f:release.openshift.io/create-only: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"b988498a-9c9a-412a-bc50-6ae3ddd669f8"}: {}
      f:spec: {}
    manager: cluster-version-operator
    operation: Update
    time: "2023-12-19T12:47:23Z"
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        f:oauthMetadata:
          .: {}
          f:name: {}
        f:serviceAccountIssuer: {}
        f:type: {}
        f:webhookTokenAuthenticator:
          .: {}
          f:kubeConfig:
            .: {}
            f:name: {}
    manager: authentication-operator
    operation: Update
    time: "2023-12-19T12:52:00Z"
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:integratedOAuthMetadata:
          .: {}
          f:name: {}
    manager: authentication-operator
    operation: Update
    subresource: status
    time: "2023-12-19T12:56:36Z"
  name: cluster
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    kind: ClusterVersion
    name: version
    uid: b988498a-9c9a-412a-bc50-6ae3ddd669f8
  resourceVersion: "22635"
  uid: 2b026091-5a91-4638-ac9c-8338fa485467
spec:
  oauthMetadata:
    name: ""
  serviceAccountIssuer: ""
  type: ""
  webhookTokenAuthenticator:
    kubeConfig:
      name: webhook-authentication-integrated-oauth
status:
  integratedOAuthMetadata:
    name: oauth-openshift
