---
apiVersion: config.openshift.io/v1
kind: ClusterOperator
metadata:
  annotations:
    capability.openshift.io/name: OperatorLifecycleManager
    include.release.openshift.io/hypershift: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
  creationTimestamp: "2023-12-19T12:47:18Z"
  generation: 1
  managedFields:
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:extension: {}
    manager: cluster-version-operator
    operation: Update
    subresource: status
    time: "2023-12-19T12:47:18Z"
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:conditions: {}
        f:relatedObjects: {}
        f:versions: {}
    manager: catalog
    operation: Update
    subresource: status
    time: "2025-07-15T12:47:55Z"
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:capability.openshift.io/name: {}
          f:include.release.openshift.io/hypershift: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"b988498a-9c9a-412a-bc50-6ae3ddd669f8"}: {}
      f:spec: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-07-15T13:33:33Z"
  name: operator-lifecycle-manager-catalog
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: b988498a-9c9a-412a-bc50-6ae3ddd669f8
  resourceVersion: "691270338"
  uid: 79c2b5ef-e3ff-4e70-a65e-0f6c214c678e
spec: {}
status:
  conditions:
  - lastTransitionTime: "2023-12-19T12:50:45Z"
    status: "False"
    type: Degraded
  - lastTransitionTime: "2025-07-15T12:47:55Z"
    message: Deployed 4.16.0-************.p0.g48d68f5.assembly.stream.el9-48d68f5
    status: "False"
    type: Progressing
  - lastTransitionTime: "2023-12-19T12:50:45Z"
    status: "True"
    type: Available
  - lastTransitionTime: "2023-12-19T12:50:45Z"
    status: "True"
    type: Upgradeable
  extension: null
  relatedObjects:
  - group: ""
    name: openshift-operator-lifecycle-manager
    resource: namespaces
  versions:
  - name: operator
    version: 4.16.36
  - name: operator-lifecycle-manager
    version: 4.16.0-************.p0.g48d68f5.assembly.stream.el9-48d68f5
