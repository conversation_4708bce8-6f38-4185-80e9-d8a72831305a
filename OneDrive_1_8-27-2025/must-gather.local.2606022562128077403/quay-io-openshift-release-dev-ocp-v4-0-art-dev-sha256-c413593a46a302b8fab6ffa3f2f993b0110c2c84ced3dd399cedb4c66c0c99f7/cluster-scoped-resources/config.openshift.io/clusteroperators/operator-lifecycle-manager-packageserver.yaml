---
apiVersion: config.openshift.io/v1
kind: ClusterOperator
metadata:
  annotations:
    capability.openshift.io/name: OperatorLifecycleManager
    include.release.openshift.io/hypershift: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
  creationTimestamp: "2023-12-19T12:47:18Z"
  generation: 1
  managedFields:
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:extension: {}
    manager: cluster-version-operator
    operation: Update
    subresource: status
    time: "2023-12-19T12:47:18Z"
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:conditions: {}
        f:relatedObjects: {}
        f:versions: {}
    manager: olm
    operation: Update
    subresource: status
    time: "2025-07-15T12:47:19Z"
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:capability.openshift.io/name: {}
          f:include.release.openshift.io/hypershift: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"b988498a-9c9a-412a-bc50-6ae3ddd669f8"}: {}
      f:spec: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-07-15T13:33:34Z"
  name: operator-lifecycle-manager-packageserver
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: b988498a-9c9a-412a-bc50-6ae3ddd669f8
  resourceVersion: "691270342"
  uid: aa48dba6-e746-40f2-b0b4-1d8072f0feea
spec: {}
status:
  conditions:
  - lastTransitionTime: "2023-12-19T12:50:46Z"
    status: "False"
    type: Degraded
  - lastTransitionTime: "2025-06-08T10:27:47Z"
    message: ClusterServiceVersion openshift-operator-lifecycle-manager/packageserver
      observed in phase Succeeded
    reason: ClusterServiceVersionSucceeded
    status: "True"
    type: Available
  - lastTransitionTime: "2025-07-15T12:47:19Z"
    message: Deployed version 0.0.1-snapshot
    status: "False"
    type: Progressing
  - lastTransitionTime: "2023-12-19T12:50:46Z"
    message: Safe to upgrade
    status: "True"
    type: Upgradeable
  extension: null
  relatedObjects:
  - group: ""
    name: openshift-operator-lifecycle-manager
    resource: namespaces
  - group: operators.coreos.com
    name: packageserver
    namespace: openshift-operator-lifecycle-manager
    resource: clusterserviceversions
  versions:
  - name: operator
    version: 4.16.36
  - name: packageserver
    version: 0.0.1-snapshot
