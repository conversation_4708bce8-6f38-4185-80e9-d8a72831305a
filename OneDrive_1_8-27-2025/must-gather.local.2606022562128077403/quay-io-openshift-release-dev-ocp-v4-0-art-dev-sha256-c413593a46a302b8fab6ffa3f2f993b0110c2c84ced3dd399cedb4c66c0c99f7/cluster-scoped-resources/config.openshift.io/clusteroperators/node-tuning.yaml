---
apiVersion: config.openshift.io/v1
kind: ClusterOperator
metadata:
  annotations:
    capability.openshift.io/name: NodeTuning
    include.release.openshift.io/hypershift: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2023-12-19T12:47:17Z"
  generation: 1
  managedFields:
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:extension: {}
    manager: cluster-version-operator
    operation: Update
    subresource: status
    time: "2023-12-19T12:47:17Z"
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:capability.openshift.io/name: {}
          f:include.release.openshift.io/hypershift: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"b988498a-9c9a-412a-bc50-6ae3ddd669f8"}: {}
      f:spec: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-07-15T13:33:17Z"
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:conditions: {}
        f:relatedObjects: {}
        f:versions: {}
    manager: cluster-node-tuning-operator
    operation: Update
    subresource: status
    time: "2025-08-16T04:21:32Z"
  name: node-tuning
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: b988498a-9c9a-412a-bc50-6ae3ddd669f8
  resourceVersion: "756337522"
  uid: a05b32fe-dfbf-4df1-8ac4-03a0035fdb6f
spec: {}
status:
  conditions:
  - lastTransitionTime: "2025-07-15T19:22:04Z"
    message: Cluster has deployed 33/33 "4.16.36" operands
    reason: AsExpected
    status: "True"
    type: Available
  - lastTransitionTime: "2025-08-16T04:21:32Z"
    message: Cluster version is "4.16.36"
    reason: AsExpected
    status: "False"
    type: Progressing
  - lastTransitionTime: "2023-12-19T12:51:01Z"
    message: DaemonSet "tuned" available
    reason: AsExpected
    status: "False"
    type: Degraded
  extension: null
  relatedObjects:
  - group: ""
    name: openshift-cluster-node-tuning-operator
    resource: namespaces
  - group: tuned.openshift.io
    name: ""
    namespace: openshift-cluster-node-tuning-operator
    resource: profiles
  - group: tuned.openshift.io
    name: ""
    namespace: openshift-cluster-node-tuning-operator
    resource: tuneds
  - group: apps
    name: tuned
    namespace: openshift-cluster-node-tuning-operator
    resource: daemonsets
  - group: performance.openshift.io
    name: ""
    resource: performanceprofiles
  versions:
  - name: operator
    version: 4.16.36
  - name: openshift-tuned
    version: 4.16.36
