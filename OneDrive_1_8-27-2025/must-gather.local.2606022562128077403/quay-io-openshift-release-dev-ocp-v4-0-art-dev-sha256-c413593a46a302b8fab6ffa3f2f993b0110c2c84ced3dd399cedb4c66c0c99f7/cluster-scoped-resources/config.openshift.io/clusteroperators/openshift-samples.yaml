---
apiVersion: config.openshift.io/v1
kind: ClusterOperator
metadata:
  annotations:
    capability.openshift.io/name: openshift-samples
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
  creationTimestamp: "2023-12-19T12:47:17Z"
  generation: 1
  managedFields:
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:extension: {}
        f:relatedObjects: {}
    manager: cluster-version-operator
    operation: Update
    subresource: status
    time: "2023-12-19T12:47:17Z"
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:capability.openshift.io/name: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"b988498a-9c9a-412a-bc50-6ae3ddd669f8"}: {}
      f:spec: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-01-31T01:20:18Z"
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:conditions: {}
        f:versions: {}
    manager: cluster-samples-operator
    operation: Update
    subresource: status
    time: "2025-08-21T17:02:00Z"
  name: openshift-samples
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: b988498a-9c9a-412a-bc50-6ae3ddd669f8
  resourceVersion: "768203782"
  uid: 09ac99ee-0619-4460-9016-6194a4c358ff
spec: {}
status:
  conditions:
  - lastTransitionTime: "2023-12-19T12:56:10Z"
    status: "False"
    type: Degraded
  - lastTransitionTime: "2025-07-15T12:46:49Z"
    message: Samples installation successful at 4.16.36
    status: "True"
    type: Available
  - lastTransitionTime: "2025-08-21T17:02:00Z"
    message: Samples installation successful at 4.16.36
    status: "False"
    type: Progressing
  extension: null
  relatedObjects:
  - group: samples.operator.openshift.io
    name: cluster
    resource: configs
  - group: ""
    name: openshift-cluster-samples-operator
    resource: namespaces
  - group: template.openshift.io
    name: ""
    namespace: openshift
    resource: templates
  - group: image.openshift.io
    name: ""
    namespace: openshift
    resource: imagestreams
  versions:
  - name: operator
    version: 4.16.36
