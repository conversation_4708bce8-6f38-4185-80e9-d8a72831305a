---
apiVersion: config.openshift.io/v1
kind: ClusterOperator
metadata:
  annotations:
    capability.openshift.io/name: Storage
    include.release.openshift.io/hypershift: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2023-12-19T12:47:17Z"
  generation: 1
  managedFields:
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:extension: {}
    manager: cluster-version-operator
    operation: Update
    subresource: status
    time: "2023-12-19T12:47:18Z"
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:capability.openshift.io/name: {}
          f:include.release.openshift.io/hypershift: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"b988498a-9c9a-412a-bc50-6ae3ddd669f8"}: {}
      f:spec: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-07-15T13:33:27Z"
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:conditions: {}
        f:relatedObjects: {}
        f:versions: {}
    manager: cluster-storage-operator
    operation: Update
    subresource: status
    time: "2025-08-16T04:21:32Z"
  name: storage
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: b988498a-9c9a-412a-bc50-6ae3ddd669f8
  resourceVersion: "756337482"
  uid: a65bd26f-a31d-4a9e-b97f-474b5a9f3db5
spec: {}
status:
  conditions:
  - lastTransitionTime: "2023-12-19T13:25:27Z"
    message: |-
      AzureFileCSIDriverOperatorCRDegraded: All is well
      AzureDiskCSIDriverOperatorCRDegraded: All is well
    reason: AsExpected
    status: "False"
    type: Degraded
  - lastTransitionTime: "2025-08-16T04:21:32Z"
    message: |-
      AzureFileCSIDriverOperatorCRProgressing: All is well
      AzureDiskCSIDriverOperatorCRProgressing: All is well
    reason: AsExpected
    status: "False"
    type: Progressing
  - lastTransitionTime: "2023-12-19T12:51:02Z"
    message: |-
      DefaultStorageClassControllerAvailable: StorageClass provided by supplied CSI Driver instead of the cluster-storage-operator
      AzureFileCSIDriverOperatorCRAvailable: All is well
      AzureDiskCSIDriverOperatorCRAvailable: All is well
    reason: AsExpected
    status: "True"
    type: Available
  - lastTransitionTime: "2023-12-19T12:50:33Z"
    message: All is well
    reason: AsExpected
    status: "True"
    type: Upgradeable
  - lastTransitionTime: "2025-07-15T12:50:04Z"
    reason: NoData
    status: Unknown
    type: EvaluationConditionsDetected
  extension: null
  relatedObjects:
  - group: ""
    name: azure-disk-csi-driver-operator
    namespace: openshift-cluster-csi-drivers
    resource: serviceaccounts
  - group: rbac.authorization.k8s.io
    name: azure-disk-csi-driver-operator-role
    namespace: openshift-cluster-csi-drivers
    resource: roles
  - group: rbac.authorization.k8s.io
    name: azure-disk-csi-driver-operator-clusterrole
    resource: clusterroles
  - group: rbac.authorization.k8s.io
    name: azure-disk-csi-driver-operator-clusterrolebinding
    resource: clusterrolebindings
  - group: rbac.authorization.k8s.io
    name: azure-disk-csi-driver-operator-rolebinding
    namespace: openshift-cluster-csi-drivers
    resource: rolebindings
  - group: operator.openshift.io
    name: disk.csi.azure.com
    resource: clustercsidrivers
  - group: ""
    name: azure-file-csi-driver-operator
    namespace: openshift-cluster-csi-drivers
    resource: serviceaccounts
  - group: rbac.authorization.k8s.io
    name: azure-file-csi-driver-operator-role
    namespace: openshift-cluster-csi-drivers
    resource: roles
  - group: rbac.authorization.k8s.io
    name: azure-file-csi-driver-operator-rolebinding
    namespace: openshift-cluster-csi-drivers
    resource: rolebindings
  - group: rbac.authorization.k8s.io
    name: azure-file-csi-driver-operator-clusterrole
    resource: clusterroles
  - group: rbac.authorization.k8s.io
    name: azure-file-csi-driver-operator-clusterrolebinding
    resource: clusterrolebindings
  - group: operator.openshift.io
    name: file.csi.azure.com
    resource: clustercsidrivers
  - group: ""
    name: openshift-cluster-storage-operator
    resource: namespaces
  - group: ""
    name: openshift-cluster-csi-drivers
    resource: namespaces
  - group: operator.openshift.io
    name: cluster
    resource: storages
  - group: rbac.authorization.k8s.io
    name: cluster-storage-operator-role
    resource: clusterrolebindings
  - group: sharedresource.openshift.io
    name: ""
    resource: sharedconfigmaps
  - group: sharedresource.openshift.io
    name: ""
    resource: sharedsecrets
  versions:
  - name: operator
    version: 4.16.36
