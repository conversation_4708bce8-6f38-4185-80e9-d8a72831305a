---
apiVersion: config.openshift.io/v1
kind: ClusterOperator
metadata:
  annotations:
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    network.operator.openshift.io/last-seen-state: '{"DaemonsetStates":[],"DeploymentStates":[],"StatefulsetStates":[]}'
    network.operator.openshift.io/relatedClusterObjects: ""
  creationTimestamp: "2023-12-19T12:47:17Z"
  generation: 1
  managedFields:
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:extension: {}
    manager: cluster-version-operator
    operation: Update
    subresource: status
    time: "2023-12-19T12:47:17Z"
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:network.operator.openshift.io/relatedClusterObjects: {}
    manager: cluster-network-operator
    operation: Update
    time: "2024-06-28T22:44:39Z"
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"b988498a-9c9a-412a-bc50-6ae3ddd669f8"}: {}
      f:spec: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-01-31T01:19:56Z"
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:network.operator.openshift.io/last-seen-state: {}
    manager: network-operator
    operation: Update
    time: "2025-08-16T04:21:33Z"
  - apiVersion: config.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:conditions: {}
        f:relatedObjects: {}
        f:versions: {}
    manager: network-operator
    operation: Update
    subresource: status
    time: "2025-08-22T11:28:46Z"
  name: network
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: b988498a-9c9a-412a-bc50-6ae3ddd669f8
  resourceVersion: "769870876"
  uid: 36c74a8b-9ed7-4606-8d95-493bfdad7fdc
spec: {}
status:
  conditions:
  - lastTransitionTime: "2025-07-21T14:52:21Z"
    status: "False"
    type: Degraded
  - lastTransitionTime: "2023-12-19T12:48:31Z"
    status: "False"
    type: ManagementStateDegraded
  - lastTransitionTime: "2023-12-19T12:48:31Z"
    status: "True"
    type: Upgradeable
  - lastTransitionTime: "2025-08-22T11:28:46Z"
    status: "False"
    type: Progressing
  - lastTransitionTime: "2023-12-19T12:48:37Z"
    status: "True"
    type: Available
  extension: null
  relatedObjects:
  - group: ""
    name: applied-cluster
    namespace: openshift-network-operator
    resource: configmaps
  - group: apiextensions.k8s.io
    name: cloudprivateipconfigs.cloud.network.openshift.io
    resource: customresourcedefinitions
  - group: ""
    name: cloud-network-config-controller
    namespace: openshift-cloud-network-config-controller
    resource: serviceaccounts
  - group: rbac.authorization.k8s.io
    name: cloud-network-config-controller
    resource: clusterroles
  - group: rbac.authorization.k8s.io
    name: cloud-network-config-controller
    namespace: openshift-cloud-network-config-controller
    resource: roles
  - group: rbac.authorization.k8s.io
    name: cloud-network-config-controller
    resource: clusterrolebindings
  - group: rbac.authorization.k8s.io
    name: cloud-network-config-controller-rb
    namespace: openshift-cloud-network-config-controller
    resource: rolebindings
  - group: ""
    name: kube-cloud-config
    namespace: openshift-cloud-network-config-controller
    resource: configmaps
  - group: ""
    name: trusted-ca
    namespace: openshift-cloud-network-config-controller
    resource: configmaps
  - group: apps
    name: cloud-network-config-controller
    namespace: openshift-cloud-network-config-controller
    resource: deployments
  - group: apiextensions.k8s.io
    name: network-attachment-definitions.k8s.cni.cncf.io
    resource: customresourcedefinitions
  - group: apiextensions.k8s.io
    name: ippools.whereabouts.cni.cncf.io
    resource: customresourcedefinitions
  - group: apiextensions.k8s.io
    name: overlappingrangeipreservations.whereabouts.cni.cncf.io
    resource: customresourcedefinitions
  - group: ""
    name: openshift-multus
    resource: namespaces
  - group: rbac.authorization.k8s.io
    name: multus
    resource: clusterroles
  - group: rbac.authorization.k8s.io
    name: multus-ancillary-tools
    resource: clusterroles
  - group: ""
    name: multus
    namespace: openshift-multus
    resource: serviceaccounts
  - group: rbac.authorization.k8s.io
    name: multus-transient
    resource: clusterrolebindings
  - group: rbac.authorization.k8s.io
    name: multus-group
    resource: clusterrolebindings
  - group: ""
    name: multus-ancillary-tools
    namespace: openshift-multus
    resource: serviceaccounts
  - group: rbac.authorization.k8s.io
    name: multus-ancillary-tools
    resource: clusterrolebindings
  - group: rbac.authorization.k8s.io
    name: multus-cluster-readers
    resource: clusterrolebindings
  - group: rbac.authorization.k8s.io
    name: multus-whereabouts
    resource: clusterrolebindings
  - group: rbac.authorization.k8s.io
    name: multus-whereabouts
    namespace: openshift-multus
    resource: rolebindings
  - group: rbac.authorization.k8s.io
    name: whereabouts-cni
    resource: clusterroles
  - group: rbac.authorization.k8s.io
    name: whereabouts-cni
    namespace: openshift-multus
    resource: roles
  - group: rbac.authorization.k8s.io
    name: net-attach-def-project
    resource: clusterroles
  - group: ""
    name: default-cni-sysctl-allowlist
    namespace: openshift-multus
    resource: configmaps
  - group: ""
    name: cni-copy-resources
    namespace: openshift-multus
    resource: configmaps
  - group: ""
    name: multus-daemon-config
    namespace: openshift-multus
    resource: configmaps
  - group: apps
    name: multus
    namespace: openshift-multus
    resource: daemonsets
  - group: apps
    name: multus-additional-cni-plugins
    namespace: openshift-multus
    resource: daemonsets
  - group: ""
    name: metrics-daemon-sa
    namespace: openshift-multus
    resource: serviceaccounts
  - group: rbac.authorization.k8s.io
    name: metrics-daemon-role
    resource: clusterroles
  - group: rbac.authorization.k8s.io
    name: metrics-daemon-sa-rolebinding
    resource: clusterrolebindings
  - group: apps
    name: network-metrics-daemon
    namespace: openshift-multus
    resource: daemonsets
  - group: monitoring.coreos.com
    name: monitor-network
    namespace: openshift-multus
    resource: servicemonitors
  - group: ""
    name: network-metrics-service
    namespace: openshift-multus
    resource: services
  - group: rbac.authorization.k8s.io
    name: prometheus-k8s
    namespace: openshift-multus
    resource: roles
  - group: rbac.authorization.k8s.io
    name: prometheus-k8s
    namespace: openshift-multus
    resource: rolebindings
  - group: ""
    name: multus-admission-controller
    namespace: openshift-multus
    resource: services
  - group: ""
    name: multus-ac
    namespace: openshift-multus
    resource: serviceaccounts
  - group: rbac.authorization.k8s.io
    name: multus-admission-controller-webhook
    resource: clusterroles
  - group: rbac.authorization.k8s.io
    name: multus-admission-controller-webhook
    resource: clusterrolebindings
  - group: admissionregistration.k8s.io
    name: multus.openshift.io
    resource: validatingwebhookconfigurations
  - group: apps
    name: multus-admission-controller
    namespace: openshift-multus
    resource: deployments
  - group: monitoring.coreos.com
    name: monitor-multus-admission-controller
    namespace: openshift-multus
    resource: servicemonitors
  - group: rbac.authorization.k8s.io
    name: prometheus-k8s
    namespace: openshift-multus
    resource: roles
  - group: rbac.authorization.k8s.io
    name: prometheus-k8s
    namespace: openshift-multus
    resource: rolebindings
  - group: monitoring.coreos.com
    name: prometheus-k8s-rules
    namespace: openshift-multus
    resource: prometheusrules
  - group: ""
    name: openshift-ovn-kubernetes
    resource: namespaces
  - group: apiextensions.k8s.io
    name: egressfirewalls.k8s.ovn.org
    resource: customresourcedefinitions
  - group: apiextensions.k8s.io
    name: egressips.k8s.ovn.org
    resource: customresourcedefinitions
  - group: apiextensions.k8s.io
    name: egressqoses.k8s.ovn.org
    resource: customresourcedefinitions
  - group: apiextensions.k8s.io
    name: adminpolicybasedexternalroutes.k8s.ovn.org
    resource: customresourcedefinitions
  - group: apiextensions.k8s.io
    name: egressservices.k8s.ovn.org
    resource: customresourcedefinitions
  - group: apiextensions.k8s.io
    name: adminnetworkpolicies.policy.networking.k8s.io
    resource: customresourcedefinitions
  - group: apiextensions.k8s.io
    name: baselineadminnetworkpolicies.policy.networking.k8s.io
    resource: customresourcedefinitions
  - group: ""
    name: ovn-kubernetes-node
    namespace: openshift-ovn-kubernetes
    resource: serviceaccounts
  - group: rbac.authorization.k8s.io
    name: openshift-ovn-kubernetes-node-limited
    namespace: openshift-ovn-kubernetes
    resource: roles
  - group: rbac.authorization.k8s.io
    name: openshift-ovn-kubernetes-nodes-identity-limited
    namespace: openshift-ovn-kubernetes
    resource: rolebindings
  - group: rbac.authorization.k8s.io
    name: openshift-ovn-kubernetes-node-limited
    resource: clusterroles
  - group: rbac.authorization.k8s.io
    name: openshift-ovn-kubernetes-node-identity-limited
    resource: clusterrolebindings
  - group: rbac.authorization.k8s.io
    name: openshift-ovn-kubernetes-kube-rbac-proxy
    resource: clusterroles
  - group: rbac.authorization.k8s.io
    name: openshift-ovn-kubernetes-node-kube-rbac-proxy
    resource: clusterrolebindings
  - group: ""
    name: ovnkube-config
    namespace: openshift-ovn-kubernetes
    resource: configmaps
  - group: ""
    name: ovn-kubernetes-control-plane
    namespace: openshift-ovn-kubernetes
    resource: serviceaccounts
  - group: rbac.authorization.k8s.io
    name: openshift-ovn-kubernetes-control-plane-limited
    resource: clusterroles
  - group: rbac.authorization.k8s.io
    name: openshift-ovn-kubernetes-control-plane-limited
    resource: clusterrolebindings
  - group: rbac.authorization.k8s.io
    name: openshift-ovn-kubernetes-control-plane-limited
    namespace: openshift-ovn-kubernetes
    resource: roles
  - group: rbac.authorization.k8s.io
    name: openshift-ovn-kubernetes-control-plane-limited
    namespace: openshift-ovn-kubernetes
    resource: rolebindings
  - group: network.operator.openshift.io
    name: ovn
    namespace: openshift-ovn-kubernetes
    resource: operatorpkis
  - group: network.operator.openshift.io
    name: signer
    namespace: openshift-ovn-kubernetes
    resource: operatorpkis
  - group: flowcontrol.apiserver.k8s.io
    name: openshift-ovn-kubernetes
    resource: flowschemas
  - group: rbac.authorization.k8s.io
    name: openshift-ovn-kubernetes-cluster-reader
    resource: clusterroles
  - group: ""
    name: ovnkube-script-lib
    namespace: openshift-ovn-kubernetes
    resource: configmaps
  - group: monitoring.coreos.com
    name: master-rules
    namespace: openshift-ovn-kubernetes
    resource: prometheusrules
  - group: monitoring.coreos.com
    name: networking-rules
    namespace: openshift-ovn-kubernetes
    resource: prometheusrules
  - group: ""
    name: openshift-network-features
    namespace: openshift-config-managed
    resource: configmaps
  - group: monitoring.coreos.com
    name: monitor-ovn-control-plane-metrics
    namespace: openshift-ovn-kubernetes
    resource: servicemonitors
  - group: ""
    name: ovn-kubernetes-control-plane
    namespace: openshift-ovn-kubernetes
    resource: services
  - group: monitoring.coreos.com
    name: monitor-ovn-node
    namespace: openshift-ovn-kubernetes
    resource: servicemonitors
  - group: ""
    name: ovn-kubernetes-node
    namespace: openshift-ovn-kubernetes
    resource: services
  - group: rbac.authorization.k8s.io
    name: prometheus-k8s
    namespace: openshift-ovn-kubernetes
    resource: roles
  - group: rbac.authorization.k8s.io
    name: prometheus-k8s
    namespace: openshift-ovn-kubernetes
    resource: rolebindings
  - group: ""
    name: openshift-host-network
    resource: namespaces
  - group: ""
    name: host-network-namespace-quotas
    namespace: openshift-host-network
    resource: resourcequotas
  - group: apps
    name: ovnkube-control-plane
    namespace: openshift-ovn-kubernetes
    resource: deployments
  - group: apps
    name: ovnkube-node
    namespace: openshift-ovn-kubernetes
    resource: daemonsets
  - group: ""
    name: openshift-network-diagnostics
    resource: namespaces
  - group: ""
    name: network-diagnostics
    namespace: openshift-network-diagnostics
    resource: serviceaccounts
  - group: rbac.authorization.k8s.io
    name: network-diagnostics
    namespace: openshift-network-diagnostics
    resource: roles
  - group: rbac.authorization.k8s.io
    name: network-diagnostics
    namespace: openshift-network-diagnostics
    resource: rolebindings
  - group: rbac.authorization.k8s.io
    name: network-diagnostics
    resource: clusterroles
  - group: rbac.authorization.k8s.io
    name: network-diagnostics
    resource: clusterrolebindings
  - group: rbac.authorization.k8s.io
    name: network-diagnostics
    namespace: kube-system
    resource: rolebindings
  - group: apps
    name: network-check-source
    namespace: openshift-network-diagnostics
    resource: deployments
  - group: ""
    name: network-check-source
    namespace: openshift-network-diagnostics
    resource: services
  - group: monitoring.coreos.com
    name: network-check-source
    namespace: openshift-network-diagnostics
    resource: servicemonitors
  - group: rbac.authorization.k8s.io
    name: prometheus-k8s
    namespace: openshift-network-diagnostics
    resource: roles
  - group: rbac.authorization.k8s.io
    name: prometheus-k8s
    namespace: openshift-network-diagnostics
    resource: rolebindings
  - group: apps
    name: network-check-target
    namespace: openshift-network-diagnostics
    resource: daemonsets
  - group: ""
    name: network-check-target
    namespace: openshift-network-diagnostics
    resource: services
  - group: rbac.authorization.k8s.io
    name: openshift-network-public-role
    namespace: openshift-config-managed
    resource: roles
  - group: rbac.authorization.k8s.io
    name: openshift-network-public-role-binding
    namespace: openshift-config-managed
    resource: rolebindings
  - group: ""
    name: openshift-network-node-identity
    resource: namespaces
  - group: ""
    name: network-node-identity
    namespace: openshift-network-node-identity
    resource: serviceaccounts
  - group: rbac.authorization.k8s.io
    name: network-node-identity
    resource: clusterrolebindings
  - group: rbac.authorization.k8s.io
    name: network-node-identity
    resource: clusterroles
  - group: rbac.authorization.k8s.io
    name: network-node-identity-leases
    namespace: openshift-network-node-identity
    resource: rolebindings
  - group: rbac.authorization.k8s.io
    name: network-node-identity-leases
    namespace: openshift-network-node-identity
    resource: roles
  - group: rbac.authorization.k8s.io
    name: system:openshift:scc:hostnetwork-v2
    namespace: openshift-network-node-identity
    resource: rolebindings
  - group: ""
    name: ovnkube-identity-cm
    namespace: openshift-network-node-identity
    resource: configmaps
  - group: network.operator.openshift.io
    name: network-node-identity
    namespace: openshift-network-node-identity
    resource: operatorpkis
  - group: admissionregistration.k8s.io
    name: network-node-identity.openshift.io
    resource: validatingwebhookconfigurations
  - group: apps
    name: network-node-identity
    namespace: openshift-network-node-identity
    resource: daemonsets
  - group: monitoring.coreos.com
    name: openshift-network-operator-ipsec-rules
    namespace: openshift-network-operator
    resource: prometheusrules
  - group: rbac.authorization.k8s.io
    name: openshift-iptables-alerter
    resource: clusterroles
  - group: ""
    name: iptables-alerter
    namespace: openshift-network-operator
    resource: serviceaccounts
  - group: rbac.authorization.k8s.io
    name: openshift-iptables-alerter
    resource: clusterrolebindings
  - group: ""
    name: iptables-alerter-script
    namespace: openshift-network-operator
    resource: configmaps
  - group: apps
    name: iptables-alerter
    namespace: openshift-network-operator
    resource: daemonsets
  - group: ""
    name: openshift-network-operator
    resource: namespaces
  - group: operator.openshift.io
    name: cluster
    resource: networks
  - group: ""
    name: openshift-cloud-network-config-controller
    resource: namespaces
  versions:
  - name: operator
    version: 4.16.36
