[{"pid": 1, "tid": 1, "process": "systemd", "thread": "systemd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2, "tid": 2, "process": "", "thread": "kthreadd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3, "tid": 3, "process": "", "thread": "rcu_gp", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4, "tid": 4, "process": "", "thread": "rcu_par_gp", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5, "tid": 5, "process": "", "thread": "slub_flushwq", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6, "tid": 6, "process": "", "thread": "netns", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 8, "tid": 8, "process": "", "thread": "kworker/0:0H-events_highpri", "affinity": [0]}, {"pid": 10, "tid": 10, "process": "", "thread": "mm_percpu_wq", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 12, "tid": 12, "process": "", "thread": "rcu_tasks_kthre", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 13, "tid": 13, "process": "", "thread": "rcu_tasks_rude_", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 14, "tid": 14, "process": "", "thread": "rcu_tasks_trace", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 15, "tid": 15, "process": "", "thread": "ksoftirqd/0", "affinity": [0]}, {"pid": 16, "tid": 16, "process": "", "thread": "rcu_preempt", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 17, "tid": 17, "process": "", "thread": "migration/0", "affinity": [0]}, {"pid": 18, "tid": 18, "process": "", "thread": "idle_inject/0", "affinity": [0]}, {"pid": 20, "tid": 20, "process": "", "thread": "cpuhp/0", "affinity": [0]}, {"pid": 21, "tid": 21, "process": "", "thread": "cpuhp/1", "affinity": [1]}, {"pid": 22, "tid": 22, "process": "", "thread": "idle_inject/1", "affinity": [1]}, {"pid": 23, "tid": 23, "process": "", "thread": "migration/1", "affinity": [1]}, {"pid": 24, "tid": 24, "process": "", "thread": "ksoftirqd/1", "affinity": [1]}, {"pid": 26, "tid": 26, "process": "", "thread": "kworker/1:0H-events_highpri", "affinity": [1]}, {"pid": 27, "tid": 27, "process": "", "thread": "cpuhp/2", "affinity": [2]}, {"pid": 28, "tid": 28, "process": "", "thread": "idle_inject/2", "affinity": [2]}, {"pid": 29, "tid": 29, "process": "", "thread": "migration/2", "affinity": [2]}, {"pid": 30, "tid": 30, "process": "", "thread": "ksoftirqd/2", "affinity": [2]}, {"pid": 32, "tid": 32, "process": "", "thread": "kworker/2:0H-events_highpri", "affinity": [2]}, {"pid": 33, "tid": 33, "process": "", "thread": "cpuhp/3", "affinity": [3]}, {"pid": 34, "tid": 34, "process": "", "thread": "idle_inject/3", "affinity": [3]}, {"pid": 35, "tid": 35, "process": "", "thread": "migration/3", "affinity": [3]}, {"pid": 36, "tid": 36, "process": "", "thread": "ksoftirqd/3", "affinity": [3]}, {"pid": 38, "tid": 38, "process": "", "thread": "kworker/3:0H-events_highpri", "affinity": [3]}, {"pid": 39, "tid": 39, "process": "", "thread": "cpuhp/4", "affinity": [4]}, {"pid": 40, "tid": 40, "process": "", "thread": "idle_inject/4", "affinity": [4]}, {"pid": 41, "tid": 41, "process": "", "thread": "migration/4", "affinity": [4]}, {"pid": 42, "tid": 42, "process": "", "thread": "ksoftirqd/4", "affinity": [4]}, {"pid": 44, "tid": 44, "process": "", "thread": "kworker/4:0H-events_highpri", "affinity": [4]}, {"pid": 45, "tid": 45, "process": "", "thread": "cpuhp/5", "affinity": [5]}, {"pid": 46, "tid": 46, "process": "", "thread": "idle_inject/5", "affinity": [5]}, {"pid": 47, "tid": 47, "process": "", "thread": "migration/5", "affinity": [5]}, {"pid": 48, "tid": 48, "process": "", "thread": "ksoftirqd/5", "affinity": [5]}, {"pid": 50, "tid": 50, "process": "", "thread": "kworker/5:0H-events_highpri", "affinity": [5]}, {"pid": 51, "tid": 51, "process": "", "thread": "cpuhp/6", "affinity": [6]}, {"pid": 52, "tid": 52, "process": "", "thread": "idle_inject/6", "affinity": [6]}, {"pid": 53, "tid": 53, "process": "", "thread": "migration/6", "affinity": [6]}, {"pid": 54, "tid": 54, "process": "", "thread": "ksoftirqd/6", "affinity": [6]}, {"pid": 56, "tid": 56, "process": "", "thread": "kworker/6:0H-events_highpri", "affinity": [6]}, {"pid": 57, "tid": 57, "process": "", "thread": "cpuhp/7", "affinity": [7]}, {"pid": 58, "tid": 58, "process": "", "thread": "idle_inject/7", "affinity": [7]}, {"pid": 59, "tid": 59, "process": "", "thread": "migration/7", "affinity": [7]}, {"pid": 60, "tid": 60, "process": "", "thread": "ksoftirqd/7", "affinity": [7]}, {"pid": 70, "tid": 70, "process": "", "thread": "kdevtmpfs", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 71, "tid": 71, "process": "", "thread": "inet_frag_wq", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 72, "tid": 72, "process": "", "thread": "kauditd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 75, "tid": 75, "process": "", "thread": "khungtaskd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 76, "tid": 76, "process": "", "thread": "oom_reaper", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 77, "tid": 77, "process": "", "thread": "writeback", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 78, "tid": 78, "process": "", "thread": "kcompactd0", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 79, "tid": 79, "process": "", "thread": "ksmd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 81, "tid": 81, "process": "", "thread": "khugepaged", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 82, "tid": 82, "process": "", "thread": "cryptd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 83, "tid": 83, "process": "", "thread": "kin<PERSON><PERSON><PERSON>d", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 84, "tid": 84, "process": "", "thread": "kblockd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 85, "tid": 85, "process": "", "thread": "blkcg_punt_bio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 87, "tid": 87, "process": "", "thread": "tpm_dev_wq", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 88, "tid": 88, "process": "", "thread": "md", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 89, "tid": 89, "process": "", "thread": "md_bitmap", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 90, "tid": 90, "process": "", "thread": "edac-poller", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 91, "tid": 91, "process": "", "thread": "watchdogd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 94, "tid": 94, "process": "", "thread": "kworker/3:1H-kblockd", "affinity": [3]}, {"pid": 95, "tid": 95, "process": "", "thread": "kswapd0", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 96, "tid": 96, "process": "", "thread": "kth<PERSON>ld", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 102, "tid": 102, "process": "", "thread": "acpi_thermal_pm", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 103, "tid": 103, "process": "", "thread": "kmpath_rdacd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 104, "tid": 104, "process": "", "thread": "ka<PERSON><PERSON>", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 106, "tid": 106, "process": "", "thread": "mld", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 107, "tid": 107, "process": "", "thread": "kworker/0:1H-xfs-log/sda4", "affinity": [0]}, {"pid": 108, "tid": 108, "process": "", "thread": "ipv6_addrconf", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 118, "tid": 118, "process": "", "thread": "kstrp", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 126, "tid": 126, "process": "", "thread": "zswap-shrink", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 127, "tid": 127, "process": "", "thread": "kworker/u17:0", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 238, "tid": 238, "process": "", "thread": "kworker/1:1H-kblockd", "affinity": [1]}, {"pid": 357, "tid": 357, "process": "", "thread": "kworker/2:1H-kblockd", "affinity": [2]}, {"pid": 359, "tid": 359, "process": "", "thread": "kworker/4:1H-kblockd", "affinity": [4]}, {"pid": 361, "tid": 361, "process": "", "thread": "kworker/6:1H-kblockd", "affinity": [6]}, {"pid": 389, "tid": 389, "process": "", "thread": "kworker/5:1H-kblockd", "affinity": [5]}, {"pid": 467, "tid": 467, "process": "", "thread": "iscsi_conn_clea", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 471, "tid": 471, "process": "", "thread": "tls-strp", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 472, "tid": 472, "process": "", "thread": "cnic_wq", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 473, "tid": 473, "process": "", "thread": "bnx2i_thread/0", "affinity": [0]}, {"pid": 474, "tid": 474, "process": "", "thread": "bnx2i_thread/1", "affinity": [1]}, {"pid": 475, "tid": 475, "process": "", "thread": "bnx2i_thread/2", "affinity": [2]}, {"pid": 476, "tid": 476, "process": "", "thread": "bnx2i_thread/3", "affinity": [3]}, {"pid": 477, "tid": 477, "process": "", "thread": "bnx2i_thread/4", "affinity": [4]}, {"pid": 478, "tid": 478, "process": "", "thread": "bnx2i_thread/5", "affinity": [5]}, {"pid": 479, "tid": 479, "process": "", "thread": "bnx2i_thread/6", "affinity": [6]}, {"pid": 480, "tid": 480, "process": "", "thread": "bnx2i_thread/7", "affinity": [7]}, {"pid": 568, "tid": 568, "process": "", "thread": "kworker/7:1H-kblockd", "affinity": [7]}, {"pid": 662, "tid": 662, "process": "", "thread": "hv_vmbus_con", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 663, "tid": 663, "process": "", "thread": "hv_vmbus_rescin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 664, "tid": 664, "process": "", "thread": "hv_pri_chan", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 665, "tid": 665, "process": "", "thread": "hv_sub_chan", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 666, "tid": 666, "process": "", "thread": "ata_sff", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 668, "tid": 668, "process": "", "thread": "nvme-wq", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 669, "tid": 669, "process": "", "thread": "nvme-reset-wq", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 670, "tid": 670, "process": "", "thread": "nvme-delete-wq", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 672, "tid": 672, "process": "", "thread": "scsi_eh_0", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 673, "tid": 673, "process": "", "thread": "scsi_eh_1", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 674, "tid": 674, "process": "", "thread": "scsi_tmf_0", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 675, "tid": 675, "process": "", "thread": "scsi_eh_2", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 676, "tid": 676, "process": "", "thread": "scsi_tmf_1", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 677, "tid": 677, "process": "", "thread": "scsi_tmf_2", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 678, "tid": 678, "process": "", "thread": "nvme-auth-wq", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 679, "tid": 679, "process": "", "thread": "scsi_eh_3", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 680, "tid": 680, "process": "", "thread": "scsi_tmf_3", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 681, "tid": 681, "process": "", "thread": "scsi_eh_4", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 682, "tid": 682, "process": "", "thread": "scsi_tmf_4", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 684, "tid": 684, "process": "", "thread": "nvme_tcp_wq", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 701, "tid": 701, "process": "", "thread": "mlx5_health773e", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 702, "tid": 702, "process": "", "thread": "mlx5_page_alloc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 703, "tid": 703, "process": "", "thread": "mlx5_cmd_773e:0", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 705, "tid": 705, "process": "", "thread": "kworker/7:2H", "affinity": [7]}, {"pid": 707, "tid": 707, "process": "", "thread": "mlx5_events", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 708, "tid": 708, "process": "", "thread": "mlx5_fw_reset_e", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 709, "tid": 709, "process": "", "thread": "mlx5_fc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 710, "tid": 710, "process": "", "thread": "mlx5_hv_vhca", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 713, "tid": 713, "process": "", "thread": "mlx5e", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 715, "tid": 715, "process": "", "thread": "mlx5e_arfs", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 718, "tid": 718, "process": "", "thread": "ib-comp-wq", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 719, "tid": 719, "process": "", "thread": "ib-comp-unb-wq", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 720, "tid": 720, "process": "", "thread": "ib_mcast", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 721, "tid": 721, "process": "", "thread": "ib_nl_sa_wq", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 725, "tid": 725, "process": "", "thread": "mkey_cache", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 743, "tid": 743, "process": "", "thread": "kmpathd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 744, "tid": 744, "process": "", "thread": "kmpath_handlerd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 761, "tid": 761, "process": "", "thread": "rdma_cm", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 762, "tid": 762, "process": "", "thread": "target_completi", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 763, "tid": 763, "process": "", "thread": "target_submissi", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 764, "tid": 764, "process": "", "thread": "xcopy_wq", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 765, "tid": 765, "process": "", "thread": "xfsalloc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 766, "tid": 766, "process": "", "thread": "xfs_mru_cache", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 767, "tid": 767, "process": "", "thread": "xfs-buf/sda4", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 768, "tid": 768, "process": "", "thread": "xfs-conv/sda4", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 769, "tid": 769, "process": "", "thread": "xfs-reclaim/sda", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 770, "tid": 770, "process": "", "thread": "xfs-blockgc/sda", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 771, "tid": 771, "process": "", "thread": "xfs-inodegc/sda", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 772, "tid": 772, "process": "", "thread": "xfs-log/sda4", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 773, "tid": 773, "process": "", "thread": "xfs-cil/sda4", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 774, "tid": 774, "process": "", "thread": "xfsaild/sda4", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 776, "tid": 776, "process": "", "thread": "rpciod", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 777, "tid": 777, "process": "", "thread": "xprtiod", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 879, "tid": 879, "process": "systemd-journald", "thread": "systemd-journal", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 897, "tid": 897, "process": "systemd-udevd", "thread": "systemd-udevd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 940, "tid": 940, "process": "", "thread": "hv_balloon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 951, "tid": 951, "process": "rdma-ndd", "thread": "rdma-ndd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 965, "tid": 965, "process": "", "thread": "nfit", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 993, "tid": 993, "process": "", "thread": "jbd2/sda3-8", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 994, "tid": 994, "process": "", "thread": "ext4-rsv-conver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1005, "tid": 1005, "process": "auditd", "thread": "auditd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1005, "tid": 1006, "process": "auditd", "thread": "auditd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1031, "tid": 1031, "process": "dbus-broker-launch", "thread": "dbus-broker-lau", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1033, "tid": 1033, "process": "dbus-broker", "thread": "dbus-broker", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1057, "tid": 1057, "process": "irqbalance", "thread": "irqbalance", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1057, "tid": 1064, "process": "irqbalance", "thread": "gmain", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1080, "tid": 1080, "process": "systemd-logind", "thread": "systemd-logind", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1104, "tid": 1104, "process": "chronyd", "thread": "chronyd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1180, "tid": 1180, "process": "ovsdb-server", "thread": "ovsdb-server", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1248, "tid": 1248, "process": "ovs-vswitchd", "thread": "ovs-vswitchd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1248, "tid": 1256, "process": "ovs-vswitchd", "thread": "urcu8", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1248, "tid": 2148, "process": "ovs-vswitchd", "thread": "handler24", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1248, "tid": 2149, "process": "ovs-vswitchd", "thread": "handler25", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1248, "tid": 2150, "process": "ovs-vswitchd", "thread": "handler33", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1248, "tid": 2151, "process": "ovs-vswitchd", "thread": "handler26", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1248, "tid": 2152, "process": "ovs-vswitchd", "thread": "handler27", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1248, "tid": 2153, "process": "ovs-vswitchd", "thread": "handler28", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1248, "tid": 2154, "process": "ovs-vswitchd", "thread": "handler29", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1248, "tid": 2155, "process": "ovs-vswitchd", "thread": "handler30", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1248, "tid": 2156, "process": "ovs-vswitchd", "thread": "revalidator31", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1248, "tid": 2157, "process": "ovs-vswitchd", "thread": "revalidator32", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1248, "tid": 2158, "process": "ovs-vswitchd", "thread": "revalidator34", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1283, "tid": 1283, "process": "NetworkManager", "thread": "NetworkManager", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1283, "tid": 1286, "process": "NetworkManager", "thread": "gmain", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1283, "tid": 1287, "process": "NetworkManager", "thread": "gdbus", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1336, "tid": 1336, "process": "rhsmcertd", "thread": "rhsmcertd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1336, "tid": 1338, "process": "rhsmcertd", "thread": "gmain", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1337, "tid": 1337, "process": "sshd:", "thread": "sshd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1342, "tid": 1342, "process": "agetty", "thread": "agetty", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1346, "tid": 1346, "process": "agetty", "thread": "agetty", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1479, "tid": 1479, "process": "rpcbind", "thread": "rpcbind", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1497, "tid": 1497, "process": "rpc.statd", "thread": "rpc.statd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2602, "tid": 2602, "process": "dnsmasq", "thread": "dnsmasq", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 2633, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 2635, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 2636, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 2637, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 2638, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 2639, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 2640, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 2641, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 2642, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 2643, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 2644, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 2645, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 2722, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 2750, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 2844, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 2924, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 2930, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 2932, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 2933, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 3015, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 3042, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2633, "tid": 3046, "process": "crio", "thread": "crio", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2675, "tid": 2675, "process": "kubelet", "thread": "kubelet", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2675, "tid": 2676, "process": "kubelet", "thread": "kubelet", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2675, "tid": 2677, "process": "kubelet", "thread": "kubelet", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2675, "tid": 2678, "process": "kubelet", "thread": "kubelet", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2675, "tid": 2679, "process": "kubelet", "thread": "kubelet", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2675, "tid": 2680, "process": "kubelet", "thread": "kubelet", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2675, "tid": 2686, "process": "kubelet", "thread": "kubelet", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2675, "tid": 2687, "process": "kubelet", "thread": "kubelet", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2675, "tid": 2688, "process": "kubelet", "thread": "kubelet", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2675, "tid": 2689, "process": "kubelet", "thread": "kubelet", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2675, "tid": 2696, "process": "kubelet", "thread": "kubelet", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2675, "tid": 2709, "process": "kubelet", "thread": "kubelet", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2675, "tid": 2723, "process": "kubelet", "thread": "kubelet", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2675, "tid": 2729, "process": "kubelet", "thread": "kubelet", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2675, "tid": 2730, "process": "kubelet", "thread": "kubelet", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2675, "tid": 2732, "process": "kubelet", "thread": "kubelet", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2675, "tid": 2776, "process": "kubelet", "thread": "kubelet", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2675, "tid": 11452, "process": "kubelet", "thread": "kubelet", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2675, "tid": 11453, "process": "kubelet", "thread": "kubelet", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2675, "tid": 2367611, "process": "kubelet", "thread": "kubelet", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2675, "tid": 2367613, "process": "kubelet", "thread": "kubelet", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2917, "tid": 2917, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2954, "tid": 2954, "process": "cluster-node-tuning-operator", "thread": "cluster-node-tu", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2954, "tid": 3135, "process": "cluster-node-tuning-operator", "thread": "cluster-node-tu", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2954, "tid": 3136, "process": "cluster-node-tuning-operator", "thread": "cluster-node-tu", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2954, "tid": 3137, "process": "cluster-node-tuning-operator", "thread": "cluster-node-tu", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2954, "tid": 3138, "process": "cluster-node-tuning-operator", "thread": "cluster-node-tu", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2954, "tid": 3140, "process": "cluster-node-tuning-operator", "thread": "cluster-node-tu", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2954, "tid": 3222, "process": "cluster-node-tuning-operator", "thread": "cluster-node-tu", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2954, "tid": 3230, "process": "cluster-node-tuning-operator", "thread": "cluster-node-tu", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2954, "tid": 3231, "process": "cluster-node-tuning-operator", "thread": "cluster-node-tu", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2954, "tid": 3232, "process": "cluster-node-tuning-operator", "thread": "cluster-node-tu", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2954, "tid": 3245, "process": "cluster-node-tuning-operator", "thread": "cluster-node-tu", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2954, "tid": 3246, "process": "cluster-node-tuning-operator", "thread": "cluster-node-tu", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2954, "tid": 3247, "process": "cluster-node-tuning-operator", "thread": "cluster-node-tu", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3021, "tid": 3021, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3066, "tid": 3066, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3075, "tid": 3075, "process": "multus-daemon", "thread": "multus-daemon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3075, "tid": 3662, "process": "multus-daemon", "thread": "multus-daemon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3075, "tid": 3663, "process": "multus-daemon", "thread": "multus-daemon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3075, "tid": 3664, "process": "multus-daemon", "thread": "multus-daemon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3075, "tid": 3665, "process": "multus-daemon", "thread": "multus-daemon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3075, "tid": 3666, "process": "multus-daemon", "thread": "multus-daemon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3075, "tid": 3687, "process": "multus-daemon", "thread": "multus-daemon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3075, "tid": 3688, "process": "multus-daemon", "thread": "multus-daemon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3075, "tid": 5825, "process": "multus-daemon", "thread": "multus-daemon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3075, "tid": 5826, "process": "multus-daemon", "thread": "multus-daemon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3075, "tid": 5827, "process": "multus-daemon", "thread": "multus-daemon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3075, "tid": 5901, "process": "multus-daemon", "thread": "multus-daemon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3075, "tid": 5902, "process": "multus-daemon", "thread": "multus-daemon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3075, "tid": 5903, "process": "multus-daemon", "thread": "multus-daemon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3075, "tid": 5906, "process": "multus-daemon", "thread": "multus-daemon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3266, "tid": 3266, "process": "machine-config-daemon", "thread": "machine-config-", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3266, "tid": 3667, "process": "machine-config-daemon", "thread": "machine-config-", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3266, "tid": 3668, "process": "machine-config-daemon", "thread": "machine-config-", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3266, "tid": 3669, "process": "machine-config-daemon", "thread": "machine-config-", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3266, "tid": 3670, "process": "machine-config-daemon", "thread": "machine-config-", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3266, "tid": 3671, "process": "machine-config-daemon", "thread": "machine-config-", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3266, "tid": 3674, "process": "machine-config-daemon", "thread": "machine-config-", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3266, "tid": 3675, "process": "machine-config-daemon", "thread": "machine-config-", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3266, "tid": 3691, "process": "machine-config-daemon", "thread": "machine-config-", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3266, "tid": 4154, "process": "machine-config-daemon", "thread": "machine-config-", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3266, "tid": 4236, "process": "machine-config-daemon", "thread": "machine-config-", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3266, "tid": 6761, "process": "machine-config-daemon", "thread": "machine-config-", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3266, "tid": 6762, "process": "machine-config-daemon", "thread": "machine-config-", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3278, "tid": 3278, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3279, "tid": 3279, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3281, "tid": 3281, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3365, "tid": 3365, "process": "bash", "thread": "bash", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3374, "tid": 3374, "process": "sh", "thread": "sh", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3461, "tid": 3461, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3493, "tid": 3493, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3493, "tid": 3655, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3493, "tid": 3656, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3493, "tid": 3658, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3493, "tid": 3659, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3493, "tid": 3660, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3493, "tid": 3679, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3493, "tid": 3721, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3493, "tid": 8599, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3493, "tid": 46623, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3493, "tid": 388999, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3493, "tid": 749510, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3677, "tid": 3677, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3693, "tid": 3693, "process": "ovn-controller", "thread": "ovn-controller", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3693, "tid": 3869, "process": "ovn-controller", "thread": "ovn_pinctrl0", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3693, "tid": 3870, "process": "ovn-controller", "thread": "urcu1", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3693, "tid": 3871, "process": "ovn-controller", "thread": "ovn_statctrl2", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3693, "tid": 3872, "process": "ovn-controller", "thread": "stopwatch3", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3699, "tid": 3699, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3790, "tid": 3790, "process": "node_exporter", "thread": "node_exporter", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3790, "tid": 3998, "process": "node_exporter", "thread": "node_exporter", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3790, "tid": 3999, "process": "node_exporter", "thread": "node_exporter", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3790, "tid": 4000, "process": "node_exporter", "thread": "node_exporter", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3790, "tid": 4004, "process": "node_exporter", "thread": "node_exporter", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3790, "tid": 4013, "process": "node_exporter", "thread": "node_exporter", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3790, "tid": 4891, "process": "node_exporter", "thread": "node_exporter", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3790, "tid": 4893, "process": "node_exporter", "thread": "node_exporter", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3790, "tid": 7062, "process": "node_exporter", "thread": "node_exporter", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3790, "tid": 10831, "process": "node_exporter", "thread": "node_exporter", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3790, "tid": 24518, "process": "node_exporter", "thread": "node_exporter", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3790, "tid": 127514, "process": "node_exporter", "thread": "node_exporter", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3790, "tid": 3221659, "process": "node_exporter", "thread": "node_exporter", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3790, "tid": 3342623, "process": "node_exporter", "thread": "node_exporter", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3810, "tid": 3810, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3811, "tid": 3811, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3821, "tid": 3821, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3876, "tid": 3876, "process": "azure-cloud-node-manager", "thread": "azure-cloud-nod", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3876, "tid": 4125, "process": "azure-cloud-node-manager", "thread": "azure-cloud-nod", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3876, "tid": 4126, "process": "azure-cloud-node-manager", "thread": "azure-cloud-nod", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3876, "tid": 4127, "process": "azure-cloud-node-manager", "thread": "azure-cloud-nod", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3876, "tid": 4128, "process": "azure-cloud-node-manager", "thread": "azure-cloud-nod", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3876, "tid": 4129, "process": "azure-cloud-node-manager", "thread": "azure-cloud-nod", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3876, "tid": 4247, "process": "azure-cloud-node-manager", "thread": "azure-cloud-nod", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3876, "tid": 4301, "process": "azure-cloud-node-manager", "thread": "azure-cloud-nod", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3876, "tid": 4302, "process": "azure-cloud-node-manager", "thread": "azure-cloud-nod", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3876, "tid": 4303, "process": "azure-cloud-node-manager", "thread": "azure-cloud-nod", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3876, "tid": 4304, "process": "azure-cloud-node-manager", "thread": "azure-cloud-nod", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3883, "tid": 3883, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3883, "tid": 4036, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3883, "tid": 4038, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3883, "tid": 4039, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3883, "tid": 4040, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3883, "tid": 4041, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3883, "tid": 4071, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3883, "tid": 4083, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3883, "tid": 4084, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3883, "tid": 4085, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3883, "tid": 6848, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3883, "tid": 6849, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3901, "tid": 3901, "process": "azurediskplugin", "thread": "azurediskplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3901, "tid": 4130, "process": "azurediskplugin", "thread": "azurediskplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3901, "tid": 4131, "process": "azurediskplugin", "thread": "azurediskplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3901, "tid": 4132, "process": "azurediskplugin", "thread": "azurediskplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3901, "tid": 4133, "process": "azurediskplugin", "thread": "azurediskplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3901, "tid": 4134, "process": "azurediskplugin", "thread": "azurediskplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3901, "tid": 4264, "process": "azurediskplugin", "thread": "azurediskplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3901, "tid": 4265, "process": "azurediskplugin", "thread": "azurediskplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3901, "tid": 4266, "process": "azurediskplugin", "thread": "azurediskplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3901, "tid": 6744, "process": "azurediskplugin", "thread": "azurediskplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3901, "tid": 6745, "process": "azurediskplugin", "thread": "azurediskplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3901, "tid": 251141, "process": "azurediskplugin", "thread": "azurediskplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3907, "tid": 3907, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3978, "tid": 3978, "process": "bash", "thread": "bash", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4074, "tid": 4074, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4106, "tid": 4106, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4106, "tid": 4172, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4106, "tid": 4173, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4106, "tid": 4174, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4106, "tid": 4175, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4106, "tid": 4176, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4106, "tid": 4189, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4106, "tid": 4190, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4106, "tid": 4197, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4106, "tid": 4198, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4106, "tid": 4210, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4106, "tid": 855512, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4112, "tid": 4112, "process": "coreutils", "thread": "tail", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4136, "tid": 4136, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4157, "tid": 4157, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4157, "tid": 4230, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4157, "tid": 4232, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4157, "tid": 4233, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4157, "tid": 4234, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4157, "tid": 4235, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4157, "tid": 4237, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4157, "tid": 4238, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4157, "tid": 4242, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4157, "tid": 6467, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4157, "tid": 6468, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4157, "tid": 6469, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4157, "tid": 1478139, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4187, "tid": 4187, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4212, "tid": 4212, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4212, "tid": 4274, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4212, "tid": 4275, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4212, "tid": 4276, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4212, "tid": 4277, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4212, "tid": 4278, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4212, "tid": 4292, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4212, "tid": 4294, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4212, "tid": 4298, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4212, "tid": 4299, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4212, "tid": 6537, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4212, "tid": 6538, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4212, "tid": 6539, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4271, "tid": 4271, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4318, "tid": 4318, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4332, "tid": 4332, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4332, "tid": 4401, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4332, "tid": 4402, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4332, "tid": 4403, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4332, "tid": 4404, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4332, "tid": 4405, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4332, "tid": 4406, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4332, "tid": 4408, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4332, "tid": 4409, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4332, "tid": 4410, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4332, "tid": 4416, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4332, "tid": 502415, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4333, "tid": 4333, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4333, "tid": 4411, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4333, "tid": 4412, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4333, "tid": 4413, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4333, "tid": 4414, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4333, "tid": 4415, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4333, "tid": 6751, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4333, "tid": 6752, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4333, "tid": 6753, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4333, "tid": 6754, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4333, "tid": 6755, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4333, "tid": 6756, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4333, "tid": 6757, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4333, "tid": 70436, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4333, "tid": 1650148, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4420, "tid": 4420, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4431, "tid": 4431, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4444, "tid": 4444, "process": "bash", "thread": "bash", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4453, "tid": 4453, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4453, "tid": 4501, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4453, "tid": 4502, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4453, "tid": 4503, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4453, "tid": 4504, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4453, "tid": 4505, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4453, "tid": 6763, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4453, "tid": 6764, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4453, "tid": 6765, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4453, "tid": 7915, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4453, "tid": 16936, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4453, "tid": 78144, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4453, "tid": 1327556, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4491, "tid": 4491, "process": "ovn-northd", "thread": "ovn-northd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4491, "tid": 4498, "process": "ovn-northd", "thread": "stopwatch0", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4491, "tid": 4499, "process": "ovn-northd", "thread": "urcu1", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4506, "tid": 4506, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4518, "tid": 4518, "process": "bash", "thread": "bash", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4542, "tid": 4542, "process": "ovsdb-server", "thread": "ovsdb-server", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4606, "tid": 4606, "process": "bash", "thread": "iptables-alerte", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4668, "tid": 4668, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4731, "tid": 4731, "process": "azurefileplugin", "thread": "azurefileplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4731, "tid": 4755, "process": "azurefileplugin", "thread": "azurefileplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4731, "tid": 4756, "process": "azurefileplugin", "thread": "azurefileplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4731, "tid": 4757, "process": "azurefileplugin", "thread": "azurefileplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4731, "tid": 4758, "process": "azurefileplugin", "thread": "azurefileplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4731, "tid": 4759, "process": "azurefileplugin", "thread": "azurefileplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4731, "tid": 4768, "process": "azurefileplugin", "thread": "azurefileplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4731, "tid": 4770, "process": "azurefileplugin", "thread": "azurefileplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4731, "tid": 4771, "process": "azurefileplugin", "thread": "azurefileplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4731, "tid": 4786, "process": "azurefileplugin", "thread": "azurefileplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4731, "tid": 5810, "process": "azurefileplugin", "thread": "azurefileplugin", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4761, "tid": 4761, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4776, "tid": 4776, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4776, "tid": 4800, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4776, "tid": 4801, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4776, "tid": 4802, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4776, "tid": 4803, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4776, "tid": 4804, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4776, "tid": 5812, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4776, "tid": 5813, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4776, "tid": 5814, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4776, "tid": 5815, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4776, "tid": 5816, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4776, "tid": 5817, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4776, "tid": 5818, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4776, "tid": 69727, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4776, "tid": 2118576, "process": "csi-node-driver-registrar", "thread": "csi-node-driver", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4811, "tid": 4811, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4823, "tid": 4823, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4823, "tid": 4842, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4823, "tid": 4843, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4823, "tid": 4844, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4823, "tid": 4845, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4823, "tid": 4849, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4823, "tid": 5819, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4823, "tid": 5820, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4823, "tid": 5821, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4823, "tid": 5822, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4823, "tid": 7064, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4823, "tid": 899616, "process": "livenessprobe", "thread": "livenessprobe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4910, "tid": 4910, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4912, "tid": 4912, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4933, "tid": 4933, "process": "bash", "thread": "bash", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4940, "tid": 4940, "process": "agent", "thread": "agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4940, "tid": 5120, "process": "agent", "thread": "agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4940, "tid": 5121, "process": "agent", "thread": "agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4940, "tid": 5122, "process": "agent", "thread": "agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4940, "tid": 5123, "process": "agent", "thread": "agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4940, "tid": 5124, "process": "agent", "thread": "agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4940, "tid": 5159, "process": "agent", "thread": "agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4940, "tid": 5160, "process": "agent", "thread": "agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4940, "tid": 5161, "process": "agent", "thread": "agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4940, "tid": 5162, "process": "agent", "thread": "agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4940, "tid": 5163, "process": "agent", "thread": "agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4940, "tid": 5184, "process": "agent", "thread": "agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4940, "tid": 5724, "process": "agent", "thread": "agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4940, "tid": 6563, "process": "agent", "thread": "agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4940, "tid": 6564, "process": "agent", "thread": "agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4940, "tid": 6565, "process": "agent", "thread": "agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4940, "tid": 3275893, "process": "agent", "thread": "agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 4983, "tid": 4983, "process": "ovsdb-server", "thread": "ovsdb-server", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5109, "tid": 5109, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5126, "tid": 5126, "process": "trace-agent", "thread": "trace-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5126, "tid": 5153, "process": "trace-agent", "thread": "trace-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5126, "tid": 5154, "process": "trace-agent", "thread": "trace-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5126, "tid": 5155, "process": "trace-agent", "thread": "trace-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5126, "tid": 5156, "process": "trace-agent", "thread": "trace-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5126, "tid": 5158, "process": "trace-agent", "thread": "trace-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5126, "tid": 5185, "process": "trace-agent", "thread": "trace-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5126, "tid": 5186, "process": "trace-agent", "thread": "trace-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5126, "tid": 5187, "process": "trace-agent", "thread": "trace-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5126, "tid": 5188, "process": "trace-agent", "thread": "trace-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5126, "tid": 5195, "process": "trace-agent", "thread": "trace-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5126, "tid": 5808, "process": "trace-agent", "thread": "trace-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5126, "tid": 5809, "process": "trace-agent", "thread": "trace-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5126, "tid": 79587, "process": "trace-agent", "thread": "trace-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5167, "tid": 5167, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5178, "tid": 5178, "process": "process-agent", "thread": "process-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5178, "tid": 5210, "process": "process-agent", "thread": "process-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5178, "tid": 5211, "process": "process-agent", "thread": "process-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5178, "tid": 5212, "process": "process-agent", "thread": "process-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5178, "tid": 5213, "process": "process-agent", "thread": "process-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5178, "tid": 5215, "process": "process-agent", "thread": "process-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5178, "tid": 5293, "process": "process-agent", "thread": "process-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5178, "tid": 5302, "process": "process-agent", "thread": "process-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5178, "tid": 5303, "process": "process-agent", "thread": "process-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5178, "tid": 5304, "process": "process-agent", "thread": "process-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5178, "tid": 5305, "process": "process-agent", "thread": "process-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5178, "tid": 5310, "process": "process-agent", "thread": "process-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5178, "tid": 5313, "process": "process-agent", "thread": "process-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5178, "tid": 7922, "process": "process-agent", "thread": "process-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5178, "tid": 22323, "process": "process-agent", "thread": "process-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5216, "tid": 5216, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 5239, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 5307, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 5308, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 5309, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 5311, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 5312, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 5317, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 5319, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 5320, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 5321, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 5322, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 5323, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 5340, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 5655, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 5719, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 5720, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 5721, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 5722, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 6612, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 16522, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 1204087, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5239, "tid": 2425589, "process": "system-probe", "thread": "system-probe", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5411, "tid": 5411, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 5423, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 5550, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 5551, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 5552, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 5553, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 5554, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 5585, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 5586, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 5587, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 5596, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 5597, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 5598, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 5600, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 5603, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 5667, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 5952, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 5953, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 5954, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 5970, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 5971, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 5997, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 2834138, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5423, "tid": 4178708, "process": "ovnkube", "thread": "ovnkube", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5455, "tid": 5455, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5466, "tid": 5466, "process": "bash", "thread": "bash", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5504, "tid": 5504, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5543, "tid": 5543, "process": "bash", "thread": "bash", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5566, "tid": 5566, "process": "oc", "thread": "oc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5566, "tid": 5580, "process": "oc", "thread": "oc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5566, "tid": 5581, "process": "oc", "thread": "oc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5566, "tid": 5582, "process": "oc", "thread": "oc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5566, "tid": 5583, "process": "oc", "thread": "oc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5566, "tid": 5584, "process": "oc", "thread": "oc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5566, "tid": 5593, "process": "oc", "thread": "oc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5566, "tid": 5594, "process": "oc", "thread": "oc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5566, "tid": 5595, "process": "oc", "thread": "oc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5566, "tid": 5609, "process": "oc", "thread": "oc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5566, "tid": 5610, "process": "oc", "thread": "oc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5566, "tid": 5611, "process": "oc", "thread": "oc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5566, "tid": 5613, "process": "oc", "thread": "oc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5566, "tid": 5614, "process": "oc", "thread": "oc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5566, "tid": 5615, "process": "oc", "thread": "oc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 5574, "tid": 5574, "process": "coreutils", "thread": "sleep", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6040, "tid": 6040, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6054, "tid": 6054, "process": "cluster-network-check-target", "thread": "cluster-network", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6054, "tid": 6193, "process": "cluster-network-check-target", "thread": "cluster-network", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6054, "tid": 6194, "process": "cluster-network-check-target", "thread": "cluster-network", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6054, "tid": 6195, "process": "cluster-network-check-target", "thread": "cluster-network", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6054, "tid": 6196, "process": "cluster-network-check-target", "thread": "cluster-network", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6054, "tid": 6197, "process": "cluster-network-check-target", "thread": "cluster-network", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6054, "tid": 227084, "process": "cluster-network-check-target", "thread": "cluster-network", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6054, "tid": 227085, "process": "cluster-network-check-target", "thread": "cluster-network", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6054, "tid": 424354, "process": "cluster-network-check-target", "thread": "cluster-network", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6054, "tid": 1234212, "process": "cluster-network-check-target", "thread": "cluster-network", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6060, "tid": 6060, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6084, "tid": 6084, "process": "ingress-operator", "thread": "ingress-operato", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6084, "tid": 6236, "process": "ingress-operator", "thread": "ingress-operato", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6084, "tid": 6237, "process": "ingress-operator", "thread": "ingress-operato", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6084, "tid": 6238, "process": "ingress-operator", "thread": "ingress-operato", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6084, "tid": 6239, "process": "ingress-operator", "thread": "ingress-operato", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6084, "tid": 6243, "process": "ingress-operator", "thread": "ingress-operato", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6084, "tid": 6364, "process": "ingress-operator", "thread": "ingress-operato", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6084, "tid": 6365, "process": "ingress-operator", "thread": "ingress-operato", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6084, "tid": 6367, "process": "ingress-operator", "thread": "ingress-operato", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6084, "tid": 6440, "process": "ingress-operator", "thread": "ingress-operato", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6084, "tid": 6441, "process": "ingress-operator", "thread": "ingress-operato", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6084, "tid": 6442, "process": "ingress-operator", "thread": "ingress-operato", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6096, "tid": 6096, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6120, "tid": 6120, "process": "network-metrics", "thread": "network-metrics", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6120, "tid": 6227, "process": "network-metrics", "thread": "network-metrics", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6120, "tid": 6228, "process": "network-metrics", "thread": "network-metrics", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6120, "tid": 6229, "process": "network-metrics", "thread": "network-metrics", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6120, "tid": 6230, "process": "network-metrics", "thread": "network-metrics", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6120, "tid": 6232, "process": "network-metrics", "thread": "network-metrics", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6120, "tid": 6260, "process": "network-metrics", "thread": "network-metrics", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6120, "tid": 6290, "process": "network-metrics", "thread": "network-metrics", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6120, "tid": 6291, "process": "network-metrics", "thread": "network-metrics", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6120, "tid": 6292, "process": "network-metrics", "thread": "network-metrics", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6120, "tid": 6293, "process": "network-metrics", "thread": "network-metrics", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6120, "tid": 6340, "process": "network-metrics", "thread": "network-metrics", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6120, "tid": 8387, "process": "network-metrics", "thread": "network-metrics", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6120, "tid": 413943, "process": "network-metrics", "thread": "network-metrics", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6126, "tid": 6126, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6157, "tid": 6157, "process": "coredns", "thread": "coredns", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6157, "tid": 6240, "process": "coredns", "thread": "coredns", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6157, "tid": 6241, "process": "coredns", "thread": "coredns", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6157, "tid": 6244, "process": "coredns", "thread": "coredns", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6157, "tid": 6245, "process": "coredns", "thread": "coredns", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6157, "tid": 6246, "process": "coredns", "thread": "coredns", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6157, "tid": 6324, "process": "coredns", "thread": "coredns", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6157, "tid": 6325, "process": "coredns", "thread": "coredns", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6157, "tid": 6326, "process": "coredns", "thread": "coredns", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6157, "tid": 6392, "process": "coredns", "thread": "coredns", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6157, "tid": 6393, "process": "coredns", "thread": "coredns", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6157, "tid": 6437, "process": "coredns", "thread": "coredns", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6157, "tid": 6438, "process": "coredns", "thread": "coredns", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6157, "tid": 19524, "process": "coredns", "thread": "coredns", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6271, "tid": 6271, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6296, "tid": 6296, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6296, "tid": 6351, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6296, "tid": 6353, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6296, "tid": 6356, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6296, "tid": 6357, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6296, "tid": 6359, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6296, "tid": 6366, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6296, "tid": 6368, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6296, "tid": 6386, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6296, "tid": 6387, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6296, "tid": 6388, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6296, "tid": 1871984, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6297, "tid": 6297, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6334, "tid": 6334, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6334, "tid": 6376, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6334, "tid": 6377, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6334, "tid": 6378, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6334, "tid": 6379, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6334, "tid": 6380, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6334, "tid": 6389, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6334, "tid": 6390, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6334, "tid": 6391, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6334, "tid": 6394, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6334, "tid": 7603, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6334, "tid": 7604, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6334, "tid": 7605, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6334, "tid": 7606, "process": "kube-rbac-proxy", "thread": "kube-rbac-proxy", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6664, "tid": 6664, "process": "python3", "thread": "tuned", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6664, "tid": 6748, "process": "python3", "thread": "tuned", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 6664, "tid": 6750, "process": "python3", "thread": "tuned", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 11483, "tid": 11483, "process": "", "thread": "cifsiod", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 11484, "tid": 11484, "process": "", "thread": "smb3decryptd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 11485, "tid": 11485, "process": "", "thread": "cifsfileinfoput", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 11486, "tid": 11486, "process": "", "thread": "cifsoplockd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 11487, "tid": 11487, "process": "", "thread": "deferredclose", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 11488, "tid": 11488, "process": "", "thread": "cifs-dfscache", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154246, "tid": 154246, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154264, "tid": 154264, "process": "tini", "thread": "tini", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154315, "tid": 154315, "process": "argocd-applicationset-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154315, "tid": 154318, "process": "argocd-applicationset-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154315, "tid": 154319, "process": "argocd-applicationset-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154315, "tid": 154320, "process": "argocd-applicationset-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154315, "tid": 154321, "process": "argocd-applicationset-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154315, "tid": 154323, "process": "argocd-applicationset-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154315, "tid": 154332, "process": "argocd-applicationset-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154315, "tid": 154333, "process": "argocd-applicationset-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154315, "tid": 154362, "process": "argocd-applicationset-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154315, "tid": 154363, "process": "argocd-applicationset-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154315, "tid": 154364, "process": "argocd-applicationset-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154315, "tid": 154365, "process": "argocd-applicationset-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154334, "tid": 154334, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154350, "tid": 154350, "process": "argocd-application-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154350, "tid": 154385, "process": "argocd-application-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154350, "tid": 154386, "process": "argocd-application-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154350, "tid": 154387, "process": "argocd-application-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154350, "tid": 154389, "process": "argocd-application-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154350, "tid": 154390, "process": "argocd-application-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154350, "tid": 154396, "process": "argocd-application-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154350, "tid": 154397, "process": "argocd-application-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154350, "tid": 154398, "process": "argocd-application-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154350, "tid": 154399, "process": "argocd-application-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154350, "tid": 154400, "process": "argocd-application-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154350, "tid": 190569, "process": "argocd-application-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154350, "tid": 2157050, "process": "argocd-application-controller", "thread": "argocd-applicat", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154540, "tid": 154540, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154552, "tid": 154552, "process": "tini", "thread": "tini", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154573, "tid": 154573, "process": "argocd-repo-server", "thread": "argocd-repo-ser", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154573, "tid": 154577, "process": "argocd-repo-server", "thread": "argocd-repo-ser", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154573, "tid": 154578, "process": "argocd-repo-server", "thread": "argocd-repo-ser", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154573, "tid": 154579, "process": "argocd-repo-server", "thread": "argocd-repo-ser", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154573, "tid": 154580, "process": "argocd-repo-server", "thread": "argocd-repo-ser", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154573, "tid": 154581, "process": "argocd-repo-server", "thread": "argocd-repo-ser", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154573, "tid": 154582, "process": "argocd-repo-server", "thread": "argocd-repo-ser", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154573, "tid": 154583, "process": "argocd-repo-server", "thread": "argocd-repo-ser", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154573, "tid": 154584, "process": "argocd-repo-server", "thread": "argocd-repo-ser", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154573, "tid": 154585, "process": "argocd-repo-server", "thread": "argocd-repo-ser", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154573, "tid": 154586, "process": "argocd-repo-server", "thread": "argocd-repo-ser", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154573, "tid": 154587, "process": "argocd-repo-server", "thread": "argocd-repo-ser", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154573, "tid": 1699994, "process": "argocd-repo-server", "thread": "argocd-repo-ser", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 154592, "tid": 154592, "process": "gpg-agent", "thread": "gpg-agent", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 395394, "tid": 395394, "process": "", "thread": "cifsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 396066, "tid": 396066, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 396078, "tid": 396078, "process": "vault", "thread": "vault", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 396078, "tid": 396181, "process": "vault", "thread": "vault", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 396078, "tid": 396182, "process": "vault", "thread": "vault", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 396078, "tid": 396183, "process": "vault", "thread": "vault", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 396078, "tid": 396184, "process": "vault", "thread": "vault", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 396078, "tid": 396185, "process": "vault", "thread": "vault", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 396078, "tid": 396186, "process": "vault", "thread": "vault", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 396078, "tid": 396187, "process": "vault", "thread": "vault", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 396078, "tid": 396188, "process": "vault", "thread": "vault", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 396078, "tid": 396189, "process": "vault", "thread": "vault", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 396078, "tid": 396190, "process": "vault", "thread": "vault", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 396078, "tid": 396191, "process": "vault", "thread": "vault", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 396078, "tid": 396192, "process": "vault", "thread": "vault", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 396078, "tid": 396193, "process": "vault", "thread": "vault", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 396078, "tid": 737842, "process": "vault", "thread": "vault", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 396078, "tid": 1046538, "process": "vault", "thread": "vault", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1139344, "tid": 1139344, "process": "", "thread": "kworker/3:1-events", "affinity": [3]}, {"pid": 1640194, "tid": 1640194, "process": "", "thread": "kworker/3:0-rcu_gp", "affinity": [3]}, {"pid": 1837483, "tid": 1837483, "process": "", "thread": "kworker/4:2-cgroup_destroy", "affinity": [4]}, {"pid": 1881256, "tid": 1881256, "process": "", "thread": "kworker/4:1-events", "affinity": [4]}, {"pid": 1922003, "tid": 1922003, "process": "", "thread": "kworker/u16:2-flush-8:0", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 1931050, "tid": 1931050, "process": "", "thread": "kworker/2:2-events", "affinity": [2]}, {"pid": 2006411, "tid": 2006411, "process": "", "thread": "kworker/u16:6-flush-8:0", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2024877, "tid": 2024877, "process": "", "thread": "kworker/1:2-events", "affinity": [1]}, {"pid": 2032844, "tid": 2032844, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032874, "tid": 2032874, "process": "td-agent-bit", "thread": "td-agent-bit", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032874, "tid": 2032898, "process": "td-agent-bit", "thread": "flb-pipeline", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032874, "tid": 2032899, "process": "td-agent-bit", "thread": "flb-logger", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032874, "tid": 2032901, "process": "td-agent-bit", "thread": "flb-out-forward", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032874, "tid": 2032902, "process": "td-agent-bit", "thread": "flb-out-forward", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032903, "tid": 2032903, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2030920, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2030963, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032915, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032951, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032952, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032953, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032954, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032955, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032956, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032957, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032958, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032959, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032960, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032961, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032962, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032963, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032964, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032965, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032966, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032967, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032968, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032969, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032970, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032971, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032972, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032973, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032974, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032975, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032976, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032977, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032978, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032979, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032980, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032981, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032982, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032983, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032984, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032985, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032986, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032987, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032988, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032989, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032990, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032991, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032992, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032993, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032994, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032995, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2032999, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2033000, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2033001, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2033002, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2033003, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2033004, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2033005, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2033006, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2033007, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2032915, "tid": 2033008, "process": "mdsd", "thread": "mdsd", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2050438, "tid": 2050438, "process": "", "thread": "kworker/1:1-xfs-inodegc/sda4", "affinity": [1]}, {"pid": 2059968, "tid": 2059968, "process": "", "thread": "kworker/u16:4-flush-8:0", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2062502, "tid": 2062502, "process": "", "thread": "kworker/0:0-xfs-conv/sda4", "affinity": [0]}, {"pid": 2065679, "tid": 2065679, "process": "", "thread": "kworker/6:1-cif<PERSON>d", "affinity": [6]}, {"pid": 2071570, "tid": 2071570, "process": "", "thread": "kworker/7:1-events", "affinity": [7]}, {"pid": 2071888, "tid": 2071888, "process": "", "thread": "kworker/5:0-cif<PERSON>d", "affinity": [5]}, {"pid": 2072001, "tid": 2072001, "process": "", "thread": "kworker/u16:0-flush-8:0", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2074614, "tid": 2074614, "process": "coreutils", "thread": "sleep", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2079874, "tid": 2079874, "process": "", "thread": "kworker/5:1-cifsiod", "affinity": [5]}, {"pid": 2081240, "tid": 2081240, "process": "", "thread": "kworker/0:2-xfs-conv/sda4", "affinity": [0]}, {"pid": 2081656, "tid": 2081656, "process": "", "thread": "kworker/u16:3-flush-cifs-6", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2081696, "tid": 2081696, "process": "", "thread": "kworker/2:0-ata_sff", "affinity": [2]}, {"pid": 2082282, "tid": 2082282, "process": "", "thread": "kworker/7:0-cif<PERSON>d", "affinity": [7]}, {"pid": 2086325, "tid": 2086325, "process": "", "thread": "kworker/6:0-events", "affinity": [6]}, {"pid": 2089515, "tid": 2089515, "process": "", "thread": "kworker/5:2-events", "affinity": [5]}, {"pid": 2089643, "tid": 2089643, "process": "", "thread": "kworker/2:1-ata_sff", "affinity": [2]}, {"pid": 2091380, "tid": 2091380, "process": "", "thread": "kworker/7:2-mm_percpu_wq", "affinity": [7]}, {"pid": 2092450, "tid": 2092450, "process": "", "thread": "kworker/1:0-events", "affinity": [1]}, {"pid": 2093134, "tid": 2093134, "process": "", "thread": "kworker/u16:1-events_unbound", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2094578, "tid": 2094578, "process": "", "thread": "kworker/6:2-events", "affinity": [6]}, {"pid": 2095361, "tid": 2095361, "process": "", "thread": "kworker/0:1-xfs-conv/sda4", "affinity": [0]}, {"pid": 2095542, "tid": 2095542, "process": "", "thread": "kworker/0:3-xfs-conv/sda4", "affinity": [0]}, {"pid": 2095543, "tid": 2095543, "process": "", "thread": "kworker/0:4-xfs-conv/sda4", "affinity": [0]}, {"pid": 2095544, "tid": 2095544, "process": "", "thread": "kworker/0:5", "affinity": [0]}, {"pid": 2095545, "tid": 2095545, "process": "", "thread": "kworker/0:6-events", "affinity": [0]}, {"pid": 2095561, "tid": 2095561, "process": "", "thread": "kworker/4:0-events", "affinity": [4]}, {"pid": 2096035, "tid": 2096035, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2096046, "tid": 2096046, "process": "coreutils", "thread": "sleep", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2096135, "tid": 2096135, "process": "coreutils", "thread": "sleep", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2096204, "tid": 2096204, "process": "coreutils", "thread": "sleep", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2096507, "tid": 2096507, "process": "coreutils", "thread": "sleep", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2097236, "tid": 2097236, "process": "runc", "thread": "runc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2097236, "tid": 2097237, "process": "runc", "thread": "runc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2097236, "tid": 2097238, "process": "runc", "thread": "runc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2097236, "tid": 2097239, "process": "runc", "thread": "runc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2097236, "tid": 2097240, "process": "runc", "thread": "runc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2097236, "tid": 2097241, "process": "runc", "thread": "runc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2097236, "tid": 2097242, "process": "runc", "thread": "runc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2097236, "tid": 2097243, "process": "runc", "thread": "runc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2097236, "tid": 2097244, "process": "runc", "thread": "runc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2097236, "tid": 2097246, "process": "runc", "thread": "runc", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2097248, "tid": 2097248, "process": "gather-sysinfo", "thread": "gather-sysinfo", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2097248, "tid": 2097260, "process": "gather-sysinfo", "thread": "gather-sysinfo", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2097248, "tid": 2097261, "process": "gather-sysinfo", "thread": "gather-sysinfo", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2097248, "tid": 2097262, "process": "gather-sysinfo", "thread": "gather-sysinfo", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2097248, "tid": 2097263, "process": "gather-sysinfo", "thread": "gather-sysinfo", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2097248, "tid": 2097264, "process": "gather-sysinfo", "thread": "gather-sysinfo", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2097248, "tid": 2097265, "process": "gather-sysinfo", "thread": "gather-sysinfo", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2097248, "tid": 2097266, "process": "gather-sysinfo", "thread": "gather-sysinfo", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2097248, "tid": 2097267, "process": "gather-sysinfo", "thread": "gather-sysinfo", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 2097248, "tid": 2097268, "process": "gather-sysinfo", "thread": "gather-sysinfo", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3869994, "tid": 3869994, "process": "conmon", "thread": "conmon", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3870006, "tid": 1575990, "process": "metrics-server", "thread": "metrics-server", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3870006, "tid": 3870006, "process": "metrics-server", "thread": "metrics-server", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3870006, "tid": 3870025, "process": "metrics-server", "thread": "metrics-server", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3870006, "tid": 3870026, "process": "metrics-server", "thread": "metrics-server", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3870006, "tid": 3870027, "process": "metrics-server", "thread": "metrics-server", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3870006, "tid": 3870031, "process": "metrics-server", "thread": "metrics-server", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3870006, "tid": 3870033, "process": "metrics-server", "thread": "metrics-server", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3870006, "tid": 3870035, "process": "metrics-server", "thread": "metrics-server", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3870006, "tid": 3870036, "process": "metrics-server", "thread": "metrics-server", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3870006, "tid": 3870037, "process": "metrics-server", "thread": "metrics-server", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3870006, "tid": 3870038, "process": "metrics-server", "thread": "metrics-server", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3870006, "tid": 3870039, "process": "metrics-server", "thread": "metrics-server", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3870006, "tid": 3870040, "process": "metrics-server", "thread": "metrics-server", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3870006, "tid": 3870041, "process": "metrics-server", "thread": "metrics-server", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}, {"pid": 3870006, "tid": 3877626, "process": "metrics-server", "thread": "metrics-server", "affinity": [0, 1, 2, 3, 4, 5, 6, 7]}]