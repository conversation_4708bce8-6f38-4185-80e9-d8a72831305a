0000:00:00.0 0600: 8086:7192 (rev 03)
	Control: I/O- Mem+ BusMaster+ SpecCycle- MemWINV- VGASnoop- ParErr- Stepping- SERR- FastB2B- DisINTx-
	Status: Cap- 66MHz- UDF- FastB2B- ParErr- DEVSEL=medium >TAbort- <TAbort- <MAbort- >SERR- <PERR- INTx-
	Latency: 0

0000:00:07.0 0601: 8086:7110 (rev 01)
	Subsystem: 1414:0000
	Control: I/O+ Mem+ BusMaster+ SpecCycle- MemWINV- VGASnoop- ParErr- Stepping- SERR- FastB2B- DisINTx-
	Status: Cap- 66MHz- UDF- FastB2B- ParErr- DEVSEL=medium >TAbort- <TAbort- <MAbort- >SERR- <PERR- INTx-
	Latency: 0

0000:00:07.1 0101: 8086:7111 (rev 01) (prog-if 80 [ISA Compatibility mode-only controller, supports bus mastering])
	Control: I/O+ Mem- BusMaster+ SpecCycle- MemWINV- VGASnoop- ParErr- Stepping- SERR- FastB2B- DisINTx-
	Status: Cap- 66MHz- UDF- FastB2B+ ParErr- DEVSEL=medium >TAbort- <TAbort- <MAbort- >SERR- <PERR- INTx-
	Latency: 0
	Region 0: Memory at 000001f0 (32-bit, non-prefetchable) [virtual] [size=8]
	Region 1: Memory at 000003f0 (type 3, non-prefetchable) [virtual]
	Region 2: Memory at 00000170 (32-bit, non-prefetchable) [virtual] [size=8]
	Region 3: Memory at 00000370 (type 3, non-prefetchable) [virtual]
	Region 4: I/O ports at ffa0 [virtual] [size=16]
	Kernel driver in use: ata_piix
	Kernel modules: ata_piix, ata_generic

0000:00:07.3 0680: 8086:7113 (rev 02)
	Control: I/O+ Mem- BusMaster- SpecCycle- MemWINV- VGASnoop- ParErr- Stepping- SERR- FastB2B- DisINTx-
	Status: Cap- 66MHz- UDF- FastB2B+ ParErr- DEVSEL=medium >TAbort- <TAbort- <MAbort- >SERR- <PERR- INTx-
	Interrupt: pin A routed to IRQ 9
	Kernel modules: i2c_piix4

0000:00:08.0 0300: 1414:5353 (prog-if 00 [VGA controller])
	Control: I/O+ Mem+ BusMaster+ SpecCycle+ MemWINV+ VGASnoop- ParErr- Stepping- SERR+ FastB2B- DisINTx-
	Status: Cap- 66MHz- UDF- FastB2B- ParErr- DEVSEL=fast >TAbort- <TAbort- <MAbort- >SERR- <PERR- INTx-
	Latency: 0
	Interrupt: pin A routed to IRQ 11
	Region 0: Memory at f8000000 (32-bit, non-prefetchable) [size=64M]
	Expansion ROM at 000c0000 [virtual] [disabled] [size=128K]
	Kernel driver in use: hyperv_drm
	Kernel modules: hyperv_drm

64c2:00:02.0 0200: 15b3:1018 (rev 80)
	Subsystem: 15b3:0080
	Physical Slot: 1
	Control: I/O- Mem+ BusMaster+ SpecCycle- MemWINV- VGASnoop- ParErr- Stepping- SERR- FastB2B- DisINTx+
	Status: Cap+ 66MHz- UDF- FastB2B- ParErr- DEVSEL=fast >TAbort- <TAbort- <MAbort- >SERR- <PERR- INTx-
	Latency: 0
	NUMA node: 0
	Region 0: Memory at fc0000000 (64-bit, prefetchable) [size=1M]
	Capabilities: <access denied>
	Kernel driver in use: mlx5_core
	Kernel modules: mlx5_core

