{"pod_resources": [{"name": "node-ca-pdjt4", "namespace": "openshift-image-registry", "containers": [{"name": "node-ca"}]}, {"name": "datadog-agent-cluster-agent-849d76d4c7-dpwlk", "namespace": "datadog", "containers": [{"name": "cluster-agent"}]}, {"name": "metrics-agent-b4767f9ff-q7kzh", "namespace": "cloudability", "containers": [{"name": "metrics-agent"}]}, {"name": "datadog-agent-dlszf", "namespace": "datadog", "containers": [{"name": "agent"}, {"name": "trace-agent"}, {"name": "process-agent"}, {"name": "system-probe"}]}, {"name": "multus-j9ckp", "namespace": "openshift-multus", "containers": [{"name": "kube-multus"}]}, {"name": "network-check-target-c7g56", "namespace": "openshift-network-diagnostics", "containers": [{"name": "network-check-target-container"}]}, {"name": "machine-config-daemon-t6bqx", "namespace": "openshift-machine-config-operator", "containers": [{"name": "machine-config-daemon"}, {"name": "kube-rbac-proxy"}]}, {"name": "vault-3", "namespace": "vault", "containers": [{"name": "vault"}]}, {"name": "dns-default-v8rkw", "namespace": "openshift-dns", "containers": [{"name": "dns"}, {"name": "kube-rbac-proxy"}]}, {"name": "azure-cloud-node-manager-q9dr6", "namespace": "openshift-cloud-controller-manager", "containers": [{"name": "cloud-node-manager"}]}, {"name": "ingress-canary-8nxmc", "namespace": "openshift-ingress-canary", "containers": [{"name": "serve-healthcheck-canary"}]}, {"name": "tuned-5tlcx", "namespace": "openshift-cluster-node-tuning-operator", "containers": [{"name": "tuned"}]}, {"name": "kube-rbac-proxy-crio-ocpazt003-k6zjs-worker-centralus2-w7f8z", "namespace": "openshift-machine-config-operator", "containers": [{"name": "kube-rbac-proxy-crio"}]}, {"name": "node-exporter-2hwds", "namespace": "openshift-monitoring", "containers": [{"name": "node-exporter"}, {"name": "kube-rbac-proxy"}]}, {"name": "node-resolver-mckf8", "namespace": "openshift-dns", "containers": [{"name": "dns-node-resolver"}]}, {"name": "iptables-alerter-s5t44", "namespace": "openshift-network-operator", "containers": [{"name": "iptables-alerter"}]}, {"name": "mdsd-4ws87", "namespace": "openshift-azure-logging", "containers": [{"name": "fluentbit"}, {"name": "mdsd"}]}, {"name": "azure-file-csi-driver-node-5tkzb", "namespace": "openshift-cluster-csi-drivers", "containers": [{"name": "csi-driver"}, {"name": "csi-node-driver-registrar"}, {"name": "csi-liveness-probe"}]}, {"name": "azure-disk-csi-driver-node-mwcdh", "namespace": "openshift-cluster-csi-drivers", "containers": [{"name": "csi-driver"}, {"name": "kube-rbac-proxy-8206"}, {"name": "csi-node-driver-registrar"}, {"name": "csi-liveness-probe"}]}, {"name": "network-metrics-daemon-g7wpt", "namespace": "openshift-multus", "containers": [{"name": "network-metrics-daemon"}, {"name": "kube-rbac-proxy"}]}, {"name": "multus-additional-cni-plugins-k65xr", "namespace": "openshift-multus", "containers": [{"name": "kube-multus-additional-cni-plugins"}]}, {"name": "ovnkube-node-s6hwj", "namespace": "openshift-ovn-kubernetes", "containers": [{"name": "ovn-controller"}, {"name": "ovn-acl-logging"}, {"name": "kube-rbac-proxy-node"}, {"name": "kube-rbac-proxy-ovn-metrics"}, {"name": "northd"}, {"name": "nbdb"}, {"name": "sbdb"}, {"name": "ovnkube-controller"}, {"name": "drop-icmp"}]}, {"name": "perf-node-gather-daemonset-tm29c", "namespace": "openshift-must-gather-xwm28", "containers": [{"name": "node-probe"}]}]}