[{"namespace": "cloudability", "name": "metrics-agent-b4767f9ff-q7kzh", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "Burs<PERSON>", "containers": [{"name": "metrics-agent", "resources": {"limits": {"cpu": "1"}, "requests": {"cpu": "500m"}}}]}, {"namespace": "datadog", "name": "datadog-agent-cluster-agent-849d76d4c7-dpwlk", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "BestEffort", "containers": [{"name": "cluster-agent"}]}, {"namespace": "datadog", "name": "datadog-agent-dlszf", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "BestEffort", "containers": [{"name": "agent"}, {"name": "trace-agent"}, {"name": "process-agent"}, {"name": "system-probe"}]}, {"namespace": "openshift-azure-logging", "name": "mdsd-4ws87", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "Burs<PERSON>", "containers": [{"name": "fluentbit"}, {"name": "mdsd", "resources": {"limits": {"cpu": "200m"}, "requests": {"cpu": "10m"}}}]}, {"namespace": "openshift-cloud-controller-manager", "name": "azure-cloud-node-manager-q9dr6", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "Burs<PERSON>", "containers": [{"name": "cloud-node-manager", "resources": {"requests": {"cpu": "50m"}}}]}, {"namespace": "openshift-cluster-csi-drivers", "name": "azure-disk-csi-driver-node-mwcdh", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "Burs<PERSON>", "containers": [{"name": "csi-driver", "resources": {"requests": {"cpu": "10m"}}}, {"name": "kube-rbac-proxy-8206", "resources": {"requests": {"cpu": "10m"}}}, {"name": "csi-node-driver-registrar", "resources": {"requests": {"cpu": "10m"}}}, {"name": "csi-liveness-probe", "resources": {"requests": {"cpu": "10m"}}}]}, {"namespace": "openshift-cluster-csi-drivers", "name": "azure-file-csi-driver-node-5tkzb", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "Burs<PERSON>", "containers": [{"name": "csi-driver", "resources": {"requests": {"cpu": "10m"}}}, {"name": "csi-node-driver-registrar", "resources": {"requests": {"cpu": "10m"}}}, {"name": "csi-liveness-probe", "resources": {"requests": {"cpu": "10m"}}}]}, {"namespace": "openshift-cluster-node-tuning-operator", "name": "tuned-5tlcx", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "Burs<PERSON>", "containers": [{"name": "tuned", "resources": {"requests": {"cpu": "10m"}}}]}, {"namespace": "openshift-dns", "name": "dns-default-v8rkw", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "Burs<PERSON>", "containers": [{"name": "dns", "resources": {"requests": {"cpu": "50m"}}}, {"name": "kube-rbac-proxy", "resources": {"requests": {"cpu": "10m"}}}]}, {"namespace": "openshift-dns", "name": "node-resolver-mckf8", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "Burs<PERSON>", "containers": [{"name": "dns-node-resolver", "resources": {"requests": {"cpu": "5m"}}}]}, {"namespace": "openshift-image-registry", "name": "node-ca-pdjt4", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "Burs<PERSON>", "containers": [{"name": "node-ca", "resources": {"requests": {"cpu": "10m"}}}]}, {"namespace": "openshift-ingress-canary", "name": "ingress-canary-8nxmc", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "Burs<PERSON>", "containers": [{"name": "serve-healthcheck-canary", "resources": {"requests": {"cpu": "10m"}}}]}, {"namespace": "openshift-machine-config-operator", "name": "kube-rbac-proxy-crio-ocpazt003-k6zjs-worker-centralus2-w7f8z", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "Burs<PERSON>", "containers": [{"name": "kube-rbac-proxy-crio", "resources": {"requests": {"cpu": "20m"}}}]}, {"namespace": "openshift-machine-config-operator", "name": "machine-config-daemon-t6bqx", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "Burs<PERSON>", "containers": [{"name": "machine-config-daemon", "resources": {"requests": {"cpu": "20m"}}}, {"name": "kube-rbac-proxy", "resources": {"requests": {"cpu": "20m"}}}]}, {"namespace": "openshift-monitoring", "name": "node-exporter-2hwds", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "Burs<PERSON>", "containers": [{"name": "node-exporter", "resources": {"requests": {"cpu": "8m"}}}, {"name": "kube-rbac-proxy", "resources": {"requests": {"cpu": "1m"}}}]}, {"namespace": "openshift-multus", "name": "multus-additional-cni-plugins-k65xr", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "Burs<PERSON>", "containers": [{"name": "kube-multus-additional-cni-plugins", "resources": {"requests": {"cpu": "10m"}}}]}, {"namespace": "openshift-multus", "name": "multus-j9ckp", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "Burs<PERSON>", "containers": [{"name": "kube-multus", "resources": {"requests": {"cpu": "10m"}}}]}, {"namespace": "openshift-multus", "name": "network-metrics-daemon-g7wpt", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "Burs<PERSON>", "containers": [{"name": "network-metrics-daemon", "resources": {"requests": {"cpu": "10m"}}}, {"name": "kube-rbac-proxy", "resources": {"requests": {"cpu": "10m"}}}]}, {"namespace": "openshift-must-gather-xwm28", "name": "perf-node-gather-daemonset-tm29c", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "<PERSON><PERSON><PERSON><PERSON>", "containers": [{"name": "node-probe", "resources": {"limits": {"cpu": "100m"}, "requests": {"cpu": "100m"}}}]}, {"namespace": "openshift-network-diagnostics", "name": "network-check-target-c7g56", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "Burs<PERSON>", "containers": [{"name": "network-check-target-container", "resources": {"requests": {"cpu": "10m"}}}]}, {"namespace": "openshift-network-operator", "name": "iptables-alerter-s5t44", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "Burs<PERSON>", "containers": [{"name": "iptables-alerter", "resources": {"limits": {"cpu": "10m"}, "requests": {"cpu": "10m"}}}]}, {"namespace": "openshift-ovn-kubernetes", "name": "ovnkube-node-s6hwj", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "Burs<PERSON>", "containers": [{"name": "ovn-controller", "resources": {"requests": {"cpu": "10m"}}}, {"name": "ovn-acl-logging", "resources": {"requests": {"cpu": "10m"}}}, {"name": "kube-rbac-proxy-node", "resources": {"requests": {"cpu": "10m"}}}, {"name": "kube-rbac-proxy-ovn-metrics", "resources": {"requests": {"cpu": "10m"}}}, {"name": "northd", "resources": {"requests": {"cpu": "10m"}}}, {"name": "nbdb", "resources": {"requests": {"cpu": "10m"}}}, {"name": "sbdb", "resources": {"requests": {"cpu": "10m"}}}, {"name": "ovnkube-controller", "resources": {"requests": {"cpu": "10m"}}}, {"name": "drop-icmp", "resources": {"requests": {"cpu": "5m"}}}]}, {"namespace": "vault", "name": "vault-3", "nodeName": "ocpazt003-k6zjs-worker-centralus2-w7f8z", "qosClass": "BestEffort", "containers": [{"name": "vault"}]}]