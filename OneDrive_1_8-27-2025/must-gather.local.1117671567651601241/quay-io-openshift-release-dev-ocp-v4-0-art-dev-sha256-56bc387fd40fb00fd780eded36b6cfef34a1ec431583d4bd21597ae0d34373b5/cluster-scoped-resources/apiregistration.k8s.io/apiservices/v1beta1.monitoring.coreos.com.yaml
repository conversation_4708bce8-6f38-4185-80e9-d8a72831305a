---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T14:53:38Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: "true"
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:53:38Z"
  name: v1beta1.monitoring.coreos.com
  resourceVersion: "820"
  uid: d4a55ee8-3539-4df3-932c-a890f8e80933
spec:
  group: monitoring.coreos.com
  groupPriorityMinimum: 1000
  version: v1beta1
  versionPriority: 100
status:
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:38Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
