---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T14:52:54Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: "true"
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:52:54Z"
  name: v1alpha1.operator.openshift.io
  resourceVersion: "274"
  uid: e3f7e0a7-e6c0-475d-b013-d255d6e4b33a
spec:
  group: operator.openshift.io
  groupPriorityMinimum: 1080
  version: v1alpha1
  versionPriority: 100
status:
  conditions:
  - lastTransitionTime: "2025-06-25T14:52:54Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
