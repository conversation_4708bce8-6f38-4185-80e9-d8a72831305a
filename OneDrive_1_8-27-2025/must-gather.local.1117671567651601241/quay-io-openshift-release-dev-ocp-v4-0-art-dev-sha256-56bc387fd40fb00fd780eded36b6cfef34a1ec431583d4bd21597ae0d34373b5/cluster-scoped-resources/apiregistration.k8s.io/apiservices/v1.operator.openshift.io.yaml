---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T14:52:51Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: "true"
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:52:51Z"
  name: v1.operator.openshift.io
  resourceVersion: "244"
  uid: dc76ecae-040a-48d4-8888-af518481376f
spec:
  group: operator.openshift.io
  groupPriorityMinimum: 1080
  version: v1
  versionPriority: 100
status:
  conditions:
  - lastTransitionTime: "2025-06-25T14:52:51Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
