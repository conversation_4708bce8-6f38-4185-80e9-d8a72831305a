---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-07-01T14:02:34Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: "true"
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-07-01T14:02:34Z"
  name: v1alpha1.argoproj.io
  resourceVersion: "3149060"
  uid: 2a75a4a0-a5a0-48d8-aaec-f0fc4125164e
spec:
  group: argoproj.io
  groupPriorityMinimum: 1000
  version: v1alpha1
  versionPriority: 100
status:
  conditions:
  - lastTransitionTime: "2025-07-01T14:02:34Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
