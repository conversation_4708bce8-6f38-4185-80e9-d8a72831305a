---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T14:53:40Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: "true"
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:53:40Z"
  name: v1alpha1.performance.openshift.io
  resourceVersion: "956"
  uid: 1d85e1ac-3cce-4206-aceb-cd7209cd0a80
spec:
  group: performance.openshift.io
  groupPriorityMinimum: 1000
  version: v1alpha1
  versionPriority: 100
status:
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:40Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
