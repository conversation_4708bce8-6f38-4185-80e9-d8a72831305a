---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T14:52:34Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: onstart
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:52:34Z"
  name: v1.
  resourceVersion: "10"
  uid: ce7a35af-0f35-4d17-8182-7db5805577d4
spec:
  groupPriorityMinimum: 18000
  version: v1
  versionPriority: 1
status:
  conditions:
  - lastTransitionTime: "2025-06-25T14:52:34Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
