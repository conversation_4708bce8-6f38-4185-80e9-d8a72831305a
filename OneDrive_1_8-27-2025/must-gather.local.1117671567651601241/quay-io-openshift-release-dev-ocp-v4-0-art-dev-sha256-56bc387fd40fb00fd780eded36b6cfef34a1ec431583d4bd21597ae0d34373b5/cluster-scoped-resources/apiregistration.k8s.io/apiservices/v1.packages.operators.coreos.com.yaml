---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T14:59:18Z"
  labels:
    olm.managed: "true"
    olm.owner: packageserver
    olm.owner.kind: ClusterServiceVersion
    olm.owner.namespace: openshift-operator-lifecycle-manager
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:olm.managed: {}
          f:olm.owner: {}
          f:olm.owner.kind: {}
          f:olm.owner.namespace: {}
      f:spec:
        f:caBundle: {}
        f:group: {}
        f:groupPriorityMinimum: {}
        f:service:
          .: {}
          f:name: {}
          f:namespace: {}
          f:port: {}
        f:version: {}
        f:versionPriority: {}
    manager: olm
    operation: Update
    time: "2025-06-25T14:59:18Z"
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:conditions:
          .: {}
          k:{"type":"Available"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T15:08:20Z"
  name: v1.packages.operators.coreos.com
  resourceVersion: "32460"
  uid: a1e7991c-7cbf-4272-b792-4e2750efaa8c
spec:
  caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJ2VENDQVdLZ0F3SUJBZ0lJWmVvd3NWZlU2WEF3Q2dZSUtvWkl6ajBFQXdJd1FqRVdNQlFHQTFVRUNoTU4KVW1Wa0lFaGhkQ3dnU1c1akxqRW9NQ1lHQTFVRUF4TWZiMnh0TFhObGJHWnphV2R1WldRdE5qVmxZVE13WWpFMQpOMlEwWlRrM01EQWVGdzB5TlRBMk1qVXhORFU1TVRoYUZ3MHlOekEyTWpVeE5EVTVNVGhhTUVJeEZqQVVCZ05WCkJBb1REVkpsWkNCSVlYUXNJRWx1WXk0eEtEQW1CZ05WQkFNVEgyOXNiUzF6Wld4bWMybG5ibVZrTFRZMVpXRXoKTUdJeE5UZGtOR1U1TnpBd1dUQVRCZ2NxaGtqT1BRSUJCZ2dxaGtqT1BRTUJCd05DQUFTcloxTlBCc0o5WmdJUwpiamp2Mjd4RXJJdXNDOWZLOG9zRElEM2pJMy84ZVBwbDdPSFAxV0pXNEFUZENEZUNqQzRGUGZKaCtQanFtTzRzCnVTRnNYZkh0bzBJd1FEQU9CZ05WSFE4QkFmOEVCQU1DQWdRd0R3WURWUjBUQVFIL0JBVXdBd0VCL3pBZEJnTlYKSFE0RUZnUVVCUUhlL2lGeGU4YldRemVNaGMyMldNaTl0eUF3Q2dZSUtvWkl6ajBFQXdJRFNRQXdSZ0loQU9NTQpOa29pQURxb255N0o4S3RpUkZtSG5WSG5FNWRqYzFWcitxOEVkY0tyQWlFQXBaa3lxOW51eVozRTBrNCtwTXNUCkNrWUVwV0hWS0Rsb0JCSlBBUUV0MWdjPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
  group: packages.operators.coreos.com
  groupPriorityMinimum: 2000
  service:
    name: packageserver-service
    namespace: openshift-operator-lifecycle-manager
    port: 5443
  version: v1
  versionPriority: 15
status:
  conditions:
  - lastTransitionTime: "2025-06-25T15:08:20Z"
    message: all checks passed
    reason: Passed
    status: "True"
    type: Available
