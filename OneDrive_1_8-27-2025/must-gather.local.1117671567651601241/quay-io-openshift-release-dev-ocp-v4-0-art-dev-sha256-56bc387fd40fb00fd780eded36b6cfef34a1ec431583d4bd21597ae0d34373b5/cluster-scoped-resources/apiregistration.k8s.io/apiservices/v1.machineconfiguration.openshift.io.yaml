---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T14:53:54Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: "true"
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:53:54Z"
  name: v1.machineconfiguration.openshift.io
  resourceVersion: "1587"
  uid: 5a1b8fca-7797-4461-9ae6-018109ae468e
spec:
  group: machineconfiguration.openshift.io
  groupPriorityMinimum: 1000
  version: v1
  versionPriority: 100
status:
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:54Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
