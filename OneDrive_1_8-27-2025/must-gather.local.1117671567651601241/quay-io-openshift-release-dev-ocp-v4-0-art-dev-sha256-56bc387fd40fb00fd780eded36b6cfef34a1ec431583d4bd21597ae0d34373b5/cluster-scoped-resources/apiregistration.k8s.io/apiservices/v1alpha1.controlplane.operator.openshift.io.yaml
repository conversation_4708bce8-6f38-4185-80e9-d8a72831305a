---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T15:18:06Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: "true"
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T15:18:06Z"
  name: v1alpha1.controlplane.operator.openshift.io
  resourceVersion: "40030"
  uid: c8216dce-892d-4b7d-9f04-e1a2807be54f
spec:
  group: controlplane.operator.openshift.io
  groupPriorityMinimum: 1000
  version: v1alpha1
  versionPriority: 100
status:
  conditions:
  - lastTransitionTime: "2025-06-25T15:18:06Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
