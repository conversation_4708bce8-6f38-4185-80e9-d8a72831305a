---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T14:53:48Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: "true"
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:53:48Z"
  name: v2.operators.coreos.com
  resourceVersion: "1264"
  uid: 1bc1ba6f-3152-49c7-918c-703dfd83d64b
spec:
  group: operators.coreos.com
  groupPriorityMinimum: 1000
  version: v2
  versionPriority: 100
status:
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:48Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
