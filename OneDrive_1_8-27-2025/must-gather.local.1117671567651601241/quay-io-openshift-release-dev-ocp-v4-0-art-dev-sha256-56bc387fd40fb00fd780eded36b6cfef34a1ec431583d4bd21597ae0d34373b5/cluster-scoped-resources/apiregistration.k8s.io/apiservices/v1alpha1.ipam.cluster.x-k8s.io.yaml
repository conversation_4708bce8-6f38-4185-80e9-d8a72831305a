---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T18:39:14Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: "true"
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T18:39:14Z"
  name: v1alpha1.ipam.cluster.x-k8s.io
  resourceVersion: "143315"
  uid: cd32bdde-7a3f-4d45-85da-26d63344b36f
spec:
  group: ipam.cluster.x-k8s.io
  groupPriorityMinimum: 1000
  version: v1alpha1
  versionPriority: 100
status:
  conditions:
  - lastTransitionTime: "2025-06-25T18:39:14Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
