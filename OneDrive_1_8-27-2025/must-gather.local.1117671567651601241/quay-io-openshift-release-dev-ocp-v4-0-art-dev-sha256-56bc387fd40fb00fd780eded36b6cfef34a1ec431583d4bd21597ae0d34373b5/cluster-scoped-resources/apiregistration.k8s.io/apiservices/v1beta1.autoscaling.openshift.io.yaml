---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T14:53:40Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: "true"
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:53:40Z"
  name: v1beta1.autoscaling.openshift.io
  resourceVersion: "890"
  uid: 57ae72c1-c085-4f48-aa83-5e01174a7f72
spec:
  group: autoscaling.openshift.io
  groupPriorityMinimum: 1000
  version: v1beta1
  versionPriority: 100
status:
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:40Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
