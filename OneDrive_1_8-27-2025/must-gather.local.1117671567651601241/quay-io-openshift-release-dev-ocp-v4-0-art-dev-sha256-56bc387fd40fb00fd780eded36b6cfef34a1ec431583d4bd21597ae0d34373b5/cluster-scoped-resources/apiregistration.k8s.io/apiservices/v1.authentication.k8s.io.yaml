---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T14:52:34Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: onstart
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:52:34Z"
  name: v1.authentication.k8s.io
  resourceVersion: "13"
  uid: b845bf4c-0a49-48bf-96e2-6d9249cc3acf
spec:
  group: authentication.k8s.io
  groupPriorityMinimum: 17700
  version: v1
  versionPriority: 15
status:
  conditions:
  - lastTransitionTime: "2025-06-25T14:52:34Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
