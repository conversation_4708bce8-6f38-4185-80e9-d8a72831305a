---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T14:52:34Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: onstart
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:52:34Z"
  name: v1.authorization.k8s.io
  resourceVersion: "17"
  uid: 7b060a8e-e55f-44a2-bc11-e001954479c1
spec:
  group: authorization.k8s.io
  groupPriorityMinimum: 17600
  version: v1
  versionPriority: 15
status:
  conditions:
  - lastTransitionTime: "2025-06-25T14:52:34Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
