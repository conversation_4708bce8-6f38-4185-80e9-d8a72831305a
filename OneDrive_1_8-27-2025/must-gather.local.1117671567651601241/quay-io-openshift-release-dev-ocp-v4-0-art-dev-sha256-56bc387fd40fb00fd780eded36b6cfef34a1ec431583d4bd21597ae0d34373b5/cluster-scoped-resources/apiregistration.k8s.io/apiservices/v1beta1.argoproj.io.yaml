---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-07-01T14:02:35Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: "true"
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-07-01T14:02:35Z"
  name: v1beta1.argoproj.io
  resourceVersion: "3149120"
  uid: f2cb5b3d-1b5d-45de-89e7-52c3441bcea5
spec:
  group: argoproj.io
  groupPriorityMinimum: 1000
  version: v1beta1
  versionPriority: 100
status:
  conditions:
  - lastTransitionTime: "2025-07-01T14:02:35Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
