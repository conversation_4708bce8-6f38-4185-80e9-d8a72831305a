---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T18:23:56Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: onstart
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T18:23:56Z"
  name: v1.flowcontrol.apiserver.k8s.io
  resourceVersion: "136433"
  uid: 84f6a943-1cb6-402e-87d4-73d76fd1ba78
spec:
  group: flowcontrol.apiserver.k8s.io
  groupPriorityMinimum: 16100
  version: v1
  versionPriority: 21
status:
  conditions:
  - lastTransitionTime: "2025-06-25T18:23:56Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
