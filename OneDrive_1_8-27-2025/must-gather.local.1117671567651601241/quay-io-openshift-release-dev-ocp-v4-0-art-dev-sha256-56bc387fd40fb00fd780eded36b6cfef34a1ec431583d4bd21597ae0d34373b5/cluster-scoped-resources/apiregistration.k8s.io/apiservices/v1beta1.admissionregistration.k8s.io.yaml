---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-08-01T11:50:56Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: onstart
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-08-01T11:50:56Z"
  name: v1beta1.admissionregistration.k8s.io
  resourceVersion: "21330754"
  uid: a5497570-3945-4c6b-add2-2fd716648cc5
spec:
  group: admissionregistration.k8s.io
  groupPriorityMinimum: 16700
  version: v1beta1
  versionPriority: 12
status:
  conditions:
  - lastTransitionTime: "2025-08-01T11:50:56Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
