---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T14:53:22Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: "true"
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:53:22Z"
  name: v1.cloudcredential.openshift.io
  resourceVersion: "502"
  uid: b8fabcd1-9f6a-4d05-9511-b4a561795d8b
spec:
  group: cloudcredential.openshift.io
  groupPriorityMinimum: 1000
  version: v1
  versionPriority: 100
status:
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:22Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
