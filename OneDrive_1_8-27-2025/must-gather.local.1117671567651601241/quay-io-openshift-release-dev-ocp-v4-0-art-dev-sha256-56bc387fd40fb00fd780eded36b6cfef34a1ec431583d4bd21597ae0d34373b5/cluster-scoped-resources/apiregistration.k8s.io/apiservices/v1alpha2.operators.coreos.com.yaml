---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T14:53:50Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: "true"
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:53:50Z"
  name: v1alpha2.operators.coreos.com
  resourceVersion: "1343"
  uid: 03f47d0c-3974-43b5-b71c-8037d8c5d32e
spec:
  group: operators.coreos.com
  groupPriorityMinimum: 1000
  version: v1alpha2
  versionPriority: 100
status:
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:50Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
