---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T14:54:02Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: "true"
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:54:02Z"
  name: v1alpha5.infrastructure.cluster.x-k8s.io
  resourceVersion: "1908"
  uid: 9f3b9d76-c9c7-4e0c-a515-02e95d507e9a
spec:
  group: infrastructure.cluster.x-k8s.io
  groupPriorityMinimum: 1000
  version: v1alpha5
  versionPriority: 100
status:
  conditions:
  - lastTransitionTime: "2025-06-25T14:54:02Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
