---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T18:39:14Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: "true"
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T18:39:14Z"
  name: v1beta1.ipam.cluster.x-k8s.io
  resourceVersion: "143314"
  uid: c37d0cef-a839-4832-b932-524157a0cc04
spec:
  group: ipam.cluster.x-k8s.io
  groupPriorityMinimum: 1000
  version: v1beta1
  versionPriority: 100
status:
  conditions:
  - lastTransitionTime: "2025-06-25T18:39:14Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
