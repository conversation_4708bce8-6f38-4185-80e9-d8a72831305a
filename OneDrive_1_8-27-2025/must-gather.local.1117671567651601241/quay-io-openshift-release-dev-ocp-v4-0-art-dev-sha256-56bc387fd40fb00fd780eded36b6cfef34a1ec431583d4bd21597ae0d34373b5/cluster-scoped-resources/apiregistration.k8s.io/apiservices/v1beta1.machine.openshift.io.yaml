---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T14:54:01Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: "true"
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:54:01Z"
  name: v1beta1.machine.openshift.io
  resourceVersion: "1799"
  uid: *************-4edc-b05d-aa2b9844f2b5
spec:
  group: machine.openshift.io
  groupPriorityMinimum: 1000
  version: v1beta1
  versionPriority: 100
status:
  conditions:
  - lastTransitionTime: "2025-06-25T14:54:01Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
