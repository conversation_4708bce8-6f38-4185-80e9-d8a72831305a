---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T14:53:39Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: "true"
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:53:39Z"
  name: v1alpha1.operators.coreos.com
  resourceVersion: "875"
  uid: ef61b6a7-485b-4452-b93d-979c7ce33853
spec:
  group: operators.coreos.com
  groupPriorityMinimum: 1000
  version: v1alpha1
  versionPriority: 100
status:
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:39Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
