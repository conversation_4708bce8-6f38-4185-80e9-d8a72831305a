---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T14:53:38Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: "true"
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:53:38Z"
  name: v1.imageregistry.operator.openshift.io
  resourceVersion: "797"
  uid: d1c444a7-8e95-48e9-8ebc-a01353d09bdb
spec:
  group: imageregistry.operator.openshift.io
  groupPriorityMinimum: 1000
  version: v1
  versionPriority: 100
status:
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:38Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
