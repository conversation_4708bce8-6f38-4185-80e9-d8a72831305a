---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T15:22:31Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: "true"
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T15:22:31Z"
  name: v1alpha1.upgrade.managed.openshift.io
  resourceVersion: "42667"
  uid: 30036417-bd97-450d-8487-f740f846a114
spec:
  group: upgrade.managed.openshift.io
  groupPriorityMinimum: 1000
  version: v1alpha1
  versionPriority: 100
status:
  conditions:
  - lastTransitionTime: "2025-06-25T15:22:31Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
