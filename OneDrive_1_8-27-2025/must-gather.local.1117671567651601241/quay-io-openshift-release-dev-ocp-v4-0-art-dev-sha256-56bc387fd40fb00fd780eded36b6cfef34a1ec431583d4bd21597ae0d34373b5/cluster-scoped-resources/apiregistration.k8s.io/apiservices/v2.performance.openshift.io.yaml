---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T14:53:40Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: "true"
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:53:40Z"
  name: v2.performance.openshift.io
  resourceVersion: "957"
  uid: b5a39a54-75fd-4a59-b86a-d795bf310a09
spec:
  group: performance.openshift.io
  groupPriorityMinimum: 1000
  version: v2
  versionPriority: 100
status:
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:40Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
