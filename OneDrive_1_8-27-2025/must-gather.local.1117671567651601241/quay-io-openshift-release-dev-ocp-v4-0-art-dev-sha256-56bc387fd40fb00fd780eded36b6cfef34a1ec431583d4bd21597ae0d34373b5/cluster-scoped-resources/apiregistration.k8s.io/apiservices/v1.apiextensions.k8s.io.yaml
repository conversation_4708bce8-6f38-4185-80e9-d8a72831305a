---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  creationTimestamp: "2025-06-25T14:52:34Z"
  labels:
    kube-aggregator.kubernetes.io/automanaged: onstart
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          .: {}
          f:kube-aggregator.kubernetes.io/automanaged: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:version: {}
        f:versionPriority: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:52:34Z"
  name: v1.apiextensions.k8s.io
  resourceVersion: "15"
  uid: 1ef3ca84-6fff-40b8-b63d-3258615f17e5
spec:
  group: apiextensions.k8s.io
  groupPriorityMinimum: 16700
  version: v1
  versionPriority: 15
status:
  conditions:
  - lastTransitionTime: "2025-06-25T14:52:34Z"
    message: Local APIServices are always available
    reason: Local
    status: "True"
    type: Available
