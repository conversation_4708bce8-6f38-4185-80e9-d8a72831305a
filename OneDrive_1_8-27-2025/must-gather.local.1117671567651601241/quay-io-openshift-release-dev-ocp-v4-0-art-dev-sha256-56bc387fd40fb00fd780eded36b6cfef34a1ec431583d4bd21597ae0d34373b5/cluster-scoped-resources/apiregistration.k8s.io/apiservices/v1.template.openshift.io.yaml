---
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  annotations:
    service.alpha.openshift.io/inject-cabundle: "true"
  creationTimestamp: "2025-06-25T15:00:25Z"
  managedFields:
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:service.alpha.openshift.io/inject-cabundle: {}
      f:spec:
        f:group: {}
        f:groupPriorityMinimum: {}
        f:service:
          .: {}
          f:name: {}
          f:namespace: {}
          f:port: {}
        f:version: {}
        f:versionPriority: {}
    manager: cluster-openshift-apiserver-operator
    operation: Update
    time: "2025-06-25T15:00:25Z"
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        f:caBundle: {}
    manager: service-ca-operator
    operation: Update
    time: "2025-06-25T15:00:25Z"
  - apiVersion: apiregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:conditions:
          .: {}
          k:{"type":"Available"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T15:08:20Z"
  name: v1.template.openshift.io
  resourceVersion: "32464"
  uid: fe5b7ffb-a33b-4606-92a3-69aac68de024
spec:
  caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURVVENDQWptZ0F3SUJBZ0lJZHpKblVydFl3TVV3RFFZSktvWklodmNOQVFFTEJRQXdOakUwTURJR0ExVUUKQXd3cmIzQmxibk5vYVdaMExYTmxjblpwWTJVdGMyVnlkbWx1WnkxemFXZHVaWEpBTVRjMU1EZzJNelE1TkRBZQpGdzB5TlRBMk1qVXhORFU0TVROYUZ3MHlOekE0TWpReE5EVTRNVFJhTURZeE5EQXlCZ05WQkFNTUsyOXdaVzV6CmFHbG1kQzF6WlhKMmFXTmxMWE5sY25acGJtY3RjMmxuYm1WeVFERTNOVEE0TmpNME9UUXdnZ0VpTUEwR0NTcUcKU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRRFNsSjQwa1FtblRTK245U1lCd2QwYW8zRDlUVXlPK1BFQgp0R3FjbGh0L3pmemk5SVRtRmVZeHNOcVZMUDlLQ1hNY0J5L0I2Nk9qV1l5SzJlYU1QbGhCUXp2cmhmU3lCaU5DClpwSFVkYXEzSjZrVVlSUGpGWHRNa1o5YUNVa016UTNqYm42Sm9zbTBxcXFvd0F2Vk1YL21jaWFJWXJNVTFVMEkKbWNhd0VBK3BqeS9ZKzEzSWtlRm9FbitUK0dwUjB6RVFLRnJaK05OM2dTOHBIQ3dpbW9odS9NVHA0WjdEQXI3RApJQzVqcklzU0JoTXQzdVFMU1dhb0VmcHoxemRsSmVFamNwbVdiWEIzbTB6d3FDekNPQm9YZTh3RlZYVGdTK0ZFCjEwNXp4cTBsYzIxS21BRVRDSml4cEQzK1p6NXA5dk1Pa3VMTmsxdXF5c0xhMU5lWlExaVJBZ01CQUFHall6QmgKTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQQmdOVkhSTUJBZjhFQlRBREFRSC9NQjBHQTFVZERnUVdCQlQrU081SwpBTkxudVZ6eG5CZStMS3hyL3dMb2JUQWZCZ05WSFNNRUdEQVdnQlQrU081S0FOTG51Vnp4bkJlK0xLeHIvd0xvCmJUQU5CZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUFTcWc0cG5LdWQrUmN3NUFVcjdVQXBPZTVaOXcyTUhKS3R6MmoKK3BuUHg3dituNWZJdDZoV3BxYlNzREovenZaeDhjSW9mcWgrNTFUOTQ3bmdHQjhxdThtOEprOHFnMncxcENJYgpqWDhNRThBTnVwM3diYVZEYXdGQXJrTE5HbmwzTFB0akV5amkyZGpTUjlUK3pZWHVUa3JvSVQ1Mmg3NUY1bFI1CmFvelJmVjE3bmQvcGpEcHhSNkYycUpMVVh4eGt2elZnQjdBMGs0M1RYSitCTkdrSmNHQXVuQjBPd2JTN2E1T2QKRFRBSGk3NVM5QmN5akhLVk1iNVg0L3FLS25yNlVqSHgxZHp1bC9xWHBYWmFhWndJRHFURHZSZzczRStrTC9xdgpyMUFCcWdxZ0RNb0J6bUpLd1lVa29lbzNmYS9EaThid2NWVnJEekJSeG8rclZWYlc5QT09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
  group: template.openshift.io
  groupPriorityMinimum: 9900
  service:
    name: api
    namespace: openshift-apiserver
    port: 443
  version: v1
  versionPriority: 15
status:
  conditions:
  - lastTransitionTime: "2025-06-25T15:08:20Z"
    message: all checks passed
    reason: Passed
    status: "True"
    type: Available
