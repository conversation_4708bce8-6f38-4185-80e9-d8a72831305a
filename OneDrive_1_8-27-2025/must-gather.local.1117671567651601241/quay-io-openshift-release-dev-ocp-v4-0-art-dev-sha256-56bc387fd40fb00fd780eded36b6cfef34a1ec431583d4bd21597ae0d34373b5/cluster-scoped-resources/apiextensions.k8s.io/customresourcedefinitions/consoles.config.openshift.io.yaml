---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/470
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    release.openshift.io/bootstrap-required: "true"
  creationTimestamp: "2025-06-25T14:52:52Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-bootstrap
    operation: Update
    time: "2025-06-25T14:52:52Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:52:52Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:api.openshift.io/merged-by-featuregates: {}
          f:release.openshift.io/bootstrap-required: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:19:45Z"
  name: consoles.config.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "133573"
  uid: 6c5c70fd-3646-474d-8562-654f98f059ef
spec:
  conversion:
    strategy: None
  group: config.openshift.io
  names:
    kind: Console
    listKind: ConsoleList
    plural: consoles
    singular: console
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "Console holds cluster-wide configuration for the web console,
          including the logout URL, and reports the public URL of the console. The
          canonical name is `cluster`. \n Compatibility level 1: Stable within a major
          release for a minimum of 12 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: spec holds user settable values for configuration
            properties:
              authentication:
                description: ConsoleAuthentication defines a list of optional configuration
                  for console authentication.
                properties:
                  logoutRedirect:
                    description: 'An optional, absolute URL to redirect web browsers
                      to after logging out of the console. If not specified, it will
                      redirect to the default login page. This is required when using
                      an identity provider that supports single sign-on (SSO) such
                      as: - OpenID (Keycloak, Azure) - RequestHeader (GSSAPI, SSPI,
                      SAML) - OAuth (GitHub, GitLab, Google) Logging out of the console
                      will destroy the user''s token. The logoutRedirect provides
                      the user the option to perform single logout (SLO) through the
                      identity provider to destroy their single sign-on session.'
                    pattern: ^$|^((https):\/\/?)[^\s()<>]+(?:\([\w\d]+\)|([^[:punct:]\s]|\/?))$
                    type: string
                type: object
            type: object
          status:
            description: status holds observed values from the cluster. They may not
              be overridden.
            properties:
              consoleURL:
                description: The URL for the console. This will be derived from the
                  host for the route that is created for the console.
                type: string
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: Console
    listKind: ConsoleList
    plural: consoles
    singular: console
  conditions:
  - lastTransitionTime: "2025-06-25T14:52:52Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:52:52Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
