---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/470
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    release.openshift.io/bootstrap-required: "true"
    release.openshift.io/feature-set: Default
  creationTimestamp: "2025-06-25T14:52:51Z"
  generation: 3
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
          f:release.openshift.io/feature-set: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
    manager: cluster-bootstrap
    operation: Update
    time: "2025-06-25T14:52:51Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:52:51Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:api.openshift.io/merged-by-featuregates: {}
          f:release.openshift.io/bootstrap-required: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T11:46:54Z"
  name: apiservers.config.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21327789"
  uid: aaf5954d-3b50-4920-9e7f-5a46bef2684c
spec:
  conversion:
    strategy: None
  group: config.openshift.io
  names:
    kind: APIServer
    listKind: APIServerList
    plural: apiservers
    singular: apiserver
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "APIServer holds configuration (like serving certificates, client
          CA and CORS domains) shared by all API servers in the system, among them
          especially kube-apiserver and openshift-apiserver. The canonical name of
          an instance is 'cluster'. \n Compatibility level 1: Stable within a major
          release for a minimum of 12 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: spec holds user settable values for configuration
            properties:
              additionalCORSAllowedOrigins:
                description: additionalCORSAllowedOrigins lists additional, user-defined
                  regular expressions describing hosts for which the API server allows
                  access using the CORS headers. This may be needed to access the
                  API and the integrated OAuth server from JavaScript applications.
                  The values are regular expressions that correspond to the Golang
                  regular expression language.
                items:
                  type: string
                type: array
              audit:
                default:
                  profile: Default
                description: audit specifies the settings for audit configuration
                  to be applied to all OpenShift-provided API servers in the cluster.
                properties:
                  customRules:
                    description: customRules specify profiles per group. These profile
                      take precedence over the top-level profile field if they apply.
                      They are evaluation from top to bottom and the first one that
                      matches, applies.
                    items:
                      description: AuditCustomRule describes a custom rule for an
                        audit profile that takes precedence over the top-level profile.
                      properties:
                        group:
                          description: group is a name of group a request user must
                            be member of in order to this profile to apply.
                          minLength: 1
                          type: string
                        profile:
                          description: "profile specifies the name of the desired
                            audit policy configuration to be deployed to all OpenShift-provided
                            API servers in the cluster. \n The following profiles
                            are provided: - Default: the existing default policy.
                            - WriteRequestBodies: like 'Default', but logs request
                            and response HTTP payloads for write requests (create,
                            update, patch). - AllRequestBodies: like 'WriteRequestBodies',
                            but also logs request and response HTTP payloads for read
                            requests (get, list). - None: no requests are logged at
                            all, not even oauthaccesstokens and oauthauthorizetokens.
                            \n If unset, the 'Default' profile is used as the default."
                          enum:
                          - Default
                          - WriteRequestBodies
                          - AllRequestBodies
                          - None
                          type: string
                      required:
                      - group
                      - profile
                      type: object
                    type: array
                    x-kubernetes-list-map-keys:
                    - group
                    x-kubernetes-list-type: map
                  profile:
                    default: Default
                    description: "profile specifies the name of the desired top-level
                      audit profile to be applied to all requests sent to any of the
                      OpenShift-provided API servers in the cluster (kube-apiserver,
                      openshift-apiserver and oauth-apiserver), with the exception
                      of those requests that match one or more of the customRules.
                      \n The following profiles are provided: - Default: default policy
                      which means MetaData level logging with the exception of events
                      (not logged at all), oauthaccesstokens and oauthauthorizetokens
                      (both logged at RequestBody level). - WriteRequestBodies: like
                      'Default', but logs request and response HTTP payloads for write
                      requests (create, update, patch). - AllRequestBodies: like 'WriteRequestBodies',
                      but also logs request and response HTTP payloads for read requests
                      (get, list). - None: no requests are logged at all, not even
                      oauthaccesstokens and oauthauthorizetokens. \n Warning: It is
                      not recommended to disable audit logging by using the `None`
                      profile unless you are fully aware of the risks of not logging
                      data that can be beneficial when troubleshooting issues. If
                      you disable audit logging and a support situation arises, you
                      might need to enable audit logging and reproduce the issue in
                      order to troubleshoot properly. \n If unset, the 'Default' profile
                      is used as the default."
                    enum:
                    - Default
                    - WriteRequestBodies
                    - AllRequestBodies
                    - None
                    type: string
                type: object
              clientCA:
                description: 'clientCA references a ConfigMap containing a certificate
                  bundle for the signers that will be recognized for incoming client
                  certificates in addition to the operator managed signers. If this
                  is empty, then only operator managed signers are valid. You usually
                  only have to set this if you have your own PKI you wish to honor
                  client certificates from. The ConfigMap must exist in the openshift-config
                  namespace and contain the following required fields: - ConfigMap.Data["ca-bundle.crt"]
                  - CA bundle.'
                properties:
                  name:
                    description: name is the metadata.name of the referenced config
                      map
                    type: string
                required:
                - name
                type: object
              encryption:
                description: encryption allows the configuration of encryption of
                  resources at the datastore layer.
                properties:
                  type:
                    description: "type defines what encryption type should be used
                      to encrypt resources at the datastore layer. When this field
                      is unset (i.e. when it is set to the empty string), identity
                      is implied. The behavior of unset can and will change over time.
                      \ Even if encryption is enabled by default, the meaning of unset
                      may change to a different encryption type based on changes in
                      best practices. \n When encryption is enabled, all sensitive
                      resources shipped with the platform are encrypted. This list
                      of sensitive resources can and will change over time.  The current
                      authoritative list is: \n 1. secrets 2. configmaps 3. routes.route.openshift.io
                      4. oauthaccesstokens.oauth.openshift.io 5. oauthauthorizetokens.oauth.openshift.io"
                    enum:
                    - ""
                    - identity
                    - aescbc
                    - aesgcm
                    type: string
                type: object
              servingCerts:
                description: servingCert is the TLS cert info for serving secure traffic.
                  If not specified, operator managed certificates will be used for
                  serving secure traffic.
                properties:
                  namedCertificates:
                    description: namedCertificates references secrets containing the
                      TLS cert info for serving secure traffic to specific hostnames.
                      If no named certificates are provided, or no named certificates
                      match the server name as understood by a client, the defaultServingCertificate
                      will be used.
                    items:
                      description: APIServerNamedServingCert maps a server DNS name,
                        as understood by a client, to a certificate.
                      properties:
                        names:
                          description: names is a optional list of explicit DNS names
                            (leading wildcards allowed) that should use this certificate
                            to serve secure traffic. If no names are provided, the
                            implicit names will be extracted from the certificates.
                            Exact names trump over wildcard names. Explicit names
                            defined here trump over extracted implicit names.
                          items:
                            type: string
                          type: array
                        servingCertificate:
                          description: 'servingCertificate references a kubernetes.io/tls
                            type secret containing the TLS cert info for serving secure
                            traffic. The secret must exist in the openshift-config
                            namespace and contain the following required fields: -
                            Secret.Data["tls.key"] - TLS private key. - Secret.Data["tls.crt"]
                            - TLS certificate.'
                          properties:
                            name:
                              description: name is the metadata.name of the referenced
                                secret
                              type: string
                          required:
                          - name
                          type: object
                      type: object
                    type: array
                type: object
              tlsSecurityProfile:
                description: "tlsSecurityProfile specifies settings for TLS connections
                  for externally exposed servers. \n If unset, a default (which may
                  change between releases) is chosen. Note that only Old, Intermediate
                  and Custom profiles are currently supported, and the maximum available
                  minTLSVersion is VersionTLS12."
                properties:
                  custom:
                    description: "custom is a user-defined TLS security profile. Be
                      extremely careful using a custom profile as invalid configurations
                      can be catastrophic. An example custom profile looks like this:
                      \n ciphers: \n - ECDHE-ECDSA-CHACHA20-POLY1305 \n - ECDHE-RSA-CHACHA20-POLY1305
                      \n - ECDHE-RSA-AES128-GCM-SHA256 \n - ECDHE-ECDSA-AES128-GCM-SHA256
                      \n minTLSVersion: VersionTLS11"
                    nullable: true
                    properties:
                      ciphers:
                        description: "ciphers is used to specify the cipher algorithms
                          that are negotiated during the TLS handshake.  Operators
                          may remove entries their operands do not support.  For example,
                          to use DES-CBC3-SHA  (yaml): \n ciphers: - DES-CBC3-SHA"
                        items:
                          type: string
                        type: array
                        x-kubernetes-list-type: atomic
                      minTLSVersion:
                        description: "minTLSVersion is used to specify the minimal
                          version of the TLS protocol that is negotiated during the
                          TLS handshake. For example, to use TLS versions 1.1, 1.2
                          and 1.3 (yaml): \n minTLSVersion: VersionTLS11 \n NOTE:
                          currently the highest minTLSVersion allowed is VersionTLS12"
                        enum:
                        - VersionTLS10
                        - VersionTLS11
                        - VersionTLS12
                        - VersionTLS13
                        type: string
                    type: object
                  intermediate:
                    description: "intermediate is a TLS security profile based on:
                      \n https://wiki.mozilla.org/Security/Server_Side_TLS#Intermediate_compatibility_.28recommended.29
                      \n and looks like this (yaml): \n ciphers: \n - TLS_AES_128_GCM_SHA256
                      \n - TLS_AES_256_GCM_SHA384 \n - TLS_CHACHA20_POLY1305_SHA256
                      \n - ECDHE-ECDSA-AES128-GCM-SHA256 \n - ECDHE-RSA-AES128-GCM-SHA256
                      \n - ECDHE-ECDSA-AES256-GCM-SHA384 \n - ECDHE-RSA-AES256-GCM-SHA384
                      \n - ECDHE-ECDSA-CHACHA20-POLY1305 \n - ECDHE-RSA-CHACHA20-POLY1305
                      \n - DHE-RSA-AES128-GCM-SHA256 \n - DHE-RSA-AES256-GCM-SHA384
                      \n minTLSVersion: VersionTLS12"
                    nullable: true
                    type: object
                  modern:
                    description: "modern is a TLS security profile based on: \n https://wiki.mozilla.org/Security/Server_Side_TLS#Modern_compatibility
                      \n and looks like this (yaml): \n ciphers: \n - TLS_AES_128_GCM_SHA256
                      \n - TLS_AES_256_GCM_SHA384 \n - TLS_CHACHA20_POLY1305_SHA256
                      \n minTLSVersion: VersionTLS13"
                    nullable: true
                    type: object
                  old:
                    description: "old is a TLS security profile based on: \n https://wiki.mozilla.org/Security/Server_Side_TLS#Old_backward_compatibility
                      \n and looks like this (yaml): \n ciphers: \n - TLS_AES_128_GCM_SHA256
                      \n - TLS_AES_256_GCM_SHA384 \n - TLS_CHACHA20_POLY1305_SHA256
                      \n - ECDHE-ECDSA-AES128-GCM-SHA256 \n - ECDHE-RSA-AES128-GCM-SHA256
                      \n - ECDHE-ECDSA-AES256-GCM-SHA384 \n - ECDHE-RSA-AES256-GCM-SHA384
                      \n - ECDHE-ECDSA-CHACHA20-POLY1305 \n - ECDHE-RSA-CHACHA20-POLY1305
                      \n - DHE-RSA-AES128-GCM-SHA256 \n - DHE-RSA-AES256-GCM-SHA384
                      \n - DHE-RSA-CHACHA20-POLY1305 \n - ECDHE-ECDSA-AES128-SHA256
                      \n - ECDHE-RSA-AES128-SHA256 \n - ECDHE-ECDSA-AES128-SHA \n
                      - ECDHE-RSA-AES128-SHA \n - ECDHE-ECDSA-AES256-SHA384 \n - ECDHE-RSA-AES256-SHA384
                      \n - ECDHE-ECDSA-AES256-SHA \n - ECDHE-RSA-AES256-SHA \n - DHE-RSA-AES128-SHA256
                      \n - DHE-RSA-AES256-SHA256 \n - AES128-GCM-SHA256 \n - AES256-GCM-SHA384
                      \n - AES128-SHA256 \n - AES256-SHA256 \n - AES128-SHA \n - AES256-SHA
                      \n - DES-CBC3-SHA \n minTLSVersion: VersionTLS10"
                    nullable: true
                    type: object
                  type:
                    description: "type is one of Old, Intermediate, Modern or Custom.
                      Custom provides the ability to specify individual TLS security
                      profile parameters. Old, Intermediate and Modern are TLS security
                      profiles based on: \n https://wiki.mozilla.org/Security/Server_Side_TLS#Recommended_configurations
                      \n The profiles are intent based, so they may change over time
                      as new ciphers are developed and existing ciphers are found
                      to be insecure.  Depending on precisely which ciphers are available
                      to a process, the list may be reduced. \n Note that the Modern
                      profile is currently not supported because it is not yet well
                      adopted by common software libraries."
                    enum:
                    - Old
                    - Intermediate
                    - Modern
                    - Custom
                    type: string
                type: object
            type: object
          status:
            description: status holds observed values from the cluster. They may not
              be overridden.
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: APIServer
    listKind: APIServerList
    plural: apiservers
    singular: apiserver
  conditions:
  - lastTransitionTime: "2025-06-25T14:52:51Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:52:51Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
