---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/1237
    capability.openshift.io/name: Insights
    include.release.openshift.io/hypershift: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:54:04Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:54:04Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:capability.openshift.io/name: {}
          f:include.release.openshift.io/hypershift: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:42:38Z"
  name: insightsoperators.operator.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "146167"
  uid: d9f107f4-0ae7-4252-aa1a-3fdf29b8aef1
spec:
  conversion:
    strategy: None
  group: operator.openshift.io
  names:
    kind: InsightsOperator
    listKind: InsightsOperatorList
    plural: insightsoperators
    singular: insightsoperator
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "InsightsOperator holds cluster-wide information about the Insights
          Operator. \n Compatibility level 1: Stable within a major release for a
          minimum of 12 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: spec is the specification of the desired behavior of the
              Insights.
            properties:
              logLevel:
                default: Normal
                description: "logLevel is an intent based logging for an overall component.
                  \ It does not give fine grained control, but it is a simple way
                  to manage coarse grained logging choices that operators have to
                  interpret for their operands. \n Valid values are: \"Normal\", \"Debug\",
                  \"Trace\", \"TraceAll\". Defaults to \"Normal\"."
                enum:
                - ""
                - Normal
                - Debug
                - Trace
                - TraceAll
                type: string
              managementState:
                description: managementState indicates whether and how the operator
                  should manage the component
                pattern: ^(Managed|Unmanaged|Force|Removed)$
                type: string
              observedConfig:
                description: observedConfig holds a sparse config that controller
                  has observed from the cluster state.  It exists in spec because
                  it is an input to the level for the operator
                nullable: true
                type: object
                x-kubernetes-preserve-unknown-fields: true
              operatorLogLevel:
                default: Normal
                description: "operatorLogLevel is an intent based logging for the
                  operator itself.  It does not give fine grained control, but it
                  is a simple way to manage coarse grained logging choices that operators
                  have to interpret for themselves. \n Valid values are: \"Normal\",
                  \"Debug\", \"Trace\", \"TraceAll\". Defaults to \"Normal\"."
                enum:
                - ""
                - Normal
                - Debug
                - Trace
                - TraceAll
                type: string
              unsupportedConfigOverrides:
                description: 'unsupportedConfigOverrides holds a sparse config that
                  will override any previously set options.  It only needs to be the
                  fields to override it will end up overlaying in the following order:
                  1. hardcoded defaults 2. observedConfig 3. unsupportedConfigOverrides'
                nullable: true
                type: object
                x-kubernetes-preserve-unknown-fields: true
            type: object
          status:
            description: status is the most recently observed status of the Insights
              operator.
            properties:
              conditions:
                description: conditions is a list of conditions and their status
                items:
                  description: OperatorCondition is just the standard condition fields.
                  properties:
                    lastTransitionTime:
                      format: date-time
                      type: string
                    message:
                      type: string
                    reason:
                      type: string
                    status:
                      type: string
                    type:
                      type: string
                  type: object
                type: array
              gatherStatus:
                description: gatherStatus provides basic information about the last
                  Insights data gathering. When omitted, this means no data gathering
                  has taken place yet.
                properties:
                  gatherers:
                    description: gatherers is a list of active gatherers (and their
                      statuses) in the last gathering.
                    items:
                      description: gathererStatus represents information about a particular
                        data gatherer.
                      properties:
                        conditions:
                          description: conditions provide details on the status of
                            each gatherer.
                          items:
                            description: "Condition contains details for one aspect
                              of the current state of this API Resource. --- This
                              struct is intended for direct use as an array at the
                              field path .status.conditions.  For example, type FooStatus
                              struct{     // Represents the observations of a foo's
                              current state.     // Known .status.conditions.type
                              are: \"Available\", \"Progressing\", and \"Degraded\"
                              \    // +patchMergeKey=type     // +patchStrategy=merge
                              \    // +listType=map     // +listMapKey=type     Conditions
                              []metav1.Condition `json:\"conditions,omitempty\" patchStrategy:\"merge\"
                              patchMergeKey:\"type\" protobuf:\"bytes,1,rep,name=conditions\"`
                              \n     // other fields }"
                            properties:
                              lastTransitionTime:
                                description: lastTransitionTime is the last time the
                                  condition transitioned from one status to another.
                                  This should be when the underlying condition changed.  If
                                  that is not known, then using the time when the
                                  API field changed is acceptable.
                                format: date-time
                                type: string
                              message:
                                description: message is a human readable message indicating
                                  details about the transition. This may be an empty
                                  string.
                                maxLength: 32768
                                type: string
                              observedGeneration:
                                description: observedGeneration represents the .metadata.generation
                                  that the condition was set based upon. For instance,
                                  if .metadata.generation is currently 12, but the
                                  .status.conditions[x].observedGeneration is 9, the
                                  condition is out of date with respect to the current
                                  state of the instance.
                                format: int64
                                minimum: 0
                                type: integer
                              reason:
                                description: reason contains a programmatic identifier
                                  indicating the reason for the condition's last transition.
                                  Producers of specific condition types may define
                                  expected values and meanings for this field, and
                                  whether the values are considered a guaranteed API.
                                  The value should be a CamelCase string. This field
                                  may not be empty.
                                maxLength: 1024
                                minLength: 1
                                pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                                type: string
                              status:
                                description: status of the condition, one of True,
                                  False, Unknown.
                                enum:
                                - "True"
                                - "False"
                                - Unknown
                                type: string
                              type:
                                description: type of condition in CamelCase or in
                                  foo.example.com/CamelCase. --- Many .condition.type
                                  values are consistent across resources like Available,
                                  but because arbitrary conditions can be useful (see
                                  .node.status.conditions), the ability to deconflict
                                  is important. The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                                maxLength: 316
                                pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                                type: string
                            required:
                            - lastTransitionTime
                            - message
                            - reason
                            - status
                            - type
                            type: object
                          minItems: 1
                          type: array
                          x-kubernetes-list-type: atomic
                        lastGatherDuration:
                          description: lastGatherDuration represents the time spent
                            gathering.
                          pattern: ^([1-9][0-9]*(\.[0-9]+)?(ns|us|µs|ms|s|m|h))+$
                          type: string
                        name:
                          description: name is the name of the gatherer.
                          maxLength: 256
                          minLength: 5
                          type: string
                      required:
                      - conditions
                      - lastGatherDuration
                      - name
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  lastGatherDuration:
                    description: lastGatherDuration is the total time taken to process
                      all gatherers during the last gather event.
                    pattern: ^0|([1-9][0-9]*(\.[0-9]+)?(ns|us|µs|ms|s|m|h))+$
                    type: string
                  lastGatherTime:
                    description: lastGatherTime is the last time when Insights data
                      gathering finished. An empty value means that no data has been
                      gathered yet.
                    format: date-time
                    type: string
                type: object
              generations:
                description: generations are used to determine when an item needs
                  to be reconciled or has changed in a way that needs a reaction.
                items:
                  description: GenerationStatus keeps track of the generation for
                    a given resource so that decisions about forced updates can be
                    made.
                  properties:
                    group:
                      description: group is the group of the thing you're tracking
                      type: string
                    hash:
                      description: hash is an optional field set for resources without
                        generation that are content sensitive like secrets and configmaps
                      type: string
                    lastGeneration:
                      description: lastGeneration is the last generation of the workload
                        controller involved
                      format: int64
                      type: integer
                    name:
                      description: name is the name of the thing you're tracking
                      type: string
                    namespace:
                      description: namespace is where the thing you're tracking is
                      type: string
                    resource:
                      description: resource is the resource type of the thing you're
                        tracking
                      type: string
                  type: object
                type: array
              insightsReport:
                description: insightsReport provides general Insights analysis results.
                  When omitted, this means no data gathering has taken place yet.
                properties:
                  downloadedAt:
                    description: downloadedAt is the time when the last Insights report
                      was downloaded. An empty value means that there has not been
                      any Insights report downloaded yet and it usually appears in
                      disconnected clusters (or clusters when the Insights data gathering
                      is disabled).
                    format: date-time
                    type: string
                  healthChecks:
                    description: healthChecks provides basic information about active
                      Insights health checks in a cluster.
                    items:
                      description: healthCheck represents an Insights health check
                        attributes.
                      properties:
                        advisorURI:
                          description: advisorURI provides the URL link to the Insights
                            Advisor.
                          pattern: ^https:\/\/\S+
                          type: string
                        description:
                          description: description provides basic description of the
                            healtcheck.
                          maxLength: 2048
                          minLength: 10
                          type: string
                        state:
                          description: state determines what the current state of
                            the health check is. Health check is enabled by default
                            and can be disabled by the user in the Insights advisor
                            user interface.
                          enum:
                          - Enabled
                          - Disabled
                          type: string
                        totalRisk:
                          description: totalRisk of the healthcheck. Indicator of
                            the total risk posed by the detected issue; combination
                            of impact and likelihood. The values can be from 1 to
                            4, and the higher the number, the more important the issue.
                          format: int32
                          maximum: 4
                          minimum: 1
                          type: integer
                      required:
                      - advisorURI
                      - description
                      - state
                      - totalRisk
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                type: object
              observedGeneration:
                description: observedGeneration is the last generation change you've
                  dealt with
                format: int64
                type: integer
              readyReplicas:
                description: readyReplicas indicates how many replicas are ready and
                  at the desired state
                format: int32
                type: integer
              version:
                description: version is the level this availability applies to
                type: string
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      scale:
        labelSelectorPath: .status.selector
        specReplicasPath: .spec.replicas
        statusReplicasPath: .status.availableReplicas
      status: {}
status:
  acceptedNames:
    kind: InsightsOperator
    listKind: InsightsOperatorList
    plural: insightsoperators
    singular: insightsoperator
  conditions:
  - lastTransitionTime: "2025-06-25T14:54:04Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:54:04Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
