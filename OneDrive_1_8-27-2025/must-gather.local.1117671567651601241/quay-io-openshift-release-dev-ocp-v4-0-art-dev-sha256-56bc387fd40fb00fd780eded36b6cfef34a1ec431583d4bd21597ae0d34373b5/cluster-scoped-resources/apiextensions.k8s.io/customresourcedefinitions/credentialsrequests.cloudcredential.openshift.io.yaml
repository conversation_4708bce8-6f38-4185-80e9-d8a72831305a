---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    capability.openshift.io/name: CloudCredential
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
  creationTimestamp: "2025-06-25T14:53:22Z"
  generation: 2
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:capability.openshift.io/name: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
    manager: cluster-bootstrap
    operation: Update
    time: "2025-06-25T14:53:22Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:22Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:19:44Z"
  name: credentialsrequests.cloudcredential.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "133558"
  uid: 7571829a-a2e4-4bf2-a8ed-c41325c6f5af
spec:
  conversion:
    strategy: None
  group: cloudcredential.openshift.io
  names:
    kind: CredentialsRequest
    listKind: CredentialsRequestList
    plural: credentialsrequests
    singular: credentialsrequest
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: CredentialsRequest is the Schema for the credentialsrequests
          API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: CredentialsRequestSpec defines the desired state of CredentialsRequest
            properties:
              cloudTokenPath:
                description: "cloudTokenPath is the path where the Kubernetes ServiceAccount
                  token (JSON Web Token) is mounted on the deployment for the workload
                  requesting a credentials secret. The presence of this field in combination
                  with fields such as spec.providerSpec.stsIAMRoleARN indicate that
                  CCO should broker creation of a credentials secret containing fields
                  necessary for token based authentication methods such as with the
                  AWS Secure Token Service (STS). \n cloudTokenPath may also be used
                  to specify the azure_federated_token_file path used in Azure configuration
                  secrets generated by ccoctl. Defaults to \"/var/run/secrets/openshift/serviceaccount/token\"."
                type: string
              providerSpec:
                description: ProviderSpec contains the cloud provider specific credentials
                  specification.
                type: object
                x-kubernetes-preserve-unknown-fields: true
              secretRef:
                description: SecretRef points to the secret where the credentials
                  should be stored once generated.
                properties:
                  apiVersion:
                    description: API version of the referent.
                    type: string
                  fieldPath:
                    description: 'If referring to a piece of an object instead of
                      an entire object, this string should contain a valid JSON/Go
                      field access statement, such as desiredState.manifest.containers[2].
                      For example, if the object reference is to a container within
                      a pod, this would take on a value like: "spec.containers{name}"
                      (where "name" refers to the name of the container that triggered
                      the event) or if no container name is specified "spec.containers[2]"
                      (container with index 2 in this pod). This syntax is chosen
                      only to have some well-defined way of referencing a part of
                      an object. TODO: this design is not final and this field is
                      subject to change in the future.'
                    type: string
                  kind:
                    description: 'Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
                    type: string
                  name:
                    description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names'
                    type: string
                  namespace:
                    description: 'Namespace of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/'
                    type: string
                  resourceVersion:
                    description: 'Specific resourceVersion to which this reference
                      is made, if any. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency'
                    type: string
                  uid:
                    description: 'UID of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids'
                    type: string
                type: object
              serviceAccountNames:
                description: ServiceAccountNames contains a list of ServiceAccounts
                  that will use permissions associated with this CredentialsRequest.
                  This is not used by CCO, but the information is needed for being
                  able to properly set up access control in the cloud provider when
                  the ServiceAccounts are used as part of the cloud credentials flow.
                items:
                  type: string
                type: array
            required:
            - secretRef
            type: object
          status:
            description: CredentialsRequestStatus defines the observed state of CredentialsRequest
            properties:
              conditions:
                description: Conditions includes detailed status for the CredentialsRequest
                items:
                  description: CredentialsRequestCondition contains details for any
                    of the conditions on a CredentialsRequest object
                  properties:
                    lastProbeTime:
                      description: LastProbeTime is the last time we probed the condition
                      format: date-time
                      type: string
                    lastTransitionTime:
                      description: LastTransitionTime is the last time the condition
                        transitioned from one status to another.
                      format: date-time
                      type: string
                    message:
                      description: Message is a human-readable message indicating
                        details about the last transition
                      type: string
                    reason:
                      description: Reason is a unique, one-word, CamelCase reason
                        for the condition's last transition
                      type: string
                    status:
                      description: Status is the status of the condition
                      type: string
                    type:
                      description: Type is the specific type of the condition
                      type: string
                  required:
                  - status
                  - type
                  type: object
                type: array
              lastSyncCloudCredsSecretResourceVersion:
                description: LastSyncCloudCredsSecretResourceVersion is the resource
                  version of the cloud credentials secret resource when the credentials
                  request resource was last synced. Used to determine if the cloud
                  credentials have been updated since the last sync.
                type: string
              lastSyncGeneration:
                description: LastSyncGeneration is the generation of the credentials
                  request resource that was last synced. Used to determine if the
                  object has changed and requires a sync.
                format: int64
                type: integer
              lastSyncTimestamp:
                description: LastSyncTimestamp is the time that the credentials were
                  last synced.
                format: date-time
                type: string
              providerStatus:
                description: ProviderStatus contains cloud provider specific status.
                type: object
                x-kubernetes-preserve-unknown-fields: true
              provisioned:
                description: Provisioned is true once the credentials have been initially
                  provisioned.
                type: boolean
            required:
            - lastSyncGeneration
            - provisioned
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: CredentialsRequest
    listKind: CredentialsRequestList
    plural: credentialsrequests
    singular: credentialsrequest
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:22Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:22Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
