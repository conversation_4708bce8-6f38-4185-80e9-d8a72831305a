---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/470
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    release.openshift.io/bootstrap-required: "true"
  creationTimestamp: "2025-06-25T14:52:47Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-bootstrap
    operation: Update
    time: "2025-06-25T14:52:47Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:52:51Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:api.openshift.io/merged-by-featuregates: {}
          f:release.openshift.io/bootstrap-required: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:19:44Z"
  name: proxies.config.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "133560"
  uid: cd233f87-988d-436b-955d-3205c4bd4458
spec:
  conversion:
    strategy: None
  group: config.openshift.io
  names:
    kind: Proxy
    listKind: ProxyList
    plural: proxies
    singular: proxy
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "Proxy holds cluster-wide information on how to configure default
          proxies for the cluster. The canonical name is `cluster` \n Compatibility
          level 1: Stable within a major release for a minimum of 12 months or 3 minor
          releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: Spec holds user-settable values for the proxy configuration
            properties:
              httpProxy:
                description: httpProxy is the URL of the proxy for HTTP requests.  Empty
                  means unset and will not result in an env var.
                type: string
              httpsProxy:
                description: httpsProxy is the URL of the proxy for HTTPS requests.  Empty
                  means unset and will not result in an env var.
                type: string
              noProxy:
                description: noProxy is a comma-separated list of hostnames and/or
                  CIDRs and/or IPs for which the proxy should not be used. Empty means
                  unset and will not result in an env var.
                type: string
              readinessEndpoints:
                description: readinessEndpoints is a list of endpoints used to verify
                  readiness of the proxy.
                items:
                  type: string
                type: array
              trustedCA:
                description: "trustedCA is a reference to a ConfigMap containing a
                  CA certificate bundle. The trustedCA field should only be consumed
                  by a proxy validator. The validator is responsible for reading the
                  certificate bundle from the required key \"ca-bundle.crt\", merging
                  it with the system default trust bundle, and writing the merged
                  trust bundle to a ConfigMap named \"trusted-ca-bundle\" in the \"openshift-config-managed\"
                  namespace. Clients that expect to make proxy connections must use
                  the trusted-ca-bundle for all HTTPS requests to the proxy, and may
                  use the trusted-ca-bundle for non-proxy HTTPS requests as well.
                  \n The namespace for the ConfigMap referenced by trustedCA is \"openshift-config\".
                  Here is an example ConfigMap (in yaml): \n apiVersion: v1 kind:
                  ConfigMap metadata: name: user-ca-bundle namespace: openshift-config
                  data: ca-bundle.crt: | -----BEGIN CERTIFICATE----- Custom CA certificate
                  bundle. -----END CERTIFICATE-----"
                properties:
                  name:
                    description: name is the metadata.name of the referenced config
                      map
                    type: string
                required:
                - name
                type: object
            type: object
          status:
            description: status holds observed values from the cluster. They may not
              be overridden.
            properties:
              httpProxy:
                description: httpProxy is the URL of the proxy for HTTP requests.
                type: string
              httpsProxy:
                description: httpsProxy is the URL of the proxy for HTTPS requests.
                type: string
              noProxy:
                description: noProxy is a comma-separated list of hostnames and/or
                  CIDRs for which the proxy should not be used.
                type: string
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: Proxy
    listKind: ProxyList
    plural: proxies
    singular: proxy
  conditions:
  - lastTransitionTime: "2025-06-25T14:52:51Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:52:51Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
