---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/475
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    release.openshift.io/feature-set: Default
  creationTimestamp: "2025-06-25T14:53:40Z"
  generation: 3
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:40Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
          f:release.openshift.io/feature-set: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T12:17:55Z"
  name: networks.operator.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21351964"
  uid: 0b6f0725-fc2a-4789-a9c1-ccff6175b74c
spec:
  conversion:
    strategy: None
  group: operator.openshift.io
  names:
    kind: Network
    listKind: NetworkList
    plural: networks
    singular: network
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "Network describes the cluster's desired network configuration.
          It is consumed by the cluster-network-operator. \n Compatibility level 1:
          Stable within a major release for a minimum of 12 months or 3 minor releases
          (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: NetworkSpec is the top-level network configuration object.
            properties:
              additionalNetworks:
                description: additionalNetworks is a list of extra networks to make
                  available to pods when multiple networks are enabled.
                items:
                  description: AdditionalNetworkDefinition configures an extra network
                    that is available but not created by default. Instead, pods must
                    request them by name. type must be specified, along with exactly
                    one "Config" that matches the type.
                  properties:
                    name:
                      description: name is the name of the network. This will be populated
                        in the resulting CRD This must be unique.
                      type: string
                    namespace:
                      description: namespace is the namespace of the network. This
                        will be populated in the resulting CRD If not given the network
                        will be created in the default namespace.
                      type: string
                    rawCNIConfig:
                      description: rawCNIConfig is the raw CNI configuration json
                        to create in the NetworkAttachmentDefinition CRD
                      type: string
                    simpleMacvlanConfig:
                      description: SimpleMacvlanConfig configures the macvlan interface
                        in case of type:NetworkTypeSimpleMacvlan
                      properties:
                        ipamConfig:
                          description: IPAMConfig configures IPAM module will be used
                            for IP Address Management (IPAM).
                          properties:
                            staticIPAMConfig:
                              description: StaticIPAMConfig configures the static
                                IP address in case of type:IPAMTypeStatic
                              properties:
                                addresses:
                                  description: Addresses configures IP address for
                                    the interface
                                  items:
                                    description: StaticIPAMAddresses provides IP address
                                      and Gateway for static IPAM addresses
                                    properties:
                                      address:
                                        description: Address is the IP address in
                                          CIDR format
                                        type: string
                                      gateway:
                                        description: Gateway is IP inside of subnet
                                          to designate as the gateway
                                        type: string
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                dns:
                                  description: DNS configures DNS for the interface
                                  properties:
                                    domain:
                                      description: Domain configures the domainname
                                        the local domain used for short hostname lookups
                                      type: string
                                    nameservers:
                                      description: Nameservers points DNS servers
                                        for IP lookup
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    search:
                                      description: Search configures priority ordered
                                        search domains for short hostname lookups
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                  type: object
                                routes:
                                  description: Routes configures IP routes for the
                                    interface
                                  items:
                                    description: StaticIPAMRoutes provides Destination/Gateway
                                      pairs for static IPAM routes
                                    properties:
                                      destination:
                                        description: Destination points the IP route
                                          destination
                                        type: string
                                      gateway:
                                        description: Gateway is the route's next-hop
                                          IP address If unset, a default gateway is
                                          assumed (as determined by the CNI plugin).
                                        type: string
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                            type:
                              description: Type is the type of IPAM module will be
                                used for IP Address Management(IPAM). The supported
                                values are IPAMTypeDHCP, IPAMTypeStatic
                              type: string
                          type: object
                        master:
                          description: master is the host interface to create the
                            macvlan interface from. If not specified, it will be default
                            route interface
                          type: string
                        mode:
                          description: 'mode is the macvlan mode: bridge, private,
                            vepa, passthru. The default is bridge'
                          type: string
                        mtu:
                          description: mtu is the mtu to use for the macvlan interface.
                            if unset, host's kernel will select the value.
                          format: int32
                          minimum: 0
                          type: integer
                      type: object
                    type:
                      description: type is the type of network The supported values
                        are NetworkTypeRaw, NetworkTypeSimpleMacvlan
                      type: string
                  required:
                  - name
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - name
                x-kubernetes-list-type: map
              clusterNetwork:
                description: clusterNetwork is the IP address pool to use for pod
                  IPs. Some network providers support multiple ClusterNetworks. Others
                  only support one. This is equivalent to the cluster-cidr.
                items:
                  description: ClusterNetworkEntry is a subnet from which to allocate
                    PodIPs. A network of size HostPrefix (in CIDR notation) will be
                    allocated when nodes join the cluster. If the HostPrefix field
                    is not used by the plugin, it can be left unset. Not all network
                    providers support multiple ClusterNetworks
                  properties:
                    cidr:
                      type: string
                    hostPrefix:
                      format: int32
                      minimum: 0
                      type: integer
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              defaultNetwork:
                description: defaultNetwork is the "default" network that all pods
                  will receive
                properties:
                  openshiftSDNConfig:
                    description: 'openShiftSDNConfig was previously used to configure
                      the openshift-sdn plugin. DEPRECATED: OpenShift SDN is no longer
                      supported.'
                    properties:
                      enableUnidling:
                        description: enableUnidling controls whether or not the service
                          proxy will support idling and unidling of services. By default,
                          unidling is enabled.
                        type: boolean
                      mode:
                        description: mode is one of "Multitenant", "Subnet", or "NetworkPolicy"
                        type: string
                      mtu:
                        description: mtu is the mtu to use for the tunnel interface.
                          Defaults to 1450 if unset. This must be 50 bytes smaller
                          than the machine's uplink.
                        format: int32
                        minimum: 0
                        type: integer
                      useExternalOpenvswitch:
                        description: useExternalOpenvswitch used to control whether
                          the operator would deploy an OVS DaemonSet itself or expect
                          someone else to start OVS. As of 4.6, OVS is always run
                          as a system service, and this flag is ignored.
                        type: boolean
                      vxlanPort:
                        description: vxlanPort is the port to use for all vxlan packets.
                          The default is 4789.
                        format: int32
                        minimum: 0
                        type: integer
                    type: object
                  ovnKubernetesConfig:
                    description: ovnKubernetesConfig configures the ovn-kubernetes
                      plugin.
                    properties:
                      egressIPConfig:
                        description: egressIPConfig holds the configuration for EgressIP
                          options.
                        properties:
                          reachabilityTotalTimeoutSeconds:
                            description: reachabilityTotalTimeout configures the EgressIP
                              node reachability check total timeout in seconds. If
                              the EgressIP node cannot be reached within this timeout,
                              the node is declared down. Setting a large value may
                              cause the EgressIP feature to react slowly to node changes.
                              In particular, it may react slowly for EgressIP nodes
                              that really have a genuine problem and are unreachable.
                              When omitted, this means the user has no opinion and
                              the platform is left to choose a reasonable default,
                              which is subject to change over time. The current default
                              is 1 second. A value of 0 disables the EgressIP node's
                              reachability check.
                            format: int32
                            maximum: 60
                            minimum: 0
                            type: integer
                        type: object
                      gatewayConfig:
                        description: gatewayConfig holds the configuration for node
                          gateway options.
                        properties:
                          ipForwarding:
                            description: IPForwarding controls IP forwarding for all
                              traffic on OVN-Kubernetes managed interfaces (such as
                              br-ex). By default this is set to Restricted, and Kubernetes
                              related traffic is still forwarded appropriately, but
                              other IP traffic will not be routed by the OCP node.
                              If there is a desire to allow the host to forward traffic
                              across OVN-Kubernetes managed interfaces, then set this
                              field to "Global". The supported values are "Restricted"
                              and "Global".
                            type: string
                          ipv4:
                            description: ipv4 allows users to configure IP settings
                              for IPv4 connections. When omitted, this means no opinion
                              and the default configuration is used. Check individual
                              members fields within ipv4 for details of default values.
                            properties:
                              internalMasqueradeSubnet:
                                description: internalMasqueradeSubnet contains the
                                  masquerade addresses in IPV4 CIDR format used internally
                                  by ovn-kubernetes to enable host to service traffic.
                                  Each host in the cluster is configured with these
                                  addresses, as well as the shared gateway bridge
                                  interface. The values can be changed after installation.
                                  The subnet chosen should not overlap with other
                                  networks specified for OVN-Kubernetes as well as
                                  other networks used on the host. Additionally the
                                  subnet must be large enough to accommodate 6 IPs
                                  (maximum prefix length /29). When omitted, this
                                  means no opinion and the platform is left to choose
                                  a reasonable default which is subject to change
                                  over time. The current default subnet is *************/29
                                  The value must be in proper IPV4 CIDR format
                                maxLength: 18
                                type: string
                                x-kubernetes-validations:
                                - message: Subnet must be in valid IPV4 CIDR format
                                  rule: isCIDR(self) && cidr(self).ip().family() ==
                                    4
                                - message: subnet must be in the range /0 to /29 inclusive
                                  rule: isCIDR(self) && cidr(self).prefixLength()
                                    <= 29
                                - message: first IP address octet must not be 0
                                  rule: isCIDR(self) && int(self.split('.')[0]) >
                                    0
                            type: object
                          ipv6:
                            description: ipv6 allows users to configure IP settings
                              for IPv6 connections. When omitted, this means no opinion
                              and the default configuration is used. Check individual
                              members fields within ipv6 for details of default values.
                            properties:
                              internalMasqueradeSubnet:
                                description: internalMasqueradeSubnet contains the
                                  masquerade addresses in IPV6 CIDR format used internally
                                  by ovn-kubernetes to enable host to service traffic.
                                  Each host in the cluster is configured with these
                                  addresses, as well as the shared gateway bridge
                                  interface. The values can be changed after installation.
                                  The subnet chosen should not overlap with other
                                  networks specified for OVN-Kubernetes as well as
                                  other networks used on the host. Additionally the
                                  subnet must be large enough to accommodate 6 IPs
                                  (maximum prefix length /125). When omitted, this
                                  means no opinion and the platform is left to choose
                                  a reasonable default which is subject to change
                                  over time. The current default subnet is fd69::/125
                                  Note that IPV6 dual addresses are not permitted
                                type: string
                                x-kubernetes-validations:
                                - message: Subnet must be in valid IPV6 CIDR format
                                  rule: isCIDR(self) && cidr(self).ip().family() ==
                                    6
                                - message: subnet must be in the range /0 to /125
                                    inclusive
                                  rule: isCIDR(self) && cidr(self).prefixLength()
                                    <= 125
                            type: object
                          routingViaHost:
                            default: false
                            description: RoutingViaHost allows pod egress traffic
                              to exit via the ovn-k8s-mp0 management port into the
                              host before sending it out. If this is not set, traffic
                              will always egress directly from OVN to outside without
                              touching the host stack. Setting this to true means
                              hardware offload will not be supported. Default is false
                              if GatewayConfig is specified.
                            type: boolean
                        type: object
                      genevePort:
                        description: geneve port is the UDP port to be used by geneve
                          encapulation. Default is 6081
                        format: int32
                        minimum: 1
                        type: integer
                      hybridOverlayConfig:
                        description: HybridOverlayConfig configures an additional
                          overlay network for peers that are not using OVN.
                        properties:
                          hybridClusterNetwork:
                            description: HybridClusterNetwork defines a network space
                              given to nodes on an additional overlay network.
                            items:
                              description: ClusterNetworkEntry is a subnet from which
                                to allocate PodIPs. A network of size HostPrefix (in
                                CIDR notation) will be allocated when nodes join the
                                cluster. If the HostPrefix field is not used by the
                                plugin, it can be left unset. Not all network providers
                                support multiple ClusterNetworks
                              properties:
                                cidr:
                                  type: string
                                hostPrefix:
                                  format: int32
                                  minimum: 0
                                  type: integer
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          hybridOverlayVXLANPort:
                            description: HybridOverlayVXLANPort defines the VXLAN
                              port number to be used by the additional overlay network.
                              Default is 4789
                            format: int32
                            type: integer
                        type: object
                      ipsecConfig:
                        default:
                          mode: Disabled
                        description: ipsecConfig enables and configures IPsec for
                          pods on the pod network within the cluster.
                        properties:
                          mode:
                            description: mode defines the behaviour of the ipsec configuration
                              within the platform. Valid values are `Disabled`, `External`
                              and `Full`. When 'Disabled', ipsec will not be enabled
                              at the node level. When 'External', ipsec is enabled
                              on the node level but requires the user to configure
                              the secure communication parameters. This mode is for
                              external secure communications and the configuration
                              can be done using the k8s-nmstate operator. When 'Full',
                              ipsec is configured on the node level and inter-pod
                              secure communication within the cluster is configured.
                              Note with `Full`, if ipsec is desired for communication
                              with external (to the cluster) entities (such as storage
                              arrays), this is left to the user to configure.
                            enum:
                            - Disabled
                            - External
                            - Full
                            type: string
                        type: object
                        x-kubernetes-validations:
                        - message: ipsecConfig.mode is required
                          rule: self == oldSelf || has(self.mode)
                      ipv4:
                        description: ipv4 allows users to configure IP settings for
                          IPv4 connections. When ommitted, this means no opinions
                          and the default configuration is used. Check individual
                          fields within ipv4 for details of default values.
                        properties:
                          internalJoinSubnet:
                            description: internalJoinSubnet is a v4 subnet used internally
                              by ovn-kubernetes in case the default one is being already
                              used by something else. It must not overlap with any
                              other subnet being used by OpenShift or by the node
                              network. The size of the subnet must be larger than
                              the number of nodes. The value cannot be changed after
                              installation. The current default value is **********/16
                              The subnet must be large enough to accomadate one IP
                              per node in your cluster The value must be in proper
                              IPV4 CIDR format
                            maxLength: 18
                            type: string
                            x-kubernetes-validations:
                            - message: Subnet must be in valid IPV4 CIDR format
                              rule: isCIDR(self) && cidr(self).ip().family() == 4
                            - message: subnet must be in the range /0 to /30 inclusive
                              rule: isCIDR(self) && cidr(self).prefixLength() <= 30
                            - message: first IP address octet must not be 0
                              rule: isCIDR(self) && int(self.split('.')[0]) > 0
                          internalTransitSwitchSubnet:
                            description: internalTransitSwitchSubnet is a v4 subnet
                              in IPV4 CIDR format used internally by OVN-Kubernetes
                              for the distributed transit switch in the OVN Interconnect
                              architecture that connects the cluster routers on each
                              node together to enable east west traffic. The subnet
                              chosen should not overlap with other networks specified
                              for OVN-Kubernetes as well as other networks used on
                              the host. The value cannot be changed after installation.
                              When ommitted, this means no opinion and the platform
                              is left to choose a reasonable default which is subject
                              to change over time. The current default subnet is **********/16
                              The subnet must be large enough to accomadate one IP
                              per node in your cluster The value must be in proper
                              IPV4 CIDR format
                            maxLength: 18
                            type: string
                            x-kubernetes-validations:
                            - message: Subnet must be in valid IPV4 CIDR format
                              rule: isCIDR(self) && cidr(self).ip().family() == 4
                            - message: subnet must be in the range /0 to /30 inclusive
                              rule: isCIDR(self) && cidr(self).prefixLength() <= 30
                            - message: first IP address octet must not be 0
                              rule: isCIDR(self) && int(self.split('.')[0]) > 0
                        type: object
                      ipv6:
                        description: ipv6 allows users to configure IP settings for
                          IPv6 connections. When ommitted, this means no opinions
                          and the default configuration is used. Check individual
                          fields within ipv4 for details of default values.
                        properties:
                          internalJoinSubnet:
                            description: internalJoinSubnet is a v6 subnet used internally
                              by ovn-kubernetes in case the default one is being already
                              used by something else. It must not overlap with any
                              other subnet being used by OpenShift or by the node
                              network. The size of the subnet must be larger than
                              the number of nodes. The value cannot be changed after
                              installation. The subnet must be large enough to accomadate
                              one IP per node in your cluster The current default
                              value is fd98::/48 The value must be in proper IPV6
                              CIDR format Note that IPV6 dual addresses are not permitted
                            maxLength: 48
                            type: string
                            x-kubernetes-validations:
                            - message: Subnet must be in valid IPV6 CIDR format
                              rule: isCIDR(self) && cidr(self).ip().family() == 6
                            - message: subnet must be in the range /0 to /125 inclusive
                              rule: isCIDR(self) && cidr(self).prefixLength() <= 125
                          internalTransitSwitchSubnet:
                            description: internalTransitSwitchSubnet is a v4 subnet
                              in IPV4 CIDR format used internally by OVN-Kubernetes
                              for the distributed transit switch in the OVN Interconnect
                              architecture that connects the cluster routers on each
                              node together to enable east west traffic. The subnet
                              chosen should not overlap with other networks specified
                              for OVN-Kubernetes as well as other networks used on
                              the host. The value cannot be changed after installation.
                              When ommitted, this means no opinion and the platform
                              is left to choose a reasonable default which is subject
                              to change over time. The subnet must be large enough
                              to accomadate one IP per node in your cluster The current
                              default subnet is fd97::/64 The value must be in proper
                              IPV6 CIDR format Note that IPV6 dual addresses are not
                              permitted
                            maxLength: 48
                            type: string
                            x-kubernetes-validations:
                            - message: Subnet must be in valid IPV6 CIDR format
                              rule: isCIDR(self) && cidr(self).ip().family() == 6
                            - message: subnet must be in the range /0 to /125 inclusive
                              rule: isCIDR(self) && cidr(self).prefixLength() <= 125
                        type: object
                      mtu:
                        description: mtu is the MTU to use for the tunnel interface.
                          This must be 100 bytes smaller than the uplink mtu. Default
                          is 1400
                        format: int32
                        minimum: 0
                        type: integer
                      policyAuditConfig:
                        description: policyAuditConfig is the configuration for network
                          policy audit events. If unset, reported defaults are used.
                        properties:
                          destination:
                            default: "null"
                            description: 'destination is the location for policy log
                              messages. Regardless of this config, persistent logs
                              will always be dumped to the host at /var/log/ovn/ however
                              Additionally syslog output may be configured as follows.
                              Valid values are: - "libc" -> to use the libc syslog()
                              function of the host node''s journdald process - "udp:host:port"
                              -> for sending syslog over UDP - "unix:file" -> for
                              using the UNIX domain socket directly - "null" -> to
                              discard all messages logged to syslog The default is
                              "null"'
                            type: string
                          maxFileSize:
                            default: 50
                            description: maxFilesSize is the max size an ACL_audit
                              log file is allowed to reach before rotation occurs
                              Units are in MB and the Default is 50MB
                            format: int32
                            minimum: 1
                            type: integer
                          maxLogFiles:
                            default: 5
                            description: maxLogFiles specifies the maximum number
                              of ACL_audit log files that can be present.
                            format: int32
                            minimum: 1
                            type: integer
                          rateLimit:
                            default: 20
                            description: rateLimit is the approximate maximum number
                              of messages to generate per-second per-node. If unset
                              the default of 20 msg/sec is used.
                            format: int32
                            minimum: 1
                            type: integer
                          syslogFacility:
                            default: local0
                            description: syslogFacility the RFC5424 facility for generated
                              messages, e.g. "kern". Default is "local0"
                            type: string
                        type: object
                      v4InternalSubnet:
                        description: v4InternalSubnet is a v4 subnet used internally
                          by ovn-kubernetes in case the default one is being already
                          used by something else. It must not overlap with any other
                          subnet being used by OpenShift or by the node network. The
                          size of the subnet must be larger than the number of nodes.
                          The value cannot be changed after installation. Default
                          is **********/16
                        type: string
                      v6InternalSubnet:
                        description: v6InternalSubnet is a v6 subnet used internally
                          by ovn-kubernetes in case the default one is being already
                          used by something else. It must not overlap with any other
                          subnet being used by OpenShift or by the node network. The
                          size of the subnet must be larger than the number of nodes.
                          The value cannot be changed after installation. Default
                          is fd98::/48
                        type: string
                    type: object
                  type:
                    description: type is the type of network All NetworkTypes are
                      supported except for NetworkTypeRaw
                    type: string
                type: object
              deployKubeProxy:
                description: deployKubeProxy specifies whether or not a standalone
                  kube-proxy should be deployed by the operator. Some network providers
                  include kube-proxy or similar functionality. If unset, the plugin
                  will attempt to select the correct value, which is false when ovn-kubernetes
                  is used and true otherwise.
                type: boolean
              disableMultiNetwork:
                description: disableMultiNetwork specifies whether or not multiple
                  pod network support should be disabled. If unset, this property
                  defaults to 'false' and multiple network support is enabled.
                type: boolean
              disableNetworkDiagnostics:
                default: false
                description: disableNetworkDiagnostics specifies whether or not PodNetworkConnectivityCheck
                  CRs from a test pod to every node, apiserver and LB should be disabled
                  or not. If unset, this property defaults to 'false' and network
                  diagnostics is enabled. Setting this to 'true' would reduce the
                  additional load of the pods performing the checks.
                type: boolean
              exportNetworkFlows:
                description: exportNetworkFlows enables and configures the export
                  of network flow metadata from the pod network by using protocols
                  NetFlow, SFlow or IPFIX. Currently only supported on OVN-Kubernetes
                  plugin. If unset, flows will not be exported to any collector.
                properties:
                  ipfix:
                    description: ipfix defines IPFIX configuration.
                    properties:
                      collectors:
                        description: ipfixCollectors is list of strings formatted
                          as ip:port with a maximum of ten items
                        items:
                          pattern: ^(([0-9]|[0-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[0-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5]):([1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$
                          type: string
                        maxItems: 10
                        minItems: 1
                        type: array
                        x-kubernetes-list-type: atomic
                    type: object
                  netFlow:
                    description: netFlow defines the NetFlow configuration.
                    properties:
                      collectors:
                        description: netFlow defines the NetFlow collectors that will
                          consume the flow data exported from OVS. It is a list of
                          strings formatted as ip:port with a maximum of ten items
                        items:
                          pattern: ^(([0-9]|[0-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[0-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5]):([1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$
                          type: string
                        maxItems: 10
                        minItems: 1
                        type: array
                        x-kubernetes-list-type: atomic
                    type: object
                  sFlow:
                    description: sFlow defines the SFlow configuration.
                    properties:
                      collectors:
                        description: sFlowCollectors is list of strings formatted
                          as ip:port with a maximum of ten items
                        items:
                          pattern: ^(([0-9]|[0-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[0-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5]):([1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$
                          type: string
                        maxItems: 10
                        minItems: 1
                        type: array
                        x-kubernetes-list-type: atomic
                    type: object
                type: object
              kubeProxyConfig:
                description: kubeProxyConfig lets us configure desired proxy configuration,
                  if deployKubeProxy is true. If not specified, sensible defaults
                  will be chosen by OpenShift directly.
                properties:
                  bindAddress:
                    description: The address to "bind" on Defaults to 0.0.0.0
                    type: string
                  iptablesSyncPeriod:
                    description: 'An internal kube-proxy parameter. In older releases
                      of OCP, this sometimes needed to be adjusted in large clusters
                      for performance reasons, but this is no longer necessary, and
                      there is no reason to change this from the default value. Default:
                      30s'
                    type: string
                  proxyArguments:
                    additionalProperties:
                      description: ProxyArgumentList is a list of arguments to pass
                        to the kubeproxy process
                      items:
                        type: string
                      type: array
                      x-kubernetes-list-type: atomic
                    description: Any additional arguments to pass to the kubeproxy
                      process
                    type: object
                type: object
              logLevel:
                default: Normal
                description: "logLevel is an intent based logging for an overall component.
                  \ It does not give fine grained control, but it is a simple way
                  to manage coarse grained logging choices that operators have to
                  interpret for their operands. \n Valid values are: \"Normal\", \"Debug\",
                  \"Trace\", \"TraceAll\". Defaults to \"Normal\"."
                enum:
                - ""
                - Normal
                - Debug
                - Trace
                - TraceAll
                type: string
              managementState:
                description: managementState indicates whether and how the operator
                  should manage the component
                pattern: ^(Managed|Unmanaged|Force|Removed)$
                type: string
              migration:
                description: migration enables and configures cluster network migration,
                  for network changes that cannot be made instantly.
                properties:
                  features:
                    description: 'features was previously used to configure which
                      network plugin features would be migrated in a network type
                      migration. DEPRECATED: network type migration is no longer supported,
                      and setting this to a non-empty value will result in the network
                      operator rejecting the configuration.'
                    properties:
                      egressFirewall:
                        default: true
                        description: 'egressFirewall specified whether or not the
                          Egress Firewall configuration was migrated. DEPRECATED:
                          network type migration is no longer supported.'
                        type: boolean
                      egressIP:
                        default: true
                        description: 'egressIP specified whether or not the Egress
                          IP configuration was migrated. DEPRECATED: network type
                          migration is no longer supported.'
                        type: boolean
                      multicast:
                        default: true
                        description: 'multicast specified whether or not the multicast
                          configuration was migrated. DEPRECATED: network type migration
                          is no longer supported.'
                        type: boolean
                    type: object
                  mode:
                    description: 'mode indicates the mode of network type migration.
                      DEPRECATED: network type migration is no longer supported, and
                      setting this to a non-empty value will result in the network
                      operator rejecting the configuration.'
                    enum:
                    - Live
                    - Offline
                    - ""
                    type: string
                  mtu:
                    description: mtu contains the MTU migration configuration. Set
                      this to allow changing the MTU values for the default network.
                      If unset, the operation of changing the MTU for the default
                      network will be rejected.
                    properties:
                      machine:
                        description: machine contains MTU migration configuration
                          for the machine's uplink. Needs to be migrated along with
                          the default network MTU unless the current uplink MTU already
                          accommodates the default network MTU.
                        properties:
                          from:
                            description: from is the MTU to migrate from.
                            format: int32
                            minimum: 0
                            type: integer
                          to:
                            description: to is the MTU to migrate to.
                            format: int32
                            minimum: 0
                            type: integer
                        type: object
                      network:
                        description: network contains information about MTU migration
                          for the default network. Migrations are only allowed to
                          MTU values lower than the machine's uplink MTU by the minimum
                          appropriate offset.
                        properties:
                          from:
                            description: from is the MTU to migrate from.
                            format: int32
                            minimum: 0
                            type: integer
                          to:
                            description: to is the MTU to migrate to.
                            format: int32
                            minimum: 0
                            type: integer
                        type: object
                    type: object
                  networkType:
                    description: 'networkType was previously used when changing the
                      default network type. DEPRECATED: network type migration is
                      no longer supported, and setting this to a non-empty value will
                      result in the network operator rejecting the configuration.'
                    type: string
                type: object
                x-kubernetes-validations:
                - message: networkType migration in mode other than 'Live' may not
                    be configured at the same time as mtu migration
                  rule: '!has(self.mtu) || !has(self.networkType) || self.networkType
                    == "" || has(self.mode) && self.mode == ''Live'''
              observedConfig:
                description: observedConfig holds a sparse config that controller
                  has observed from the cluster state.  It exists in spec because
                  it is an input to the level for the operator
                nullable: true
                type: object
                x-kubernetes-preserve-unknown-fields: true
              operatorLogLevel:
                default: Normal
                description: "operatorLogLevel is an intent based logging for the
                  operator itself.  It does not give fine grained control, but it
                  is a simple way to manage coarse grained logging choices that operators
                  have to interpret for themselves. \n Valid values are: \"Normal\",
                  \"Debug\", \"Trace\", \"TraceAll\". Defaults to \"Normal\"."
                enum:
                - ""
                - Normal
                - Debug
                - Trace
                - TraceAll
                type: string
              serviceNetwork:
                description: serviceNetwork is the ip address pool to use for Service
                  IPs Currently, all existing network providers only support a single
                  value here, but this is an array to allow for growth.
                items:
                  type: string
                type: array
                x-kubernetes-list-type: atomic
              unsupportedConfigOverrides:
                description: unsupportedConfigOverrides overrides the final configuration
                  that was computed by the operator. Red Hat does not support the
                  use of this field. Misuse of this field could lead to unexpected
                  behavior or conflict with other configuration options. Seek guidance
                  from the Red Hat support before using this field. Use of this property
                  blocks cluster upgrades, it must be removed before upgrading your
                  cluster.
                nullable: true
                type: object
                x-kubernetes-preserve-unknown-fields: true
              useMultiNetworkPolicy:
                description: useMultiNetworkPolicy enables a controller which allows
                  for MultiNetworkPolicy objects to be used on additional networks
                  as created by Multus CNI. MultiNetworkPolicy are similar to NetworkPolicy
                  objects, but NetworkPolicy objects only apply to the primary interface.
                  With MultiNetworkPolicy, you can control the traffic that a pod
                  can receive over the secondary interfaces. If unset, this property
                  defaults to 'false' and MultiNetworkPolicy objects are ignored.
                  If 'disableMultiNetwork' is 'true' then the value of this field
                  is ignored.
                type: boolean
            type: object
            x-kubernetes-validations:
            - message: invalid value for IPForwarding, valid values are 'Restricted'
                or 'Global'
              rule: '!has(self.defaultNetwork) || !has(self.defaultNetwork.ovnKubernetesConfig)
                || !has(self.defaultNetwork.ovnKubernetesConfig.gatewayConfig) ||
                !has(self.defaultNetwork.ovnKubernetesConfig.gatewayConfig.ipForwarding)
                || self.defaultNetwork.ovnKubernetesConfig.gatewayConfig.ipForwarding
                == oldSelf.defaultNetwork.ovnKubernetesConfig.gatewayConfig.ipForwarding
                || self.defaultNetwork.ovnKubernetesConfig.gatewayConfig.ipForwarding
                == ''Restricted'' || self.defaultNetwork.ovnKubernetesConfig.gatewayConfig.ipForwarding
                == ''Global'''
          status:
            description: NetworkStatus is detailed operator status, which is distilled
              up to the Network clusteroperator object.
            properties:
              conditions:
                description: conditions is a list of conditions and their status
                items:
                  description: OperatorCondition is just the standard condition fields.
                  properties:
                    lastTransitionTime:
                      format: date-time
                      type: string
                    message:
                      type: string
                    reason:
                      type: string
                    status:
                      type: string
                    type:
                      type: string
                  required:
                  - type
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              generations:
                description: generations are used to determine when an item needs
                  to be reconciled or has changed in a way that needs a reaction.
                items:
                  description: GenerationStatus keeps track of the generation for
                    a given resource so that decisions about forced updates can be
                    made.
                  properties:
                    group:
                      description: group is the group of the thing you're tracking
                      type: string
                    hash:
                      description: hash is an optional field set for resources without
                        generation that are content sensitive like secrets and configmaps
                      type: string
                    lastGeneration:
                      description: lastGeneration is the last generation of the workload
                        controller involved
                      format: int64
                      type: integer
                    name:
                      description: name is the name of the thing you're tracking
                      type: string
                    namespace:
                      description: namespace is where the thing you're tracking is
                      type: string
                    resource:
                      description: resource is the resource type of the thing you're
                        tracking
                      type: string
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              observedGeneration:
                description: observedGeneration is the last generation change you've
                  dealt with
                format: int64
                type: integer
              readyReplicas:
                description: readyReplicas indicates how many replicas are ready and
                  at the desired state
                format: int32
                type: integer
              version:
                description: version is the level this availability applies to
                type: string
            type: object
        type: object
    served: true
    storage: true
status:
  acceptedNames:
    kind: Network
    listKind: NetworkList
    plural: networks
    singular: network
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:40Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:40Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
