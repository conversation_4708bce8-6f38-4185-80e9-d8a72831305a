---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  creationTimestamp: "2025-06-25T14:56:59Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:ownerReferences:
          k:{"uid":"e8b7620a-d5d2-448e-b2bf-6f6768b9b1dc"}: {}
      f:spec:
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-network-operator/operconfig
    operation: Apply
    time: "2025-06-25T14:56:59Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:56:59Z"
  name: network-attachment-definitions.k8s.cni.cncf.io
  ownerReferences:
  - apiVersion: operator.openshift.io/v1
    blockOwnerDeletion: true
    controller: true
    kind: Network
    name: cluster
    uid: e8b7620a-d5d2-448e-b2bf-6f6768b9b1dc
  resourceVersion: "3648"
  uid: 7731f738-6497-4a45-afe5-9a2ceaacaf8c
spec:
  conversion:
    strategy: None
  group: k8s.cni.cncf.io
  names:
    kind: NetworkAttachmentDefinition
    listKind: NetworkAttachmentDefinitionList
    plural: network-attachment-definitions
    shortNames:
    - net-attach-def
    singular: network-attachment-definition
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: 'NetworkAttachmentDefinition is a CRD schema specified by the
          Network Plumbing Working Group to express the intent for attaching pods
          to one or more logical or physical networks. More information available
          at: https://github.com/k8snetworkplumbingwg/multi-net-spec'
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this represen
              tation of an object. Servers should convert recognized schemas to the
              latest internal value, and may reject unrecognized values. More info:
              https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: NetworkAttachmentDefinition spec defines the desired state
              of a network attachment
            properties:
              config:
                description: NetworkAttachmentDefinition config is a JSON-formatted
                  CNI configuration
                type: string
            type: object
        type: object
    served: true
    storage: true
status:
  acceptedNames:
    kind: NetworkAttachmentDefinition
    listKind: NetworkAttachmentDefinitionList
    plural: network-attachment-definitions
    shortNames:
    - net-attach-def
    singular: network-attachment-definition
  conditions:
  - lastTransitionTime: "2025-06-25T14:56:59Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:56:59Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
