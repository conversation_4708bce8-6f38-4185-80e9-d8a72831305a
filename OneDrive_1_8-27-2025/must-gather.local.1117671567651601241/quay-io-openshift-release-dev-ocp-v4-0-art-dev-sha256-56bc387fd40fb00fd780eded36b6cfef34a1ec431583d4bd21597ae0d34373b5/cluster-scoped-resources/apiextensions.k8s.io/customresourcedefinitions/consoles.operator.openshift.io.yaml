---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/486
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:53:38Z"
  generation: 3
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:38Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T12:08:58Z"
  name: consoles.operator.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21340383"
  uid: b7286b69-450b-4e09-8089-f398e40b8309
spec:
  conversion:
    strategy: None
  group: operator.openshift.io
  names:
    kind: Console
    listKind: ConsoleList
    plural: consoles
    singular: console
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "Console provides a means to configure an operator to manage
          the console. \n Compatibility level 1: Stable within a major release for
          a minimum of 12 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: ConsoleSpec is the specification of the desired behavior
              of the Console.
            properties:
              customization:
                description: customization is used to optionally provide a small set
                  of customization options to the web console.
                properties:
                  addPage:
                    description: addPage allows customizing actions on the Add page
                      in developer perspective.
                    properties:
                      disabledActions:
                        description: disabledActions is a list of actions that are
                          not shown to users. Each action in the list is represented
                          by its ID.
                        items:
                          type: string
                        minItems: 1
                        type: array
                    type: object
                  brand:
                    description: brand is the default branding of the web console
                      which can be overridden by providing the brand field.  There
                      is a limited set of specific brand options. This field controls
                      elements of the console such as the logo. Invalid value will
                      prevent a console rollout.
                    enum:
                    - openshift
                    - okd
                    - online
                    - ocp
                    - dedicated
                    - azure
                    - OpenShift
                    - OKD
                    - Online
                    - OCP
                    - Dedicated
                    - Azure
                    - ROSA
                    type: string
                  capabilities:
                    description: capabilities defines an array of capabilities that
                      can be interacted with in the console UI. Each capability defines
                      a visual state that can be interacted with the console to render
                      in the UI. Available capabilities are LightspeedButton. Each
                      of the available capabilities may appear only once in the list.
                    items:
                      description: Capabilities contains set of UI capabilities and
                        their state in the console UI.
                      properties:
                        name:
                          description: name is the unique name of a capability. Available
                            capabilities are LightspeedButton.
                          enum:
                          - LightspeedButton
                          type: string
                        visibility:
                          description: visibility defines the visibility state of
                            the capability.
                          properties:
                            state:
                              description: state defines if the capability is enabled
                                or disabled in the console UI. Enabling the capability
                                in the console UI is represented by the "Enabled"
                                value. Disabling the capability in the console UI
                                is represented by the "Disabled" value.
                              enum:
                              - Enabled
                              - Disabled
                              type: string
                          required:
                          - state
                          type: object
                      required:
                      - name
                      - visibility
                      type: object
                    maxItems: 1
                    minItems: 1
                    type: array
                    x-kubernetes-list-map-keys:
                    - name
                    x-kubernetes-list-type: map
                  customLogoFile:
                    description: 'customLogoFile replaces the default OpenShift logo
                      in the masthead and about dialog. It is a reference to a ConfigMap
                      in the openshift-config namespace. This can be created with
                      a command like ''oc create configmap custom-logo --from-file=/path/to/file
                      -n openshift-config''. Image size must be less than 1 MB due
                      to constraints on the ConfigMap size. The ConfigMap key should
                      include a file extension so that the console serves the file
                      with the correct MIME type. Recommended logo specifications:
                      Dimensions: Max height of 68px and max width of 200px SVG format
                      preferred'
                    properties:
                      key:
                        description: Key allows pointing to a specific key/value inside
                          of the configmap.  This is useful for logical file references.
                        type: string
                      name:
                        type: string
                    type: object
                  customProductName:
                    description: customProductName is the name that will be displayed
                      in page titles, logo alt text, and the about dialog instead
                      of the normal OpenShift product name.
                    type: string
                  developerCatalog:
                    description: developerCatalog allows to configure the shown developer
                      catalog categories (filters) and types (sub-catalogs).
                    properties:
                      categories:
                        description: categories which are shown in the developer catalog.
                        items:
                          description: DeveloperConsoleCatalogCategory for the developer
                            console catalog.
                          properties:
                            id:
                              description: ID is an identifier used in the URL to
                                enable deep linking in console. ID is required and
                                must have 1-32 URL safe (A-Z, a-z, 0-9, - and _) characters.
                              maxLength: 32
                              minLength: 1
                              pattern: ^[A-Za-z0-9-_]+$
                              type: string
                            label:
                              description: label defines a category display label.
                                It is required and must have 1-64 characters.
                              maxLength: 64
                              minLength: 1
                              type: string
                            subcategories:
                              description: subcategories defines a list of child categories.
                              items:
                                description: DeveloperConsoleCatalogCategoryMeta are
                                  the key identifiers of a developer catalog category.
                                properties:
                                  id:
                                    description: ID is an identifier used in the URL
                                      to enable deep linking in console. ID is required
                                      and must have 1-32 URL safe (A-Z, a-z, 0-9,
                                      - and _) characters.
                                    maxLength: 32
                                    minLength: 1
                                    pattern: ^[A-Za-z0-9-_]+$
                                    type: string
                                  label:
                                    description: label defines a category display
                                      label. It is required and must have 1-64 characters.
                                    maxLength: 64
                                    minLength: 1
                                    type: string
                                  tags:
                                    description: tags is a list of strings that will
                                      match the category. A selected category show
                                      all items which has at least one overlapping
                                      tag between category and item.
                                    items:
                                      type: string
                                    type: array
                                required:
                                - id
                                - label
                                type: object
                              type: array
                            tags:
                              description: tags is a list of strings that will match
                                the category. A selected category show all items which
                                has at least one overlapping tag between category
                                and item.
                              items:
                                type: string
                              type: array
                          required:
                          - id
                          - label
                          type: object
                        type: array
                      types:
                        description: types allows enabling or disabling of sub-catalog
                          types that user can see in the Developer catalog. When omitted,
                          all the sub-catalog types will be shown.
                        properties:
                          disabled:
                            description: 'disabled is a list of developer catalog
                              types (sub-catalogs IDs) that are not shown to users.
                              Types (sub-catalogs) are added via console plugins,
                              the available types (sub-catalog IDs) are available
                              in the console on the cluster configuration page, or
                              when editing the YAML in the console. Example: "Devfile",
                              "HelmChart", "BuilderImage" If the list is empty or
                              all the available sub-catalog types are added, then
                              the complete developer catalog should be hidden.'
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: set
                          enabled:
                            description: 'enabled is a list of developer catalog types
                              (sub-catalogs IDs) that will be shown to users. Types
                              (sub-catalogs) are added via console plugins, the available
                              types (sub-catalog IDs) are available in the console
                              on the cluster configuration page, or when editing the
                              YAML in the console. Example: "Devfile", "HelmChart",
                              "BuilderImage" If the list is non-empty, a new type
                              will not be shown to the user until it is added to list.
                              If the list is empty the complete developer catalog
                              will be shown.'
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: set
                          state:
                            default: Enabled
                            description: state defines if a list of catalog types
                              should be enabled or disabled.
                            enum:
                            - Enabled
                            - Disabled
                            type: string
                        required:
                        - state
                        type: object
                        x-kubernetes-validations:
                        - message: enabled is forbidden when state is not Enabled
                          rule: 'self.state == ''Enabled'' ? true : !has(self.enabled)'
                        - message: disabled is forbidden when state is not Disabled
                          rule: 'self.state == ''Disabled'' ? true : !has(self.disabled)'
                    type: object
                  documentationBaseURL:
                    description: documentationBaseURL links to external documentation
                      are shown in various sections of the web console.  Providing
                      documentationBaseURL will override the default documentation
                      URL. Invalid value will prevent a console rollout.
                    pattern: ^$|^((https):\/\/?)[^\s()<>]+(?:\([\w\d]+\)|([^[:punct:]\s]|\/?))\/$
                    type: string
                  perspectives:
                    description: perspectives allows enabling/disabling of perspective(s)
                      that user can see in the Perspective switcher dropdown.
                    items:
                      description: Perspective defines a perspective that cluster
                        admins want to show/hide in the perspective switcher dropdown
                      properties:
                        id:
                          description: 'id defines the id of the perspective. Example:
                            "dev", "admin". The available perspective ids can be found
                            in the code snippet section next to the yaml editor. Incorrect
                            or unknown ids will be ignored.'
                          type: string
                        pinnedResources:
                          description: pinnedResources defines the list of default
                            pinned resources that users will see on the perspective
                            navigation if they have not customized these pinned resources
                            themselves. The list of available Kubernetes resources
                            could be read via `kubectl api-resources`. The console
                            will also provide a configuration UI and a YAML snippet
                            that will list the available resources that can be pinned
                            to the navigation. Incorrect or unknown resources will
                            be ignored.
                          items:
                            description: PinnedResourceReference includes the group,
                              version and type of resource
                            properties:
                              group:
                                description: 'group is the API Group of the Resource.
                                  Enter empty string for the core group. This value
                                  should consist of only lowercase alphanumeric characters,
                                  hyphens and periods. Example: "", "apps", "build.openshift.io",
                                  etc.'
                                pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                                type: string
                              resource:
                                description: 'resource is the type that is being referenced.
                                  It is normally the plural form of the resource kind
                                  in lowercase. This value should consist of only
                                  lowercase alphanumeric characters and hyphens. Example:
                                  "deployments", "deploymentconfigs", "pods", etc.'
                                pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                                type: string
                              version:
                                description: 'version is the API Version of the Resource.
                                  This value should consist of only lowercase alphanumeric
                                  characters. Example: "v1", "v1beta1", etc.'
                                pattern: ^[a-z0-9]+$
                                type: string
                            required:
                            - group
                            - resource
                            - version
                            type: object
                          maxItems: 100
                          type: array
                        visibility:
                          description: visibility defines the state of perspective
                            along with access review checks if needed for that perspective.
                          properties:
                            accessReview:
                              description: accessReview defines required and missing
                                access review checks.
                              minProperties: 1
                              properties:
                                missing:
                                  description: missing defines a list of permission
                                    checks. The perspective will only be shown when
                                    at least one check fails. When omitted, the access
                                    review is skipped and the perspective will not
                                    be shown unless it is required to do so based
                                    on the configuration of the required access review
                                    list.
                                  items:
                                    description: ResourceAttributes includes the authorization
                                      attributes available for resource requests to
                                      the Authorizer interface
                                    properties:
                                      group:
                                        description: Group is the API Group of the
                                          Resource.  "*" means all.
                                        type: string
                                      name:
                                        description: Name is the name of the resource
                                          being requested for a "get" or deleted for
                                          a "delete". "" (empty) means all.
                                        type: string
                                      namespace:
                                        description: Namespace is the namespace of
                                          the action being requested.  Currently,
                                          there is no distinction between no namespace
                                          and all namespaces "" (empty) is defaulted
                                          for LocalSubjectAccessReviews "" (empty)
                                          is empty for cluster-scoped resources ""
                                          (empty) means "all" for namespace scoped
                                          resources from a SubjectAccessReview or
                                          SelfSubjectAccessReview
                                        type: string
                                      resource:
                                        description: Resource is one of the existing
                                          resource types.  "*" means all.
                                        type: string
                                      subresource:
                                        description: Subresource is one of the existing
                                          resource types.  "" means none.
                                        type: string
                                      verb:
                                        description: 'Verb is a kubernetes resource
                                          API verb, like: get, list, watch, create,
                                          update, delete, proxy.  "*" means all.'
                                        type: string
                                      version:
                                        description: Version is the API Version of
                                          the Resource.  "*" means all.
                                        type: string
                                    type: object
                                  type: array
                                required:
                                  description: required defines a list of permission
                                    checks. The perspective will only be shown when
                                    all checks are successful. When omitted, the access
                                    review is skipped and the perspective will not
                                    be shown unless it is required to do so based
                                    on the configuration of the missing access review
                                    list.
                                  items:
                                    description: ResourceAttributes includes the authorization
                                      attributes available for resource requests to
                                      the Authorizer interface
                                    properties:
                                      group:
                                        description: Group is the API Group of the
                                          Resource.  "*" means all.
                                        type: string
                                      name:
                                        description: Name is the name of the resource
                                          being requested for a "get" or deleted for
                                          a "delete". "" (empty) means all.
                                        type: string
                                      namespace:
                                        description: Namespace is the namespace of
                                          the action being requested.  Currently,
                                          there is no distinction between no namespace
                                          and all namespaces "" (empty) is defaulted
                                          for LocalSubjectAccessReviews "" (empty)
                                          is empty for cluster-scoped resources ""
                                          (empty) means "all" for namespace scoped
                                          resources from a SubjectAccessReview or
                                          SelfSubjectAccessReview
                                        type: string
                                      resource:
                                        description: Resource is one of the existing
                                          resource types.  "*" means all.
                                        type: string
                                      subresource:
                                        description: Subresource is one of the existing
                                          resource types.  "" means none.
                                        type: string
                                      verb:
                                        description: 'Verb is a kubernetes resource
                                          API verb, like: get, list, watch, create,
                                          update, delete, proxy.  "*" means all.'
                                        type: string
                                      version:
                                        description: Version is the API Version of
                                          the Resource.  "*" means all.
                                        type: string
                                    type: object
                                  type: array
                              type: object
                            state:
                              description: state defines the perspective is enabled
                                or disabled or access review check is required.
                              enum:
                              - Enabled
                              - Disabled
                              - AccessReview
                              type: string
                          required:
                          - state
                          type: object
                          x-kubernetes-validations:
                          - message: accessReview configuration is required when state
                              is AccessReview, and forbidden otherwise
                            rule: 'self.state == ''AccessReview'' ?  has(self.accessReview)
                              : !has(self.accessReview)'
                      required:
                      - id
                      - visibility
                      type: object
                      x-kubernetes-validations:
                      - message: pinnedResources is allowed only for dev and forbidden
                          for other perspectives
                        rule: 'has(self.id) && self.id != ''dev''? !has(self.pinnedResources)
                          : true'
                    type: array
                    x-kubernetes-list-map-keys:
                    - id
                    x-kubernetes-list-type: map
                  projectAccess:
                    description: projectAccess allows customizing the available list
                      of ClusterRoles in the Developer perspective Project access
                      page which can be used by a project admin to specify roles to
                      other users and restrict access within the project. If set,
                      the list will replace the default ClusterRole options.
                    properties:
                      availableClusterRoles:
                        description: availableClusterRoles is the list of ClusterRole
                          names that are assignable to users through the project access
                          tab.
                        items:
                          type: string
                        type: array
                    type: object
                  quickStarts:
                    description: quickStarts allows customization of available ConsoleQuickStart
                      resources in console.
                    properties:
                      disabled:
                        description: disabled is a list of ConsoleQuickStart resource
                          names that are not shown to users.
                        items:
                          type: string
                        type: array
                    type: object
                type: object
              ingress:
                description: ingress allows to configure the alternative ingress for
                  the console. This field is intended for clusters without ingress
                  capability, where access to routes is not possible.
                properties:
                  clientDownloadsURL:
                    description: clientDownloadsURL is a URL to be used as the address
                      to download client binaries. If not specified, the downloads
                      route hostname will be used. This field is required for clusters
                      without ingress capability, where access to routes is not possible.
                      The console operator will monitor the URL and may go degraded
                      if it's unreachable for an extended period. Must use the HTTPS
                      scheme.
                    maxLength: 1024
                    type: string
                    x-kubernetes-validations:
                    - message: client downloads url must be a valid absolute URL
                      rule: size(self) == 0 || isURL(self)
                    - message: client downloads url scheme must be https
                      rule: size(self) == 0 || url(self).getScheme() == 'https'
                  consoleURL:
                    description: consoleURL is a URL to be used as the base console
                      address. If not specified, the console route hostname will be
                      used. This field is required for clusters without ingress capability,
                      where access to routes is not possible. Make sure that appropriate
                      ingress is set up at this URL. The console operator will monitor
                      the URL and may go degraded if it's unreachable for an extended
                      period. Must use the HTTPS scheme.
                    maxLength: 1024
                    type: string
                    x-kubernetes-validations:
                    - message: console url must be a valid absolute URL
                      rule: size(self) == 0 || isURL(self)
                    - message: console url scheme must be https
                      rule: size(self) == 0 || url(self).getScheme() == 'https'
                type: object
              logLevel:
                default: Normal
                description: "logLevel is an intent based logging for an overall component.
                  \ It does not give fine grained control, but it is a simple way
                  to manage coarse grained logging choices that operators have to
                  interpret for their operands. \n Valid values are: \"Normal\", \"Debug\",
                  \"Trace\", \"TraceAll\". Defaults to \"Normal\"."
                enum:
                - ""
                - Normal
                - Debug
                - Trace
                - TraceAll
                type: string
              managementState:
                description: managementState indicates whether and how the operator
                  should manage the component
                pattern: ^(Managed|Unmanaged|Force|Removed)$
                type: string
              observedConfig:
                description: observedConfig holds a sparse config that controller
                  has observed from the cluster state.  It exists in spec because
                  it is an input to the level for the operator
                nullable: true
                type: object
                x-kubernetes-preserve-unknown-fields: true
              operatorLogLevel:
                default: Normal
                description: "operatorLogLevel is an intent based logging for the
                  operator itself.  It does not give fine grained control, but it
                  is a simple way to manage coarse grained logging choices that operators
                  have to interpret for themselves. \n Valid values are: \"Normal\",
                  \"Debug\", \"Trace\", \"TraceAll\". Defaults to \"Normal\"."
                enum:
                - ""
                - Normal
                - Debug
                - Trace
                - TraceAll
                type: string
              plugins:
                description: plugins defines a list of enabled console plugin names.
                items:
                  type: string
                type: array
              providers:
                description: providers contains configuration for using specific service
                  providers.
                properties:
                  statuspage:
                    description: statuspage contains ID for statuspage.io page that
                      provides status info about.
                    properties:
                      pageID:
                        description: pageID is the unique ID assigned by Statuspage
                          for your page. This must be a public page.
                        type: string
                    type: object
                type: object
              route:
                description: route contains hostname and secret reference that contains
                  the serving certificate. If a custom route is specified, a new route
                  will be created with the provided hostname, under which console
                  will be available. In case of custom hostname uses the default routing
                  suffix of the cluster, the Secret specification for a serving certificate
                  will not be needed. In case of custom hostname points to an arbitrary
                  domain, manual DNS configurations steps are necessary. The default
                  console route will be maintained to reserve the default hostname
                  for console if the custom route is removed. If not specified, default
                  route will be used. DEPRECATED
                properties:
                  hostname:
                    description: hostname is the desired custom domain under which
                      console will be available.
                    type: string
                  secret:
                    description: 'secret points to secret in the openshift-config
                      namespace that contains custom certificate and key and needs
                      to be created manually by the cluster admin. Referenced Secret
                      is required to contain following key value pairs: - "tls.crt"
                      - to specifies custom certificate - "tls.key" - to specifies
                      private key of the custom certificate If the custom hostname
                      uses the default routing suffix of the cluster, the Secret specification
                      for a serving certificate will not be needed.'
                    properties:
                      name:
                        description: name is the metadata.name of the referenced secret
                        type: string
                    required:
                    - name
                    type: object
                type: object
              unsupportedConfigOverrides:
                description: unsupportedConfigOverrides overrides the final configuration
                  that was computed by the operator. Red Hat does not support the
                  use of this field. Misuse of this field could lead to unexpected
                  behavior or conflict with other configuration options. Seek guidance
                  from the Red Hat support before using this field. Use of this property
                  blocks cluster upgrades, it must be removed before upgrading your
                  cluster.
                nullable: true
                type: object
                x-kubernetes-preserve-unknown-fields: true
            type: object
          status:
            description: ConsoleStatus defines the observed status of the Console.
            properties:
              conditions:
                description: conditions is a list of conditions and their status
                items:
                  description: OperatorCondition is just the standard condition fields.
                  properties:
                    lastTransitionTime:
                      format: date-time
                      type: string
                    message:
                      type: string
                    reason:
                      type: string
                    status:
                      type: string
                    type:
                      type: string
                  required:
                  - type
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              generations:
                description: generations are used to determine when an item needs
                  to be reconciled or has changed in a way that needs a reaction.
                items:
                  description: GenerationStatus keeps track of the generation for
                    a given resource so that decisions about forced updates can be
                    made.
                  properties:
                    group:
                      description: group is the group of the thing you're tracking
                      type: string
                    hash:
                      description: hash is an optional field set for resources without
                        generation that are content sensitive like secrets and configmaps
                      type: string
                    lastGeneration:
                      description: lastGeneration is the last generation of the workload
                        controller involved
                      format: int64
                      type: integer
                    name:
                      description: name is the name of the thing you're tracking
                      type: string
                    namespace:
                      description: namespace is where the thing you're tracking is
                      type: string
                    resource:
                      description: resource is the resource type of the thing you're
                        tracking
                      type: string
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              observedGeneration:
                description: observedGeneration is the last generation change you've
                  dealt with
                format: int64
                type: integer
              readyReplicas:
                description: readyReplicas indicates how many replicas are ready and
                  at the desired state
                format: int32
                type: integer
              version:
                description: version is the level this availability applies to
                type: string
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: Console
    listKind: ConsoleList
    plural: consoles
    singular: console
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:38Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:38Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
