---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.18.0
    operatorframework.io/installed-alongside-b3bf724166efcfbc: openshift-gitops-operator/openshift-gitops-operator.v1.16.2
    operatorframework.io/installed-alongside-bd3517416c857c29: openshift-gitops-operator/openshift-gitops-operator.v1.17.0
  creationTimestamp: "2025-07-01T14:02:34Z"
  generation: 1
  labels:
    olm.managed: "true"
    operators.coreos.com/openshift-gitops-operator.openshift-gitops-operator: ""
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-07-01T14:02:34Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:controller-gen.kubebuilder.io/version: {}
          f:operatorframework.io/installed-alongside-b3bf724166efcfbc: {}
          f:operatorframework.io/installed-alongside-bd3517416c857c29: {}
        f:labels:
          .: {}
          f:olm.managed: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: catalog
    operation: Update
    time: "2025-08-07T15:05:14Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          f:operators.coreos.com/openshift-gitops-operator.openshift-gitops-operator: {}
    manager: olm
    operation: Update
    time: "2025-08-07T15:05:28Z"
  name: gitopsservices.pipelines.openshift.io
  resourceVersion: "25002026"
  uid: 7acf0600-2c26-492c-b83f-62f8363b695f
spec:
  conversion:
    strategy: None
  group: pipelines.openshift.io
  names:
    kind: GitopsService
    listKind: GitopsServiceList
    plural: gitopsservices
    singular: gitopsservice
  scope: Cluster
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: GitopsService is the Schema for the gitopsservices API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: GitopsServiceSpec defines the desired state of GitopsService
            properties:
              nodeSelector:
                additionalProperties:
                  type: string
                description: NodeSelector is a map of key value pairs used for node
                  selection in the default workloads
                type: object
              runOnInfra:
                description: InfraNodeEnabled will add infra NodeSelector to all the
                  default workloads of gitops operator
                type: boolean
              tolerations:
                description: Tolerations allow the default workloads to schedule onto
                  nodes with matching taints
                items:
                  description: |-
                    The pod this Toleration is attached to tolerates any taint that matches
                    the triple <key,value,effect> using the matching operator <operator>.
                  properties:
                    effect:
                      description: |-
                        Effect indicates the taint effect to match. Empty means match all taint effects.
                        When specified, allowed values are NoSchedule, PreferNoSchedule and NoExecute.
                      type: string
                    key:
                      description: |-
                        Key is the taint key that the toleration applies to. Empty means match all taint keys.
                        If the key is empty, operator must be Exists; this combination means to match all values and all keys.
                      type: string
                    operator:
                      description: |-
                        Operator represents a key's relationship to the value.
                        Valid operators are Exists and Equal. Defaults to Equal.
                        Exists is equivalent to wildcard for value, so that a pod can
                        tolerate all taints of a particular category.
                      type: string
                    tolerationSeconds:
                      description: |-
                        TolerationSeconds represents the period of time the toleration (which must be
                        of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default,
                        it is not set, which means tolerate the taint forever (do not evict). Zero and
                        negative values will be treated as 0 (evict immediately) by the system.
                      format: int64
                      type: integer
                    value:
                      description: |-
                        Value is the taint value the toleration matches to.
                        If the operator is Exists, the value should be empty, otherwise just a regular string.
                      type: string
                  type: object
                type: array
            type: object
          status:
            description: GitopsServiceStatus defines the observed state of GitopsService
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: GitopsService
    listKind: GitopsServiceList
    plural: gitopsservices
    singular: gitopsservice
  conditions:
  - lastTransitionTime: "2025-07-01T14:02:34Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-07-01T14:02:34Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1alpha1
