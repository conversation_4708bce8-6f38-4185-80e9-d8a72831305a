---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.kubernetes.io: https://github.com/kubernetes-csi/external-snapshotter/pull/814
    controller-gen.kubebuilder.io/version: v0.15.0
  creationTimestamp: "2025-06-25T14:58:12Z"
  generation: 3
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"KubernetesAPIApprovalPolicyConformant"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T18:42:43Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.kubernetes.io: {}
          f:controller-gen.kubebuilder.io/version: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: csi-snapshot-controller-operator
    operation: Update
    time: "2025-08-01T12:09:20Z"
  name: volumesnapshotclasses.snapshot.storage.k8s.io
  resourceVersion: "21341643"
  uid: 05ae104c-d6ca-4ac1-9aa7-d30c9074a190
spec:
  conversion:
    strategy: None
  group: snapshot.storage.k8s.io
  names:
    kind: VolumeSnapshotClass
    listKind: VolumeSnapshotClassList
    plural: volumesnapshotclasses
    shortNames:
    - vsclass
    - vsclasses
    singular: volumesnapshotclass
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - jsonPath: .driver
      name: Driver
      type: string
    - description: Determines whether a VolumeSnapshotContent created through the
        VolumeSnapshotClass should be deleted when its bound VolumeSnapshot is deleted.
      jsonPath: .deletionPolicy
      name: DeletionPolicy
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1
    schema:
      openAPIV3Schema:
        description: |-
          VolumeSnapshotClass specifies parameters that a underlying storage system uses when
          creating a volume snapshot. A specific VolumeSnapshotClass is used by specifying its
          name in a VolumeSnapshot object.
          VolumeSnapshotClasses are non-namespaced
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          deletionPolicy:
            description: |-
              deletionPolicy determines whether a VolumeSnapshotContent created through
              the VolumeSnapshotClass should be deleted when its bound VolumeSnapshot is deleted.
              Supported values are "Retain" and "Delete".
              "Retain" means that the VolumeSnapshotContent and its physical snapshot on underlying storage system are kept.
              "Delete" means that the VolumeSnapshotContent and its physical snapshot on underlying storage system are deleted.
              Required.
            enum:
            - Delete
            - Retain
            type: string
          driver:
            description: |-
              driver is the name of the storage driver that handles this VolumeSnapshotClass.
              Required.
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          parameters:
            additionalProperties:
              type: string
            description: |-
              parameters is a key-value map with storage driver specific parameters for creating snapshots.
              These values are opaque to Kubernetes.
            type: object
        required:
        - deletionPolicy
        - driver
        type: object
    served: true
    storage: true
    subresources: {}
  - additionalPrinterColumns:
    - jsonPath: .driver
      name: Driver
      type: string
    - description: Determines whether a VolumeSnapshotContent created through the
        VolumeSnapshotClass should be deleted when its bound VolumeSnapshot is deleted.
      jsonPath: .deletionPolicy
      name: DeletionPolicy
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    deprecated: true
    deprecationWarning: snapshot.storage.k8s.io/v1beta1 VolumeSnapshotClass is deprecated;
      use snapshot.storage.k8s.io/v1 VolumeSnapshotClass
    name: v1beta1
    schema:
      openAPIV3Schema:
        description: VolumeSnapshotClass specifies parameters that a underlying storage
          system uses when creating a volume snapshot. A specific VolumeSnapshotClass
          is used by specifying its name in a VolumeSnapshot object. VolumeSnapshotClasses
          are non-namespaced
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          deletionPolicy:
            description: deletionPolicy determines whether a VolumeSnapshotContent
              created through the VolumeSnapshotClass should be deleted when its bound
              VolumeSnapshot is deleted. Supported values are "Retain" and "Delete".
              "Retain" means that the VolumeSnapshotContent and its physical snapshot
              on underlying storage system are kept. "Delete" means that the VolumeSnapshotContent
              and its physical snapshot on underlying storage system are deleted.
              Required.
            enum:
            - Delete
            - Retain
            type: string
          driver:
            description: driver is the name of the storage driver that handles this
              VolumeSnapshotClass. Required.
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          parameters:
            additionalProperties:
              type: string
            description: parameters is a key-value map with storage driver specific
              parameters for creating snapshots. These values are opaque to Kubernetes.
            type: object
        required:
        - deletionPolicy
        - driver
        type: object
    served: false
    storage: false
    subresources: {}
status:
  acceptedNames:
    kind: VolumeSnapshotClass
    listKind: VolumeSnapshotClassList
    plural: volumesnapshotclasses
    shortNames:
    - vsclass
    - vsclasses
    singular: volumesnapshotclass
  conditions:
  - lastTransitionTime: "2025-06-25T14:58:12Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:58:12Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  - lastTransitionTime: "2025-06-25T14:58:12Z"
    message: approved in https://github.com/kubernetes-csi/external-snapshotter/pull/814
    reason: ApprovedAnnotation
    status: "True"
    type: KubernetesAPIApprovalPolicyConformant
  storedVersions:
  - v1
