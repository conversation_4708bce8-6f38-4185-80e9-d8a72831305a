---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/470
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    release.openshift.io/bootstrap-required: "true"
  creationTimestamp: "2025-06-25T14:52:52Z"
  generation: 2
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
    manager: cluster-bootstrap
    operation: Update
    time: "2025-06-25T14:52:52Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:52:52Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:api.openshift.io/merged-by-featuregates: {}
          f:release.openshift.io/bootstrap-required: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:19:46Z"
  name: featuregates.config.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "133576"
  uid: c8852775-4c44-441f-aeb6-39ebbde45b75
spec:
  conversion:
    strategy: None
  group: config.openshift.io
  names:
    kind: FeatureGate
    listKind: FeatureGateList
    plural: featuregates
    singular: featuregate
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "Feature holds cluster-wide information about feature gates.
          \ The canonical name is `cluster` \n Compatibility level 1: Stable within
          a major release for a minimum of 12 months or 3 minor releases (whichever
          is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: spec holds user settable values for configuration
            properties:
              customNoUpgrade:
                description: customNoUpgrade allows the enabling or disabling of any
                  feature. Turning this feature set on IS NOT SUPPORTED, CANNOT BE
                  UNDONE, and PREVENTS UPGRADES. Because of its nature, this setting
                  cannot be validated.  If you have any typos or accidentally apply
                  invalid combinations your cluster may fail in an unrecoverable way.  featureSet
                  must equal "CustomNoUpgrade" must be set to use this field.
                nullable: true
                properties:
                  disabled:
                    description: disabled is a list of all feature gates that you
                      want to force off
                    items:
                      description: FeatureGateName is a string to enforce patterns
                        on the name of a FeatureGate
                      pattern: ^([A-Za-z0-9-]+\.)*[A-Za-z0-9-]+\.?$
                      type: string
                    type: array
                  enabled:
                    description: enabled is a list of all feature gates that you want
                      to force on
                    items:
                      description: FeatureGateName is a string to enforce patterns
                        on the name of a FeatureGate
                      pattern: ^([A-Za-z0-9-]+\.)*[A-Za-z0-9-]+\.?$
                      type: string
                    type: array
                type: object
              featureSet:
                description: featureSet changes the list of features in the cluster.  The
                  default is empty.  Be very careful adjusting this setting. Turning
                  on or off features may cause irreversible changes in your cluster
                  which cannot be undone.
                enum:
                - CustomNoUpgrade
                - DevPreviewNoUpgrade
                - TechPreviewNoUpgrade
                - ""
                type: string
                x-kubernetes-validations:
                - message: CustomNoUpgrade may not be changed
                  rule: 'oldSelf == ''CustomNoUpgrade'' ? self == ''CustomNoUpgrade''
                    : true'
                - message: TechPreviewNoUpgrade may not be changed
                  rule: 'oldSelf == ''TechPreviewNoUpgrade'' ? self == ''TechPreviewNoUpgrade''
                    : true'
                - message: DevPreviewNoUpgrade may not be changed
                  rule: 'oldSelf == ''DevPreviewNoUpgrade'' ? self == ''DevPreviewNoUpgrade''
                    : true'
            type: object
            x-kubernetes-validations:
            - message: .spec.featureSet cannot be removed
              rule: 'has(oldSelf.featureSet) ? has(self.featureSet) : true'
          status:
            description: status holds observed values from the cluster. They may not
              be overridden.
            properties:
              conditions:
                description: 'conditions represent the observations of the current
                  state. Known .status.conditions.type are: "DeterminationDegraded"'
                items:
                  description: "Condition contains details for one aspect of the current
                    state of this API Resource. --- This struct is intended for direct
                    use as an array at the field path .status.conditions.  For example,
                    \n type FooStatus struct{ // Represents the observations of a
                    foo's current state. // Known .status.conditions.type are: \"Available\",
                    \"Progressing\", and \"Degraded\" // +patchMergeKey=type // +patchStrategy=merge
                    // +listType=map // +listMapKey=type Conditions []metav1.Condition
                    `json:\"conditions,omitempty\" patchStrategy:\"merge\" patchMergeKey:\"type\"
                    protobuf:\"bytes,1,rep,name=conditions\"` \n // other fields }"
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the last time the condition
                        transitioned from one status to another. This should be when
                        the underlying condition changed.  If that is not known, then
                        using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: message is a human readable message indicating
                        details about the transition. This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: observedGeneration represents the .metadata.generation
                        that the condition was set based upon. For instance, if .metadata.generation
                        is currently 12, but the .status.conditions[x].observedGeneration
                        is 9, the condition is out of date with respect to the current
                        state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: reason contains a programmatic identifier indicating
                        the reason for the condition's last transition. Producers
                        of specific condition types may define expected values and
                        meanings for this field, and whether the values are considered
                        a guaranteed API. The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                        --- Many .condition.type values are consistent across resources
                        like Available, but because arbitrary conditions can be useful
                        (see .node.status.conditions), the ability to deconflict is
                        important. The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              featureGates:
                description: featureGates contains a list of enabled and disabled
                  featureGates that are keyed by payloadVersion. Operators other than
                  the CVO and cluster-config-operator, must read the .status.featureGates,
                  locate the version they are managing, find the enabled/disabled
                  featuregates and make the operand and operator match. The enabled/disabled
                  values for a particular version may change during the life of the
                  cluster as various .spec.featureSet values are selected. Operators
                  may choose to restart their processes to pick up these changes,
                  but remembering past enable/disable lists is beyond the scope of
                  this API and is the responsibility of individual operators. Only
                  featureGates with .version in the ClusterVersion.status will be
                  present in this list.
                items:
                  properties:
                    disabled:
                      description: disabled is a list of all feature gates that are
                        disabled in the cluster for the named version.
                      items:
                        properties:
                          name:
                            description: name is the name of the FeatureGate.
                            pattern: ^([A-Za-z0-9-]+\.)*[A-Za-z0-9-]+\.?$
                            type: string
                        required:
                        - name
                        type: object
                      type: array
                    enabled:
                      description: enabled is a list of all feature gates that are
                        enabled in the cluster for the named version.
                      items:
                        properties:
                          name:
                            description: name is the name of the FeatureGate.
                            pattern: ^([A-Za-z0-9-]+\.)*[A-Za-z0-9-]+\.?$
                            type: string
                        required:
                        - name
                        type: object
                      type: array
                    version:
                      description: version matches the version provided by the ClusterVersion
                        and in the ClusterOperator.Status.Versions field.
                      type: string
                  required:
                  - version
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - version
                x-kubernetes-list-type: map
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: FeatureGate
    listKind: FeatureGateList
    plural: featuregates
    singular: featuregate
  conditions:
  - lastTransitionTime: "2025-06-25T14:52:52Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:52:52Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
