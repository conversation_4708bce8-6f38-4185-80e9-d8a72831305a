---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/1126
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    release.openshift.io/bootstrap-required: "true"
  creationTimestamp: "2025-06-25T14:52:54Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-bootstrap
    operation: Update
    time: "2025-06-25T14:52:54Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:52:54Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:api.openshift.io/merged-by-featuregates: {}
          f:release.openshift.io/bootstrap-required: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:19:46Z"
  name: imagetagmirrorsets.config.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "133584"
  uid: 21457a61-7cd4-4aa5-b026-1b723db49d4e
spec:
  conversion:
    strategy: None
  group: config.openshift.io
  names:
    kind: ImageTagMirrorSet
    listKind: ImageTagMirrorSetList
    plural: imagetagmirrorsets
    shortNames:
    - itms
    singular: imagetagmirrorset
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "ImageTagMirrorSet holds cluster-wide information about how to
          handle registry mirror rules on using tag pull specification. When multiple
          policies are defined, the outcome of the behavior is defined on each field.
          \n Compatibility level 1: Stable within a major release for a minimum of
          12 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: spec holds user settable values for configuration
            properties:
              imageTagMirrors:
                description: "imageTagMirrors allows images referenced by image tags
                  in pods to be pulled from alternative mirrored repository locations.
                  The image pull specification provided to the pod will be compared
                  to the source locations described in imageTagMirrors and the image
                  may be pulled down from any of the mirrors in the list instead of
                  the specified repository allowing administrators to choose a potentially
                  faster mirror. To use mirrors to pull images using digest specification
                  only, users should configure a list of mirrors using \"ImageDigestMirrorSet\"
                  CRD. \n If the image pull specification matches the repository of
                  \"source\" in multiple imagetagmirrorset objects, only the objects
                  which define the most specific namespace match will be used. For
                  example, if there are objects using quay.io/libpod and quay.io/libpod/busybox
                  as the \"source\", only the objects using quay.io/libpod/busybox
                  are going to apply for pull specification quay.io/libpod/busybox.
                  Each “source” repository is treated independently; configurations
                  for different “source” repositories don’t interact. \n If the \"mirrors\"
                  is not specified, the image will continue to be pulled from the
                  specified repository in the pull spec. \n When multiple policies
                  are defined for the same “source” repository, the sets of defined
                  mirrors will be merged together, preserving the relative order of
                  the mirrors, if possible. For example, if policy A has mirrors `a,
                  b, c` and policy B has mirrors `c, d, e`, the mirrors will be used
                  in the order `a, b, c, d, e`.  If the orders of mirror entries conflict
                  (e.g. `a, b` vs. `b, a`) the configuration is not rejected but the
                  resulting order is unspecified. Users who want to use a deterministic
                  order of mirrors, should configure them into one list of mirrors
                  using the expected order."
                items:
                  description: ImageTagMirrors holds cluster-wide information about
                    how to handle mirrors in the registries config.
                  properties:
                    mirrorSourcePolicy:
                      description: mirrorSourcePolicy defines the fallback policy
                        if fails to pull image from the mirrors. If unset, the image
                        will continue to be pulled from the repository in the pull
                        spec. sourcePolicy is valid configuration only when one or
                        more mirrors are in the mirror list.
                      enum:
                      - NeverContactSource
                      - AllowContactingSource
                      type: string
                    mirrors:
                      description: 'mirrors is zero or more locations that may also
                        contain the same images. No mirror will be configured if not
                        specified. Images can be pulled from these mirrors only if
                        they are referenced by their tags. The mirrored location is
                        obtained by replacing the part of the input reference that
                        matches source by the mirrors entry, e.g. for registry.redhat.io/product/repo
                        reference, a (source, mirror) pair *.redhat.io, mirror.local/redhat
                        causes a mirror.local/redhat/product/repo repository to be
                        used. Pulling images by tag can potentially yield different
                        images, depending on which endpoint we pull from. Configuring
                        a list of mirrors using "ImageDigestMirrorSet" CRD and forcing
                        digest-pulls for mirrors avoids that issue. The order of mirrors
                        in this list is treated as the user''s desired priority, while
                        source is by default considered lower priority than all mirrors.
                        If no mirror is specified or all image pulls from the mirror
                        list fail, the image will continue to be pulled from the repository
                        in the pull spec unless explicitly prohibited by "mirrorSourcePolicy".
                        Other cluster configuration, including (but not limited to)
                        other imageTagMirrors objects, may impact the exact order
                        mirrors are contacted in, or some mirrors may be contacted
                        in parallel, so this should be considered a preference rather
                        than a guarantee of ordering. "mirrors" uses one of the following
                        formats: host[:port] host[:port]/namespace[/namespace…] host[:port]/namespace[/namespace…]/repo
                        for more information about the format, see the document about
                        the location field: https://github.com/containers/image/blob/main/docs/containers-registries.conf.5.md#choosing-a-registry-toml-table'
                      items:
                        pattern: ^((?:[a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9])(?:(?:\.(?:[a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]))+)?(?::[0-9]+)?)(?:(?:/[a-z0-9]+(?:(?:(?:[._]|__|[-]*)[a-z0-9]+)+)?)+)?$
                        type: string
                      type: array
                      x-kubernetes-list-type: set
                    source:
                      description: 'source matches the repository that users refer
                        to, e.g. in image pull specifications. Setting source to a
                        registry hostname e.g. docker.io. quay.io, or registry.redhat.io,
                        will match the image pull specification of corressponding
                        registry. "source" uses one of the following formats: host[:port]
                        host[:port]/namespace[/namespace…] host[:port]/namespace[/namespace…]/repo
                        [*.]host for more information about the format, see the document
                        about the location field: https://github.com/containers/image/blob/main/docs/containers-registries.conf.5.md#choosing-a-registry-toml-table'
                      pattern: ^\*(?:\.(?:[a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]))+$|^((?:[a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9])(?:(?:\.(?:[a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]))+)?(?::[0-9]+)?)(?:(?:/[a-z0-9]+(?:(?:(?:[._]|__|[-]*)[a-z0-9]+)+)?)+)?$
                      type: string
                  required:
                  - source
                  type: object
                type: array
                x-kubernetes-list-type: atomic
            type: object
          status:
            description: status contains the observed state of the resource.
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ImageTagMirrorSet
    listKind: ImageTagMirrorSetList
    plural: imagetagmirrorsets
    shortNames:
    - itms
    singular: imagetagmirrorset
  conditions:
  - lastTransitionTime: "2025-06-25T14:52:54Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:52:54Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
