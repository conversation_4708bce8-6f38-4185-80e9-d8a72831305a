---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/470
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    release.openshift.io/bootstrap-required: "true"
    release.openshift.io/feature-set: Default
  creationTimestamp: "2025-06-25T14:52:51Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
          f:release.openshift.io/feature-set: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-bootstrap
    operation: Update
    time: "2025-06-25T14:52:51Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:52:51Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:api.openshift.io/merged-by-featuregates: {}
          f:release.openshift.io/bootstrap-required: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:19:45Z"
  name: authentications.config.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "133571"
  uid: 60cb7375-2af1-4212-9a7b-e548d641c935
spec:
  conversion:
    strategy: None
  group: config.openshift.io
  names:
    kind: Authentication
    listKind: AuthenticationList
    plural: authentications
    singular: authentication
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "Authentication specifies cluster-wide settings for authentication
          (like OAuth and webhook token authenticators). The canonical name of an
          instance is `cluster`. \n Compatibility level 1: Stable within a major release
          for a minimum of 12 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: spec holds user settable values for configuration
            properties:
              oauthMetadata:
                description: 'oauthMetadata contains the discovery endpoint data for
                  OAuth 2.0 Authorization Server Metadata for an external OAuth server.
                  This discovery document can be viewed from its served location:
                  oc get --raw ''/.well-known/oauth-authorization-server'' For further
                  details, see the IETF Draft: https://tools.ietf.org/html/draft-ietf-oauth-discovery-04#section-2
                  If oauthMetadata.name is non-empty, this value has precedence over
                  any metadata reference stored in status. The key "oauthMetadata"
                  is used to locate the data. If specified and the config map or expected
                  key is not found, no metadata is served. If the specified metadata
                  is not valid, no metadata is served. The namespace for this config
                  map is openshift-config.'
                properties:
                  name:
                    description: name is the metadata.name of the referenced config
                      map
                    type: string
                required:
                - name
                type: object
              serviceAccountIssuer:
                description: 'serviceAccountIssuer is the identifier of the bound
                  service account token issuer. The default is https://kubernetes.default.svc
                  WARNING: Updating this field will not result in immediate invalidation
                  of all bound tokens with the previous issuer value. Instead, the
                  tokens issued by previous service account issuer will continue to
                  be trusted for a time period chosen by the platform (currently set
                  to 24h). This time period is subject to change over time. This allows
                  internal components to transition to use new service account issuer
                  without service distruption.'
                type: string
              type:
                description: type identifies the cluster managed, user facing authentication
                  mode in use. Specifically, it manages the component that responds
                  to login attempts. The default is IntegratedOAuth.
                enum:
                - ""
                - None
                - IntegratedOAuth
                type: string
              webhookTokenAuthenticator:
                description: "webhookTokenAuthenticator configures a remote token
                  reviewer. These remote authentication webhooks can be used to verify
                  bearer tokens via the tokenreviews.authentication.k8s.io REST API.
                  This is required to honor bearer tokens that are provisioned by
                  an external authentication service. \n Can only be set if \"Type\"
                  is set to \"None\"."
                properties:
                  kubeConfig:
                    description: "kubeConfig references a secret that contains kube
                      config file data which describes how to access the remote webhook
                      service. The namespace for the referenced secret is openshift-config.
                      \n For further details, see: \n https://kubernetes.io/docs/reference/access-authn-authz/authentication/#webhook-token-authentication
                      \n The key \"kubeConfig\" is used to locate the data. If the
                      secret or expected key is not found, the webhook is not honored.
                      If the specified kube config data is not valid, the webhook
                      is not honored."
                    properties:
                      name:
                        description: name is the metadata.name of the referenced secret
                        type: string
                    required:
                    - name
                    type: object
                required:
                - kubeConfig
                type: object
              webhookTokenAuthenticators:
                description: webhookTokenAuthenticators is DEPRECATED, setting it
                  has no effect.
                items:
                  description: deprecatedWebhookTokenAuthenticator holds the necessary
                    configuration options for a remote token authenticator. It's the
                    same as WebhookTokenAuthenticator but it's missing the 'required'
                    validation on KubeConfig field.
                  properties:
                    kubeConfig:
                      description: 'kubeConfig contains kube config file data which
                        describes how to access the remote webhook service. For further
                        details, see: https://kubernetes.io/docs/reference/access-authn-authz/authentication/#webhook-token-authentication
                        The key "kubeConfig" is used to locate the data. If the secret
                        or expected key is not found, the webhook is not honored.
                        If the specified kube config data is not valid, the webhook
                        is not honored. The namespace for this secret is determined
                        by the point of use.'
                      properties:
                        name:
                          description: name is the metadata.name of the referenced
                            secret
                          type: string
                      required:
                      - name
                      type: object
                  type: object
                type: array
                x-kubernetes-list-type: atomic
            type: object
          status:
            description: status holds observed values from the cluster. They may not
              be overridden.
            properties:
              integratedOAuthMetadata:
                description: 'integratedOAuthMetadata contains the discovery endpoint
                  data for OAuth 2.0 Authorization Server Metadata for the in-cluster
                  integrated OAuth server. This discovery document can be viewed from
                  its served location: oc get --raw ''/.well-known/oauth-authorization-server''
                  For further details, see the IETF Draft: https://tools.ietf.org/html/draft-ietf-oauth-discovery-04#section-2
                  This contains the observed value based on cluster state. An explicitly
                  set value in spec.oauthMetadata has precedence over this field.
                  This field has no meaning if authentication spec.type is not set
                  to IntegratedOAuth. The key "oauthMetadata" is used to locate the
                  data. If the config map or expected key is not found, no metadata
                  is served. If the specified metadata is not valid, no metadata is
                  served. The namespace for this config map is openshift-config-managed.'
                properties:
                  name:
                    description: name is the metadata.name of the referenced config
                      map
                    type: string
                required:
                - name
                type: object
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: Authentication
    listKind: AuthenticationList
    plural: authentications
    singular: authentication
  conditions:
  - lastTransitionTime: "2025-06-25T14:52:51Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:52:51Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
