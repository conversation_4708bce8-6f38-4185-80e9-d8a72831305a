---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    capability.openshift.io/name: OperatorLifecycleManager
    controller-gen.kubebuilder.io/version: v0.15.0
    include.release.openshift.io/hypershift: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
  creationTimestamp: "2025-06-25T14:53:39Z"
  generation: 3
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:categories: {}
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:39Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:capability.openshift.io/name: {}
          f:controller-gen.kubebuilder.io/version: {}
          f:include.release.openshift.io/hypershift: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:categories: {}
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T12:09:03Z"
  name: catalogsources.operators.coreos.com
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21340612"
  uid: 70e55a2a-bf37-4ffc-8fe2-c3576b4c5300
spec:
  conversion:
    strategy: None
  group: operators.coreos.com
  names:
    categories:
    - olm
    kind: CatalogSource
    listKind: CatalogSourceList
    plural: catalogsources
    shortNames:
    - catsrc
    singular: catalogsource
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: The pretty name of the catalog
      jsonPath: .spec.displayName
      name: Display
      type: string
    - description: The type of the catalog
      jsonPath: .spec.sourceType
      name: Type
      type: string
    - description: The publisher of the catalog
      jsonPath: .spec.publisher
      name: Publisher
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: CatalogSource is a repository of CSVs, CRDs, and operator packages.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            properties:
              address:
                description: |-
                  Address is a host that OLM can use to connect to a pre-existing registry.
                  Format: <registry-host or ip>:<port>
                  Only used when SourceType = SourceTypeGrpc.
                  Ignored when the Image field is set.
                type: string
              configMap:
                description: |-
                  ConfigMap is the name of the ConfigMap to be used to back a configmap-server registry.
                  Only used when SourceType = SourceTypeConfigmap or SourceTypeInternal.
                type: string
              description:
                type: string
              displayName:
                description: Metadata
                type: string
              grpcPodConfig:
                description: |-
                  GrpcPodConfig exposes different overrides for the pod spec of the CatalogSource Pod.
                  Only used when SourceType = SourceTypeGrpc and Image is set.
                properties:
                  affinity:
                    description: Affinity is the catalog source's pod's affinity.
                    properties:
                      nodeAffinity:
                        description: Describes node affinity scheduling rules for
                          the pod.
                        properties:
                          preferredDuringSchedulingIgnoredDuringExecution:
                            description: |-
                              The scheduler will prefer to schedule pods to nodes that satisfy
                              the affinity expressions specified by this field, but it may choose
                              a node that violates one or more of the expressions. The node that is
                              most preferred is the one with the greatest sum of weights, i.e.
                              for each node that meets all of the scheduling requirements (resource
                              request, requiredDuringScheduling affinity expressions, etc.),
                              compute a sum by iterating through the elements of this field and adding
                              "weight" to the sum if the node matches the corresponding matchExpressions; the
                              node(s) with the highest sum are the most preferred.
                            items:
                              description: |-
                                An empty preferred scheduling term matches all objects with implicit weight 0
                                (i.e. it's a no-op). A null preferred scheduling term matches no objects (i.e. is also a no-op).
                              properties:
                                preference:
                                  description: A node selector term, associated with
                                    the corresponding weight.
                                  properties:
                                    matchExpressions:
                                      description: A list of node selector requirements
                                        by node's labels.
                                      items:
                                        description: |-
                                          A node selector requirement is a selector that contains values, a key, and an operator
                                          that relates the key and values.
                                        properties:
                                          key:
                                            description: The label key that the selector
                                              applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              Represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                            type: string
                                          values:
                                            description: |-
                                              An array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. If the operator is Gt or Lt, the values
                                              array must have a single element, which will be interpreted as an integer.
                                              This array is replaced during a strategic merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchFields:
                                      description: A list of node selector requirements
                                        by node's fields.
                                      items:
                                        description: |-
                                          A node selector requirement is a selector that contains values, a key, and an operator
                                          that relates the key and values.
                                        properties:
                                          key:
                                            description: The label key that the selector
                                              applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              Represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                            type: string
                                          values:
                                            description: |-
                                              An array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. If the operator is Gt or Lt, the values
                                              array must have a single element, which will be interpreted as an integer.
                                              This array is replaced during a strategic merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                  type: object
                                  x-kubernetes-map-type: atomic
                                weight:
                                  description: Weight associated with matching the
                                    corresponding nodeSelectorTerm, in the range 1-100.
                                  format: int32
                                  type: integer
                              required:
                              - preference
                              - weight
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          requiredDuringSchedulingIgnoredDuringExecution:
                            description: |-
                              If the affinity requirements specified by this field are not met at
                              scheduling time, the pod will not be scheduled onto the node.
                              If the affinity requirements specified by this field cease to be met
                              at some point during pod execution (e.g. due to an update), the system
                              may or may not try to eventually evict the pod from its node.
                            properties:
                              nodeSelectorTerms:
                                description: Required. A list of node selector terms.
                                  The terms are ORed.
                                items:
                                  description: |-
                                    A null or empty node selector term matches no objects. The requirements of
                                    them are ANDed.
                                    The TopologySelectorTerm type implements a subset of the NodeSelectorTerm.
                                  properties:
                                    matchExpressions:
                                      description: A list of node selector requirements
                                        by node's labels.
                                      items:
                                        description: |-
                                          A node selector requirement is a selector that contains values, a key, and an operator
                                          that relates the key and values.
                                        properties:
                                          key:
                                            description: The label key that the selector
                                              applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              Represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                            type: string
                                          values:
                                            description: |-
                                              An array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. If the operator is Gt or Lt, the values
                                              array must have a single element, which will be interpreted as an integer.
                                              This array is replaced during a strategic merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchFields:
                                      description: A list of node selector requirements
                                        by node's fields.
                                      items:
                                        description: |-
                                          A node selector requirement is a selector that contains values, a key, and an operator
                                          that relates the key and values.
                                        properties:
                                          key:
                                            description: The label key that the selector
                                              applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              Represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                            type: string
                                          values:
                                            description: |-
                                              An array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. If the operator is Gt or Lt, the values
                                              array must have a single element, which will be interpreted as an integer.
                                              This array is replaced during a strategic merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                  type: object
                                  x-kubernetes-map-type: atomic
                                type: array
                                x-kubernetes-list-type: atomic
                            required:
                            - nodeSelectorTerms
                            type: object
                            x-kubernetes-map-type: atomic
                        type: object
                      podAffinity:
                        description: Describes pod affinity scheduling rules (e.g.
                          co-locate this pod in the same node, zone, etc. as some
                          other pod(s)).
                        properties:
                          preferredDuringSchedulingIgnoredDuringExecution:
                            description: |-
                              The scheduler will prefer to schedule pods to nodes that satisfy
                              the affinity expressions specified by this field, but it may choose
                              a node that violates one or more of the expressions. The node that is
                              most preferred is the one with the greatest sum of weights, i.e.
                              for each node that meets all of the scheduling requirements (resource
                              request, requiredDuringScheduling affinity expressions, etc.),
                              compute a sum by iterating through the elements of this field and adding
                              "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
                              node(s) with the highest sum are the most preferred.
                            items:
                              description: The weights of all of the matched WeightedPodAffinityTerm
                                fields are added per-node to find the most preferred
                                node(s)
                              properties:
                                podAffinityTerm:
                                  description: Required. A pod affinity term, associated
                                    with the corresponding weight.
                                  properties:
                                    labelSelector:
                                      description: |-
                                        A label query over a set of resources, in this case pods.
                                        If it's null, this PodAffinityTerm matches with no Pods.
                                      properties:
                                        matchExpressions:
                                          description: matchExpressions is a list
                                            of label selector requirements. The requirements
                                            are ANDed.
                                          items:
                                            description: |-
                                              A label selector requirement is a selector that contains values, a key, and an operator that
                                              relates the key and values.
                                            properties:
                                              key:
                                                description: key is the label key
                                                  that the selector applies to.
                                                type: string
                                              operator:
                                                description: |-
                                                  operator represents a key's relationship to a set of values.
                                                  Valid operators are In, NotIn, Exists and DoesNotExist.
                                                type: string
                                              values:
                                                description: |-
                                                  values is an array of string values. If the operator is In or NotIn,
                                                  the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                  the values array must be empty. This array is replaced during a strategic
                                                  merge patch.
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                            required:
                                            - key
                                            - operator
                                            type: object
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        matchLabels:
                                          additionalProperties:
                                            type: string
                                          description: |-
                                            matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                            map is equivalent to an element of matchExpressions, whose key field is "key", the
                                            operator is "In", and the values array contains only "value". The requirements are ANDed.
                                          type: object
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    matchLabelKeys:
                                      description: |-
                                        MatchLabelKeys is a set of pod label keys to select which pods will
                                        be taken into consideration. The keys are used to lookup values from the
                                        incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
                                        to select the group of existing pods which pods will be taken into consideration
                                        for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                        pod labels will be ignored. The default value is empty.
                                        The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                        Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                        This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    mismatchLabelKeys:
                                      description: |-
                                        MismatchLabelKeys is a set of pod label keys to select which pods will
                                        be taken into consideration. The keys are used to lookup values from the
                                        incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
                                        to select the group of existing pods which pods will be taken into consideration
                                        for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                        pod labels will be ignored. The default value is empty.
                                        The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                        Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                        This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    namespaceSelector:
                                      description: |-
                                        A label query over the set of namespaces that the term applies to.
                                        The term is applied to the union of the namespaces selected by this field
                                        and the ones listed in the namespaces field.
                                        null selector and null or empty namespaces list means "this pod's namespace".
                                        An empty selector ({}) matches all namespaces.
                                      properties:
                                        matchExpressions:
                                          description: matchExpressions is a list
                                            of label selector requirements. The requirements
                                            are ANDed.
                                          items:
                                            description: |-
                                              A label selector requirement is a selector that contains values, a key, and an operator that
                                              relates the key and values.
                                            properties:
                                              key:
                                                description: key is the label key
                                                  that the selector applies to.
                                                type: string
                                              operator:
                                                description: |-
                                                  operator represents a key's relationship to a set of values.
                                                  Valid operators are In, NotIn, Exists and DoesNotExist.
                                                type: string
                                              values:
                                                description: |-
                                                  values is an array of string values. If the operator is In or NotIn,
                                                  the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                  the values array must be empty. This array is replaced during a strategic
                                                  merge patch.
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                            required:
                                            - key
                                            - operator
                                            type: object
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        matchLabels:
                                          additionalProperties:
                                            type: string
                                          description: |-
                                            matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                            map is equivalent to an element of matchExpressions, whose key field is "key", the
                                            operator is "In", and the values array contains only "value". The requirements are ANDed.
                                          type: object
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    namespaces:
                                      description: |-
                                        namespaces specifies a static list of namespace names that the term applies to.
                                        The term is applied to the union of the namespaces listed in this field
                                        and the ones selected by namespaceSelector.
                                        null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    topologyKey:
                                      description: |-
                                        This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                        the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                        whose value of the label with key topologyKey matches that of any node on which any of the
                                        selected pods is running.
                                        Empty topologyKey is not allowed.
                                      type: string
                                  required:
                                  - topologyKey
                                  type: object
                                weight:
                                  description: |-
                                    weight associated with matching the corresponding podAffinityTerm,
                                    in the range 1-100.
                                  format: int32
                                  type: integer
                              required:
                              - podAffinityTerm
                              - weight
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          requiredDuringSchedulingIgnoredDuringExecution:
                            description: |-
                              If the affinity requirements specified by this field are not met at
                              scheduling time, the pod will not be scheduled onto the node.
                              If the affinity requirements specified by this field cease to be met
                              at some point during pod execution (e.g. due to a pod label update), the
                              system may or may not try to eventually evict the pod from its node.
                              When there are multiple elements, the lists of nodes corresponding to each
                              podAffinityTerm are intersected, i.e. all terms must be satisfied.
                            items:
                              description: |-
                                Defines a set of pods (namely those matching the labelSelector
                                relative to the given namespace(s)) that this pod should be
                                co-located (affinity) or not co-located (anti-affinity) with,
                                where co-located is defined as running on a node whose value of
                                the label with key <topologyKey> matches that of any node on which
                                a pod of the set of pods is running
                              properties:
                                labelSelector:
                                  description: |-
                                    A label query over a set of resources, in this case pods.
                                    If it's null, this PodAffinityTerm matches with no Pods.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: |-
                                          A label selector requirement is a selector that contains values, a key, and an operator that
                                          relates the key and values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              operator represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists and DoesNotExist.
                                            type: string
                                          values:
                                            description: |-
                                              values is an array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: |-
                                        matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions, whose key field is "key", the
                                        operator is "In", and the values array contains only "value". The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                matchLabelKeys:
                                  description: |-
                                    MatchLabelKeys is a set of pod label keys to select which pods will
                                    be taken into consideration. The keys are used to lookup values from the
                                    incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
                                    to select the group of existing pods which pods will be taken into consideration
                                    for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                    pod labels will be ignored. The default value is empty.
                                    The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                    Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                    This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                mismatchLabelKeys:
                                  description: |-
                                    MismatchLabelKeys is a set of pod label keys to select which pods will
                                    be taken into consideration. The keys are used to lookup values from the
                                    incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
                                    to select the group of existing pods which pods will be taken into consideration
                                    for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                    pod labels will be ignored. The default value is empty.
                                    The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                    Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                    This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                namespaceSelector:
                                  description: |-
                                    A label query over the set of namespaces that the term applies to.
                                    The term is applied to the union of the namespaces selected by this field
                                    and the ones listed in the namespaces field.
                                    null selector and null or empty namespaces list means "this pod's namespace".
                                    An empty selector ({}) matches all namespaces.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: |-
                                          A label selector requirement is a selector that contains values, a key, and an operator that
                                          relates the key and values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              operator represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists and DoesNotExist.
                                            type: string
                                          values:
                                            description: |-
                                              values is an array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: |-
                                        matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions, whose key field is "key", the
                                        operator is "In", and the values array contains only "value". The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                namespaces:
                                  description: |-
                                    namespaces specifies a static list of namespace names that the term applies to.
                                    The term is applied to the union of the namespaces listed in this field
                                    and the ones selected by namespaceSelector.
                                    null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                topologyKey:
                                  description: |-
                                    This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                    the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                    whose value of the label with key topologyKey matches that of any node on which any of the
                                    selected pods is running.
                                    Empty topologyKey is not allowed.
                                  type: string
                              required:
                              - topologyKey
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                        type: object
                      podAntiAffinity:
                        description: Describes pod anti-affinity scheduling rules
                          (e.g. avoid putting this pod in the same node, zone, etc.
                          as some other pod(s)).
                        properties:
                          preferredDuringSchedulingIgnoredDuringExecution:
                            description: |-
                              The scheduler will prefer to schedule pods to nodes that satisfy
                              the anti-affinity expressions specified by this field, but it may choose
                              a node that violates one or more of the expressions. The node that is
                              most preferred is the one with the greatest sum of weights, i.e.
                              for each node that meets all of the scheduling requirements (resource
                              request, requiredDuringScheduling anti-affinity expressions, etc.),
                              compute a sum by iterating through the elements of this field and adding
                              "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
                              node(s) with the highest sum are the most preferred.
                            items:
                              description: The weights of all of the matched WeightedPodAffinityTerm
                                fields are added per-node to find the most preferred
                                node(s)
                              properties:
                                podAffinityTerm:
                                  description: Required. A pod affinity term, associated
                                    with the corresponding weight.
                                  properties:
                                    labelSelector:
                                      description: |-
                                        A label query over a set of resources, in this case pods.
                                        If it's null, this PodAffinityTerm matches with no Pods.
                                      properties:
                                        matchExpressions:
                                          description: matchExpressions is a list
                                            of label selector requirements. The requirements
                                            are ANDed.
                                          items:
                                            description: |-
                                              A label selector requirement is a selector that contains values, a key, and an operator that
                                              relates the key and values.
                                            properties:
                                              key:
                                                description: key is the label key
                                                  that the selector applies to.
                                                type: string
                                              operator:
                                                description: |-
                                                  operator represents a key's relationship to a set of values.
                                                  Valid operators are In, NotIn, Exists and DoesNotExist.
                                                type: string
                                              values:
                                                description: |-
                                                  values is an array of string values. If the operator is In or NotIn,
                                                  the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                  the values array must be empty. This array is replaced during a strategic
                                                  merge patch.
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                            required:
                                            - key
                                            - operator
                                            type: object
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        matchLabels:
                                          additionalProperties:
                                            type: string
                                          description: |-
                                            matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                            map is equivalent to an element of matchExpressions, whose key field is "key", the
                                            operator is "In", and the values array contains only "value". The requirements are ANDed.
                                          type: object
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    matchLabelKeys:
                                      description: |-
                                        MatchLabelKeys is a set of pod label keys to select which pods will
                                        be taken into consideration. The keys are used to lookup values from the
                                        incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
                                        to select the group of existing pods which pods will be taken into consideration
                                        for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                        pod labels will be ignored. The default value is empty.
                                        The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                        Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                        This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    mismatchLabelKeys:
                                      description: |-
                                        MismatchLabelKeys is a set of pod label keys to select which pods will
                                        be taken into consideration. The keys are used to lookup values from the
                                        incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
                                        to select the group of existing pods which pods will be taken into consideration
                                        for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                        pod labels will be ignored. The default value is empty.
                                        The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                        Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                        This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    namespaceSelector:
                                      description: |-
                                        A label query over the set of namespaces that the term applies to.
                                        The term is applied to the union of the namespaces selected by this field
                                        and the ones listed in the namespaces field.
                                        null selector and null or empty namespaces list means "this pod's namespace".
                                        An empty selector ({}) matches all namespaces.
                                      properties:
                                        matchExpressions:
                                          description: matchExpressions is a list
                                            of label selector requirements. The requirements
                                            are ANDed.
                                          items:
                                            description: |-
                                              A label selector requirement is a selector that contains values, a key, and an operator that
                                              relates the key and values.
                                            properties:
                                              key:
                                                description: key is the label key
                                                  that the selector applies to.
                                                type: string
                                              operator:
                                                description: |-
                                                  operator represents a key's relationship to a set of values.
                                                  Valid operators are In, NotIn, Exists and DoesNotExist.
                                                type: string
                                              values:
                                                description: |-
                                                  values is an array of string values. If the operator is In or NotIn,
                                                  the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                  the values array must be empty. This array is replaced during a strategic
                                                  merge patch.
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                            required:
                                            - key
                                            - operator
                                            type: object
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        matchLabels:
                                          additionalProperties:
                                            type: string
                                          description: |-
                                            matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                            map is equivalent to an element of matchExpressions, whose key field is "key", the
                                            operator is "In", and the values array contains only "value". The requirements are ANDed.
                                          type: object
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    namespaces:
                                      description: |-
                                        namespaces specifies a static list of namespace names that the term applies to.
                                        The term is applied to the union of the namespaces listed in this field
                                        and the ones selected by namespaceSelector.
                                        null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    topologyKey:
                                      description: |-
                                        This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                        the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                        whose value of the label with key topologyKey matches that of any node on which any of the
                                        selected pods is running.
                                        Empty topologyKey is not allowed.
                                      type: string
                                  required:
                                  - topologyKey
                                  type: object
                                weight:
                                  description: |-
                                    weight associated with matching the corresponding podAffinityTerm,
                                    in the range 1-100.
                                  format: int32
                                  type: integer
                              required:
                              - podAffinityTerm
                              - weight
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          requiredDuringSchedulingIgnoredDuringExecution:
                            description: |-
                              If the anti-affinity requirements specified by this field are not met at
                              scheduling time, the pod will not be scheduled onto the node.
                              If the anti-affinity requirements specified by this field cease to be met
                              at some point during pod execution (e.g. due to a pod label update), the
                              system may or may not try to eventually evict the pod from its node.
                              When there are multiple elements, the lists of nodes corresponding to each
                              podAffinityTerm are intersected, i.e. all terms must be satisfied.
                            items:
                              description: |-
                                Defines a set of pods (namely those matching the labelSelector
                                relative to the given namespace(s)) that this pod should be
                                co-located (affinity) or not co-located (anti-affinity) with,
                                where co-located is defined as running on a node whose value of
                                the label with key <topologyKey> matches that of any node on which
                                a pod of the set of pods is running
                              properties:
                                labelSelector:
                                  description: |-
                                    A label query over a set of resources, in this case pods.
                                    If it's null, this PodAffinityTerm matches with no Pods.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: |-
                                          A label selector requirement is a selector that contains values, a key, and an operator that
                                          relates the key and values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              operator represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists and DoesNotExist.
                                            type: string
                                          values:
                                            description: |-
                                              values is an array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: |-
                                        matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions, whose key field is "key", the
                                        operator is "In", and the values array contains only "value". The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                matchLabelKeys:
                                  description: |-
                                    MatchLabelKeys is a set of pod label keys to select which pods will
                                    be taken into consideration. The keys are used to lookup values from the
                                    incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
                                    to select the group of existing pods which pods will be taken into consideration
                                    for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                    pod labels will be ignored. The default value is empty.
                                    The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                    Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                    This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                mismatchLabelKeys:
                                  description: |-
                                    MismatchLabelKeys is a set of pod label keys to select which pods will
                                    be taken into consideration. The keys are used to lookup values from the
                                    incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
                                    to select the group of existing pods which pods will be taken into consideration
                                    for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                    pod labels will be ignored. The default value is empty.
                                    The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                    Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                    This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                namespaceSelector:
                                  description: |-
                                    A label query over the set of namespaces that the term applies to.
                                    The term is applied to the union of the namespaces selected by this field
                                    and the ones listed in the namespaces field.
                                    null selector and null or empty namespaces list means "this pod's namespace".
                                    An empty selector ({}) matches all namespaces.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: |-
                                          A label selector requirement is a selector that contains values, a key, and an operator that
                                          relates the key and values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              operator represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists and DoesNotExist.
                                            type: string
                                          values:
                                            description: |-
                                              values is an array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: |-
                                        matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions, whose key field is "key", the
                                        operator is "In", and the values array contains only "value". The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                namespaces:
                                  description: |-
                                    namespaces specifies a static list of namespace names that the term applies to.
                                    The term is applied to the union of the namespaces listed in this field
                                    and the ones selected by namespaceSelector.
                                    null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                topologyKey:
                                  description: |-
                                    This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                    the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                    whose value of the label with key topologyKey matches that of any node on which any of the
                                    selected pods is running.
                                    Empty topologyKey is not allowed.
                                  type: string
                              required:
                              - topologyKey
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                        type: object
                    type: object
                  extractContent:
                    description: |-
                      ExtractContent configures the gRPC catalog Pod to extract catalog metadata from the provided index image and
                      use a well-known version of the `opm` server to expose it. The catalog index image that this CatalogSource is
                      configured to use *must* be using the file-based catalogs in order to utilize this feature.
                    properties:
                      cacheDir:
                        description: CacheDir is the directory storing the pre-calculated
                          API cache.
                        type: string
                      catalogDir:
                        description: CatalogDir is the directory storing the file-based
                          catalog contents.
                        type: string
                    required:
                    - cacheDir
                    - catalogDir
                    type: object
                  memoryTarget:
                    anyOf:
                    - type: integer
                    - type: string
                    description: |-
                      MemoryTarget configures the $GOMEMLIMIT value for the gRPC catalog Pod. This is a soft memory limit for the server,
                      which the runtime will attempt to meet but makes no guarantees that it will do so. If this value is set, the Pod
                      will have the following modifications made to the container running the server:
                      - the $GOMEMLIMIT environment variable will be set to this value in bytes
                      - the memory request will be set to this value


                      This field should be set if it's desired to reduce the footprint of a catalog server as much as possible, or if
                      a catalog being served is very large and needs more than the default allocation. If your index image has a file-
                      system cache, determine a good approximation for this value by doubling the size of the package cache at
                      /tmp/cache/cache/packages.json in the index image.


                      This field is best-effort; if unset, no default will be used and no Pod memory limit or $GOMEMLIMIT value will be set.
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                  nodeSelector:
                    additionalProperties:
                      type: string
                    description: |-
                      NodeSelector is a selector which must be true for the pod to fit on a node.
                      Selector which must match a node's labels for the pod to be scheduled on that node.
                    type: object
                  priorityClassName:
                    description: |-
                      If specified, indicates the pod's priority.
                      If not specified, the pod priority will be default or zero if there is no
                      default.
                    type: string
                  securityContextConfig:
                    description: |-
                      SecurityContextConfig can be one of `legacy` or `restricted`. The CatalogSource's pod is either injected with the
                      right pod.spec.securityContext and pod.spec.container[*].securityContext values to allow the pod to run in Pod
                      Security Admission (PSA) `restricted` mode, or doesn't set these values at all, in which case the pod can only be
                      run in PSA `baseline` or `privileged` namespaces. If the SecurityContextConfig is unspecified, the mode will be
                      determined by the namespace's PSA configuration. If the namespace is enforcing `restricted` mode, then the pod
                      will be configured as if `restricted` was specified. Otherwise, it will be configured as if `legacy` was
                      specified. Specifying a value other than `legacy` or `restricted` result in a validation error. When using older
                      catalog images, which can not run in `restricted` mode, the SecurityContextConfig should be set to `legacy`.


                      More information about PSA can be found here: https://kubernetes.io/docs/concepts/security/pod-security-admission/'
                    enum:
                    - legacy
                    - restricted
                    type: string
                  tolerations:
                    description: Tolerations are the catalog source's pod's tolerations.
                    items:
                      description: |-
                        The pod this Toleration is attached to tolerates any taint that matches
                        the triple <key,value,effect> using the matching operator <operator>.
                      properties:
                        effect:
                          description: |-
                            Effect indicates the taint effect to match. Empty means match all taint effects.
                            When specified, allowed values are NoSchedule, PreferNoSchedule and NoExecute.
                          type: string
                        key:
                          description: |-
                            Key is the taint key that the toleration applies to. Empty means match all taint keys.
                            If the key is empty, operator must be Exists; this combination means to match all values and all keys.
                          type: string
                        operator:
                          description: |-
                            Operator represents a key's relationship to the value.
                            Valid operators are Exists and Equal. Defaults to Equal.
                            Exists is equivalent to wildcard for value, so that a pod can
                            tolerate all taints of a particular category.
                          type: string
                        tolerationSeconds:
                          description: |-
                            TolerationSeconds represents the period of time the toleration (which must be
                            of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default,
                            it is not set, which means tolerate the taint forever (do not evict). Zero and
                            negative values will be treated as 0 (evict immediately) by the system.
                          format: int64
                          type: integer
                        value:
                          description: |-
                            Value is the taint value the toleration matches to.
                            If the operator is Exists, the value should be empty, otherwise just a regular string.
                          type: string
                      type: object
                    type: array
                type: object
              icon:
                properties:
                  base64data:
                    type: string
                  mediatype:
                    type: string
                required:
                - base64data
                - mediatype
                type: object
              image:
                description: |-
                  Image is an operator-registry container image to instantiate a registry-server with.
                  Only used when SourceType = SourceTypeGrpc.
                  If present, the address field is ignored.
                type: string
              priority:
                description: |-
                  Priority field assigns a weight to the catalog source to prioritize them so that it can be consumed by the dependency resolver.
                  Usage:
                  Higher weight indicates that this catalog source is preferred over lower weighted catalog sources during dependency resolution.
                  The range of the priority value can go from positive to negative in the range of int32.
                  The default value to a catalog source with unassigned priority would be 0.
                  The catalog source with the same priority values will be ranked lexicographically based on its name.
                type: integer
              publisher:
                type: string
              runAsRoot:
                description: |-
                  RunAsRoot allows admins to indicate that they wish to run the CatalogSource pod in a privileged
                  pod as root.  This should only be enabled when running older catalog images which could not be run as non-root.
                type: boolean
              secrets:
                description: |-
                  Secrets represent set of secrets that can be used to access the contents of the catalog.
                  It is best to keep this list small, since each will need to be tried for every catalog entry.
                items:
                  type: string
                type: array
              sourceType:
                description: SourceType is the type of source
                type: string
              updateStrategy:
                description: |-
                  UpdateStrategy defines how updated catalog source images can be discovered
                  Consists of an interval that defines polling duration and an embedded strategy type
                properties:
                  registryPoll:
                    properties:
                      interval:
                        description: |-
                          Interval is used to determine the time interval between checks of the latest catalog source version.
                          The catalog operator polls to see if a new version of the catalog source is available.
                          If available, the latest image is pulled and gRPC traffic is directed to the latest catalog source.
                        type: string
                    type: object
                type: object
            required:
            - sourceType
            type: object
          status:
            properties:
              conditions:
                description: |-
                  Represents the state of a CatalogSource. Note that Message and Reason represent the original
                  status information, which may be migrated to be conditions based in the future. Any new features
                  introduced will use conditions.
                items:
                  description: "Condition contains details for one aspect of the current
                    state of this API Resource.\n---\nThis struct is intended for
                    direct use as an array at the field path .status.conditions.  For
                    example,\n\n\n\ttype FooStatus struct{\n\t    // Represents the
                    observations of a foo's current state.\n\t    // Known .status.conditions.type
                    are: \"Available\", \"Progressing\", and \"Degraded\"\n\t    //
                    +patchMergeKey=type\n\t    // +patchStrategy=merge\n\t    // +listType=map\n\t
                    \   // +listMapKey=type\n\t    Conditions []metav1.Condition `json:\"conditions,omitempty\"
                    patchStrategy:\"merge\" patchMergeKey:\"type\" protobuf:\"bytes,1,rep,name=conditions\"`\n\n\n\t
                    \   // other fields\n\t}"
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: |-
                        type of condition in CamelCase or in foo.example.com/CamelCase.
                        ---
                        Many .condition.type values are consistent across resources like Available, but because arbitrary conditions can be
                        useful (see .node.status.conditions), the ability to deconflict is important.
                        The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              configMapReference:
                description: ConfigMapReference (deprecated) is the reference to the
                  ConfigMap containing the catalog source's configuration, when the
                  catalog source is a ConfigMap
                properties:
                  lastUpdateTime:
                    format: date-time
                    type: string
                  name:
                    type: string
                  namespace:
                    type: string
                  resourceVersion:
                    type: string
                  uid:
                    description: |-
                      UID is a type that holds unique ID values, including UUIDs.  Because we
                      don't ONLY use UUIDs, this is an alias to string.  Being a type captures
                      intent and helps make sure that UIDs and names do not get conflated.
                    type: string
                required:
                - name
                - namespace
                type: object
              connectionState:
                description: ConnectionState represents the current state of the CatalogSource's
                  connection to the registry
                properties:
                  address:
                    type: string
                  lastConnect:
                    format: date-time
                    type: string
                  lastObservedState:
                    type: string
                required:
                - lastObservedState
                type: object
              latestImageRegistryPoll:
                description: The last time the CatalogSource image registry has been
                  polled to ensure the image is up-to-date
                format: date-time
                type: string
              message:
                description: A human readable message indicating details about why
                  the CatalogSource is in this condition.
                type: string
              reason:
                description: Reason is the reason the CatalogSource was transitioned
                  to its current state.
                type: string
              registryService:
                description: RegistryService represents the current state of the GRPC
                  service used to serve the catalog
                properties:
                  createdAt:
                    format: date-time
                    type: string
                  port:
                    type: string
                  protocol:
                    type: string
                  serviceName:
                    type: string
                  serviceNamespace:
                    type: string
                type: object
            type: object
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    categories:
    - olm
    kind: CatalogSource
    listKind: CatalogSourceList
    plural: catalogsources
    shortNames:
    - catsrc
    singular: catalogsource
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:39Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:39Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1alpha1
