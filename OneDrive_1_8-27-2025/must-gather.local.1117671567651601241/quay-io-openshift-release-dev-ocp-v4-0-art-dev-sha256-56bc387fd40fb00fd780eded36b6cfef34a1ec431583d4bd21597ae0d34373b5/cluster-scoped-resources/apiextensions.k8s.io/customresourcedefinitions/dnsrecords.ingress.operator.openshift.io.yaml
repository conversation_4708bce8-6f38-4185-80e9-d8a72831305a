---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/584
    api.openshift.io/merged-by-featuregates: "true"
    capability.openshift.io/name: Ingress
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:53:39Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:39Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:capability.openshift.io/name: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:42:13Z"
  name: dnsrecords.ingress.operator.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "144724"
  uid: b93536da-5b42-48bf-966e-505cd366a0f1
spec:
  conversion:
    strategy: None
  group: ingress.operator.openshift.io
  names:
    kind: DNSRecord
    listKind: DNSRecordList
    plural: dnsrecords
    singular: dnsrecord
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "DNSRecord is a DNS record managed in the zones defined by dns.config.openshift.io/cluster
          .spec.publicZone and .spec.privateZone. \n Cluster admin manipulation of
          this resource is not supported. This resource is only for internal communication
          of OpenShift operators. \n If DNSManagementPolicy is \"Unmanaged\", the
          operator will not be responsible for managing the DNS records on the cloud
          provider. \n Compatibility level 1: Stable within a major release for a
          minimum of 12 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: spec is the specification of the desired behavior of the
              dnsRecord.
            properties:
              dnsManagementPolicy:
                default: Managed
                description: "dnsManagementPolicy denotes the current policy applied
                  on the DNS record. Records that have policy set as \"Unmanaged\"
                  are ignored by the ingress operator.  This means that the DNS record
                  on the cloud provider is not managed by the operator, and the \"Published\"
                  status condition will be updated to \"Unknown\" status, since it
                  is externally managed. Any existing record on the cloud provider
                  can be deleted at the discretion of the cluster admin. \n This field
                  defaults to Managed. Valid values are \"Managed\" and \"Unmanaged\"."
                enum:
                - Managed
                - Unmanaged
                type: string
              dnsName:
                description: dnsName is the hostname of the DNS record
                minLength: 1
                type: string
              recordTTL:
                description: recordTTL is the record TTL in seconds. If zero, the
                  default is 30. RecordTTL will not be used in AWS regions Alias targets,
                  but will be used in CNAME targets, per AWS API contract.
                format: int64
                minimum: 0
                type: integer
              recordType:
                description: recordType is the DNS record type. For example, "A" or
                  "CNAME".
                enum:
                - CNAME
                - A
                type: string
              targets:
                description: targets are record targets.
                items:
                  type: string
                minItems: 1
                type: array
            required:
            - dnsManagementPolicy
            - dnsName
            - recordTTL
            - recordType
            - targets
            type: object
          status:
            description: status is the most recently observed status of the dnsRecord.
            properties:
              observedGeneration:
                description: observedGeneration is the most recently observed generation
                  of the DNSRecord.  When the DNSRecord is updated, the controller
                  updates the corresponding record in each managed zone.  If an update
                  for a particular zone fails, that failure is recorded in the status
                  condition for the zone so that the controller can determine that
                  it needs to retry the update for that specific zone.
                format: int64
                type: integer
              zones:
                description: zones are the status of the record in each zone.
                items:
                  description: DNSZoneStatus is the status of a record within a specific
                    zone.
                  properties:
                    conditions:
                      description: "conditions are any conditions associated with
                        the record in the zone. \n If publishing the record succeeds,
                        the \"Published\" condition will be set with status \"True\"
                        and upon failure it will be set to \"False\" along with the
                        reason and message describing the cause of the failure."
                      items:
                        description: DNSZoneCondition is just the standard condition
                          fields.
                        properties:
                          lastTransitionTime:
                            format: date-time
                            type: string
                          message:
                            type: string
                          reason:
                            type: string
                          status:
                            minLength: 1
                            type: string
                          type:
                            minLength: 1
                            type: string
                        required:
                        - status
                        - type
                        type: object
                      type: array
                    dnsZone:
                      description: dnsZone is the zone where the record is published.
                      properties:
                        id:
                          description: "id is the identifier that can be used to find
                            the DNS hosted zone. \n on AWS zone can be fetched using
                            `ID` as id in [1] on Azure zone can be fetched using `ID`
                            as a pre-determined name in [2], on GCP zone can be fetched
                            using `ID` as a pre-determined name in [3]. \n [1]: https://docs.aws.amazon.com/cli/latest/reference/route53/get-hosted-zone.html#options
                            [2]: https://docs.microsoft.com/en-us/cli/azure/network/dns/zone?view=azure-cli-latest#az-network-dns-zone-show
                            [3]: https://cloud.google.com/dns/docs/reference/v1/managedZones/get"
                          type: string
                        tags:
                          additionalProperties:
                            type: string
                          description: "tags can be used to query the DNS hosted zone.
                            \n on AWS, resourcegroupstaggingapi [1] can be used to
                            fetch a zone using `Tags` as tag-filters, \n [1]: https://docs.aws.amazon.com/cli/latest/reference/resourcegroupstaggingapi/get-resources.html#options"
                          type: object
                      type: object
                  type: object
                type: array
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: DNSRecord
    listKind: DNSRecordList
    plural: dnsrecords
    singular: dnsrecord
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:39Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:39Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
