---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/616
    api.openshift.io/merged-by-featuregates: "true"
    capability.openshift.io/name: Ingress
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:53:25Z"
  generation: 3
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
    manager: cluster-bootstrap
    operation: Update
    time: "2025-06-25T14:53:25Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:25Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:api.openshift.io/merged-by-featuregates: {}
          f:capability.openshift.io/name: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T12:08:53Z"
  name: ingresscontrollers.operator.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21340277"
  uid: 4567154d-4877-4a20-b754-ce2dec5ca8b0
spec:
  conversion:
    strategy: None
  group: operator.openshift.io
  names:
    kind: IngressController
    listKind: IngressControllerList
    plural: ingresscontrollers
    singular: ingresscontroller
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "IngressController describes a managed ingress controller for
          the cluster. The controller can service OpenShift Route and Kubernetes Ingress
          resources. \n When an IngressController is created, a new ingress controller
          deployment is created to allow external traffic to reach the services that
          expose Ingress or Route resources. Updating this resource may lead to disruption
          for public facing network connections as a new ingress controller revision
          may be rolled out. \n https://kubernetes.io/docs/concepts/services-networking/ingress-controllers
          \n Whenever possible, sensible defaults for the platform are used. See each
          field for more details. \n Compatibility level 1: Stable within a major
          release for a minimum of 12 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: spec is the specification of the desired behavior of the
              IngressController.
            properties:
              clientTLS:
                description: clientTLS specifies settings for requesting and verifying
                  client certificates, which can be used to enable mutual TLS for
                  edge-terminated and reencrypt routes.
                properties:
                  allowedSubjectPatterns:
                    description: allowedSubjectPatterns specifies a list of regular
                      expressions that should be matched against the distinguished
                      name on a valid client certificate to filter requests.  The
                      regular expressions must use PCRE syntax.  If this list is empty,
                      no filtering is performed.  If the list is nonempty, then at
                      least one pattern must match a client certificate's distinguished
                      name or else the ingress controller rejects the certificate
                      and denies the connection.
                    items:
                      type: string
                    type: array
                    x-kubernetes-list-type: atomic
                  clientCA:
                    description: clientCA specifies a configmap containing the PEM-encoded
                      CA certificate bundle that should be used to verify a client's
                      certificate.  The administrator must create this configmap in
                      the openshift-config namespace.
                    properties:
                      name:
                        description: name is the metadata.name of the referenced config
                          map
                        type: string
                    required:
                    - name
                    type: object
                  clientCertificatePolicy:
                    description: "clientCertificatePolicy specifies whether the ingress
                      controller requires clients to provide certificates.  This field
                      accepts the values \"Required\" or \"Optional\". \n Note that
                      the ingress controller only checks client certificates for edge-terminated
                      and reencrypt TLS routes; it cannot check certificates for cleartext
                      HTTP or passthrough TLS routes."
                    enum:
                    - ""
                    - Required
                    - Optional
                    type: string
                required:
                - clientCA
                - clientCertificatePolicy
                type: object
              defaultCertificate:
                description: "defaultCertificate is a reference to a secret containing
                  the default certificate served by the ingress controller. When Routes
                  don't specify their own certificate, defaultCertificate is used.
                  \n The secret must contain the following keys and data: \n tls.crt:
                  certificate file contents tls.key: key file contents \n If unset,
                  a wildcard certificate is automatically generated and used. The
                  certificate is valid for the ingress controller domain (and subdomains)
                  and the generated certificate's CA will be automatically integrated
                  with the cluster's trust store. \n If a wildcard certificate is
                  used and shared by multiple HTTP/2 enabled routes (which implies
                  ALPN) then clients (i.e., notably browsers) are at liberty to reuse
                  open connections. This means a client can reuse a connection to
                  another route and that is likely to fail. This behaviour is generally
                  known as connection coalescing. \n The in-use certificate (whether
                  generated or user-specified) will be automatically integrated with
                  OpenShift's built-in OAuth server."
                properties:
                  name:
                    default: ""
                    description: 'Name of the referent. This field is effectively
                      required, but due to backwards compatibility is allowed to be
                      empty. Instances of this type with an empty value here are almost
                      certainly wrong. TODO: Add other useful fields. apiVersion,
                      kind, uid? More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                      TODO: Drop `kubebuilder:default` when controller-gen doesn''t
                      need it https://github.com/kubernetes-sigs/kubebuilder/issues/3896.'
                    type: string
                type: object
                x-kubernetes-map-type: atomic
              domain:
                description: "domain is a DNS name serviced by the ingress controller
                  and is used to configure multiple features: \n * For the LoadBalancerService
                  endpoint publishing strategy, domain is used to configure DNS records.
                  See endpointPublishingStrategy. \n * When using a generated default
                  certificate, the certificate will be valid for domain and its subdomains.
                  See defaultCertificate. \n * The value is published to individual
                  Route statuses so that end-users know where to target external DNS
                  records. \n domain must be unique among all IngressControllers,
                  and cannot be updated. \n If empty, defaults to ingress.config.openshift.io/cluster
                  .spec.domain."
                type: string
              endpointPublishingStrategy:
                description: "endpointPublishingStrategy is used to publish the ingress
                  controller endpoints to other networks, enable load balancer integrations,
                  etc. \n If unset, the default is based on infrastructure.config.openshift.io/cluster
                  .status.platform: \n AWS:          LoadBalancerService (with External
                  scope) Azure:        LoadBalancerService (with External scope) GCP:
                  \         LoadBalancerService (with External scope) IBMCloud:     LoadBalancerService
                  (with External scope) AlibabaCloud: LoadBalancerService (with External
                  scope) Libvirt:      HostNetwork \n Any other platform types (including
                  None) default to HostNetwork. \n endpointPublishingStrategy cannot
                  be updated."
                properties:
                  hostNetwork:
                    description: hostNetwork holds parameters for the HostNetwork
                      endpoint publishing strategy. Present only if type is HostNetwork.
                    properties:
                      httpPort:
                        default: 80
                        description: httpPort is the port on the host which should
                          be used to listen for HTTP requests. This field should be
                          set when port 80 is already in use. The value should not
                          coincide with the NodePort range of the cluster. When the
                          value is 0 or is not specified it defaults to 80.
                        format: int32
                        maximum: 65535
                        minimum: 0
                        type: integer
                      httpsPort:
                        default: 443
                        description: httpsPort is the port on the host which should
                          be used to listen for HTTPS requests. This field should
                          be set when port 443 is already in use. The value should
                          not coincide with the NodePort range of the cluster. When
                          the value is 0 or is not specified it defaults to 443.
                        format: int32
                        maximum: 65535
                        minimum: 0
                        type: integer
                      protocol:
                        description: "protocol specifies whether the IngressController
                          expects incoming connections to use plain TCP or whether
                          the IngressController expects PROXY protocol. \n PROXY protocol
                          can be used with load balancers that support it to communicate
                          the source addresses of client connections when forwarding
                          those connections to the IngressController.  Using PROXY
                          protocol enables the IngressController to report those source
                          addresses instead of reporting the load balancer's address
                          in HTTP headers and logs.  Note that enabling PROXY protocol
                          on the IngressController will cause connections to fail
                          if you are not using a load balancer that uses PROXY protocol
                          to forward connections to the IngressController.  See http://www.haproxy.org/download/2.2/doc/proxy-protocol.txt
                          for information about PROXY protocol. \n The following values
                          are valid for this field: \n * The empty string. * \"TCP\".
                          * \"PROXY\". \n The empty string specifies the default,
                          which is TCP without PROXY protocol.  Note that the default
                          is subject to change."
                        enum:
                        - ""
                        - TCP
                        - PROXY
                        type: string
                      statsPort:
                        default: 1936
                        description: statsPort is the port on the host where the stats
                          from the router are published. The value should not coincide
                          with the NodePort range of the cluster. If an external load
                          balancer is configured to forward connections to this IngressController,
                          the load balancer should use this port for health checks.
                          The load balancer can send HTTP probes on this port on a
                          given node, with the path /healthz/ready to determine if
                          the ingress controller is ready to receive traffic on the
                          node. For proper operation the load balancer must not forward
                          traffic to a node until the health check reports ready.
                          The load balancer should also stop forwarding requests within
                          a maximum of 45 seconds after /healthz/ready starts reporting
                          not-ready. Probing every 5 to 10 seconds, with a 5-second
                          timeout and with a threshold of two successful or failed
                          requests to become healthy or unhealthy respectively, are
                          well-tested values. When the value is 0 or is not specified
                          it defaults to 1936.
                        format: int32
                        maximum: 65535
                        minimum: 0
                        type: integer
                    type: object
                  loadBalancer:
                    description: loadBalancer holds parameters for the load balancer.
                      Present only if type is LoadBalancerService.
                    properties:
                      allowedSourceRanges:
                        description: "allowedSourceRanges specifies an allowlist of
                          IP address ranges to which access to the load balancer should
                          be restricted.  Each range must be specified using CIDR
                          notation (e.g. \"10.0.0.0/8\" or \"fd00::/8\"). If no range
                          is specified, \"0.0.0.0/0\" for IPv4 and \"::/0\" for IPv6
                          are used by default, which allows all source addresses.
                          \n To facilitate migration from earlier versions of OpenShift
                          that did not have the allowedSourceRanges field, you may
                          set the service.beta.kubernetes.io/load-balancer-source-ranges
                          annotation on the \"router-<ingresscontroller name>\" service
                          in the \"openshift-ingress\" namespace, and this annotation
                          will take effect if allowedSourceRanges is empty on OpenShift
                          4.12."
                        items:
                          description: CIDR is an IP address range in CIDR notation
                            (for example, "10.0.0.0/8" or "fd00::/8").
                          pattern: (^(([0-9]|[0-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[0-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])/([0-9]|[12][0-9]|3[0-2])$)|(^s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3}))|:)))(%.+)?s*(\/(12[0-8]|1[0-1][0-9]|[1-9][0-9]|[0-9]))$)
                          type: string
                        nullable: true
                        type: array
                        x-kubernetes-list-type: atomic
                      dnsManagementPolicy:
                        default: Managed
                        description: 'dnsManagementPolicy indicates if the lifecycle
                          of the wildcard DNS record associated with the load balancer
                          service will be managed by the ingress operator. It defaults
                          to Managed. Valid values are: Managed and Unmanaged.'
                        enum:
                        - Managed
                        - Unmanaged
                        type: string
                      providerParameters:
                        description: "providerParameters holds desired load balancer
                          information specific to the underlying infrastructure provider.
                          \n If empty, defaults will be applied. See specific providerParameters
                          fields for details about their defaults."
                        properties:
                          aws:
                            description: "aws provides configuration settings that
                              are specific to AWS load balancers. \n If empty, defaults
                              will be applied. See specific aws fields for details
                              about their defaults."
                            properties:
                              classicLoadBalancer:
                                description: classicLoadBalancerParameters holds configuration
                                  parameters for an AWS classic load balancer. Present
                                  only if type is Classic.
                                properties:
                                  connectionIdleTimeout:
                                    description: connectionIdleTimeout specifies the
                                      maximum time period that a connection may be
                                      idle before the load balancer closes the connection.  The
                                      value must be parseable as a time duration value;
                                      see <https://pkg.go.dev/time#ParseDuration>.  A
                                      nil or zero value means no opinion, in which
                                      case a default value is used.  The default value
                                      for this field is 60s.  This default is subject
                                      to change.
                                    format: duration
                                    type: string
                                  subnets:
                                    description: "subnets specifies the subnets to
                                      which the load balancer will attach. The subnets
                                      may be specified by either their ID or name.
                                      The total number of subnets is limited to 10.
                                      \n In order for the load balancer to be provisioned
                                      with subnets, each subnet must exist, each subnet
                                      must be from a different availability zone,
                                      and the load balancer service must be recreated
                                      to pick up new values. \n When omitted from
                                      the spec, the subnets will be auto-discovered
                                      for each availability zone. Auto-discovered
                                      subnets are not reported in the status of the
                                      IngressController object."
                                    properties:
                                      ids:
                                        description: ids specifies a list of AWS subnets
                                          by subnet ID. Subnet IDs must start with
                                          "subnet-", consist only of alphanumeric
                                          characters, must be exactly 24 characters
                                          long, must be unique, and the total number
                                          of subnets specified by ids and names must
                                          not exceed 10.
                                        items:
                                          description: AWSSubnetID is a reference
                                            to an AWS subnet ID.
                                          maxLength: 24
                                          minLength: 24
                                          pattern: ^subnet-[0-9A-Za-z]+$
                                          type: string
                                        maxItems: 10
                                        type: array
                                        x-kubernetes-list-type: atomic
                                        x-kubernetes-validations:
                                        - message: subnet ids cannot contain duplicates
                                          rule: self.all(x, self.exists_one(y, x ==
                                            y))
                                      names:
                                        description: names specifies a list of AWS
                                          subnets by subnet name. Subnet names must
                                          not start with "subnet-", must not include
                                          commas, must be under 256 characters in
                                          length, must be unique, and the total number
                                          of subnets specified by ids and names must
                                          not exceed 10.
                                        items:
                                          description: AWSSubnetName is a reference
                                            to an AWS subnet name.
                                          maxLength: 256
                                          minLength: 1
                                          type: string
                                          x-kubernetes-validations:
                                          - message: subnet name cannot contain a
                                              comma
                                            rule: '!self.contains('','')'
                                          - message: subnet name cannot start with
                                              'subnet-'
                                            rule: '!self.startsWith(''subnet-'')'
                                        maxItems: 10
                                        type: array
                                        x-kubernetes-list-type: atomic
                                        x-kubernetes-validations:
                                        - message: subnet names cannot contain duplicates
                                          rule: self.all(x, self.exists_one(y, x ==
                                            y))
                                    type: object
                                    x-kubernetes-validations:
                                    - message: the total number of subnets cannot
                                        exceed 10
                                      rule: 'has(self.ids) && has(self.names) ? size(self.ids
                                        + self.names) <= 10 : true'
                                    - message: must specify at least 1 subnet name
                                        or id
                                      rule: has(self.ids) && self.ids.size() > 0 ||
                                        has(self.names) && self.names.size() > 0
                                type: object
                              networkLoadBalancer:
                                description: networkLoadBalancerParameters holds configuration
                                  parameters for an AWS network load balancer. Present
                                  only if type is NLB.
                                properties:
                                  eipAllocations:
                                    description: "eipAllocations is a list of IDs
                                      for Elastic IP (EIP) addresses that are assigned
                                      to the Network Load Balancer. The following
                                      restrictions apply: \n eipAllocations can only
                                      be used with external scope, not internal. An
                                      EIP can be allocated to only a single IngressController.
                                      The number of EIP allocations must match the
                                      number of subnets that are used for the load
                                      balancer. Each EIP allocation must be unique.
                                      A maximum of 10 EIP allocations are permitted.
                                      \n See https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/elastic-ip-addresses-eip.html
                                      for general information about configuration,
                                      characteristics, and limitations of Elastic
                                      IP addresses."
                                    items:
                                      description: EIPAllocation is an ID for an Elastic
                                        IP (EIP) address that can be allocated to
                                        an ELB in the AWS environment. Values must
                                        begin with `eipalloc-` followed by exactly
                                        17 hexadecimal (`[0-9a-fA-F]`) characters.
                                      maxLength: 26
                                      minLength: 26
                                      type: string
                                      x-kubernetes-validations:
                                      - message: eipAllocations should start with
                                          'eipalloc-'
                                        rule: self.startsWith('eipalloc-')
                                      - message: eipAllocations must be 'eipalloc-'
                                          followed by exactly 17 hexadecimal characters
                                          (0-9, a-f, A-F)
                                        rule: self.split("-", 2)[1].matches('[0-9a-fA-F]{17}$')
                                    maxItems: 10
                                    type: array
                                    x-kubernetes-list-type: atomic
                                    x-kubernetes-validations:
                                    - message: eipAllocations cannot contain duplicates
                                      rule: self.all(x, self.exists_one(y, x == y))
                                  subnets:
                                    description: "subnets specifies the subnets to
                                      which the load balancer will attach. The subnets
                                      may be specified by either their ID or name.
                                      The total number of subnets is limited to 10.
                                      \n In order for the load balancer to be provisioned
                                      with subnets, each subnet must exist, each subnet
                                      must be from a different availability zone,
                                      and the load balancer service must be recreated
                                      to pick up new values. \n When omitted from
                                      the spec, the subnets will be auto-discovered
                                      for each availability zone. Auto-discovered
                                      subnets are not reported in the status of the
                                      IngressController object."
                                    properties:
                                      ids:
                                        description: ids specifies a list of AWS subnets
                                          by subnet ID. Subnet IDs must start with
                                          "subnet-", consist only of alphanumeric
                                          characters, must be exactly 24 characters
                                          long, must be unique, and the total number
                                          of subnets specified by ids and names must
                                          not exceed 10.
                                        items:
                                          description: AWSSubnetID is a reference
                                            to an AWS subnet ID.
                                          maxLength: 24
                                          minLength: 24
                                          pattern: ^subnet-[0-9A-Za-z]+$
                                          type: string
                                        maxItems: 10
                                        type: array
                                        x-kubernetes-list-type: atomic
                                        x-kubernetes-validations:
                                        - message: subnet ids cannot contain duplicates
                                          rule: self.all(x, self.exists_one(y, x ==
                                            y))
                                      names:
                                        description: names specifies a list of AWS
                                          subnets by subnet name. Subnet names must
                                          not start with "subnet-", must not include
                                          commas, must be under 256 characters in
                                          length, must be unique, and the total number
                                          of subnets specified by ids and names must
                                          not exceed 10.
                                        items:
                                          description: AWSSubnetName is a reference
                                            to an AWS subnet name.
                                          maxLength: 256
                                          minLength: 1
                                          type: string
                                          x-kubernetes-validations:
                                          - message: subnet name cannot contain a
                                              comma
                                            rule: '!self.contains('','')'
                                          - message: subnet name cannot start with
                                              'subnet-'
                                            rule: '!self.startsWith(''subnet-'')'
                                        maxItems: 10
                                        type: array
                                        x-kubernetes-list-type: atomic
                                        x-kubernetes-validations:
                                        - message: subnet names cannot contain duplicates
                                          rule: self.all(x, self.exists_one(y, x ==
                                            y))
                                    type: object
                                    x-kubernetes-validations:
                                    - message: the total number of subnets cannot
                                        exceed 10
                                      rule: 'has(self.ids) && has(self.names) ? size(self.ids
                                        + self.names) <= 10 : true'
                                    - message: must specify at least 1 subnet name
                                        or id
                                      rule: has(self.ids) && self.ids.size() > 0 ||
                                        has(self.names) && self.names.size() > 0
                                type: object
                                x-kubernetes-validations:
                                - message: number of subnets must be equal to number
                                    of eipAllocations
                                  rule: 'has(self.subnets) && has(self.subnets.ids)
                                    && has(self.subnets.names) && has(self.eipAllocations)
                                    ? size(self.subnets.ids + self.subnets.names)
                                    == size(self.eipAllocations) : true'
                                - message: number of subnets must be equal to number
                                    of eipAllocations
                                  rule: 'has(self.subnets) && has(self.subnets.ids)
                                    && !has(self.subnets.names) && has(self.eipAllocations)
                                    ? size(self.subnets.ids) == size(self.eipAllocations)
                                    : true'
                                - message: number of subnets must be equal to number
                                    of eipAllocations
                                  rule: 'has(self.subnets) && has(self.subnets.names)
                                    && !has(self.subnets.ids) && has(self.eipAllocations)
                                    ? size(self.subnets.names) == size(self.eipAllocations)
                                    : true'
                              type:
                                description: "type is the type of AWS load balancer
                                  to instantiate for an ingresscontroller. \n Valid
                                  values are: \n * \"Classic\": A Classic Load Balancer
                                  that makes routing decisions at either the transport
                                  layer (TCP/SSL) or the application layer (HTTP/HTTPS).
                                  See the following for additional details: \n https://docs.aws.amazon.com/AmazonECS/latest/developerguide/load-balancer-types.html#clb
                                  \n * \"NLB\": A Network Load Balancer that makes
                                  routing decisions at the transport layer (TCP/SSL).
                                  See the following for additional details: \n https://docs.aws.amazon.com/AmazonECS/latest/developerguide/load-balancer-types.html#nlb"
                                enum:
                                - Classic
                                - NLB
                                type: string
                            required:
                            - type
                            type: object
                          gcp:
                            description: "gcp provides configuration settings that
                              are specific to GCP load balancers. \n If empty, defaults
                              will be applied. See specific gcp fields for details
                              about their defaults."
                            properties:
                              clientAccess:
                                description: "clientAccess describes how client access
                                  is restricted for internal load balancers. \n Valid
                                  values are: * \"Global\": Specifying an internal
                                  load balancer with Global client access allows clients
                                  from any region within the VPC to communicate with
                                  the load balancer. \n https://cloud.google.com/kubernetes-engine/docs/how-to/internal-load-balancing#global_access
                                  \n * \"Local\": Specifying an internal load balancer
                                  with Local client access means only clients within
                                  the same region (and VPC) as the GCP load balancer
                                  can communicate with the load balancer. Note that
                                  this is the default behavior. \n https://cloud.google.com/load-balancing/docs/internal#client_access"
                                enum:
                                - Global
                                - Local
                                type: string
                            type: object
                          ibm:
                            description: "ibm provides configuration settings that
                              are specific to IBM Cloud load balancers. \n If empty,
                              defaults will be applied. See specific ibm fields for
                              details about their defaults."
                            properties:
                              protocol:
                                description: "protocol specifies whether the load
                                  balancer uses PROXY protocol to forward connections
                                  to the IngressController. See \"service.kubernetes.io/ibm-load-balancer-cloud-provider-enable-features:
                                  \"proxy-protocol\"\" at https://cloud.ibm.com/docs/containers?topic=containers-vpc-lbaas\"
                                  \n PROXY protocol can be used with load balancers
                                  that support it to communicate the source addresses
                                  of client connections when forwarding those connections
                                  to the IngressController.  Using PROXY protocol
                                  enables the IngressController to report those source
                                  addresses instead of reporting the load balancer's
                                  address in HTTP headers and logs.  Note that enabling
                                  PROXY protocol on the IngressController will cause
                                  connections to fail if you are not using a load
                                  balancer that uses PROXY protocol to forward connections
                                  to the IngressController.  See http://www.haproxy.org/download/2.2/doc/proxy-protocol.txt
                                  for information about PROXY protocol. \n Valid values
                                  for protocol are TCP, PROXY and omitted. When omitted,
                                  this means no opinion and the platform is left to
                                  choose a reasonable default, which is subject to
                                  change over time. The current default is TCP, without
                                  the proxy protocol enabled."
                                enum:
                                - ""
                                - TCP
                                - PROXY
                                type: string
                            type: object
                          type:
                            description: type is the underlying infrastructure provider
                              for the load balancer. Allowed values are "AWS", "Azure",
                              "BareMetal", "GCP", "IBM", "Nutanix", "OpenStack", and
                              "VSphere".
                            enum:
                            - AWS
                            - Azure
                            - BareMetal
                            - GCP
                            - Nutanix
                            - OpenStack
                            - VSphere
                            - IBM
                            type: string
                        required:
                        - type
                        type: object
                      scope:
                        description: scope indicates the scope at which the load balancer
                          is exposed. Possible values are "External" and "Internal".
                        enum:
                        - Internal
                        - External
                        type: string
                    required:
                    - dnsManagementPolicy
                    - scope
                    type: object
                    x-kubernetes-validations:
                    - message: eipAllocations are forbidden when the scope is Internal.
                      rule: '!has(self.scope) || self.scope != ''Internal'' || !has(self.providerParameters)
                        || !has(self.providerParameters.aws) || !has(self.providerParameters.aws.networkLoadBalancer)
                        || !has(self.providerParameters.aws.networkLoadBalancer.eipAllocations)'
                  nodePort:
                    description: nodePort holds parameters for the NodePortService
                      endpoint publishing strategy. Present only if type is NodePortService.
                    properties:
                      protocol:
                        description: "protocol specifies whether the IngressController
                          expects incoming connections to use plain TCP or whether
                          the IngressController expects PROXY protocol. \n PROXY protocol
                          can be used with load balancers that support it to communicate
                          the source addresses of client connections when forwarding
                          those connections to the IngressController.  Using PROXY
                          protocol enables the IngressController to report those source
                          addresses instead of reporting the load balancer's address
                          in HTTP headers and logs.  Note that enabling PROXY protocol
                          on the IngressController will cause connections to fail
                          if you are not using a load balancer that uses PROXY protocol
                          to forward connections to the IngressController.  See http://www.haproxy.org/download/2.2/doc/proxy-protocol.txt
                          for information about PROXY protocol. \n The following values
                          are valid for this field: \n * The empty string. * \"TCP\".
                          * \"PROXY\". \n The empty string specifies the default,
                          which is TCP without PROXY protocol.  Note that the default
                          is subject to change."
                        enum:
                        - ""
                        - TCP
                        - PROXY
                        type: string
                    type: object
                  private:
                    description: private holds parameters for the Private endpoint
                      publishing strategy. Present only if type is Private.
                    properties:
                      protocol:
                        description: "protocol specifies whether the IngressController
                          expects incoming connections to use plain TCP or whether
                          the IngressController expects PROXY protocol. \n PROXY protocol
                          can be used with load balancers that support it to communicate
                          the source addresses of client connections when forwarding
                          those connections to the IngressController.  Using PROXY
                          protocol enables the IngressController to report those source
                          addresses instead of reporting the load balancer's address
                          in HTTP headers and logs.  Note that enabling PROXY protocol
                          on the IngressController will cause connections to fail
                          if you are not using a load balancer that uses PROXY protocol
                          to forward connections to the IngressController.  See http://www.haproxy.org/download/2.2/doc/proxy-protocol.txt
                          for information about PROXY protocol. \n The following values
                          are valid for this field: \n * The empty string. * \"TCP\".
                          * \"PROXY\". \n The empty string specifies the default,
                          which is TCP without PROXY protocol.  Note that the default
                          is subject to change."
                        enum:
                        - ""
                        - TCP
                        - PROXY
                        type: string
                    type: object
                  type:
                    description: "type is the publishing strategy to use. Valid values
                      are: \n * LoadBalancerService \n Publishes the ingress controller
                      using a Kubernetes LoadBalancer Service. \n In this configuration,
                      the ingress controller deployment uses container networking.
                      A LoadBalancer Service is created to publish the deployment.
                      \n See: https://kubernetes.io/docs/concepts/services-networking/service/#loadbalancer
                      \n If domain is set, a wildcard DNS record will be managed to
                      point at the LoadBalancer Service's external name. DNS records
                      are managed only in DNS zones defined by dns.config.openshift.io/cluster
                      .spec.publicZone and .spec.privateZone. \n Wildcard DNS management
                      is currently supported only on the AWS, Azure, and GCP platforms.
                      \n * HostNetwork \n Publishes the ingress controller on node
                      ports where the ingress controller is deployed. \n In this configuration,
                      the ingress controller deployment uses host networking, bound
                      to node ports 80 and 443. The user is responsible for configuring
                      an external load balancer to publish the ingress controller
                      via the node ports. \n * Private \n Does not publish the ingress
                      controller. \n In this configuration, the ingress controller
                      deployment uses container networking, and is not explicitly
                      published. The user must manually publish the ingress controller.
                      \n * NodePortService \n Publishes the ingress controller using
                      a Kubernetes NodePort Service. \n In this configuration, the
                      ingress controller deployment uses container networking. A NodePort
                      Service is created to publish the deployment. The specific node
                      ports are dynamically allocated by OpenShift; however, to support
                      static port allocations, user changes to the node port field
                      of the managed NodePort Service will preserved."
                    enum:
                    - LoadBalancerService
                    - HostNetwork
                    - Private
                    - NodePortService
                    type: string
                required:
                - type
                type: object
              httpCompression:
                description: httpCompression defines a policy for HTTP traffic compression.
                  By default, there is no HTTP compression.
                properties:
                  mimeTypes:
                    description: "mimeTypes is a list of MIME types that should have
                      compression applied. This list can be empty, in which case the
                      ingress controller does not apply compression. \n Note: Not
                      all MIME types benefit from compression, but HAProxy will still
                      use resources to try to compress if instructed to.  Generally
                      speaking, text (html, css, js, etc.) formats benefit from compression,
                      but formats that are already compressed (image, audio, video,
                      etc.) benefit little in exchange for the time and cpu spent
                      on compressing again. See https://joehonton.medium.com/the-gzip-penalty-d31bd697f1a2"
                    items:
                      description: "CompressionMIMEType defines the format of a single
                        MIME type. E.g. \"text/css; charset=utf-8\", \"text/html\",
                        \"text/*\", \"image/svg+xml\", \"application/octet-stream\",
                        \"X-custom/customsub\", etc. \n The format should follow the
                        Content-Type definition in RFC 1341: Content-Type := type
                        \"/\" subtype *[\";\" parameter] - The type in Content-Type
                        can be one of: application, audio, image, message, multipart,
                        text, video, or a custom type preceded by \"X-\" and followed
                        by a token as defined below. - The token is a string of at
                        least one character, and not containing white space, control
                        characters, or any of the characters in the tspecials set.
                        - The tspecials set contains the characters ()<>@,;:\\\"/[]?.=
                        - The subtype in Content-Type is also a token. - The optional
                        parameter/s following the subtype are defined as: token \"=\"
                        (token / quoted-string) - The quoted-string, as defined in
                        RFC 822, is surrounded by double quotes and can contain white
                        space plus any character EXCEPT \\, \", and CR. It can also
                        contain any single ASCII character as long as it is escaped
                        by \\."
                      pattern: ^(?i)(x-[^][ ()\\<>@,;:"/?.=\x00-\x1F\x7F]+|application|audio|image|message|multipart|text|video)/[^][
                        ()\\<>@,;:"/?.=\x00-\x1F\x7F]+(; *[^][ ()\\<>@,;:"/?.=\x00-\x1F\x7F]+=([^][
                        ()\\<>@,;:"/?.=\x00-\x1F\x7F]+|"(\\[\x00-\x7F]|[^\x0D"\\])*"))*$
                      type: string
                    type: array
                    x-kubernetes-list-type: set
                type: object
              httpEmptyRequestsPolicy:
                default: Respond
                description: "httpEmptyRequestsPolicy describes how HTTP connections
                  should be handled if the connection times out before a request is
                  received. Allowed values for this field are \"Respond\" and \"Ignore\".
                  \ If the field is set to \"Respond\", the ingress controller sends
                  an HTTP 400 or 408 response, logs the connection (if access logging
                  is enabled), and counts the connection in the appropriate metrics.
                  \ If the field is set to \"Ignore\", the ingress controller closes
                  the connection without sending a response, logging the connection,
                  or incrementing metrics.  The default value is \"Respond\". \n Typically,
                  these connections come from load balancers' health probes or Web
                  browsers' speculative connections (\"preconnect\") and can be safely
                  ignored.  However, these requests may also be caused by network
                  errors, and so setting this field to \"Ignore\" may impede detection
                  and diagnosis of problems.  In addition, these requests may be caused
                  by port scans, in which case logging empty requests may aid in detecting
                  intrusion attempts."
                enum:
                - Respond
                - Ignore
                type: string
              httpErrorCodePages:
                description: httpErrorCodePages specifies a configmap with custom
                  error pages. The administrator must create this configmap in the
                  openshift-config namespace. This configmap should have keys in the
                  format "error-page-<error code>.http", where <error code> is an
                  HTTP error code. For example, "error-page-503.http" defines an error
                  page for HTTP 503 responses. Currently only error pages for 503
                  and 404 responses can be customized. Each value in the configmap
                  should be the full response, including HTTP headers. Eg- https://raw.githubusercontent.com/openshift/router/fadab45747a9b30cc3f0a4b41ad2871f95827a93/images/router/haproxy/conf/error-page-503.http
                  If this field is empty, the ingress controller uses the default
                  error pages.
                properties:
                  name:
                    description: name is the metadata.name of the referenced config
                      map
                    type: string
                required:
                - name
                type: object
              httpHeaders:
                description: "httpHeaders defines policy for HTTP headers. \n If this
                  field is empty, the default values are used."
                properties:
                  actions:
                    description: 'actions specifies options for modifying headers
                      and their values. Note that this option only applies to cleartext
                      HTTP connections and to secure HTTP connections for which the
                      ingress controller terminates encryption (that is, edge-terminated
                      or reencrypt connections).  Headers cannot be modified for TLS
                      passthrough connections. Setting the HSTS (`Strict-Transport-Security`)
                      header is not supported via actions. `Strict-Transport-Security`
                      may only be configured using the "haproxy.router.openshift.io/hsts_header"
                      route annotation, and only in accordance with the policy specified
                      in Ingress.Spec.RequiredHSTSPolicies. Any actions defined here
                      are applied after any actions related to the following other
                      fields: cache-control, spec.clientTLS, spec.httpHeaders.forwardedHeaderPolicy,
                      spec.httpHeaders.uniqueId, and spec.httpHeaders.headerNameCaseAdjustments.
                      In case of HTTP request headers, the actions specified in spec.httpHeaders.actions
                      on the Route will be executed after the actions specified in
                      the IngressController''s spec.httpHeaders.actions field. In
                      case of HTTP response headers, the actions specified in spec.httpHeaders.actions
                      on the IngressController will be executed after the actions
                      specified in the Route''s spec.httpHeaders.actions field. Headers
                      set using this API cannot be captured for use in access logs.
                      The following header names are reserved and may not be modified
                      via this API: Strict-Transport-Security, Proxy, Host, Cookie,
                      Set-Cookie. Note that the total size of all net added headers
                      *after* interpolating dynamic values must not exceed the value
                      of spec.tuningOptions.headerBufferMaxRewriteBytes on the IngressController.
                      Please refer to the documentation for that API field for more
                      details.'
                    properties:
                      request:
                        description: 'request is a list of HTTP request headers to
                          modify. Actions defined here will modify the request headers
                          of all requests passing through an ingress controller. These
                          actions are applied to all Routes i.e. for all connections
                          handled by the ingress controller defined within a cluster.
                          IngressController actions for request headers will be executed
                          before Route actions. Currently, actions may define to either
                          `Set` or `Delete` headers values. Actions are applied in
                          sequence as defined in this list. A maximum of 20 request
                          header actions may be configured. Sample fetchers allowed
                          are "req.hdr" and "ssl_c_der". Converters allowed are "lower"
                          and "base64". Example header values: "%[req.hdr(X-target),lower]",
                          "%{+Q}[ssl_c_der,base64]".'
                        items:
                          description: IngressControllerHTTPHeader specifies configuration
                            for setting or deleting an HTTP header.
                          properties:
                            action:
                              description: action specifies actions to perform on
                                headers, such as setting or deleting headers.
                              properties:
                                set:
                                  description: set specifies how the HTTP header should
                                    be set. This field is required when type is Set
                                    and forbidden otherwise.
                                  properties:
                                    value:
                                      description: value specifies a header value.
                                        Dynamic values can be added. The value will
                                        be interpreted as an HAProxy format string
                                        as defined in http://cbonte.github.io/haproxy-dconv/2.6/configuration.html#8.2.6  and
                                        may use HAProxy's %[] syntax and otherwise
                                        must be a valid HTTP header value as defined
                                        in https://datatracker.ietf.org/doc/html/rfc7230#section-3.2.
                                        The value of this field must be no more than
                                        16384 characters in length. Note that the
                                        total size of all net added headers *after*
                                        interpolating dynamic values must not exceed
                                        the value of spec.tuningOptions.headerBufferMaxRewriteBytes
                                        on the IngressController.
                                      maxLength: 16384
                                      minLength: 1
                                      type: string
                                  required:
                                  - value
                                  type: object
                                type:
                                  description: type defines the type of the action
                                    to be applied on the header. Possible values are
                                    Set or Delete. Set allows you to set HTTP request
                                    and response headers. Delete allows you to delete
                                    HTTP request and response headers.
                                  enum:
                                  - Set
                                  - Delete
                                  type: string
                              required:
                              - type
                              type: object
                              x-kubernetes-validations:
                              - message: set is required when type is Set, and forbidden
                                  otherwise
                                rule: 'has(self.type) && self.type == ''Set'' ?  has(self.set)
                                  : !has(self.set)'
                            name:
                              description: 'name specifies the name of a header on
                                which to perform an action. Its value must be a valid
                                HTTP header name as defined in RFC 2616 section 4.2.
                                The name must consist only of alphanumeric and the
                                following special characters, "-!#$%&''*+.^_`". The
                                following header names are reserved and may not be
                                modified via this API: Strict-Transport-Security,
                                Proxy, Host, Cookie, Set-Cookie. It must be no more
                                than 255 characters in length. Header name must be
                                unique.'
                              maxLength: 255
                              minLength: 1
                              pattern: ^[-!#$%&'*+.0-9A-Z^_`a-z|~]+$
                              type: string
                              x-kubernetes-validations:
                              - message: strict-transport-security header may not
                                  be modified via header actions
                                rule: self.lowerAscii() != 'strict-transport-security'
                              - message: proxy header may not be modified via header
                                  actions
                                rule: self.lowerAscii() != 'proxy'
                              - message: host header may not be modified via header
                                  actions
                                rule: self.lowerAscii() != 'host'
                              - message: cookie header may not be modified via header
                                  actions
                                rule: self.lowerAscii() != 'cookie'
                              - message: set-cookie header may not be modified via
                                  header actions
                                rule: self.lowerAscii() != 'set-cookie'
                          required:
                          - action
                          - name
                          type: object
                        maxItems: 20
                        type: array
                        x-kubernetes-list-map-keys:
                        - name
                        x-kubernetes-list-type: map
                        x-kubernetes-validations:
                        - message: Either the header value provided is not in correct
                            format or the sample fetcher/converter specified is not
                            allowed. The dynamic header value will be interpreted
                            as an HAProxy format string as defined in http://cbonte.github.io/haproxy-dconv/2.6/configuration.html#8.2.6
                            and may use HAProxy's %[] syntax and otherwise must be
                            a valid HTTP header value as defined in https://datatracker.ietf.org/doc/html/rfc7230#section-3.2.
                            Sample fetchers allowed are req.hdr, ssl_c_der. Converters
                            allowed are lower, base64.
                          rule: self.all(key, key.action.type == "Delete" || (has(key.action.set)
                            && key.action.set.value.matches('^(?:%(?:%|(?:\\{[-+]?[QXE](?:,[-+]?[QXE])*\\})?\\[(?:req\\.hdr\\([0-9A-Za-z-]+\\)|ssl_c_der)(?:,(?:lower|base64))*\\])|[^%[:cntrl:]])+$')))
                      response:
                        description: 'response is a list of HTTP response headers
                          to modify. Actions defined here will modify the response
                          headers of all requests passing through an ingress controller.
                          These actions are applied to all Routes i.e. for all connections
                          handled by the ingress controller defined within a cluster.
                          IngressController actions for response headers will be executed
                          after Route actions. Currently, actions may define to either
                          `Set` or `Delete` headers values. Actions are applied in
                          sequence as defined in this list. A maximum of 20 response
                          header actions may be configured. Sample fetchers allowed
                          are "res.hdr" and "ssl_c_der". Converters allowed are "lower"
                          and "base64". Example header values: "%[res.hdr(X-target),lower]",
                          "%{+Q}[ssl_c_der,base64]".'
                        items:
                          description: IngressControllerHTTPHeader specifies configuration
                            for setting or deleting an HTTP header.
                          properties:
                            action:
                              description: action specifies actions to perform on
                                headers, such as setting or deleting headers.
                              properties:
                                set:
                                  description: set specifies how the HTTP header should
                                    be set. This field is required when type is Set
                                    and forbidden otherwise.
                                  properties:
                                    value:
                                      description: value specifies a header value.
                                        Dynamic values can be added. The value will
                                        be interpreted as an HAProxy format string
                                        as defined in http://cbonte.github.io/haproxy-dconv/2.6/configuration.html#8.2.6  and
                                        may use HAProxy's %[] syntax and otherwise
                                        must be a valid HTTP header value as defined
                                        in https://datatracker.ietf.org/doc/html/rfc7230#section-3.2.
                                        The value of this field must be no more than
                                        16384 characters in length. Note that the
                                        total size of all net added headers *after*
                                        interpolating dynamic values must not exceed
                                        the value of spec.tuningOptions.headerBufferMaxRewriteBytes
                                        on the IngressController.
                                      maxLength: 16384
                                      minLength: 1
                                      type: string
                                  required:
                                  - value
                                  type: object
                                type:
                                  description: type defines the type of the action
                                    to be applied on the header. Possible values are
                                    Set or Delete. Set allows you to set HTTP request
                                    and response headers. Delete allows you to delete
                                    HTTP request and response headers.
                                  enum:
                                  - Set
                                  - Delete
                                  type: string
                              required:
                              - type
                              type: object
                              x-kubernetes-validations:
                              - message: set is required when type is Set, and forbidden
                                  otherwise
                                rule: 'has(self.type) && self.type == ''Set'' ?  has(self.set)
                                  : !has(self.set)'
                            name:
                              description: 'name specifies the name of a header on
                                which to perform an action. Its value must be a valid
                                HTTP header name as defined in RFC 2616 section 4.2.
                                The name must consist only of alphanumeric and the
                                following special characters, "-!#$%&''*+.^_`". The
                                following header names are reserved and may not be
                                modified via this API: Strict-Transport-Security,
                                Proxy, Host, Cookie, Set-Cookie. It must be no more
                                than 255 characters in length. Header name must be
                                unique.'
                              maxLength: 255
                              minLength: 1
                              pattern: ^[-!#$%&'*+.0-9A-Z^_`a-z|~]+$
                              type: string
                              x-kubernetes-validations:
                              - message: strict-transport-security header may not
                                  be modified via header actions
                                rule: self.lowerAscii() != 'strict-transport-security'
                              - message: proxy header may not be modified via header
                                  actions
                                rule: self.lowerAscii() != 'proxy'
                              - message: host header may not be modified via header
                                  actions
                                rule: self.lowerAscii() != 'host'
                              - message: cookie header may not be modified via header
                                  actions
                                rule: self.lowerAscii() != 'cookie'
                              - message: set-cookie header may not be modified via
                                  header actions
                                rule: self.lowerAscii() != 'set-cookie'
                          required:
                          - action
                          - name
                          type: object
                        maxItems: 20
                        type: array
                        x-kubernetes-list-map-keys:
                        - name
                        x-kubernetes-list-type: map
                        x-kubernetes-validations:
                        - message: Either the header value provided is not in correct
                            format or the sample fetcher/converter specified is not
                            allowed. The dynamic header value will be interpreted
                            as an HAProxy format string as defined in http://cbonte.github.io/haproxy-dconv/2.6/configuration.html#8.2.6
                            and may use HAProxy's %[] syntax and otherwise must be
                            a valid HTTP header value as defined in https://datatracker.ietf.org/doc/html/rfc7230#section-3.2.
                            Sample fetchers allowed are res.hdr, ssl_c_der. Converters
                            allowed are lower, base64.
                          rule: self.all(key, key.action.type == "Delete" || (has(key.action.set)
                            && key.action.set.value.matches('^(?:%(?:%|(?:\\{[-+]?[QXE](?:,[-+]?[QXE])*\\})?\\[(?:res\\.hdr\\([0-9A-Za-z-]+\\)|ssl_c_der)(?:,(?:lower|base64))*\\])|[^%[:cntrl:]])+$')))
                    type: object
                  forwardedHeaderPolicy:
                    description: "forwardedHeaderPolicy specifies when and how the
                      IngressController sets the Forwarded, X-Forwarded-For, X-Forwarded-Host,
                      X-Forwarded-Port, X-Forwarded-Proto, and X-Forwarded-Proto-Version
                      HTTP headers.  The value may be one of the following: \n * \"Append\",
                      which specifies that the IngressController appends the headers,
                      preserving existing headers. \n * \"Replace\", which specifies
                      that the IngressController sets the headers, replacing any existing
                      Forwarded or X-Forwarded-* headers. \n * \"IfNone\", which specifies
                      that the IngressController sets the headers if they are not
                      already set. \n * \"Never\", which specifies that the IngressController
                      never sets the headers, preserving any existing headers. \n
                      By default, the policy is \"Append\"."
                    enum:
                    - Append
                    - Replace
                    - IfNone
                    - Never
                    type: string
                  headerNameCaseAdjustments:
                    description: "headerNameCaseAdjustments specifies case adjustments
                      that can be applied to HTTP header names.  Each adjustment is
                      specified as an HTTP header name with the desired capitalization.
                      \ For example, specifying \"X-Forwarded-For\" indicates that
                      the \"x-forwarded-for\" HTTP header should be adjusted to have
                      the specified capitalization. \n These adjustments are only
                      applied to cleartext, edge-terminated, and re-encrypt routes,
                      and only when using HTTP/1. \n For request headers, these adjustments
                      are applied only for routes that have the haproxy.router.openshift.io/h1-adjust-case=true
                      annotation.  For response headers, these adjustments are applied
                      to all HTTP responses. \n If this field is empty, no request
                      headers are adjusted."
                    items:
                      description: IngressControllerHTTPHeaderNameCaseAdjustment is
                        the name of an HTTP header (for example, "X-Forwarded-For")
                        in the desired capitalization.  The value must be a valid
                        HTTP header name as defined in RFC 2616 section 4.2.
                      maxLength: 1024
                      minLength: 0
                      pattern: ^$|^[-!#$%&'*+.0-9A-Z^_`a-z|~]+$
                      type: string
                    nullable: true
                    type: array
                    x-kubernetes-list-type: atomic
                  uniqueId:
                    description: "uniqueId describes configuration for a custom HTTP
                      header that the ingress controller should inject into incoming
                      HTTP requests. Typically, this header is configured to have
                      a value that is unique to the HTTP request.  The header can
                      be used by applications or included in access logs to facilitate
                      tracing individual HTTP requests. \n If this field is empty,
                      no such header is injected into requests."
                    properties:
                      format:
                        description: 'format specifies the format for the injected
                          HTTP header''s value. This field has no effect unless name
                          is specified.  For the HAProxy-based ingress controller
                          implementation, this format uses the same syntax as the
                          HTTP log format.  If the field is empty, the default value
                          is "%{+X}o\\ %ci:%cp_%fi:%fp_%Ts_%rt:%pid"; see the corresponding
                          HAProxy documentation: http://cbonte.github.io/haproxy-dconv/2.0/configuration.html#8.2.3'
                        maxLength: 1024
                        minLength: 0
                        pattern: ^(%(%|(\{[-+]?[QXE](,[-+]?[QXE])*\})?([A-Za-z]+|\[[.0-9A-Z_a-z]+(\([^)]+\))?(,[.0-9A-Z_a-z]+(\([^)]+\))?)*\]))|[^%[:cntrl:]])*$
                        type: string
                      name:
                        description: name specifies the name of the HTTP header (for
                          example, "unique-id") that the ingress controller should
                          inject into HTTP requests.  The field's value must be a
                          valid HTTP header name as defined in RFC 2616 section 4.2.  If
                          the field is empty, no header is injected.
                        maxLength: 1024
                        minLength: 0
                        pattern: ^$|^[-!#$%&'*+.0-9A-Z^_`a-z|~]+$
                        type: string
                    type: object
                type: object
              idleConnectionTerminationPolicy:
                default: Deferred
                description: "idleConnectionTerminationPolicy maps directly to HAProxy's
                  idle-close-on-response option and controls whether HAProxy keeps
                  idle frontend connections open during a soft stop (router reload).
                  \n Allowed values for this field are \"Immediate\" and \"Deferred\".
                  The default value is \"Deferred\". \n When set to \"Immediate\",
                  idle connections are closed immediately during router reloads. This
                  ensures immediate propagation of route changes but may impact clients
                  sensitive to connection resets. \n When set to \"Deferred\", HAProxy
                  will maintain idle connections during a soft reload instead of closing
                  them immediately. These connections remain open until any of the
                  following occurs: \n - A new request is received on the connection,
                  in which case HAProxy handles it in the old process and closes the
                  connection after sending the response. \n - HAProxy's `timeout http-keep-alive`
                  duration expires (300 seconds in OpenShift's configuration, not
                  configurable). \n - The client's keep-alive timeout expires, causing
                  the client to close the connection. \n Setting Deferred can help
                  prevent errors in clients or load balancers that do not properly
                  handle connection resets. Additionally, this option allows you to
                  retain the pre-2.4 HAProxy behaviour: in HAProxy version 2.2 (OpenShift
                  versions < 4.14), maintaining idle connections during a soft reload
                  was the default behaviour, but starting with HAProxy 2.4, the default
                  changed to closing idle connections immediately. \n Important Consideration:
                  \n - Using Deferred will result in temporary inconsistencies for
                  the first request on each persistent connection after a route update
                  and router reload. This request will be processed by the old HAProxy
                  process using its old configuration. Subsequent requests will use
                  the updated configuration. \n Operational Considerations: \n - Keeping
                  idle connections open during reloads may lead to an accumulation
                  of old HAProxy processes if connections remain idle for extended
                  periods, especially in environments where frequent reloads occur.
                  \n - Consider monitoring the number of HAProxy processes in the
                  router pods when Deferred is set. \n - You may need to enable or
                  adjust the `ingress.operator.openshift.io/hard-stop-after` duration
                  (configured via an annotation on the IngressController resource)
                  in environments with frequent reloads to prevent resource exhaustion."
                enum:
                - Immediate
                - Deferred
                type: string
              logging:
                description: logging defines parameters for what should be logged
                  where.  If this field is empty, operational logs are enabled but
                  access logs are disabled.
                properties:
                  access:
                    description: "access describes how the client requests should
                      be logged. \n If this field is empty, access logging is disabled."
                    properties:
                      destination:
                        description: destination is where access logs go.
                        properties:
                          container:
                            description: container holds parameters for the Container
                              logging destination. Present only if type is Container.
                            properties:
                              maxLength:
                                default: 1024
                                description: "maxLength is the maximum length of the
                                  log message. \n Valid values are integers in the
                                  range 480 to 8192, inclusive. \n When omitted, the
                                  default value is 1024."
                                format: int32
                                maximum: 8192
                                minimum: 480
                                type: integer
                            type: object
                          syslog:
                            description: syslog holds parameters for a syslog endpoint.  Present
                              only if type is Syslog.
                            oneOf:
                            - properties:
                                address:
                                  format: ipv4
                            - properties:
                                address:
                                  format: ipv6
                            properties:
                              address:
                                description: address is the IP address of the syslog
                                  endpoint that receives log messages.
                                type: string
                              facility:
                                description: "facility specifies the syslog facility
                                  of log messages. \n If this field is empty, the
                                  facility is \"local1\"."
                                enum:
                                - kern
                                - user
                                - mail
                                - daemon
                                - auth
                                - syslog
                                - lpr
                                - news
                                - uucp
                                - cron
                                - auth2
                                - ftp
                                - ntp
                                - audit
                                - alert
                                - cron2
                                - local0
                                - local1
                                - local2
                                - local3
                                - local4
                                - local5
                                - local6
                                - local7
                                type: string
                              maxLength:
                                default: 1024
                                description: "maxLength is the maximum length of the
                                  log message. \n Valid values are integers in the
                                  range 480 to 4096, inclusive. \n When omitted, the
                                  default value is 1024."
                                format: int32
                                maximum: 4096
                                minimum: 480
                                type: integer
                              port:
                                description: port is the UDP port number of the syslog
                                  endpoint that receives log messages.
                                format: int32
                                maximum: 65535
                                minimum: 1
                                type: integer
                            required:
                            - address
                            - port
                            type: object
                          type:
                            description: "type is the type of destination for logs.
                              \ It must be one of the following: \n * Container \n
                              The ingress operator configures the sidecar container
                              named \"logs\" on the ingress controller pod and configures
                              the ingress controller to write logs to the sidecar.
                              \ The logs are then available as container logs.  The
                              expectation is that the administrator configures a custom
                              logging solution that reads logs from this sidecar.
                              \ Note that using container logs means that logs may
                              be dropped if the rate of logs exceeds the container
                              runtime's or the custom logging solution's capacity.
                              \n * Syslog \n Logs are sent to a syslog endpoint.  The
                              administrator must specify an endpoint that can receive
                              syslog messages.  The expectation is that the administrator
                              has configured a custom syslog instance."
                            enum:
                            - Container
                            - Syslog
                            type: string
                        required:
                        - type
                        type: object
                      httpCaptureCookies:
                        description: httpCaptureCookies specifies HTTP cookies that
                          should be captured in access logs.  If this field is empty,
                          no cookies are captured.
                        items:
                          description: IngressControllerCaptureHTTPCookie describes
                            an HTTP cookie that should be captured.
                          properties:
                            matchType:
                              description: matchType specifies the type of match to
                                be performed on the cookie name.  Allowed values are
                                "Exact" for an exact string match and "Prefix" for
                                a string prefix match.  If "Exact" is specified, a
                                name must be specified in the name field.  If "Prefix"
                                is provided, a prefix must be specified in the namePrefix
                                field.  For example, specifying matchType "Prefix"
                                and namePrefix "foo" will capture a cookie named "foo"
                                or "foobar" but not one named "bar".  The first matching
                                cookie is captured.
                              enum:
                              - Exact
                              - Prefix
                              type: string
                            maxLength:
                              description: maxLength specifies a maximum length of
                                the string that will be logged, which includes the
                                cookie name, cookie value, and one-character delimiter.  If
                                the log entry exceeds this length, the value will
                                be truncated in the log message.  Note that the ingress
                                controller may impose a separate bound on the total
                                length of HTTP headers in a request.
                              maximum: 1024
                              minimum: 1
                              type: integer
                            name:
                              description: name specifies a cookie name.  Its value
                                must be a valid HTTP cookie name as defined in RFC
                                6265 section 4.1.
                              maxLength: 1024
                              minLength: 0
                              pattern: ^[-!#$%&'*+.0-9A-Z^_`a-z|~]*$
                              type: string
                            namePrefix:
                              description: namePrefix specifies a cookie name prefix.  Its
                                value must be a valid HTTP cookie name as defined
                                in RFC 6265 section 4.1.
                              maxLength: 1024
                              minLength: 0
                              pattern: ^[-!#$%&'*+.0-9A-Z^_`a-z|~]*$
                              type: string
                          required:
                          - matchType
                          - maxLength
                          type: object
                        maxItems: 1
                        nullable: true
                        type: array
                        x-kubernetes-list-type: atomic
                      httpCaptureHeaders:
                        description: "httpCaptureHeaders defines HTTP headers that
                          should be captured in access logs.  If this field is empty,
                          no headers are captured. \n Note that this option only applies
                          to cleartext HTTP connections and to secure HTTP connections
                          for which the ingress controller terminates encryption (that
                          is, edge-terminated or reencrypt connections).  Headers
                          cannot be captured for TLS passthrough connections."
                        properties:
                          request:
                            description: "request specifies which HTTP request headers
                              to capture. \n If this field is empty, no request headers
                              are captured."
                            items:
                              description: IngressControllerCaptureHTTPHeader describes
                                an HTTP header that should be captured.
                              properties:
                                maxLength:
                                  description: maxLength specifies a maximum length
                                    for the header value.  If a header value exceeds
                                    this length, the value will be truncated in the
                                    log message.  Note that the ingress controller
                                    may impose a separate bound on the total length
                                    of HTTP headers in a request.
                                  minimum: 1
                                  type: integer
                                name:
                                  description: name specifies a header name.  Its
                                    value must be a valid HTTP header name as defined
                                    in RFC 2616 section 4.2.
                                  pattern: ^[-!#$%&'*+.0-9A-Z^_`a-z|~]+$
                                  type: string
                              required:
                              - maxLength
                              - name
                              type: object
                            nullable: true
                            type: array
                            x-kubernetes-list-type: atomic
                          response:
                            description: "response specifies which HTTP response headers
                              to capture. \n If this field is empty, no response headers
                              are captured."
                            items:
                              description: IngressControllerCaptureHTTPHeader describes
                                an HTTP header that should be captured.
                              properties:
                                maxLength:
                                  description: maxLength specifies a maximum length
                                    for the header value.  If a header value exceeds
                                    this length, the value will be truncated in the
                                    log message.  Note that the ingress controller
                                    may impose a separate bound on the total length
                                    of HTTP headers in a request.
                                  minimum: 1
                                  type: integer
                                name:
                                  description: name specifies a header name.  Its
                                    value must be a valid HTTP header name as defined
                                    in RFC 2616 section 4.2.
                                  pattern: ^[-!#$%&'*+.0-9A-Z^_`a-z|~]+$
                                  type: string
                              required:
                              - maxLength
                              - name
                              type: object
                            nullable: true
                            type: array
                            x-kubernetes-list-type: atomic
                        type: object
                      httpLogFormat:
                        description: "httpLogFormat specifies the format of the log
                          message for an HTTP request. \n If this field is empty,
                          log messages use the implementation's default HTTP log format.
                          \ For HAProxy's default HTTP log format, see the HAProxy
                          documentation: http://cbonte.github.io/haproxy-dconv/2.0/configuration.html#8.2.3
                          \n Note that this format only applies to cleartext HTTP
                          connections and to secure HTTP connections for which the
                          ingress controller terminates encryption (that is, edge-terminated
                          or reencrypt connections).  It does not affect the log format
                          for TLS passthrough connections."
                        type: string
                      logEmptyRequests:
                        default: Log
                        description: logEmptyRequests specifies how connections on
                          which no request is received should be logged.  Typically,
                          these empty requests come from load balancers' health probes
                          or Web browsers' speculative connections ("preconnect"),
                          in which case logging these requests may be undesirable.  However,
                          these requests may also be caused by network errors, in
                          which case logging empty requests may be useful for diagnosing
                          the errors.  In addition, these requests may be caused by
                          port scans, in which case logging empty requests may aid
                          in detecting intrusion attempts.  Allowed values for this
                          field are "Log" and "Ignore".  The default value is "Log".
                        enum:
                        - Log
                        - Ignore
                        type: string
                    required:
                    - destination
                    type: object
                type: object
              namespaceSelector:
                description: "namespaceSelector is used to filter the set of namespaces
                  serviced by the ingress controller. This is useful for implementing
                  shards. \n If unset, the default is no filtering."
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: A label selector requirement is a selector that
                        contains values, a key, and an operator that relates the key
                        and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: operator represents a key's relationship to
                            a set of values. Valid operators are In, NotIn, Exists
                            and DoesNotExist.
                          type: string
                        values:
                          description: values is an array of string values. If the
                            operator is In or NotIn, the values array must be non-empty.
                            If the operator is Exists or DoesNotExist, the values
                            array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: matchLabels is a map of {key,value} pairs. A single
                      {key,value} in the matchLabels map is equivalent to an element
                      of matchExpressions, whose key field is "key", the operator
                      is "In", and the values array contains only "value". The requirements
                      are ANDed.
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              nodePlacement:
                description: "nodePlacement enables explicit control over the scheduling
                  of the ingress controller. \n If unset, defaults are used. See NodePlacement
                  for more details."
                properties:
                  nodeSelector:
                    description: "nodeSelector is the node selector applied to ingress
                      controller deployments. \n If set, the specified selector is
                      used and replaces the default. \n If unset, the default depends
                      on the value of the defaultPlacement field in the cluster config.openshift.io/v1/ingresses
                      status. \n When defaultPlacement is Workers, the default is:
                      \n kubernetes.io/os: linux node-role.kubernetes.io/worker: ''
                      \n When defaultPlacement is ControlPlane, the default is: \n
                      kubernetes.io/os: linux node-role.kubernetes.io/master: '' \n
                      These defaults are subject to change. \n Note that using nodeSelector.matchExpressions
                      is not supported.  Only nodeSelector.matchLabels may be used.
                      \ This is a limitation of the Kubernetes API: the pod spec does
                      not allow complex expressions for node selectors."
                    properties:
                      matchExpressions:
                        description: matchExpressions is a list of label selector
                          requirements. The requirements are ANDed.
                        items:
                          description: A label selector requirement is a selector
                            that contains values, a key, and an operator that relates
                            the key and values.
                          properties:
                            key:
                              description: key is the label key that the selector
                                applies to.
                              type: string
                            operator:
                              description: operator represents a key's relationship
                                to a set of values. Valid operators are In, NotIn,
                                Exists and DoesNotExist.
                              type: string
                            values:
                              description: values is an array of string values. If
                                the operator is In or NotIn, the values array must
                                be non-empty. If the operator is Exists or DoesNotExist,
                                the values array must be empty. This array is replaced
                                during a strategic merge patch.
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          required:
                          - key
                          - operator
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      matchLabels:
                        additionalProperties:
                          type: string
                        description: matchLabels is a map of {key,value} pairs. A
                          single {key,value} in the matchLabels map is equivalent
                          to an element of matchExpressions, whose key field is "key",
                          the operator is "In", and the values array contains only
                          "value". The requirements are ANDed.
                        type: object
                    type: object
                    x-kubernetes-map-type: atomic
                  tolerations:
                    description: "tolerations is a list of tolerations applied to
                      ingress controller deployments. \n The default is an empty list.
                      \n See https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/"
                    items:
                      description: The pod this Toleration is attached to tolerates
                        any taint that matches the triple <key,value,effect> using
                        the matching operator <operator>.
                      properties:
                        effect:
                          description: Effect indicates the taint effect to match.
                            Empty means match all taint effects. When specified, allowed
                            values are NoSchedule, PreferNoSchedule and NoExecute.
                          type: string
                        key:
                          description: Key is the taint key that the toleration applies
                            to. Empty means match all taint keys. If the key is empty,
                            operator must be Exists; this combination means to match
                            all values and all keys.
                          type: string
                        operator:
                          description: Operator represents a key's relationship to
                            the value. Valid operators are Exists and Equal. Defaults
                            to Equal. Exists is equivalent to wildcard for value,
                            so that a pod can tolerate all taints of a particular
                            category.
                          type: string
                        tolerationSeconds:
                          description: TolerationSeconds represents the period of
                            time the toleration (which must be of effect NoExecute,
                            otherwise this field is ignored) tolerates the taint.
                            By default, it is not set, which means tolerate the taint
                            forever (do not evict). Zero and negative values will
                            be treated as 0 (evict immediately) by the system.
                          format: int64
                          type: integer
                        value:
                          description: Value is the taint value the toleration matches
                            to. If the operator is Exists, the value should be empty,
                            otherwise just a regular string.
                          type: string
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                type: object
              replicas:
                description: "replicas is the desired number of ingress controller
                  replicas. If unset, the default depends on the value of the defaultPlacement
                  field in the cluster config.openshift.io/v1/ingresses status. \n
                  The value of replicas is set based on the value of a chosen field
                  in the Infrastructure CR. If defaultPlacement is set to ControlPlane,
                  the chosen field will be controlPlaneTopology. If it is set to Workers
                  the chosen field will be infrastructureTopology. Replicas will then
                  be set to 1 or 2 based whether the chosen field's value is SingleReplica
                  or HighlyAvailable, respectively. \n These defaults are subject
                  to change."
                format: int32
                type: integer
              routeAdmission:
                description: "routeAdmission defines a policy for handling new route
                  claims (for example, to allow or deny claims across namespaces).
                  \n If empty, defaults will be applied. See specific routeAdmission
                  fields for details about their defaults."
                properties:
                  namespaceOwnership:
                    description: "namespaceOwnership describes how host name claims
                      across namespaces should be handled. \n Value must be one of:
                      \n - Strict: Do not allow routes in different namespaces to
                      claim the same host. \n - InterNamespaceAllowed: Allow routes
                      to claim different paths of the same host name across namespaces.
                      \n If empty, the default is Strict."
                    enum:
                    - InterNamespaceAllowed
                    - Strict
                    type: string
                  wildcardPolicy:
                    description: "wildcardPolicy describes how routes with wildcard
                      policies should be handled for the ingress controller. WildcardPolicy
                      controls use of routes [1] exposed by the ingress controller
                      based on the route's wildcard policy. \n [1] https://github.com/openshift/api/blob/master/route/v1/types.go
                      \n Note: Updating WildcardPolicy from WildcardsAllowed to WildcardsDisallowed
                      will cause admitted routes with a wildcard policy of Subdomain
                      to stop working. These routes must be updated to a wildcard
                      policy of None to be readmitted by the ingress controller. \n
                      WildcardPolicy supports WildcardsAllowed and WildcardsDisallowed
                      values. \n If empty, defaults to \"WildcardsDisallowed\"."
                    enum:
                    - WildcardsAllowed
                    - WildcardsDisallowed
                    type: string
                type: object
              routeSelector:
                description: "routeSelector is used to filter the set of Routes serviced
                  by the ingress controller. This is useful for implementing shards.
                  \n If unset, the default is no filtering."
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: A label selector requirement is a selector that
                        contains values, a key, and an operator that relates the key
                        and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: operator represents a key's relationship to
                            a set of values. Valid operators are In, NotIn, Exists
                            and DoesNotExist.
                          type: string
                        values:
                          description: values is an array of string values. If the
                            operator is In or NotIn, the values array must be non-empty.
                            If the operator is Exists or DoesNotExist, the values
                            array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: matchLabels is a map of {key,value} pairs. A single
                      {key,value} in the matchLabels map is equivalent to an element
                      of matchExpressions, whose key field is "key", the operator
                      is "In", and the values array contains only "value". The requirements
                      are ANDed.
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              tlsSecurityProfile:
                description: "tlsSecurityProfile specifies settings for TLS connections
                  for ingresscontrollers. \n If unset, the default is based on the
                  apiservers.config.openshift.io/cluster resource. \n Note that when
                  using the Old, Intermediate, and Modern profile types, the effective
                  profile configuration is subject to change between releases. For
                  example, given a specification to use the Intermediate profile deployed
                  on release X.Y.Z, an upgrade to release X.Y.Z+1 may cause a new
                  profile configuration to be applied to the ingress controller, resulting
                  in a rollout."
                properties:
                  custom:
                    description: "custom is a user-defined TLS security profile. Be
                      extremely careful using a custom profile as invalid configurations
                      can be catastrophic. An example custom profile looks like this:
                      \n ciphers: \n - ECDHE-ECDSA-CHACHA20-POLY1305 \n - ECDHE-RSA-CHACHA20-POLY1305
                      \n - ECDHE-RSA-AES128-GCM-SHA256 \n - ECDHE-ECDSA-AES128-GCM-SHA256
                      \n minTLSVersion: VersionTLS11"
                    nullable: true
                    properties:
                      ciphers:
                        description: "ciphers is used to specify the cipher algorithms
                          that are negotiated during the TLS handshake.  Operators
                          may remove entries their operands do not support.  For example,
                          to use DES-CBC3-SHA  (yaml): \n ciphers: - DES-CBC3-SHA"
                        items:
                          type: string
                        type: array
                        x-kubernetes-list-type: atomic
                      minTLSVersion:
                        description: "minTLSVersion is used to specify the minimal
                          version of the TLS protocol that is negotiated during the
                          TLS handshake. For example, to use TLS versions 1.1, 1.2
                          and 1.3 (yaml): \n minTLSVersion: VersionTLS11 \n NOTE:
                          currently the highest minTLSVersion allowed is VersionTLS12"
                        enum:
                        - VersionTLS10
                        - VersionTLS11
                        - VersionTLS12
                        - VersionTLS13
                        type: string
                    type: object
                  intermediate:
                    description: "intermediate is a TLS security profile based on:
                      \n https://wiki.mozilla.org/Security/Server_Side_TLS#Intermediate_compatibility_.28recommended.29
                      \n and looks like this (yaml): \n ciphers: \n - TLS_AES_128_GCM_SHA256
                      \n - TLS_AES_256_GCM_SHA384 \n - TLS_CHACHA20_POLY1305_SHA256
                      \n - ECDHE-ECDSA-AES128-GCM-SHA256 \n - ECDHE-RSA-AES128-GCM-SHA256
                      \n - ECDHE-ECDSA-AES256-GCM-SHA384 \n - ECDHE-RSA-AES256-GCM-SHA384
                      \n - ECDHE-ECDSA-CHACHA20-POLY1305 \n - ECDHE-RSA-CHACHA20-POLY1305
                      \n - DHE-RSA-AES128-GCM-SHA256 \n - DHE-RSA-AES256-GCM-SHA384
                      \n minTLSVersion: VersionTLS12"
                    nullable: true
                    type: object
                  modern:
                    description: "modern is a TLS security profile based on: \n https://wiki.mozilla.org/Security/Server_Side_TLS#Modern_compatibility
                      \n and looks like this (yaml): \n ciphers: \n - TLS_AES_128_GCM_SHA256
                      \n - TLS_AES_256_GCM_SHA384 \n - TLS_CHACHA20_POLY1305_SHA256
                      \n minTLSVersion: VersionTLS13"
                    nullable: true
                    type: object
                  old:
                    description: "old is a TLS security profile based on: \n https://wiki.mozilla.org/Security/Server_Side_TLS#Old_backward_compatibility
                      \n and looks like this (yaml): \n ciphers: \n - TLS_AES_128_GCM_SHA256
                      \n - TLS_AES_256_GCM_SHA384 \n - TLS_CHACHA20_POLY1305_SHA256
                      \n - ECDHE-ECDSA-AES128-GCM-SHA256 \n - ECDHE-RSA-AES128-GCM-SHA256
                      \n - ECDHE-ECDSA-AES256-GCM-SHA384 \n - ECDHE-RSA-AES256-GCM-SHA384
                      \n - ECDHE-ECDSA-CHACHA20-POLY1305 \n - ECDHE-RSA-CHACHA20-POLY1305
                      \n - DHE-RSA-AES128-GCM-SHA256 \n - DHE-RSA-AES256-GCM-SHA384
                      \n - DHE-RSA-CHACHA20-POLY1305 \n - ECDHE-ECDSA-AES128-SHA256
                      \n - ECDHE-RSA-AES128-SHA256 \n - ECDHE-ECDSA-AES128-SHA \n
                      - ECDHE-RSA-AES128-SHA \n - ECDHE-ECDSA-AES256-SHA384 \n - ECDHE-RSA-AES256-SHA384
                      \n - ECDHE-ECDSA-AES256-SHA \n - ECDHE-RSA-AES256-SHA \n - DHE-RSA-AES128-SHA256
                      \n - DHE-RSA-AES256-SHA256 \n - AES128-GCM-SHA256 \n - AES256-GCM-SHA384
                      \n - AES128-SHA256 \n - AES256-SHA256 \n - AES128-SHA \n - AES256-SHA
                      \n - DES-CBC3-SHA \n minTLSVersion: VersionTLS10"
                    nullable: true
                    type: object
                  type:
                    description: "type is one of Old, Intermediate, Modern or Custom.
                      Custom provides the ability to specify individual TLS security
                      profile parameters. Old, Intermediate and Modern are TLS security
                      profiles based on: \n https://wiki.mozilla.org/Security/Server_Side_TLS#Recommended_configurations
                      \n The profiles are intent based, so they may change over time
                      as new ciphers are developed and existing ciphers are found
                      to be insecure.  Depending on precisely which ciphers are available
                      to a process, the list may be reduced. \n Note that the Modern
                      profile is currently not supported because it is not yet well
                      adopted by common software libraries."
                    enum:
                    - Old
                    - Intermediate
                    - Modern
                    - Custom
                    type: string
                type: object
              tuningOptions:
                anyOf:
                - properties:
                    maxConnections:
                      enum:
                      - -1
                      - 0
                - properties:
                    maxConnections:
                      format: int32
                      maximum: 2000000
                      minimum: 2000
                description: "tuningOptions defines parameters for adjusting the performance
                  of ingress controller pods. All fields are optional and will use
                  their respective defaults if not set. See specific tuningOptions
                  fields for more details. \n Setting fields within tuningOptions
                  is generally not recommended. The default values are suitable for
                  most configurations."
                properties:
                  clientFinTimeout:
                    description: "clientFinTimeout defines how long a connection will
                      be held open while waiting for the client response to the server/backend
                      closing the connection. \n If unset, the default timeout is
                      1s"
                    format: duration
                    type: string
                  clientTimeout:
                    description: "clientTimeout defines how long a connection will
                      be held open while waiting for a client response. \n If unset,
                      the default timeout is 30s"
                    format: duration
                    type: string
                  connectTimeout:
                    description: "ConnectTimeout defines the maximum time to wait
                      for a connection attempt to a server/backend to succeed. \n
                      This field expects an unsigned duration string of decimal numbers,
                      each with optional fraction and a unit suffix, e.g. \"300ms\",
                      \"1.5h\" or \"2h45m\". Valid time units are \"ns\", \"us\" (or
                      \"µs\" U+00B5 or \"μs\" U+03BC), \"ms\", \"s\", \"m\", \"h\".
                      \n When omitted, this means the user has no opinion and the
                      platform is left to choose a reasonable default. This default
                      is subject to change over time. The current default is 5s."
                    pattern: ^(0|([0-9]+(\.[0-9]+)?(ns|us|µs|μs|ms|s|m|h))+)$
                    type: string
                  headerBufferBytes:
                    description: "headerBufferBytes describes how much memory should
                      be reserved (in bytes) for IngressController connection sessions.
                      Note that this value must be at least 16384 if HTTP/2 is enabled
                      for the IngressController (https://tools.ietf.org/html/rfc7540).
                      If this field is empty, the IngressController will use a default
                      value of 32768 bytes. \n Setting this field is generally not
                      recommended as headerBufferBytes values that are too small may
                      break the IngressController and headerBufferBytes values that
                      are too large could cause the IngressController to use significantly
                      more memory than necessary."
                    format: int32
                    minimum: 16384
                    type: integer
                  headerBufferMaxRewriteBytes:
                    description: "headerBufferMaxRewriteBytes describes how much memory
                      should be reserved (in bytes) from headerBufferBytes for HTTP
                      header rewriting and appending for IngressController connection
                      sessions. Note that incoming HTTP requests will be limited to
                      (headerBufferBytes - headerBufferMaxRewriteBytes) bytes, meaning
                      headerBufferBytes must be greater than headerBufferMaxRewriteBytes.
                      If this field is empty, the IngressController will use a default
                      value of 8192 bytes. \n Setting this field is generally not
                      recommended as headerBufferMaxRewriteBytes values that are too
                      small may break the IngressController and headerBufferMaxRewriteBytes
                      values that are too large could cause the IngressController
                      to use significantly more memory than necessary."
                    format: int32
                    minimum: 4096
                    type: integer
                  healthCheckInterval:
                    description: "healthCheckInterval defines how long the router
                      waits between two consecutive health checks on its configured
                      backends.  This value is applied globally as a default for all
                      routes, but may be overridden per-route by the route annotation
                      \"router.openshift.io/haproxy.health.check.interval\". \n Expects
                      an unsigned duration string of decimal numbers, each with optional
                      fraction and a unit suffix, eg \"300ms\", \"1.5h\" or \"2h45m\".
                      Valid time units are \"ns\", \"us\" (or \"µs\" U+00B5 or \"μs\"
                      U+03BC), \"ms\", \"s\", \"m\", \"h\". \n Setting this to less
                      than 5s can cause excess traffic due to too frequent TCP health
                      checks and accompanying SYN packet storms.  Alternatively, setting
                      this too high can result in increased latency, due to backend
                      servers that are no longer available, but haven't yet been detected
                      as such. \n An empty or zero healthCheckInterval means no opinion
                      and IngressController chooses a default, which is subject to
                      change over time. Currently the default healthCheckInterval
                      value is 5s. \n Currently the minimum allowed value is 1s and
                      the maximum allowed value is 2147483647ms (24.85 days).  Both
                      are subject to change over time."
                    pattern: ^(0|([0-9]+(\.[0-9]+)?(ns|us|µs|μs|ms|s|m|h))+)$
                    type: string
                  maxConnections:
                    description: "maxConnections defines the maximum number of simultaneous
                      connections that can be established per HAProxy process. Increasing
                      this value allows each ingress controller pod to handle more
                      connections but at the cost of additional system resources being
                      consumed. \n Permitted values are: empty, 0, -1, and the range
                      2000-2000000. \n If this field is empty or 0, the IngressController
                      will use the default value of 50000, but the default is subject
                      to change in future releases. \n If the value is -1 then HAProxy
                      will dynamically compute a maximum value based on the available
                      ulimits in the running container. Selecting -1 (i.e., auto)
                      will result in a large value being computed (~520000 on OpenShift
                      >=4.10 clusters) and therefore each HAProxy process will incur
                      significant memory usage compared to the current default of
                      50000. \n Setting a value that is greater than the current operating
                      system limit will prevent the HAProxy process from starting.
                      \n If you choose a discrete value (e.g., 750000) and the router
                      pod is migrated to a new node, there's no guarantee that that
                      new node has identical ulimits configured. In such a scenario
                      the pod would fail to start. If you have nodes with different
                      ulimits configured (e.g., different tuned profiles) and you
                      choose a discrete value then the guidance is to use -1 and let
                      the value be computed dynamically at runtime. \n You can monitor
                      memory usage for router containers with the following metric:
                      'container_memory_working_set_bytes{container=\"router\",namespace=\"openshift-ingress\"}'.
                      \n You can monitor memory usage of individual HAProxy processes
                      in router containers with the following metric: 'container_memory_working_set_bytes{container=\"router\",namespace=\"openshift-ingress\"}/container_processes{container=\"router\",namespace=\"openshift-ingress\"}'."
                    format: int32
                    type: integer
                  reloadInterval:
                    description: "reloadInterval defines the minimum interval at which
                      the router is allowed to reload to accept new changes. Increasing
                      this value can prevent the accumulation of HAProxy processes,
                      depending on the scenario. Increasing this interval can also
                      lessen load imbalance on a backend's servers when using the
                      roundrobin balancing algorithm. Alternatively, decreasing this
                      value may decrease latency since updates to HAProxy's configuration
                      can take effect more quickly. \n The value must be a time duration
                      value; see <https://pkg.go.dev/time#ParseDuration>. Currently,
                      the minimum value allowed is 1s, and the maximum allowed value
                      is 120s. Minimum and maximum allowed values may change in future
                      versions of OpenShift. Note that if a duration outside of these
                      bounds is provided, the value of reloadInterval will be capped/floored
                      and not rejected (e.g. a duration of over 120s will be capped
                      to 120s; the IngressController will not reject and replace this
                      disallowed value with the default). \n A zero value for reloadInterval
                      tells the IngressController to choose the default, which is
                      currently 5s and subject to change without notice. \n This field
                      expects an unsigned duration string of decimal numbers, each
                      with optional fraction and a unit suffix, e.g. \"300ms\", \"1.5h\"
                      or \"2h45m\". Valid time units are \"ns\", \"us\" (or \"µs\"
                      U+00B5 or \"μs\" U+03BC), \"ms\", \"s\", \"m\", \"h\". \n Note:
                      Setting a value significantly larger than the default of 5s
                      can cause latency in observing updates to routes and their endpoints.
                      HAProxy's configuration will be reloaded less frequently, and
                      newly created routes will not be served until the subsequent
                      reload."
                    pattern: ^(0|([0-9]+(\.[0-9]+)?(ns|us|µs|μs|ms|s|m|h))+)$
                    type: string
                  serverFinTimeout:
                    description: "serverFinTimeout defines how long a connection will
                      be held open while waiting for the server/backend response to
                      the client closing the connection. \n If unset, the default
                      timeout is 1s"
                    format: duration
                    type: string
                  serverTimeout:
                    description: "serverTimeout defines how long a connection will
                      be held open while waiting for a server/backend response. \n
                      If unset, the default timeout is 30s"
                    format: duration
                    type: string
                  threadCount:
                    description: "threadCount defines the number of threads created
                      per HAProxy process. Creating more threads allows each ingress
                      controller pod to handle more connections, at the cost of more
                      system resources being used. HAProxy currently supports up to
                      64 threads. If this field is empty, the IngressController will
                      use the default value.  The current default is 4 threads, but
                      this may change in future releases. \n Setting this field is
                      generally not recommended. Increasing the number of HAProxy
                      threads allows ingress controller pods to utilize more CPU time
                      under load, potentially starving other pods if set too high.
                      Reducing the number of threads may cause the ingress controller
                      to perform poorly."
                    format: int32
                    maximum: 64
                    minimum: 1
                    type: integer
                  tlsInspectDelay:
                    description: "tlsInspectDelay defines how long the router can
                      hold data to find a matching route. \n Setting this too short
                      can cause the router to fall back to the default certificate
                      for edge-terminated or reencrypt routes even when a better matching
                      certificate could be used. \n If unset, the default inspect
                      delay is 5s"
                    format: duration
                    type: string
                  tunnelTimeout:
                    description: "tunnelTimeout defines how long a tunnel connection
                      (including websockets) will be held open while the tunnel is
                      idle. \n If unset, the default timeout is 1h"
                    format: duration
                    type: string
                type: object
              unsupportedConfigOverrides:
                description: unsupportedConfigOverrides allows specifying unsupported
                  configuration options.  Its use is unsupported.
                nullable: true
                type: object
                x-kubernetes-preserve-unknown-fields: true
            type: object
          status:
            description: status is the most recently observed status of the IngressController.
            properties:
              availableReplicas:
                description: availableReplicas is number of observed available replicas
                  according to the ingress controller deployment.
                format: int32
                type: integer
              conditions:
                description: "conditions is a list of conditions and their status.
                  \n Available means the ingress controller deployment is available
                  and servicing route and ingress resources (i.e, .status.availableReplicas
                  equals .spec.replicas) \n There are additional conditions which
                  indicate the status of other ingress controller features and capabilities.
                  \n * LoadBalancerManaged - True if the following conditions are
                  met: * The endpoint publishing strategy requires a service load
                  balancer. - False if any of those conditions are unsatisfied. \n
                  * LoadBalancerReady - True if the following conditions are met:
                  * A load balancer is managed. * The load balancer is ready. - False
                  if any of those conditions are unsatisfied. \n * DNSManaged - True
                  if the following conditions are met: * The endpoint publishing strategy
                  and platform support DNS. * The ingress controller domain is set.
                  * dns.config.openshift.io/cluster configures DNS zones. - False
                  if any of those conditions are unsatisfied. \n * DNSReady - True
                  if the following conditions are met: * DNS is managed. * DNS records
                  have been successfully created. - False if any of those conditions
                  are unsatisfied."
                items:
                  description: OperatorCondition is just the standard condition fields.
                  properties:
                    lastTransitionTime:
                      format: date-time
                      type: string
                    message:
                      type: string
                    reason:
                      type: string
                    status:
                      type: string
                    type:
                      type: string
                  required:
                  - type
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              domain:
                description: domain is the actual domain in use.
                type: string
              endpointPublishingStrategy:
                description: endpointPublishingStrategy is the actual strategy in
                  use.
                properties:
                  hostNetwork:
                    description: hostNetwork holds parameters for the HostNetwork
                      endpoint publishing strategy. Present only if type is HostNetwork.
                    properties:
                      httpPort:
                        default: 80
                        description: httpPort is the port on the host which should
                          be used to listen for HTTP requests. This field should be
                          set when port 80 is already in use. The value should not
                          coincide with the NodePort range of the cluster. When the
                          value is 0 or is not specified it defaults to 80.
                        format: int32
                        maximum: 65535
                        minimum: 0
                        type: integer
                      httpsPort:
                        default: 443
                        description: httpsPort is the port on the host which should
                          be used to listen for HTTPS requests. This field should
                          be set when port 443 is already in use. The value should
                          not coincide with the NodePort range of the cluster. When
                          the value is 0 or is not specified it defaults to 443.
                        format: int32
                        maximum: 65535
                        minimum: 0
                        type: integer
                      protocol:
                        description: "protocol specifies whether the IngressController
                          expects incoming connections to use plain TCP or whether
                          the IngressController expects PROXY protocol. \n PROXY protocol
                          can be used with load balancers that support it to communicate
                          the source addresses of client connections when forwarding
                          those connections to the IngressController.  Using PROXY
                          protocol enables the IngressController to report those source
                          addresses instead of reporting the load balancer's address
                          in HTTP headers and logs.  Note that enabling PROXY protocol
                          on the IngressController will cause connections to fail
                          if you are not using a load balancer that uses PROXY protocol
                          to forward connections to the IngressController.  See http://www.haproxy.org/download/2.2/doc/proxy-protocol.txt
                          for information about PROXY protocol. \n The following values
                          are valid for this field: \n * The empty string. * \"TCP\".
                          * \"PROXY\". \n The empty string specifies the default,
                          which is TCP without PROXY protocol.  Note that the default
                          is subject to change."
                        enum:
                        - ""
                        - TCP
                        - PROXY
                        type: string
                      statsPort:
                        default: 1936
                        description: statsPort is the port on the host where the stats
                          from the router are published. The value should not coincide
                          with the NodePort range of the cluster. If an external load
                          balancer is configured to forward connections to this IngressController,
                          the load balancer should use this port for health checks.
                          The load balancer can send HTTP probes on this port on a
                          given node, with the path /healthz/ready to determine if
                          the ingress controller is ready to receive traffic on the
                          node. For proper operation the load balancer must not forward
                          traffic to a node until the health check reports ready.
                          The load balancer should also stop forwarding requests within
                          a maximum of 45 seconds after /healthz/ready starts reporting
                          not-ready. Probing every 5 to 10 seconds, with a 5-second
                          timeout and with a threshold of two successful or failed
                          requests to become healthy or unhealthy respectively, are
                          well-tested values. When the value is 0 or is not specified
                          it defaults to 1936.
                        format: int32
                        maximum: 65535
                        minimum: 0
                        type: integer
                    type: object
                  loadBalancer:
                    description: loadBalancer holds parameters for the load balancer.
                      Present only if type is LoadBalancerService.
                    properties:
                      allowedSourceRanges:
                        description: "allowedSourceRanges specifies an allowlist of
                          IP address ranges to which access to the load balancer should
                          be restricted.  Each range must be specified using CIDR
                          notation (e.g. \"10.0.0.0/8\" or \"fd00::/8\"). If no range
                          is specified, \"0.0.0.0/0\" for IPv4 and \"::/0\" for IPv6
                          are used by default, which allows all source addresses.
                          \n To facilitate migration from earlier versions of OpenShift
                          that did not have the allowedSourceRanges field, you may
                          set the service.beta.kubernetes.io/load-balancer-source-ranges
                          annotation on the \"router-<ingresscontroller name>\" service
                          in the \"openshift-ingress\" namespace, and this annotation
                          will take effect if allowedSourceRanges is empty on OpenShift
                          4.12."
                        items:
                          description: CIDR is an IP address range in CIDR notation
                            (for example, "10.0.0.0/8" or "fd00::/8").
                          pattern: (^(([0-9]|[0-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[0-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])/([0-9]|[12][0-9]|3[0-2])$)|(^s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3}))|:)))(%.+)?s*(\/(12[0-8]|1[0-1][0-9]|[1-9][0-9]|[0-9]))$)
                          type: string
                        nullable: true
                        type: array
                        x-kubernetes-list-type: atomic
                      dnsManagementPolicy:
                        default: Managed
                        description: 'dnsManagementPolicy indicates if the lifecycle
                          of the wildcard DNS record associated with the load balancer
                          service will be managed by the ingress operator. It defaults
                          to Managed. Valid values are: Managed and Unmanaged.'
                        enum:
                        - Managed
                        - Unmanaged
                        type: string
                      providerParameters:
                        description: "providerParameters holds desired load balancer
                          information specific to the underlying infrastructure provider.
                          \n If empty, defaults will be applied. See specific providerParameters
                          fields for details about their defaults."
                        properties:
                          aws:
                            description: "aws provides configuration settings that
                              are specific to AWS load balancers. \n If empty, defaults
                              will be applied. See specific aws fields for details
                              about their defaults."
                            properties:
                              classicLoadBalancer:
                                description: classicLoadBalancerParameters holds configuration
                                  parameters for an AWS classic load balancer. Present
                                  only if type is Classic.
                                properties:
                                  connectionIdleTimeout:
                                    description: connectionIdleTimeout specifies the
                                      maximum time period that a connection may be
                                      idle before the load balancer closes the connection.  The
                                      value must be parseable as a time duration value;
                                      see <https://pkg.go.dev/time#ParseDuration>.  A
                                      nil or zero value means no opinion, in which
                                      case a default value is used.  The default value
                                      for this field is 60s.  This default is subject
                                      to change.
                                    format: duration
                                    type: string
                                  subnets:
                                    description: "subnets specifies the subnets to
                                      which the load balancer will attach. The subnets
                                      may be specified by either their ID or name.
                                      The total number of subnets is limited to 10.
                                      \n In order for the load balancer to be provisioned
                                      with subnets, each subnet must exist, each subnet
                                      must be from a different availability zone,
                                      and the load balancer service must be recreated
                                      to pick up new values. \n When omitted from
                                      the spec, the subnets will be auto-discovered
                                      for each availability zone. Auto-discovered
                                      subnets are not reported in the status of the
                                      IngressController object."
                                    properties:
                                      ids:
                                        description: ids specifies a list of AWS subnets
                                          by subnet ID. Subnet IDs must start with
                                          "subnet-", consist only of alphanumeric
                                          characters, must be exactly 24 characters
                                          long, must be unique, and the total number
                                          of subnets specified by ids and names must
                                          not exceed 10.
                                        items:
                                          description: AWSSubnetID is a reference
                                            to an AWS subnet ID.
                                          maxLength: 24
                                          minLength: 24
                                          pattern: ^subnet-[0-9A-Za-z]+$
                                          type: string
                                        maxItems: 10
                                        type: array
                                        x-kubernetes-list-type: atomic
                                        x-kubernetes-validations:
                                        - message: subnet ids cannot contain duplicates
                                          rule: self.all(x, self.exists_one(y, x ==
                                            y))
                                      names:
                                        description: names specifies a list of AWS
                                          subnets by subnet name. Subnet names must
                                          not start with "subnet-", must not include
                                          commas, must be under 256 characters in
                                          length, must be unique, and the total number
                                          of subnets specified by ids and names must
                                          not exceed 10.
                                        items:
                                          description: AWSSubnetName is a reference
                                            to an AWS subnet name.
                                          maxLength: 256
                                          minLength: 1
                                          type: string
                                          x-kubernetes-validations:
                                          - message: subnet name cannot contain a
                                              comma
                                            rule: '!self.contains('','')'
                                          - message: subnet name cannot start with
                                              'subnet-'
                                            rule: '!self.startsWith(''subnet-'')'
                                        maxItems: 10
                                        type: array
                                        x-kubernetes-list-type: atomic
                                        x-kubernetes-validations:
                                        - message: subnet names cannot contain duplicates
                                          rule: self.all(x, self.exists_one(y, x ==
                                            y))
                                    type: object
                                    x-kubernetes-validations:
                                    - message: the total number of subnets cannot
                                        exceed 10
                                      rule: 'has(self.ids) && has(self.names) ? size(self.ids
                                        + self.names) <= 10 : true'
                                    - message: must specify at least 1 subnet name
                                        or id
                                      rule: has(self.ids) && self.ids.size() > 0 ||
                                        has(self.names) && self.names.size() > 0
                                type: object
                              networkLoadBalancer:
                                description: networkLoadBalancerParameters holds configuration
                                  parameters for an AWS network load balancer. Present
                                  only if type is NLB.
                                properties:
                                  eipAllocations:
                                    description: "eipAllocations is a list of IDs
                                      for Elastic IP (EIP) addresses that are assigned
                                      to the Network Load Balancer. The following
                                      restrictions apply: \n eipAllocations can only
                                      be used with external scope, not internal. An
                                      EIP can be allocated to only a single IngressController.
                                      The number of EIP allocations must match the
                                      number of subnets that are used for the load
                                      balancer. Each EIP allocation must be unique.
                                      A maximum of 10 EIP allocations are permitted.
                                      \n See https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/elastic-ip-addresses-eip.html
                                      for general information about configuration,
                                      characteristics, and limitations of Elastic
                                      IP addresses."
                                    items:
                                      description: EIPAllocation is an ID for an Elastic
                                        IP (EIP) address that can be allocated to
                                        an ELB in the AWS environment. Values must
                                        begin with `eipalloc-` followed by exactly
                                        17 hexadecimal (`[0-9a-fA-F]`) characters.
                                      maxLength: 26
                                      minLength: 26
                                      type: string
                                      x-kubernetes-validations:
                                      - message: eipAllocations should start with
                                          'eipalloc-'
                                        rule: self.startsWith('eipalloc-')
                                      - message: eipAllocations must be 'eipalloc-'
                                          followed by exactly 17 hexadecimal characters
                                          (0-9, a-f, A-F)
                                        rule: self.split("-", 2)[1].matches('[0-9a-fA-F]{17}$')
                                    maxItems: 10
                                    type: array
                                    x-kubernetes-list-type: atomic
                                    x-kubernetes-validations:
                                    - message: eipAllocations cannot contain duplicates
                                      rule: self.all(x, self.exists_one(y, x == y))
                                  subnets:
                                    description: "subnets specifies the subnets to
                                      which the load balancer will attach. The subnets
                                      may be specified by either their ID or name.
                                      The total number of subnets is limited to 10.
                                      \n In order for the load balancer to be provisioned
                                      with subnets, each subnet must exist, each subnet
                                      must be from a different availability zone,
                                      and the load balancer service must be recreated
                                      to pick up new values. \n When omitted from
                                      the spec, the subnets will be auto-discovered
                                      for each availability zone. Auto-discovered
                                      subnets are not reported in the status of the
                                      IngressController object."
                                    properties:
                                      ids:
                                        description: ids specifies a list of AWS subnets
                                          by subnet ID. Subnet IDs must start with
                                          "subnet-", consist only of alphanumeric
                                          characters, must be exactly 24 characters
                                          long, must be unique, and the total number
                                          of subnets specified by ids and names must
                                          not exceed 10.
                                        items:
                                          description: AWSSubnetID is a reference
                                            to an AWS subnet ID.
                                          maxLength: 24
                                          minLength: 24
                                          pattern: ^subnet-[0-9A-Za-z]+$
                                          type: string
                                        maxItems: 10
                                        type: array
                                        x-kubernetes-list-type: atomic
                                        x-kubernetes-validations:
                                        - message: subnet ids cannot contain duplicates
                                          rule: self.all(x, self.exists_one(y, x ==
                                            y))
                                      names:
                                        description: names specifies a list of AWS
                                          subnets by subnet name. Subnet names must
                                          not start with "subnet-", must not include
                                          commas, must be under 256 characters in
                                          length, must be unique, and the total number
                                          of subnets specified by ids and names must
                                          not exceed 10.
                                        items:
                                          description: AWSSubnetName is a reference
                                            to an AWS subnet name.
                                          maxLength: 256
                                          minLength: 1
                                          type: string
                                          x-kubernetes-validations:
                                          - message: subnet name cannot contain a
                                              comma
                                            rule: '!self.contains('','')'
                                          - message: subnet name cannot start with
                                              'subnet-'
                                            rule: '!self.startsWith(''subnet-'')'
                                        maxItems: 10
                                        type: array
                                        x-kubernetes-list-type: atomic
                                        x-kubernetes-validations:
                                        - message: subnet names cannot contain duplicates
                                          rule: self.all(x, self.exists_one(y, x ==
                                            y))
                                    type: object
                                    x-kubernetes-validations:
                                    - message: the total number of subnets cannot
                                        exceed 10
                                      rule: 'has(self.ids) && has(self.names) ? size(self.ids
                                        + self.names) <= 10 : true'
                                    - message: must specify at least 1 subnet name
                                        or id
                                      rule: has(self.ids) && self.ids.size() > 0 ||
                                        has(self.names) && self.names.size() > 0
                                type: object
                                x-kubernetes-validations:
                                - message: number of subnets must be equal to number
                                    of eipAllocations
                                  rule: 'has(self.subnets) && has(self.subnets.ids)
                                    && has(self.subnets.names) && has(self.eipAllocations)
                                    ? size(self.subnets.ids + self.subnets.names)
                                    == size(self.eipAllocations) : true'
                                - message: number of subnets must be equal to number
                                    of eipAllocations
                                  rule: 'has(self.subnets) && has(self.subnets.ids)
                                    && !has(self.subnets.names) && has(self.eipAllocations)
                                    ? size(self.subnets.ids) == size(self.eipAllocations)
                                    : true'
                                - message: number of subnets must be equal to number
                                    of eipAllocations
                                  rule: 'has(self.subnets) && has(self.subnets.names)
                                    && !has(self.subnets.ids) && has(self.eipAllocations)
                                    ? size(self.subnets.names) == size(self.eipAllocations)
                                    : true'
                              type:
                                description: "type is the type of AWS load balancer
                                  to instantiate for an ingresscontroller. \n Valid
                                  values are: \n * \"Classic\": A Classic Load Balancer
                                  that makes routing decisions at either the transport
                                  layer (TCP/SSL) or the application layer (HTTP/HTTPS).
                                  See the following for additional details: \n https://docs.aws.amazon.com/AmazonECS/latest/developerguide/load-balancer-types.html#clb
                                  \n * \"NLB\": A Network Load Balancer that makes
                                  routing decisions at the transport layer (TCP/SSL).
                                  See the following for additional details: \n https://docs.aws.amazon.com/AmazonECS/latest/developerguide/load-balancer-types.html#nlb"
                                enum:
                                - Classic
                                - NLB
                                type: string
                            required:
                            - type
                            type: object
                          gcp:
                            description: "gcp provides configuration settings that
                              are specific to GCP load balancers. \n If empty, defaults
                              will be applied. See specific gcp fields for details
                              about their defaults."
                            properties:
                              clientAccess:
                                description: "clientAccess describes how client access
                                  is restricted for internal load balancers. \n Valid
                                  values are: * \"Global\": Specifying an internal
                                  load balancer with Global client access allows clients
                                  from any region within the VPC to communicate with
                                  the load balancer. \n https://cloud.google.com/kubernetes-engine/docs/how-to/internal-load-balancing#global_access
                                  \n * \"Local\": Specifying an internal load balancer
                                  with Local client access means only clients within
                                  the same region (and VPC) as the GCP load balancer
                                  can communicate with the load balancer. Note that
                                  this is the default behavior. \n https://cloud.google.com/load-balancing/docs/internal#client_access"
                                enum:
                                - Global
                                - Local
                                type: string
                            type: object
                          ibm:
                            description: "ibm provides configuration settings that
                              are specific to IBM Cloud load balancers. \n If empty,
                              defaults will be applied. See specific ibm fields for
                              details about their defaults."
                            properties:
                              protocol:
                                description: "protocol specifies whether the load
                                  balancer uses PROXY protocol to forward connections
                                  to the IngressController. See \"service.kubernetes.io/ibm-load-balancer-cloud-provider-enable-features:
                                  \"proxy-protocol\"\" at https://cloud.ibm.com/docs/containers?topic=containers-vpc-lbaas\"
                                  \n PROXY protocol can be used with load balancers
                                  that support it to communicate the source addresses
                                  of client connections when forwarding those connections
                                  to the IngressController.  Using PROXY protocol
                                  enables the IngressController to report those source
                                  addresses instead of reporting the load balancer's
                                  address in HTTP headers and logs.  Note that enabling
                                  PROXY protocol on the IngressController will cause
                                  connections to fail if you are not using a load
                                  balancer that uses PROXY protocol to forward connections
                                  to the IngressController.  See http://www.haproxy.org/download/2.2/doc/proxy-protocol.txt
                                  for information about PROXY protocol. \n Valid values
                                  for protocol are TCP, PROXY and omitted. When omitted,
                                  this means no opinion and the platform is left to
                                  choose a reasonable default, which is subject to
                                  change over time. The current default is TCP, without
                                  the proxy protocol enabled."
                                enum:
                                - ""
                                - TCP
                                - PROXY
                                type: string
                            type: object
                          type:
                            description: type is the underlying infrastructure provider
                              for the load balancer. Allowed values are "AWS", "Azure",
                              "BareMetal", "GCP", "IBM", "Nutanix", "OpenStack", and
                              "VSphere".
                            enum:
                            - AWS
                            - Azure
                            - BareMetal
                            - GCP
                            - Nutanix
                            - OpenStack
                            - VSphere
                            - IBM
                            type: string
                        required:
                        - type
                        type: object
                      scope:
                        description: scope indicates the scope at which the load balancer
                          is exposed. Possible values are "External" and "Internal".
                        enum:
                        - Internal
                        - External
                        type: string
                    required:
                    - dnsManagementPolicy
                    - scope
                    type: object
                    x-kubernetes-validations:
                    - message: eipAllocations are forbidden when the scope is Internal.
                      rule: '!has(self.scope) || self.scope != ''Internal'' || !has(self.providerParameters)
                        || !has(self.providerParameters.aws) || !has(self.providerParameters.aws.networkLoadBalancer)
                        || !has(self.providerParameters.aws.networkLoadBalancer.eipAllocations)'
                  nodePort:
                    description: nodePort holds parameters for the NodePortService
                      endpoint publishing strategy. Present only if type is NodePortService.
                    properties:
                      protocol:
                        description: "protocol specifies whether the IngressController
                          expects incoming connections to use plain TCP or whether
                          the IngressController expects PROXY protocol. \n PROXY protocol
                          can be used with load balancers that support it to communicate
                          the source addresses of client connections when forwarding
                          those connections to the IngressController.  Using PROXY
                          protocol enables the IngressController to report those source
                          addresses instead of reporting the load balancer's address
                          in HTTP headers and logs.  Note that enabling PROXY protocol
                          on the IngressController will cause connections to fail
                          if you are not using a load balancer that uses PROXY protocol
                          to forward connections to the IngressController.  See http://www.haproxy.org/download/2.2/doc/proxy-protocol.txt
                          for information about PROXY protocol. \n The following values
                          are valid for this field: \n * The empty string. * \"TCP\".
                          * \"PROXY\". \n The empty string specifies the default,
                          which is TCP without PROXY protocol.  Note that the default
                          is subject to change."
                        enum:
                        - ""
                        - TCP
                        - PROXY
                        type: string
                    type: object
                  private:
                    description: private holds parameters for the Private endpoint
                      publishing strategy. Present only if type is Private.
                    properties:
                      protocol:
                        description: "protocol specifies whether the IngressController
                          expects incoming connections to use plain TCP or whether
                          the IngressController expects PROXY protocol. \n PROXY protocol
                          can be used with load balancers that support it to communicate
                          the source addresses of client connections when forwarding
                          those connections to the IngressController.  Using PROXY
                          protocol enables the IngressController to report those source
                          addresses instead of reporting the load balancer's address
                          in HTTP headers and logs.  Note that enabling PROXY protocol
                          on the IngressController will cause connections to fail
                          if you are not using a load balancer that uses PROXY protocol
                          to forward connections to the IngressController.  See http://www.haproxy.org/download/2.2/doc/proxy-protocol.txt
                          for information about PROXY protocol. \n The following values
                          are valid for this field: \n * The empty string. * \"TCP\".
                          * \"PROXY\". \n The empty string specifies the default,
                          which is TCP without PROXY protocol.  Note that the default
                          is subject to change."
                        enum:
                        - ""
                        - TCP
                        - PROXY
                        type: string
                    type: object
                  type:
                    description: "type is the publishing strategy to use. Valid values
                      are: \n * LoadBalancerService \n Publishes the ingress controller
                      using a Kubernetes LoadBalancer Service. \n In this configuration,
                      the ingress controller deployment uses container networking.
                      A LoadBalancer Service is created to publish the deployment.
                      \n See: https://kubernetes.io/docs/concepts/services-networking/service/#loadbalancer
                      \n If domain is set, a wildcard DNS record will be managed to
                      point at the LoadBalancer Service's external name. DNS records
                      are managed only in DNS zones defined by dns.config.openshift.io/cluster
                      .spec.publicZone and .spec.privateZone. \n Wildcard DNS management
                      is currently supported only on the AWS, Azure, and GCP platforms.
                      \n * HostNetwork \n Publishes the ingress controller on node
                      ports where the ingress controller is deployed. \n In this configuration,
                      the ingress controller deployment uses host networking, bound
                      to node ports 80 and 443. The user is responsible for configuring
                      an external load balancer to publish the ingress controller
                      via the node ports. \n * Private \n Does not publish the ingress
                      controller. \n In this configuration, the ingress controller
                      deployment uses container networking, and is not explicitly
                      published. The user must manually publish the ingress controller.
                      \n * NodePortService \n Publishes the ingress controller using
                      a Kubernetes NodePort Service. \n In this configuration, the
                      ingress controller deployment uses container networking. A NodePort
                      Service is created to publish the deployment. The specific node
                      ports are dynamically allocated by OpenShift; however, to support
                      static port allocations, user changes to the node port field
                      of the managed NodePort Service will preserved."
                    enum:
                    - LoadBalancerService
                    - HostNetwork
                    - Private
                    - NodePortService
                    type: string
                required:
                - type
                type: object
              namespaceSelector:
                description: namespaceSelector is the actual namespaceSelector in
                  use.
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: A label selector requirement is a selector that
                        contains values, a key, and an operator that relates the key
                        and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: operator represents a key's relationship to
                            a set of values. Valid operators are In, NotIn, Exists
                            and DoesNotExist.
                          type: string
                        values:
                          description: values is an array of string values. If the
                            operator is In or NotIn, the values array must be non-empty.
                            If the operator is Exists or DoesNotExist, the values
                            array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: matchLabels is a map of {key,value} pairs. A single
                      {key,value} in the matchLabels map is equivalent to an element
                      of matchExpressions, whose key field is "key", the operator
                      is "In", and the values array contains only "value". The requirements
                      are ANDed.
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              observedGeneration:
                description: observedGeneration is the most recent generation observed.
                format: int64
                type: integer
              routeSelector:
                description: routeSelector is the actual routeSelector in use.
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: A label selector requirement is a selector that
                        contains values, a key, and an operator that relates the key
                        and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: operator represents a key's relationship to
                            a set of values. Valid operators are In, NotIn, Exists
                            and DoesNotExist.
                          type: string
                        values:
                          description: values is an array of string values. If the
                            operator is In or NotIn, the values array must be non-empty.
                            If the operator is Exists or DoesNotExist, the values
                            array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: matchLabels is a map of {key,value} pairs. A single
                      {key,value} in the matchLabels map is equivalent to an element
                      of matchExpressions, whose key field is "key", the operator
                      is "In", and the values array contains only "value". The requirements
                      are ANDed.
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              selector:
                description: selector is a label selector, in string format, for ingress
                  controller pods corresponding to the IngressController. The number
                  of matching pods should equal the value of availableReplicas.
                type: string
              tlsProfile:
                description: tlsProfile is the TLS connection configuration that is
                  in effect.
                properties:
                  ciphers:
                    description: "ciphers is used to specify the cipher algorithms
                      that are negotiated during the TLS handshake.  Operators may
                      remove entries their operands do not support.  For example,
                      to use DES-CBC3-SHA  (yaml): \n ciphers: - DES-CBC3-SHA"
                    items:
                      type: string
                    type: array
                    x-kubernetes-list-type: atomic
                  minTLSVersion:
                    description: "minTLSVersion is used to specify the minimal version
                      of the TLS protocol that is negotiated during the TLS handshake.
                      For example, to use TLS versions 1.1, 1.2 and 1.3 (yaml): \n
                      minTLSVersion: VersionTLS11 \n NOTE: currently the highest minTLSVersion
                      allowed is VersionTLS12"
                    enum:
                    - VersionTLS10
                    - VersionTLS11
                    - VersionTLS12
                    - VersionTLS13
                    type: string
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      scale:
        labelSelectorPath: .status.selector
        specReplicasPath: .spec.replicas
        statusReplicasPath: .status.availableReplicas
      status: {}
status:
  acceptedNames:
    kind: IngressController
    listKind: IngressControllerList
    plural: ingresscontrollers
    singular: ingresscontroller
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:25Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:25Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
