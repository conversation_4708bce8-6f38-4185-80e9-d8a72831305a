---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/470
    api.openshift.io/merged-by-featuregates: "true"
    capability.openshift.io/name: Build
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:53:35Z"
  generation: 2
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:35Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:capability.openshift.io/name: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T11:46:54Z"
  name: builds.config.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21327786"
  uid: e510e570-bbf5-4462-b4db-0a2e2bc74a3b
spec:
  conversion:
    strategy: None
  group: config.openshift.io
  names:
    kind: Build
    listKind: BuildList
    plural: builds
    singular: build
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "Build configures the behavior of OpenShift builds for the entire
          cluster. This includes default settings that can be overridden in BuildConfig
          objects, and overrides which are applied to all builds. \n The canonical
          name is \"cluster\" \n Compatibility level 1: Stable within a major release
          for a minimum of 12 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: Spec holds user-settable values for the build controller
              configuration
            properties:
              additionalTrustedCA:
                description: "AdditionalTrustedCA is a reference to a ConfigMap containing
                  additional CAs that should be trusted for image pushes and pulls
                  during builds. The namespace for this config map is openshift-config.
                  \n DEPRECATED: Additional CAs for image pull and push should be
                  set on image.config.openshift.io/cluster instead."
                properties:
                  name:
                    description: name is the metadata.name of the referenced config
                      map
                    type: string
                required:
                - name
                type: object
              buildDefaults:
                description: BuildDefaults controls the default information for Builds
                properties:
                  defaultProxy:
                    description: "DefaultProxy contains the default proxy settings
                      for all build operations, including image pull/push and source
                      download. \n Values can be overrode by setting the `HTTP_PROXY`,
                      `HTTPS_PROXY`, and `NO_PROXY` environment variables in the build
                      config's strategy."
                    properties:
                      httpProxy:
                        description: httpProxy is the URL of the proxy for HTTP requests.  Empty
                          means unset and will not result in an env var.
                        type: string
                      httpsProxy:
                        description: httpsProxy is the URL of the proxy for HTTPS
                          requests.  Empty means unset and will not result in an env
                          var.
                        type: string
                      noProxy:
                        description: noProxy is a comma-separated list of hostnames
                          and/or CIDRs and/or IPs for which the proxy should not be
                          used. Empty means unset and will not result in an env var.
                        type: string
                      readinessEndpoints:
                        description: readinessEndpoints is a list of endpoints used
                          to verify readiness of the proxy.
                        items:
                          type: string
                        type: array
                      trustedCA:
                        description: "trustedCA is a reference to a ConfigMap containing
                          a CA certificate bundle. The trustedCA field should only
                          be consumed by a proxy validator. The validator is responsible
                          for reading the certificate bundle from the required key
                          \"ca-bundle.crt\", merging it with the system default trust
                          bundle, and writing the merged trust bundle to a ConfigMap
                          named \"trusted-ca-bundle\" in the \"openshift-config-managed\"
                          namespace. Clients that expect to make proxy connections
                          must use the trusted-ca-bundle for all HTTPS requests to
                          the proxy, and may use the trusted-ca-bundle for non-proxy
                          HTTPS requests as well. \n The namespace for the ConfigMap
                          referenced by trustedCA is \"openshift-config\". Here is
                          an example ConfigMap (in yaml): \n apiVersion: v1 kind:
                          ConfigMap metadata: name: user-ca-bundle namespace: openshift-config
                          data: ca-bundle.crt: | -----BEGIN CERTIFICATE----- Custom
                          CA certificate bundle. -----END CERTIFICATE-----"
                        properties:
                          name:
                            description: name is the metadata.name of the referenced
                              config map
                            type: string
                        required:
                        - name
                        type: object
                    type: object
                  env:
                    description: Env is a set of default environment variables that
                      will be applied to the build if the specified variables do not
                      exist on the build
                    items:
                      description: EnvVar represents an environment variable present
                        in a Container.
                      properties:
                        name:
                          description: Name of the environment variable. Must be a
                            C_IDENTIFIER.
                          type: string
                        value:
                          description: 'Variable references $(VAR_NAME) are expanded
                            using the previously defined environment variables in
                            the container and any service environment variables. If
                            a variable cannot be resolved, the reference in the input
                            string will be unchanged. Double $$ are reduced to a single
                            $, which allows for escaping the $(VAR_NAME) syntax: i.e.
                            "$$(VAR_NAME)" will produce the string literal "$(VAR_NAME)".
                            Escaped references will never be expanded, regardless
                            of whether the variable exists or not. Defaults to "".'
                          type: string
                        valueFrom:
                          description: Source for the environment variable's value.
                            Cannot be used if value is not empty.
                          properties:
                            configMapKeyRef:
                              description: Selects a key of a ConfigMap.
                              properties:
                                key:
                                  description: The key to select.
                                  type: string
                                name:
                                  default: ""
                                  description: 'Name of the referent. This field is
                                    effectively required, but due to backwards compatibility
                                    is allowed to be empty. Instances of this type
                                    with an empty value here are almost certainly
                                    wrong. TODO: Add other useful fields. apiVersion,
                                    kind, uid? More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                    TODO: Drop `kubebuilder:default` when controller-gen
                                    doesn''t need it https://github.com/kubernetes-sigs/kubebuilder/issues/3896.'
                                  type: string
                                optional:
                                  description: Specify whether the ConfigMap or its
                                    key must be defined
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            fieldRef:
                              description: 'Selects a field of the pod: supports metadata.name,
                                metadata.namespace, `metadata.labels[''<KEY>'']`,
                                `metadata.annotations[''<KEY>'']`, spec.nodeName,
                                spec.serviceAccountName, status.hostIP, status.podIP,
                                status.podIPs.'
                              properties:
                                apiVersion:
                                  description: Version of the schema the FieldPath
                                    is written in terms of, defaults to "v1".
                                  type: string
                                fieldPath:
                                  description: Path of the field to select in the
                                    specified API version.
                                  type: string
                              required:
                              - fieldPath
                              type: object
                              x-kubernetes-map-type: atomic
                            resourceFieldRef:
                              description: 'Selects a resource of the container: only
                                resources limits and requests (limits.cpu, limits.memory,
                                limits.ephemeral-storage, requests.cpu, requests.memory
                                and requests.ephemeral-storage) are currently supported.'
                              properties:
                                containerName:
                                  description: 'Container name: required for volumes,
                                    optional for env vars'
                                  type: string
                                divisor:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  description: Specifies the output format of the
                                    exposed resources, defaults to "1"
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                resource:
                                  description: 'Required: resource to select'
                                  type: string
                              required:
                              - resource
                              type: object
                              x-kubernetes-map-type: atomic
                            secretKeyRef:
                              description: Selects a key of a secret in the pod's
                                namespace
                              properties:
                                key:
                                  description: The key of the secret to select from.  Must
                                    be a valid secret key.
                                  type: string
                                name:
                                  default: ""
                                  description: 'Name of the referent. This field is
                                    effectively required, but due to backwards compatibility
                                    is allowed to be empty. Instances of this type
                                    with an empty value here are almost certainly
                                    wrong. TODO: Add other useful fields. apiVersion,
                                    kind, uid? More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                    TODO: Drop `kubebuilder:default` when controller-gen
                                    doesn''t need it https://github.com/kubernetes-sigs/kubebuilder/issues/3896.'
                                  type: string
                                optional:
                                  description: Specify whether the Secret or its key
                                    must be defined
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                      required:
                      - name
                      type: object
                    type: array
                  gitProxy:
                    description: "GitProxy contains the proxy settings for git operations
                      only. If set, this will override any Proxy settings for all
                      git commands, such as git clone. \n Values that are not set
                      here will be inherited from DefaultProxy."
                    properties:
                      httpProxy:
                        description: httpProxy is the URL of the proxy for HTTP requests.  Empty
                          means unset and will not result in an env var.
                        type: string
                      httpsProxy:
                        description: httpsProxy is the URL of the proxy for HTTPS
                          requests.  Empty means unset and will not result in an env
                          var.
                        type: string
                      noProxy:
                        description: noProxy is a comma-separated list of hostnames
                          and/or CIDRs and/or IPs for which the proxy should not be
                          used. Empty means unset and will not result in an env var.
                        type: string
                      readinessEndpoints:
                        description: readinessEndpoints is a list of endpoints used
                          to verify readiness of the proxy.
                        items:
                          type: string
                        type: array
                      trustedCA:
                        description: "trustedCA is a reference to a ConfigMap containing
                          a CA certificate bundle. The trustedCA field should only
                          be consumed by a proxy validator. The validator is responsible
                          for reading the certificate bundle from the required key
                          \"ca-bundle.crt\", merging it with the system default trust
                          bundle, and writing the merged trust bundle to a ConfigMap
                          named \"trusted-ca-bundle\" in the \"openshift-config-managed\"
                          namespace. Clients that expect to make proxy connections
                          must use the trusted-ca-bundle for all HTTPS requests to
                          the proxy, and may use the trusted-ca-bundle for non-proxy
                          HTTPS requests as well. \n The namespace for the ConfigMap
                          referenced by trustedCA is \"openshift-config\". Here is
                          an example ConfigMap (in yaml): \n apiVersion: v1 kind:
                          ConfigMap metadata: name: user-ca-bundle namespace: openshift-config
                          data: ca-bundle.crt: | -----BEGIN CERTIFICATE----- Custom
                          CA certificate bundle. -----END CERTIFICATE-----"
                        properties:
                          name:
                            description: name is the metadata.name of the referenced
                              config map
                            type: string
                        required:
                        - name
                        type: object
                    type: object
                  imageLabels:
                    description: ImageLabels is a list of docker labels that are applied
                      to the resulting image. User can override a default label by
                      providing a label with the same name in their Build/BuildConfig.
                    items:
                      properties:
                        name:
                          description: Name defines the name of the label. It must
                            have non-zero length.
                          type: string
                        value:
                          description: Value defines the literal value of the label.
                          type: string
                      type: object
                    type: array
                  resources:
                    description: Resources defines resource requirements to execute
                      the build.
                    properties:
                      claims:
                        description: "Claims lists the names of resources, defined
                          in spec.resourceClaims, that are used by this container.
                          \n This is an alpha field and requires enabling the DynamicResourceAllocation
                          feature gate. \n This field is immutable. It can only be
                          set for containers."
                        items:
                          description: ResourceClaim references one entry in PodSpec.ResourceClaims.
                          properties:
                            name:
                              description: Name must match the name of one entry in
                                pod.spec.resourceClaims of the Pod where this field
                                is used. It makes that resource available inside a
                                container.
                              type: string
                          required:
                          - name
                          type: object
                        type: array
                        x-kubernetes-list-map-keys:
                        - name
                        x-kubernetes-list-type: map
                      limits:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        description: 'Limits describes the maximum amount of compute
                          resources allowed. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/'
                        type: object
                      requests:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        description: 'Requests describes the minimum amount of compute
                          resources required. If Requests is omitted for a container,
                          it defaults to Limits if that is explicitly specified, otherwise
                          to an implementation-defined value. Requests cannot exceed
                          Limits. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/'
                        type: object
                    type: object
                type: object
              buildOverrides:
                description: BuildOverrides controls override settings for builds
                properties:
                  forcePull:
                    description: ForcePull overrides, if set, the equivalent value
                      in the builds, i.e. false disables force pull for all builds,
                      true enables force pull for all builds, independently of what
                      each build specifies itself
                    type: boolean
                  imageLabels:
                    description: ImageLabels is a list of docker labels that are applied
                      to the resulting image. If user provided a label in their Build/BuildConfig
                      with the same name as one in this list, the user's label will
                      be overwritten.
                    items:
                      properties:
                        name:
                          description: Name defines the name of the label. It must
                            have non-zero length.
                          type: string
                        value:
                          description: Value defines the literal value of the label.
                          type: string
                      type: object
                    type: array
                  nodeSelector:
                    additionalProperties:
                      type: string
                    description: NodeSelector is a selector which must be true for
                      the build pod to fit on a node
                    type: object
                  tolerations:
                    description: Tolerations is a list of Tolerations that will override
                      any existing tolerations set on a build pod.
                    items:
                      description: The pod this Toleration is attached to tolerates
                        any taint that matches the triple <key,value,effect> using
                        the matching operator <operator>.
                      properties:
                        effect:
                          description: Effect indicates the taint effect to match.
                            Empty means match all taint effects. When specified, allowed
                            values are NoSchedule, PreferNoSchedule and NoExecute.
                          type: string
                        key:
                          description: Key is the taint key that the toleration applies
                            to. Empty means match all taint keys. If the key is empty,
                            operator must be Exists; this combination means to match
                            all values and all keys.
                          type: string
                        operator:
                          description: Operator represents a key's relationship to
                            the value. Valid operators are Exists and Equal. Defaults
                            to Equal. Exists is equivalent to wildcard for value,
                            so that a pod can tolerate all taints of a particular
                            category.
                          type: string
                        tolerationSeconds:
                          description: TolerationSeconds represents the period of
                            time the toleration (which must be of effect NoExecute,
                            otherwise this field is ignored) tolerates the taint.
                            By default, it is not set, which means tolerate the taint
                            forever (do not evict). Zero and negative values will
                            be treated as 0 (evict immediately) by the system.
                          format: int64
                          type: integer
                        value:
                          description: Value is the taint value the toleration matches
                            to. If the operator is Exists, the value should be empty,
                            otherwise just a regular string.
                          type: string
                      type: object
                    type: array
                type: object
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: Build
    listKind: BuildList
    plural: builds
    singular: build
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:35Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:35Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
