---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/470
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    release.openshift.io/bootstrap-required: "true"
    release.openshift.io/feature-set: Default
  creationTimestamp: "2025-06-25T14:52:57Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-bootstrap
    operation: Update
    time: "2025-06-25T14:52:57Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:52:57Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:api.openshift.io/merged-by-featuregates: {}
          f:release.openshift.io/bootstrap-required: {}
          f:release.openshift.io/feature-set: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:19:47Z"
  name: schedulers.config.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "133603"
  uid: 68ee141b-0a98-4f27-b940-876fea98be91
spec:
  conversion:
    strategy: None
  group: config.openshift.io
  names:
    kind: Scheduler
    listKind: SchedulerList
    plural: schedulers
    singular: scheduler
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "Scheduler holds cluster-wide config information to run the Kubernetes
          Scheduler and influence its placement decisions. The canonical name for
          this config is `cluster`. \n Compatibility level 1: Stable within a major
          release for a minimum of 12 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: spec holds user settable values for configuration
            properties:
              defaultNodeSelector:
                description: 'defaultNodeSelector helps set the cluster-wide default
                  node selector to restrict pod placement to specific nodes. This
                  is applied to the pods created in all namespaces and creates an
                  intersection with any existing nodeSelectors already set on a pod,
                  additionally constraining that pod''s selector. For example, defaultNodeSelector:
                  "type=user-node,region=east" would set nodeSelector field in pod
                  spec to "type=user-node,region=east" to all pods created in all
                  namespaces. Namespaces having project-wide node selectors won''t
                  be impacted even if this field is set. This adds an annotation section
                  to the namespace. For example, if a new namespace is created with
                  node-selector=''type=user-node,region=east'', the annotation openshift.io/node-selector:
                  type=user-node,region=east gets added to the project. When the openshift.io/node-selector
                  annotation is set on the project the value is used in preference
                  to the value we are setting for defaultNodeSelector field. For instance,
                  openshift.io/node-selector: "type=user-node,region=west" means that
                  the default of "type=user-node,region=east" set in defaultNodeSelector
                  would not be applied.'
                type: string
              mastersSchedulable:
                description: 'MastersSchedulable allows masters nodes to be schedulable.
                  When this flag is turned on, all the master nodes in the cluster
                  will be made schedulable, so that workload pods can run on them.
                  The default value for this field is false, meaning none of the master
                  nodes are schedulable. Important Note: Once the workload pods start
                  running on the master nodes, extreme care must be taken to ensure
                  that cluster-critical control plane components are not impacted.
                  Please turn on this field after doing due diligence.'
                type: boolean
              policy:
                description: 'DEPRECATED: the scheduler Policy API has been deprecated
                  and will be removed in a future release. policy is a reference to
                  a ConfigMap containing scheduler policy which has user specified
                  predicates and priorities. If this ConfigMap is not available scheduler
                  will default to use DefaultAlgorithmProvider. The namespace for
                  this configmap is openshift-config.'
                properties:
                  name:
                    description: name is the metadata.name of the referenced config
                      map
                    type: string
                required:
                - name
                type: object
              profile:
                description: "profile sets which scheduling profile should be set
                  in order to configure scheduling decisions for new pods. \n Valid
                  values are \"LowNodeUtilization\", \"HighNodeUtilization\", \"NoScoring\"
                  Defaults to \"LowNodeUtilization\""
                enum:
                - ""
                - LowNodeUtilization
                - HighNodeUtilization
                - NoScoring
                type: string
            type: object
          status:
            description: status holds observed values from the cluster. They may not
              be overridden.
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: Scheduler
    listKind: SchedulerList
    plural: schedulers
    singular: scheduler
  conditions:
  - lastTransitionTime: "2025-06-25T14:52:57Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:52:57Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
