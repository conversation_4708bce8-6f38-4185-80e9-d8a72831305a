---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/513
    description: Extension for configuring openshif samples operator.
    displayName: ConfigsSamples
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:53:38Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:description: {}
          f:displayName: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T14:53:38Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:38Z"
  name: configs.samples.operator.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "848"
  uid: 258c261d-c118-4087-98d1-6faeaca01e17
spec:
  conversion:
    strategy: None
  group: samples.operator.openshift.io
  names:
    kind: Config
    listKind: ConfigList
    plural: configs
    singular: config
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "Config contains the configuration and detailed condition status
          for the Samples Operator. \n Compatibility level 1: Stable within a major
          release for a minimum of 12 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: ConfigSpec contains the desired configuration and state for
              the Samples Operator, controlling various behavior around the imagestreams
              and templates it creates/updates in the openshift namespace.
            properties:
              architectures:
                description: architectures determine which hardware architecture(s)
                  to install, where x86_64, ppc64le, and s390x are the only supported
                  choices currently.
                items:
                  type: string
                type: array
              managementState:
                description: managementState is top level on/off type of switch for
                  all operators. When "Managed", this operator processes config and
                  manipulates the samples accordingly. When "Unmanaged", this operator
                  ignores any updates to the resources it watches. When "Removed",
                  it reacts that same wasy as it does if the Config object is deleted,
                  meaning any ImageStreams or Templates it manages (i.e. it honors
                  the skipped lists) and the registry secret are deleted, along with
                  the ConfigMap in the operator's namespace that represents the last
                  config used to manipulate the samples,
                pattern: ^(Managed|Unmanaged|Force|Removed)$
                type: string
              samplesRegistry:
                description: samplesRegistry allows for the specification of which
                  registry is accessed by the ImageStreams for their image content.  Defaults
                  on the content in https://github.com/openshift/library that are
                  pulled into this github repository, but based on our pulling only
                  ocp content it typically defaults to registry.redhat.io.
                type: string
              skippedImagestreams:
                description: skippedImagestreams specifies names of image streams
                  that should NOT be created/updated.  Admins can use this to allow
                  them to delete content they don’t want.  They will still have to
                  manually delete the content but the operator will not recreate(or
                  update) anything listed here.
                items:
                  type: string
                type: array
              skippedTemplates:
                description: skippedTemplates specifies names of templates that should
                  NOT be created/updated.  Admins can use this to allow them to delete
                  content they don’t want.  They will still have to manually delete
                  the content but the operator will not recreate(or update) anything
                  listed here.
                items:
                  type: string
                type: array
            type: object
          status:
            description: ConfigStatus contains the actual configuration in effect,
              as well as various details that describe the state of the Samples Operator.
            properties:
              architectures:
                description: architectures determine which hardware architecture(s)
                  to install, where x86_64 and ppc64le are the supported choices.
                items:
                  type: string
                type: array
              conditions:
                description: conditions represents the available maintenance status
                  of the sample imagestreams and templates.
                items:
                  description: ConfigCondition captures various conditions of the
                    Config as entries are processed.
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the last time the condition
                        transitioned from one status to another.
                      format: date-time
                      type: string
                    lastUpdateTime:
                      description: lastUpdateTime is the last time this condition
                        was updated.
                      format: date-time
                      type: string
                    message:
                      description: message is a human readable message indicating
                        details about the transition.
                      type: string
                    reason:
                      description: reason is what caused the condition's last transition.
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      type: string
                    type:
                      description: type of condition.
                      type: string
                  required:
                  - status
                  - type
                  type: object
                type: array
              managementState:
                description: managementState reflects the current operational status
                  of the on/off switch for the operator.  This operator compares the
                  ManagementState as part of determining that we are turning the operator
                  back on (i.e. "Managed") when it was previously "Unmanaged".
                pattern: ^(Managed|Unmanaged|Force|Removed)$
                type: string
              samplesRegistry:
                description: samplesRegistry allows for the specification of which
                  registry is accessed by the ImageStreams for their image content.  Defaults
                  on the content in https://github.com/openshift/library that are
                  pulled into this github repository, but based on our pulling only
                  ocp content it typically defaults to registry.redhat.io.
                type: string
              skippedImagestreams:
                description: skippedImagestreams specifies names of image streams
                  that should NOT be created/updated.  Admins can use this to allow
                  them to delete content they don’t want.  They will still have to
                  manually delete the content but the operator will not recreate(or
                  update) anything listed here.
                items:
                  type: string
                type: array
              skippedTemplates:
                description: skippedTemplates specifies names of templates that should
                  NOT be created/updated.  Admins can use this to allow them to delete
                  content they don’t want.  They will still have to manually delete
                  the content but the operator will not recreate(or update) anything
                  listed here.
                items:
                  type: string
                type: array
              version:
                description: version is the value of the operator's payload based
                  version indicator when it was last successfully processed
                type: string
            type: object
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: Config
    listKind: ConfigList
    plural: configs
    singular: config
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:38Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:38Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
