---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/1453
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:53:55Z"
  generation: 3
  labels:
    openshift.io/operator-managed: ""
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:56Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:labels:
          .: {}
          f:openshift.io/operator-managed: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T12:25:54Z"
  name: kubeletconfigs.machineconfiguration.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21360000"
  uid: 219c1d6f-ba82-42e0-9bcb-87bb87b6799b
spec:
  conversion:
    strategy: None
  group: machineconfiguration.openshift.io
  names:
    kind: KubeletConfig
    listKind: KubeletConfigList
    plural: kubeletconfigs
    singular: kubeletconfig
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "KubeletConfig describes a customized Kubelet configuration.
          \n Compatibility level 1: Stable within a major release for a minimum of
          12 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: KubeletConfigSpec defines the desired state of KubeletConfig
            properties:
              autoSizingReserved:
                type: boolean
              kubeletConfig:
                description: kubeletConfig fields are defined in kubernetes upstream.
                  Please refer to the types defined in the version/commit used by
                  OpenShift of the upstream kubernetes. It's important to note that,
                  since the fields of the kubelet configuration are directly fetched
                  from upstream the validation of those values is handled directly
                  by the kubelet. Please refer to the upstream version of the relevant
                  kubernetes for the valid values of these fields. Invalid values
                  of the kubelet configuration fields may render cluster nodes unusable.
                type: object
                x-kubernetes-preserve-unknown-fields: true
              logLevel:
                format: int32
                type: integer
              machineConfigPoolSelector:
                description: MachineConfigPoolSelector selects which pools the KubeletConfig
                  shoud apply to. A nil selector will result in no pools being selected.
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: A label selector requirement is a selector that
                        contains values, a key, and an operator that relates the key
                        and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: operator represents a key's relationship to
                            a set of values. Valid operators are In, NotIn, Exists
                            and DoesNotExist.
                          type: string
                        values:
                          description: values is an array of string values. If the
                            operator is In or NotIn, the values array must be non-empty.
                            If the operator is Exists or DoesNotExist, the values
                            array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: matchLabels is a map of {key,value} pairs. A single
                      {key,value} in the matchLabels map is equivalent to an element
                      of matchExpressions, whose key field is "key", the operator
                      is "In", and the values array contains only "value". The requirements
                      are ANDed.
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              tlsSecurityProfile:
                description: If unset, the default is based on the apiservers.config.openshift.io/cluster
                  resource. Note that only Old and Intermediate profiles are currently
                  supported, and the maximum available minTLSVersion is VersionTLS12.
                properties:
                  custom:
                    description: "custom is a user-defined TLS security profile. Be
                      extremely careful using a custom profile as invalid configurations
                      can be catastrophic. An example custom profile looks like this:
                      \n ciphers: \n - ECDHE-ECDSA-CHACHA20-POLY1305 \n - ECDHE-RSA-CHACHA20-POLY1305
                      \n - ECDHE-RSA-AES128-GCM-SHA256 \n - ECDHE-ECDSA-AES128-GCM-SHA256
                      \n minTLSVersion: VersionTLS11"
                    nullable: true
                    properties:
                      ciphers:
                        description: "ciphers is used to specify the cipher algorithms
                          that are negotiated during the TLS handshake.  Operators
                          may remove entries their operands do not support.  For example,
                          to use DES-CBC3-SHA  (yaml): \n ciphers: - DES-CBC3-SHA"
                        items:
                          type: string
                        type: array
                        x-kubernetes-list-type: atomic
                      minTLSVersion:
                        description: "minTLSVersion is used to specify the minimal
                          version of the TLS protocol that is negotiated during the
                          TLS handshake. For example, to use TLS versions 1.1, 1.2
                          and 1.3 (yaml): \n minTLSVersion: VersionTLS11 \n NOTE:
                          currently the highest minTLSVersion allowed is VersionTLS12"
                        enum:
                        - VersionTLS10
                        - VersionTLS11
                        - VersionTLS12
                        - VersionTLS13
                        type: string
                    type: object
                  intermediate:
                    description: "intermediate is a TLS security profile based on:
                      \n https://wiki.mozilla.org/Security/Server_Side_TLS#Intermediate_compatibility_.28recommended.29
                      \n and looks like this (yaml): \n ciphers: \n - TLS_AES_128_GCM_SHA256
                      \n - TLS_AES_256_GCM_SHA384 \n - TLS_CHACHA20_POLY1305_SHA256
                      \n - ECDHE-ECDSA-AES128-GCM-SHA256 \n - ECDHE-RSA-AES128-GCM-SHA256
                      \n - ECDHE-ECDSA-AES256-GCM-SHA384 \n - ECDHE-RSA-AES256-GCM-SHA384
                      \n - ECDHE-ECDSA-CHACHA20-POLY1305 \n - ECDHE-RSA-CHACHA20-POLY1305
                      \n - DHE-RSA-AES128-GCM-SHA256 \n - DHE-RSA-AES256-GCM-SHA384
                      \n minTLSVersion: VersionTLS12"
                    nullable: true
                    type: object
                  modern:
                    description: "modern is a TLS security profile based on: \n https://wiki.mozilla.org/Security/Server_Side_TLS#Modern_compatibility
                      \n and looks like this (yaml): \n ciphers: \n - TLS_AES_128_GCM_SHA256
                      \n - TLS_AES_256_GCM_SHA384 \n - TLS_CHACHA20_POLY1305_SHA256
                      \n minTLSVersion: VersionTLS13"
                    nullable: true
                    type: object
                  old:
                    description: "old is a TLS security profile based on: \n https://wiki.mozilla.org/Security/Server_Side_TLS#Old_backward_compatibility
                      \n and looks like this (yaml): \n ciphers: \n - TLS_AES_128_GCM_SHA256
                      \n - TLS_AES_256_GCM_SHA384 \n - TLS_CHACHA20_POLY1305_SHA256
                      \n - ECDHE-ECDSA-AES128-GCM-SHA256 \n - ECDHE-RSA-AES128-GCM-SHA256
                      \n - ECDHE-ECDSA-AES256-GCM-SHA384 \n - ECDHE-RSA-AES256-GCM-SHA384
                      \n - ECDHE-ECDSA-CHACHA20-POLY1305 \n - ECDHE-RSA-CHACHA20-POLY1305
                      \n - DHE-RSA-AES128-GCM-SHA256 \n - DHE-RSA-AES256-GCM-SHA384
                      \n - DHE-RSA-CHACHA20-POLY1305 \n - ECDHE-ECDSA-AES128-SHA256
                      \n - ECDHE-RSA-AES128-SHA256 \n - ECDHE-ECDSA-AES128-SHA \n
                      - ECDHE-RSA-AES128-SHA \n - ECDHE-ECDSA-AES256-SHA384 \n - ECDHE-RSA-AES256-SHA384
                      \n - ECDHE-ECDSA-AES256-SHA \n - ECDHE-RSA-AES256-SHA \n - DHE-RSA-AES128-SHA256
                      \n - DHE-RSA-AES256-SHA256 \n - AES128-GCM-SHA256 \n - AES256-GCM-SHA384
                      \n - AES128-SHA256 \n - AES256-SHA256 \n - AES128-SHA \n - AES256-SHA
                      \n - DES-CBC3-SHA \n minTLSVersion: VersionTLS10"
                    nullable: true
                    type: object
                  type:
                    description: "type is one of Old, Intermediate, Modern or Custom.
                      Custom provides the ability to specify individual TLS security
                      profile parameters. Old, Intermediate and Modern are TLS security
                      profiles based on: \n https://wiki.mozilla.org/Security/Server_Side_TLS#Recommended_configurations
                      \n The profiles are intent based, so they may change over time
                      as new ciphers are developed and existing ciphers are found
                      to be insecure.  Depending on precisely which ciphers are available
                      to a process, the list may be reduced. \n Note that the Modern
                      profile is currently not supported because it is not yet well
                      adopted by common software libraries."
                    enum:
                    - Old
                    - Intermediate
                    - Modern
                    - Custom
                    type: string
                type: object
            type: object
          status:
            description: KubeletConfigStatus defines the observed state of a KubeletConfig
            properties:
              conditions:
                description: conditions represents the latest available observations
                  of current state.
                items:
                  description: KubeletConfigCondition defines the state of the KubeletConfig
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the time of the last update
                        to the current status object.
                      format: date-time
                      nullable: true
                      type: string
                    message:
                      description: message provides additional information about the
                        current condition. This is only to be consumed by humans.
                      type: string
                    reason:
                      description: reason is the reason for the condition's last transition.  Reasons
                        are PascalCase
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      type: string
                    type:
                      description: type specifies the state of the operator's reconciliation
                        functionality.
                      type: string
                  type: object
                type: array
              observedGeneration:
                description: observedGeneration represents the generation observed
                  by the controller.
                format: int64
                type: integer
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: KubeletConfig
    listKind: KubeletConfigList
    plural: kubeletconfigs
    singular: kubeletconfig
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:56Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:56Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
