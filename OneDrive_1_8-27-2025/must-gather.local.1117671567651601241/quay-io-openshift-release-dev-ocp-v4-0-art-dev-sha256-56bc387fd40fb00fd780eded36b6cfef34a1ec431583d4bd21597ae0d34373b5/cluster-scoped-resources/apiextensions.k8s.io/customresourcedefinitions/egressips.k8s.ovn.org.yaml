---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  creationTimestamp: "2025-06-25T14:57:16Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:ownerReferences:
          k:{"uid":"e8b7620a-d5d2-448e-b2bf-6f6768b9b1dc"}: {}
      f:spec:
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-network-operator/operconfig
    operation: Apply
    time: "2025-06-25T14:57:16Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:57:16Z"
  name: egressips.k8s.ovn.org
  ownerReferences:
  - apiVersion: operator.openshift.io/v1
    blockOwnerDeletion: true
    controller: true
    kind: Network
    name: cluster
    uid: e8b7620a-d5d2-448e-b2bf-6f6768b9b1dc
  resourceVersion: "3886"
  uid: 52072c58-61bf-4166-a330-f5f2c28de1ec
spec:
  conversion:
    strategy: None
  group: k8s.ovn.org
  names:
    kind: EgressIP
    listKind: EgressIPList
    plural: egressips
    shortNames:
    - eip
    singular: egressip
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.egressIPs[*]
      name: EgressIPs
      type: string
    - jsonPath: .status.items[*].node
      name: Assigned Node
      type: string
    - jsonPath: .status.items[*].egressIP
      name: Assigned EgressIPs
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: EgressIP is a CRD allowing the user to define a fixed source
          IP for all egress traffic originating from any pods which match the EgressIP
          resource according to its spec definition.
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: Specification of the desired behavior of EgressIP.
            properties:
              egressIPs:
                description: EgressIPs is the list of egress IP addresses requested.
                  Can be IPv4 and/or IPv6. This field is mandatory.
                items:
                  type: string
                type: array
              namespaceSelector:
                description: NamespaceSelector applies the egress IP only to the namespace(s)
                  whose label matches this definition. This field is mandatory.
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: A label selector requirement is a selector that
                        contains values, a key, and an operator that relates the key
                        and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: operator represents a key's relationship to
                            a set of values. Valid operators are In, NotIn, Exists
                            and DoesNotExist.
                          type: string
                        values:
                          description: values is an array of string values. If the
                            operator is In or NotIn, the values array must be non-empty.
                            If the operator is Exists or DoesNotExist, the values
                            array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: matchLabels is a map of {key,value} pairs. A single
                      {key,value} in the matchLabels map is equivalent to an element
                      of matchExpressions, whose key field is "key", the operator
                      is "In", and the values array contains only "value". The requirements
                      are ANDed.
                    type: object
                type: object
              podSelector:
                description: 'PodSelector applies the egress IP only to the pods whose
                  label matches this definition. This field is optional, and in case
                  it is not set: results in the egress IP being applied to all pods
                  in the namespace(s) matched by the NamespaceSelector. In case it
                  is set: is intersected with the NamespaceSelector, thus applying
                  the egress IP to the pods (in the namespace(s) already matched by
                  the NamespaceSelector) which match this pod selector.'
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: A label selector requirement is a selector that
                        contains values, a key, and an operator that relates the key
                        and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: operator represents a key's relationship to
                            a set of values. Valid operators are In, NotIn, Exists
                            and DoesNotExist.
                          type: string
                        values:
                          description: values is an array of string values. If the
                            operator is In or NotIn, the values array must be non-empty.
                            If the operator is Exists or DoesNotExist, the values
                            array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: matchLabels is a map of {key,value} pairs. A single
                      {key,value} in the matchLabels map is equivalent to an element
                      of matchExpressions, whose key field is "key", the operator
                      is "In", and the values array contains only "value". The requirements
                      are ANDed.
                    type: object
                type: object
            required:
            - egressIPs
            - namespaceSelector
            type: object
          status:
            description: Observed status of EgressIP. Read-only.
            properties:
              items:
                description: The list of assigned egress IPs and their corresponding
                  node assignment.
                items:
                  description: The per node status, for those egress IPs who have
                    been assigned.
                  properties:
                    egressIP:
                      description: Assigned egress IP
                      type: string
                    node:
                      description: Assigned node name
                      type: string
                  required:
                  - egressIP
                  - node
                  type: object
                type: array
            required:
            - items
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
status:
  acceptedNames:
    kind: EgressIP
    listKind: EgressIPList
    plural: egressips
    shortNames:
    - eip
    singular: egressip
  conditions:
  - lastTransitionTime: "2025-06-25T14:57:16Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:57:16Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
