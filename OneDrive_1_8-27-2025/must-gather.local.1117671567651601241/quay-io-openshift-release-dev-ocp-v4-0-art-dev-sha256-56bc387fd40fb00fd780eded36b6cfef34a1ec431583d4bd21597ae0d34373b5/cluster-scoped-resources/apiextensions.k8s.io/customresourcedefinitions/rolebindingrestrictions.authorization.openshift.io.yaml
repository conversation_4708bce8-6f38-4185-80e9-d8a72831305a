---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/470
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    release.openshift.io/bootstrap-required: "true"
  creationTimestamp: "2025-06-25T14:52:47Z"
  generation: 2
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
    manager: cluster-bootstrap
    operation: Update
    time: "2025-06-25T14:52:47Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:52:50Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:api.openshift.io/merged-by-featuregates: {}
          f:release.openshift.io/bootstrap-required: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T11:46:54Z"
  name: rolebindingrestrictions.authorization.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "********"
  uid: 555a8db6-e874-4027-9590-885f1a1170aa
spec:
  conversion:
    strategy: None
  group: authorization.openshift.io
  names:
    kind: RoleBindingRestriction
    listKind: RoleBindingRestrictionList
    plural: rolebindingrestrictions
    singular: rolebindingrestriction
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "RoleBindingRestriction is an object that can be matched against
          a subject (user, group, or service account) to determine whether rolebindings
          on that subject are allowed in the namespace to which the RoleBindingRestriction
          belongs.  If any one of those RoleBindingRestriction objects matches a subject,
          rolebindings on that subject in the namespace are allowed. \n Compatibility
          level 1: Stable within a major release for a minimum of 12 months or 3 minor
          releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: Spec defines the matcher.
            properties:
              grouprestriction:
                description: GroupRestriction matches against group subjects.
                nullable: true
                properties:
                  groups:
                    description: Groups is a list of groups used to match against
                      an individual user's groups. If the user is a member of one
                      of the whitelisted groups, the user is allowed to be bound to
                      a role.
                    items:
                      type: string
                    nullable: true
                    type: array
                  labels:
                    description: Selectors specifies a list of label selectors over
                      group labels.
                    items:
                      description: A label selector is a label query over a set of
                        resources. The result of matchLabels and matchExpressions
                        are ANDed. An empty label selector matches all objects. A
                        null label selector matches no objects.
                      properties:
                        matchExpressions:
                          description: matchExpressions is a list of label selector
                            requirements. The requirements are ANDed.
                          items:
                            description: A label selector requirement is a selector
                              that contains values, a key, and an operator that relates
                              the key and values.
                            properties:
                              key:
                                description: key is the label key that the selector
                                  applies to.
                                type: string
                              operator:
                                description: operator represents a key's relationship
                                  to a set of values. Valid operators are In, NotIn,
                                  Exists and DoesNotExist.
                                type: string
                              values:
                                description: values is an array of string values.
                                  If the operator is In or NotIn, the values array
                                  must be non-empty. If the operator is Exists or
                                  DoesNotExist, the values array must be empty. This
                                  array is replaced during a strategic merge patch.
                                items:
                                  type: string
                                type: array
                                x-kubernetes-list-type: atomic
                            required:
                            - key
                            - operator
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                        matchLabels:
                          additionalProperties:
                            type: string
                          description: matchLabels is a map of {key,value} pairs.
                            A single {key,value} in the matchLabels map is equivalent
                            to an element of matchExpressions, whose key field is
                            "key", the operator is "In", and the values array contains
                            only "value". The requirements are ANDed.
                          type: object
                      type: object
                      x-kubernetes-map-type: atomic
                    nullable: true
                    type: array
                type: object
              serviceaccountrestriction:
                description: ServiceAccountRestriction matches against service-account
                  subjects.
                nullable: true
                properties:
                  namespaces:
                    description: Namespaces specifies a list of literal namespace
                      names.
                    items:
                      type: string
                    type: array
                  serviceaccounts:
                    description: ServiceAccounts specifies a list of literal service-account
                      names.
                    items:
                      description: ServiceAccountReference specifies a service account
                        and namespace by their names.
                      properties:
                        name:
                          description: Name is the name of the service account.
                          type: string
                        namespace:
                          description: Namespace is the namespace of the service account.  Service
                            accounts from inside the whitelisted namespaces are allowed
                            to be bound to roles.  If Namespace is empty, then the
                            namespace of the RoleBindingRestriction in which the ServiceAccountReference
                            is embedded is used.
                          type: string
                      type: object
                    type: array
                type: object
              userrestriction:
                description: UserRestriction matches against user subjects.
                nullable: true
                properties:
                  groups:
                    description: Groups specifies a list of literal group names.
                    items:
                      type: string
                    nullable: true
                    type: array
                  labels:
                    description: Selectors specifies a list of label selectors over
                      user labels.
                    items:
                      description: A label selector is a label query over a set of
                        resources. The result of matchLabels and matchExpressions
                        are ANDed. An empty label selector matches all objects. A
                        null label selector matches no objects.
                      properties:
                        matchExpressions:
                          description: matchExpressions is a list of label selector
                            requirements. The requirements are ANDed.
                          items:
                            description: A label selector requirement is a selector
                              that contains values, a key, and an operator that relates
                              the key and values.
                            properties:
                              key:
                                description: key is the label key that the selector
                                  applies to.
                                type: string
                              operator:
                                description: operator represents a key's relationship
                                  to a set of values. Valid operators are In, NotIn,
                                  Exists and DoesNotExist.
                                type: string
                              values:
                                description: values is an array of string values.
                                  If the operator is In or NotIn, the values array
                                  must be non-empty. If the operator is Exists or
                                  DoesNotExist, the values array must be empty. This
                                  array is replaced during a strategic merge patch.
                                items:
                                  type: string
                                type: array
                                x-kubernetes-list-type: atomic
                            required:
                            - key
                            - operator
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                        matchLabels:
                          additionalProperties:
                            type: string
                          description: matchLabels is a map of {key,value} pairs.
                            A single {key,value} in the matchLabels map is equivalent
                            to an element of matchExpressions, whose key field is
                            "key", the operator is "In", and the values array contains
                            only "value". The requirements are ANDed.
                          type: object
                      type: object
                      x-kubernetes-map-type: atomic
                    nullable: true
                    type: array
                  users:
                    description: Users specifies a list of literal user names.
                    items:
                      type: string
                    type: array
                type: object
            type: object
        type: object
    served: true
    storage: true
status:
  acceptedNames:
    kind: RoleBindingRestriction
    listKind: RoleBindingRestrictionList
    plural: rolebindingrestrictions
    singular: rolebindingrestriction
  conditions:
  - lastTransitionTime: "2025-06-25T14:52:47Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:52:50Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
