---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/701
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:54:02Z"
  generation: 3
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:54:02Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T12:09:03Z"
  name: clustercsidrivers.operator.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21340601"
  uid: 3d806707-091e-45ff-9abb-34da0cbd5ce4
spec:
  conversion:
    strategy: None
  group: operator.openshift.io
  names:
    kind: ClusterCSIDriver
    listKind: ClusterCSIDriverList
    plural: clustercsidrivers
    singular: clustercsidriver
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "ClusterCSIDriver object allows management and configuration
          of a CSI driver operator installed by default in OpenShift. Name of the
          object must be name of the CSI driver it operates. See CSIDriverName type
          for list of allowed values. \n Compatibility level 1: Stable within a major
          release for a minimum of 12 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            properties:
              name:
                enum:
                - ebs.csi.aws.com
                - efs.csi.aws.com
                - disk.csi.azure.com
                - file.csi.azure.com
                - filestore.csi.storage.gke.io
                - pd.csi.storage.gke.io
                - cinder.csi.openstack.org
                - csi.vsphere.vmware.com
                - manila.csi.openstack.org
                - csi.ovirt.org
                - csi.kubevirt.io
                - csi.sharedresource.openshift.io
                - diskplugin.csi.alibabacloud.com
                - vpc.block.csi.ibm.io
                - powervs.csi.ibm.com
                - secrets-store.csi.k8s.io
                - smb.csi.k8s.io
                type: string
            type: object
          spec:
            description: spec holds user settable values for configuration
            properties:
              driverConfig:
                description: driverConfig can be used to specify platform specific
                  driver configuration. When omitted, this means no opinion and the
                  platform is left to choose reasonable defaults. These defaults are
                  subject to change over time.
                properties:
                  aws:
                    description: aws is used to configure the AWS CSI driver.
                    properties:
                      efsVolumeMetrics:
                        description: efsVolumeMetrics sets the configuration for collecting
                          metrics from EFS volumes used by the EFS CSI Driver.
                        properties:
                          recursiveWalk:
                            description: recursiveWalk provides additional configuration
                              for collecting volume metrics in the AWS EFS CSI Driver
                              when the state is set to RecursiveWalk.
                            properties:
                              fsRateLimit:
                                description: fsRateLimit defines the rate limit, in
                                  goroutines per file system, for processing volume
                                  metrics. When omitted, this means no opinion and
                                  the platform is left to choose a reasonable default,
                                  which is subject to change over time. The current
                                  default is 5. The valid range is from 1 to 100 goroutines.
                                format: int32
                                maximum: 100
                                minimum: 1
                                type: integer
                              refreshPeriodMinutes:
                                description: refreshPeriodMinutes specifies the frequency,
                                  in minutes, at which volume metrics are refreshed.
                                  When omitted, this means no opinion and the platform
                                  is left to choose a reasonable default, which is
                                  subject to change over time. The current default
                                  is 240. The valid range is from 1 to 43200 minutes
                                  (30 days).
                                format: int32
                                maximum: 43200
                                minimum: 1
                                type: integer
                            type: object
                          state:
                            description: 'state defines the state of metric collection
                              in the AWS EFS CSI Driver. This field is required and
                              must be set to one of the following values: Disabled
                              or RecursiveWalk. Disabled means no metrics collection
                              will be performed. This is the default value. RecursiveWalk
                              means the AWS EFS CSI Driver will recursively scan volumes
                              to collect metrics. This process may result in high
                              CPU and memory usage, depending on the volume size.'
                            enum:
                            - RecursiveWalk
                            - Disabled
                            type: string
                        required:
                        - state
                        type: object
                      kmsKeyARN:
                        description: kmsKeyARN sets the cluster default storage class
                          to encrypt volumes with a user-defined KMS key, rather than
                          the default KMS key used by AWS. The value may be either
                          the ARN or Alias ARN of a KMS key.
                        pattern: ^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b|aws-iso-e|aws-iso-f):kms:[a-z0-9-]+:[0-9]{12}:(key|alias)\/.*$
                        type: string
                    type: object
                  azure:
                    description: azure is used to configure the Azure CSI driver.
                    properties:
                      diskEncryptionSet:
                        description: diskEncryptionSet sets the cluster default storage
                          class to encrypt volumes with a customer-managed encryption
                          set, rather than the default platform-managed keys.
                        properties:
                          name:
                            description: name is the name of the disk encryption set
                              that will be set on the default storage class. The value
                              should consist of only alphanumberic characters, underscores
                              (_), hyphens, and be at most 80 characters in length.
                            maxLength: 80
                            pattern: ^[a-zA-Z0-9\_-]+$
                            type: string
                          resourceGroup:
                            description: resourceGroup defines the Azure resource
                              group that contains the disk encryption set. The value
                              should consist of only alphanumberic characters, underscores
                              (_), parentheses, hyphens and periods. The value should
                              not end in a period and be at most 90 characters in
                              length.
                            maxLength: 90
                            pattern: ^[\w\.\-\(\)]*[\w\-\(\)]$
                            type: string
                          subscriptionID:
                            description: 'subscriptionID defines the Azure subscription
                              that contains the disk encryption set. The value should
                              meet the following conditions: 1. It should be a 128-bit
                              number. 2. It should be 36 characters (32 hexadecimal
                              characters and 4 hyphens) long. 3. It should be displayed
                              in five groups separated by hyphens (-). 4. The first
                              group should be 8 characters long. 5. The second, third,
                              and fourth groups should be 4 characters long. 6. The
                              fifth group should be 12 characters long. An Example
                              SubscrionID: f2007bbf-f802-4a47-9336-cf7c6b89b378'
                            maxLength: 36
                            pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$
                            type: string
                        required:
                        - name
                        - resourceGroup
                        - subscriptionID
                        type: object
                    type: object
                  driverType:
                    description: 'driverType indicates type of CSI driver for which
                      the driverConfig is being applied to. Valid values are: AWS,
                      Azure, GCP, IBMCloud, vSphere and omitted. Consumers should
                      treat unknown values as a NO-OP.'
                    enum:
                    - ""
                    - AWS
                    - Azure
                    - GCP
                    - IBMCloud
                    - vSphere
                    type: string
                  gcp:
                    description: gcp is used to configure the GCP CSI driver.
                    properties:
                      kmsKey:
                        description: kmsKey sets the cluster default storage class
                          to encrypt volumes with customer-supplied encryption keys,
                          rather than the default keys managed by GCP.
                        properties:
                          keyRing:
                            description: keyRing is the name of the KMS Key Ring which
                              the KMS Key belongs to. The value should correspond
                              to an existing KMS key ring and should consist of only
                              alphanumeric characters, hyphens (-) and underscores
                              (_), and be at most 63 characters in length.
                            maxLength: 63
                            minLength: 1
                            pattern: ^[a-zA-Z0-9\_-]+$
                            type: string
                          location:
                            description: location is the GCP location in which the
                              Key Ring exists. The value must match an existing GCP
                              location, or "global". Defaults to global, if not set.
                            pattern: ^[a-zA-Z0-9\_-]+$
                            type: string
                          name:
                            description: name is the name of the customer-managed
                              encryption key to be used for disk encryption. The value
                              should correspond to an existing KMS key and should
                              consist of only alphanumeric characters, hyphens (-)
                              and underscores (_), and be at most 63 characters in
                              length.
                            maxLength: 63
                            minLength: 1
                            pattern: ^[a-zA-Z0-9\_-]+$
                            type: string
                          projectID:
                            description: projectID is the ID of the Project in which
                              the KMS Key Ring exists. It must be 6 to 30 lowercase
                              letters, digits, or hyphens. It must start with a letter.
                              Trailing hyphens are prohibited.
                            maxLength: 30
                            minLength: 6
                            pattern: ^[a-z][a-z0-9-]+[a-z0-9]$
                            type: string
                        required:
                        - keyRing
                        - name
                        - projectID
                        type: object
                    type: object
                  ibmcloud:
                    description: ibmcloud is used to configure the IBM Cloud CSI driver.
                    properties:
                      encryptionKeyCRN:
                        description: encryptionKeyCRN is the IBM Cloud CRN of the
                          customer-managed root key to use for disk encryption of
                          volumes for the default storage classes.
                        maxLength: 154
                        minLength: 144
                        pattern: ^crn:v[0-9]+:bluemix:(public|private):(kms|hs-crypto):[a-z-]+:a/[0-9a-f]+:[0-9a-f-]{36}:key:[0-9a-f-]{36}$
                        type: string
                    required:
                    - encryptionKeyCRN
                    type: object
                  vSphere:
                    description: vsphere is used to configure the vsphere CSI driver.
                    properties:
                      globalMaxSnapshotsPerBlockVolume:
                        description: 'globalMaxSnapshotsPerBlockVolume is a global
                          configuration parameter that applies to volumes on all kinds
                          of datastores. If omitted, the platform chooses a default,
                          which is subject to change over time, currently that default
                          is 3. Snapshots can not be disabled using this parameter.
                          Increasing number of snapshots above 3 can have negative
                          impact on performance, for more details see: https://kb.vmware.com/s/article/1025279
                          Volume snapshot documentation: https://docs.vmware.com/en/VMware-vSphere-Container-Storage-Plug-in/3.0/vmware-vsphere-csp-getting-started/GUID-E0B41C69-7EEB-450F-A73D-5FD2FF39E891.html'
                        format: int32
                        maximum: 32
                        minimum: 1
                        type: integer
                      granularMaxSnapshotsPerBlockVolumeInVSAN:
                        description: granularMaxSnapshotsPerBlockVolumeInVSAN is a
                          granular configuration parameter on vSAN datastore only.
                          It overrides GlobalMaxSnapshotsPerBlockVolume if set, while
                          it falls back to the global constraint if unset. Snapshots
                          for VSAN can not be disabled using this parameter.
                        format: int32
                        maximum: 32
                        minimum: 1
                        type: integer
                      granularMaxSnapshotsPerBlockVolumeInVVOL:
                        description: granularMaxSnapshotsPerBlockVolumeInVVOL is a
                          granular configuration parameter on Virtual Volumes datastore
                          only. It overrides GlobalMaxSnapshotsPerBlockVolume if set,
                          while it falls back to the global constraint if unset. Snapshots
                          for VVOL can not be disabled using this parameter.
                        format: int32
                        maximum: 32
                        minimum: 1
                        type: integer
                      topologyCategories:
                        description: topologyCategories indicates tag categories with
                          which vcenter resources such as hostcluster or datacenter
                          were tagged with. If cluster Infrastructure object has a
                          topology, values specified in Infrastructure object will
                          be used and modifications to topologyCategories will be
                          rejected.
                        items:
                          type: string
                        type: array
                        x-kubernetes-list-type: atomic
                    type: object
                required:
                - driverType
                type: object
                x-kubernetes-validations:
                - message: ibmcloud must be set if driverType is 'IBMCloud', but remain
                    unset otherwise
                  rule: 'has(self.driverType) && self.driverType == ''IBMCloud'' ?
                    has(self.ibmcloud) : !has(self.ibmcloud)'
              logLevel:
                default: Normal
                description: "logLevel is an intent based logging for an overall component.
                  \ It does not give fine grained control, but it is a simple way
                  to manage coarse grained logging choices that operators have to
                  interpret for their operands. \n Valid values are: \"Normal\", \"Debug\",
                  \"Trace\", \"TraceAll\". Defaults to \"Normal\"."
                enum:
                - ""
                - Normal
                - Debug
                - Trace
                - TraceAll
                type: string
              managementState:
                description: managementState indicates whether and how the operator
                  should manage the component
                pattern: ^(Managed|Unmanaged|Force|Removed)$
                type: string
              observedConfig:
                description: observedConfig holds a sparse config that controller
                  has observed from the cluster state.  It exists in spec because
                  it is an input to the level for the operator
                nullable: true
                type: object
                x-kubernetes-preserve-unknown-fields: true
              operatorLogLevel:
                default: Normal
                description: "operatorLogLevel is an intent based logging for the
                  operator itself.  It does not give fine grained control, but it
                  is a simple way to manage coarse grained logging choices that operators
                  have to interpret for themselves. \n Valid values are: \"Normal\",
                  \"Debug\", \"Trace\", \"TraceAll\". Defaults to \"Normal\"."
                enum:
                - ""
                - Normal
                - Debug
                - Trace
                - TraceAll
                type: string
              storageClassState:
                description: StorageClassState determines if CSI operator should create
                  and manage storage classes. If this field value is empty or Managed
                  - CSI operator will continuously reconcile storage class and create
                  if necessary. If this field value is Unmanaged - CSI operator will
                  not reconcile any previously created storage class. If this field
                  value is Removed - CSI operator will delete the storage class it
                  created previously. When omitted, this means the user has no opinion
                  and the platform chooses a reasonable default, which is subject
                  to change over time. The current default behaviour is Managed.
                enum:
                - ""
                - Managed
                - Unmanaged
                - Removed
                type: string
              unsupportedConfigOverrides:
                description: unsupportedConfigOverrides overrides the final configuration
                  that was computed by the operator. Red Hat does not support the
                  use of this field. Misuse of this field could lead to unexpected
                  behavior or conflict with other configuration options. Seek guidance
                  from the Red Hat support before using this field. Use of this property
                  blocks cluster upgrades, it must be removed before upgrading your
                  cluster.
                nullable: true
                type: object
                x-kubernetes-preserve-unknown-fields: true
            type: object
          status:
            description: status holds observed values from the cluster. They may not
              be overridden.
            properties:
              conditions:
                description: conditions is a list of conditions and their status
                items:
                  description: OperatorCondition is just the standard condition fields.
                  properties:
                    lastTransitionTime:
                      format: date-time
                      type: string
                    message:
                      type: string
                    reason:
                      type: string
                    status:
                      type: string
                    type:
                      type: string
                  required:
                  - type
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              generations:
                description: generations are used to determine when an item needs
                  to be reconciled or has changed in a way that needs a reaction.
                items:
                  description: GenerationStatus keeps track of the generation for
                    a given resource so that decisions about forced updates can be
                    made.
                  properties:
                    group:
                      description: group is the group of the thing you're tracking
                      type: string
                    hash:
                      description: hash is an optional field set for resources without
                        generation that are content sensitive like secrets and configmaps
                      type: string
                    lastGeneration:
                      description: lastGeneration is the last generation of the workload
                        controller involved
                      format: int64
                      type: integer
                    name:
                      description: name is the name of the thing you're tracking
                      type: string
                    namespace:
                      description: namespace is where the thing you're tracking is
                      type: string
                    resource:
                      description: resource is the resource type of the thing you're
                        tracking
                      type: string
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              observedGeneration:
                description: observedGeneration is the last generation change you've
                  dealt with
                format: int64
                type: integer
              readyReplicas:
                description: readyReplicas indicates how many replicas are ready and
                  at the desired state
                format: int32
                type: integer
              version:
                description: version is the level this availability applies to
                type: string
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ClusterCSIDriver
    listKind: ClusterCSIDriverList
    plural: clustercsidrivers
    singular: clustercsidriver
  conditions:
  - lastTransitionTime: "2025-06-25T14:54:02Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:54:02Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
