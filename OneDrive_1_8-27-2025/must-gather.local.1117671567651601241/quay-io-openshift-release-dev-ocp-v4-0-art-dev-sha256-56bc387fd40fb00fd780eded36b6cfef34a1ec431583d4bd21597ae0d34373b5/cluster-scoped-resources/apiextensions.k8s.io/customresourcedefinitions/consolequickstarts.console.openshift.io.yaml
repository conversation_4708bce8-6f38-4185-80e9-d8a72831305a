---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/750
    api.openshift.io/merged-by-featuregates: "true"
    capability.openshift.io/name: Console
    description: Extension for guiding user through various workflows in the OpenShift
      web console.
    displayName: ConsoleQuickStart
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:53:47Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:47Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:capability.openshift.io/name: {}
          f:description: {}
          f:displayName: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:42:18Z"
  name: consolequickstarts.console.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "144782"
  uid: 7f83492e-644a-40f4-adf9-64f505688e8c
spec:
  conversion:
    strategy: None
  group: console.openshift.io
  names:
    kind: ConsoleQuickStart
    listKind: ConsoleQuickStartList
    plural: consolequickstarts
    singular: consolequickstart
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "ConsoleQuickStart is an extension for guiding user through various
          workflows in the OpenShift web console. \n Compatibility level 2: Stable
          within a major release for a minimum of 9 months or 3 minor releases (whichever
          is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: ConsoleQuickStartSpec is the desired quick start configuration.
            properties:
              accessReviewResources:
                description: accessReviewResources contains a list of resources that
                  the user's access will be reviewed against in order for the user
                  to complete the Quick Start. The Quick Start will be hidden if any
                  of the access reviews fail.
                items:
                  description: ResourceAttributes includes the authorization attributes
                    available for resource requests to the Authorizer interface
                  properties:
                    group:
                      description: Group is the API Group of the Resource.  "*" means
                        all.
                      type: string
                    name:
                      description: Name is the name of the resource being requested
                        for a "get" or deleted for a "delete". "" (empty) means all.
                      type: string
                    namespace:
                      description: Namespace is the namespace of the action being
                        requested.  Currently, there is no distinction between no
                        namespace and all namespaces "" (empty) is defaulted for LocalSubjectAccessReviews
                        "" (empty) is empty for cluster-scoped resources "" (empty)
                        means "all" for namespace scoped resources from a SubjectAccessReview
                        or SelfSubjectAccessReview
                      type: string
                    resource:
                      description: Resource is one of the existing resource types.  "*"
                        means all.
                      type: string
                    subresource:
                      description: Subresource is one of the existing resource types.  ""
                        means none.
                      type: string
                    verb:
                      description: 'Verb is a kubernetes resource API verb, like:
                        get, list, watch, create, update, delete, proxy.  "*" means
                        all.'
                      type: string
                    version:
                      description: Version is the API Version of the Resource.  "*"
                        means all.
                      type: string
                  type: object
                type: array
              conclusion:
                description: conclusion sums up the Quick Start and suggests the possible
                  next steps. (includes markdown)
                type: string
              description:
                description: description is the description of the Quick Start. (includes
                  markdown)
                maxLength: 256
                minLength: 1
                type: string
              displayName:
                description: displayName is the display name of the Quick Start.
                minLength: 1
                type: string
              durationMinutes:
                description: durationMinutes describes approximately how many minutes
                  it will take to complete the Quick Start.
                minimum: 1
                type: integer
              icon:
                description: icon is a base64 encoded image that will be displayed
                  beside the Quick Start display name. The icon should be an vector
                  image for easy scaling. The size of the icon should be 40x40.
                type: string
              introduction:
                description: introduction describes the purpose of the Quick Start.
                  (includes markdown)
                minLength: 1
                type: string
              nextQuickStart:
                description: nextQuickStart is a list of the following Quick Starts,
                  suggested for the user to try.
                items:
                  type: string
                type: array
              prerequisites:
                description: prerequisites contains all prerequisites that need to
                  be met before taking a Quick Start. (includes markdown)
                items:
                  type: string
                type: array
              tags:
                description: tags is a list of strings that describe the Quick Start.
                items:
                  type: string
                type: array
              tasks:
                description: tasks is the list of steps the user has to perform to
                  complete the Quick Start.
                items:
                  description: ConsoleQuickStartTask is a single step in a Quick Start.
                  properties:
                    description:
                      description: description describes the steps needed to complete
                        the task. (includes markdown)
                      minLength: 1
                      type: string
                    review:
                      description: review contains instructions to validate the task
                        is complete. The user will select 'Yes' or 'No'. using a radio
                        button, which indicates whether the step was completed successfully.
                      properties:
                        failedTaskHelp:
                          description: failedTaskHelp contains suggestions for a failed
                            task review and is shown at the end of task. (includes
                            markdown)
                          minLength: 1
                          type: string
                        instructions:
                          description: instructions contains steps that user needs
                            to take in order to validate his work after going through
                            a task. (includes markdown)
                          minLength: 1
                          type: string
                      required:
                      - failedTaskHelp
                      - instructions
                      type: object
                    summary:
                      description: summary contains information about the passed step.
                      properties:
                        failed:
                          description: failed briefly describes the unsuccessfully
                            passed task. (includes markdown)
                          maxLength: 128
                          minLength: 1
                          type: string
                        success:
                          description: success describes the succesfully passed task.
                          minLength: 1
                          type: string
                      required:
                      - failed
                      - success
                      type: object
                    title:
                      description: title describes the task and is displayed as a
                        step heading.
                      minLength: 1
                      type: string
                  required:
                  - description
                  - title
                  type: object
                minItems: 1
                type: array
            required:
            - description
            - displayName
            - durationMinutes
            - introduction
            - tasks
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
status:
  acceptedNames:
    kind: ConsoleQuickStart
    listKind: ConsoleQuickStartList
    plural: consolequickstarts
    singular: consolequickstart
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:47Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:47Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
