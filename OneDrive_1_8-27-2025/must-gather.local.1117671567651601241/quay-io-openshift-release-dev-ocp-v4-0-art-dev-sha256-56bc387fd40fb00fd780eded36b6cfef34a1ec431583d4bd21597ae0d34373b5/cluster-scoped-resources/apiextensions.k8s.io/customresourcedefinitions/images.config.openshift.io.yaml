---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/470
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    release.openshift.io/bootstrap-required: "true"
  creationTimestamp: "2025-06-25T14:52:53Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-bootstrap
    operation: Update
    time: "2025-06-25T14:52:53Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:52:53Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:api.openshift.io/merged-by-featuregates: {}
          f:release.openshift.io/bootstrap-required: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:19:46Z"
  name: images.config.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "133583"
  uid: 3dae92f2-65ad-4c4a-a9b0-050a038c73ce
spec:
  conversion:
    strategy: None
  group: config.openshift.io
  names:
    kind: Image
    listKind: ImageList
    plural: images
    singular: image
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "Image governs policies related to imagestream imports and runtime
          configuration for external registries. It allows cluster admins to configure
          which registries OpenShift is allowed to import images from, extra CA trust
          bundles for external registries, and policies to block or allow registry
          hostnames. When exposing OpenShift's image registry to the public, this
          also lets cluster admins specify the external hostname. \n Compatibility
          level 1: Stable within a major release for a minimum of 12 months or 3 minor
          releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: spec holds user settable values for configuration
            properties:
              additionalTrustedCA:
                description: additionalTrustedCA is a reference to a ConfigMap containing
                  additional CAs that should be trusted during imagestream import,
                  pod image pull, build image pull, and imageregistry pullthrough.
                  The namespace for this config map is openshift-config.
                properties:
                  name:
                    description: name is the metadata.name of the referenced config
                      map
                    type: string
                required:
                - name
                type: object
              allowedRegistriesForImport:
                description: allowedRegistriesForImport limits the container image
                  registries that normal users may import images from. Set this list
                  to the registries that you trust to contain valid Docker images
                  and that you want applications to be able to import from. Users
                  with permission to create Images or ImageStreamMappings via the
                  API are not affected by this policy - typically only administrators
                  or system integrations will have those permissions.
                items:
                  description: RegistryLocation contains a location of the registry
                    specified by the registry domain name. The domain name might include
                    wildcards, like '*' or '??'.
                  properties:
                    domainName:
                      description: domainName specifies a domain name for the registry
                        In case the registry use non-standard (80 or 443) port, the
                        port should be included in the domain name as well.
                      type: string
                    insecure:
                      description: insecure indicates whether the registry is secure
                        (https) or insecure (http) By default (if not specified) the
                        registry is assumed as secure.
                      type: boolean
                  type: object
                type: array
              externalRegistryHostnames:
                description: externalRegistryHostnames provides the hostnames for
                  the default external image registry. The external hostname should
                  be set only when the image registry is exposed externally. The first
                  value is used in 'publicDockerImageRepository' field in ImageStreams.
                  The value must be in "hostname[:port]" format.
                items:
                  type: string
                type: array
              registrySources:
                description: registrySources contains configuration that determines
                  how the container runtime should treat individual registries when
                  accessing images for builds+pods. (e.g. whether or not to allow
                  insecure access).  It does not contain configuration for the internal
                  cluster registry.
                properties:
                  allowedRegistries:
                    description: "allowedRegistries are the only registries permitted
                      for image pull and push actions. All other registries are denied.
                      \n Only one of BlockedRegistries or AllowedRegistries may be
                      set."
                    items:
                      type: string
                    type: array
                  blockedRegistries:
                    description: "blockedRegistries cannot be used for image pull
                      and push actions. All other registries are permitted. \n Only
                      one of BlockedRegistries or AllowedRegistries may be set."
                    items:
                      type: string
                    type: array
                  containerRuntimeSearchRegistries:
                    description: 'containerRuntimeSearchRegistries are registries
                      that will be searched when pulling images that do not have fully
                      qualified domains in their pull specs. Registries will be searched
                      in the order provided in the list. Note: this search list only
                      works with the container runtime, i.e CRI-O. Will NOT work with
                      builds or imagestream imports.'
                    format: hostname
                    items:
                      type: string
                    minItems: 1
                    type: array
                    x-kubernetes-list-type: set
                  insecureRegistries:
                    description: insecureRegistries are registries which do not have
                      a valid TLS certificates or only support HTTP connections.
                    items:
                      type: string
                    type: array
                type: object
            type: object
          status:
            description: status holds observed values from the cluster. They may not
              be overridden.
            properties:
              externalRegistryHostnames:
                description: externalRegistryHostnames provides the hostnames for
                  the default external image registry. The external hostname should
                  be set only when the image registry is exposed externally. The first
                  value is used in 'publicDockerImageRepository' field in ImageStreams.
                  The value must be in "hostname[:port]" format.
                items:
                  type: string
                type: array
              internalRegistryHostname:
                description: internalRegistryHostname sets the hostname for the default
                  internal image registry. The value must be in "hostname[:port]"
                  format. This value is set by the image registry operator which controls
                  the internal registry hostname.
                type: string
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: Image
    listKind: ImageList
    plural: images
    singular: image
  conditions:
  - lastTransitionTime: "2025-06-25T14:52:53Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:52:53Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
