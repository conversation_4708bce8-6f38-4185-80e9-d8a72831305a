---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
  creationTimestamp: "2025-06-25T14:57:17Z"
  generation: 2
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:controller-gen.kubebuilder.io/version: {}
        f:ownerReferences:
          k:{"uid":"e8b7620a-d5d2-448e-b2bf-6f6768b9b1dc"}: {}
      f:spec:
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-network-operator/operconfig
    operation: Apply
    time: "2025-06-25T18:50:59Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:57:17Z"
  name: egressqoses.k8s.ovn.org
  ownerReferences:
  - apiVersion: operator.openshift.io/v1
    blockOwnerDeletion: true
    controller: true
    kind: Network
    name: cluster
    uid: e8b7620a-d5d2-448e-b2bf-6f6768b9b1dc
  resourceVersion: "156180"
  uid: 2c03021f-dda2-4fcf-8bc2-b9de1c18544f
spec:
  conversion:
    strategy: None
  group: k8s.ovn.org
  names:
    kind: EgressQoS
    listKind: EgressQoSList
    plural: egressqoses
    singular: egressqos
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.status
      name: Status
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: |-
          EgressQoS is a CRD that allows the user to define a DSCP value
          for pods egress traffic on its namespace to specified CIDRs.
          Traffic from these pods will be checked against each EgressQoSRule in
          the namespace's EgressQoS, and if there is a match the traffic is marked
          with the relevant DSCP value.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            properties:
              name:
                pattern: ^default$
                type: string
            type: object
          spec:
            description: EgressQoSSpec defines the desired state of EgressQoS
            properties:
              egress:
                description: a collection of Egress QoS rule objects
                items:
                  properties:
                    dscp:
                      description: DSCP marking value for matching pods' traffic.
                      maximum: 63
                      minimum: 0
                      type: integer
                    dstCIDR:
                      description: |-
                        DstCIDR specifies the destination's CIDR. Only traffic heading
                        to this CIDR will be marked with the DSCP value.
                        This field is optional, and in case it is not set the rule is applied
                        to all egress traffic regardless of the destination.
                      format: cidr
                      type: string
                    podSelector:
                      description: |-
                        PodSelector applies the QoS rule only to the pods in the namespace whose label
                        matches this definition. This field is optional, and in case it is not set
                        results in the rule being applied to all pods in the namespace.
                      properties:
                        matchExpressions:
                          description: matchExpressions is a list of label selector
                            requirements. The requirements are ANDed.
                          items:
                            description: |-
                              A label selector requirement is a selector that contains values, a key, and an operator that
                              relates the key and values.
                            properties:
                              key:
                                description: key is the label key that the selector
                                  applies to.
                                type: string
                              operator:
                                description: |-
                                  operator represents a key's relationship to a set of values.
                                  Valid operators are In, NotIn, Exists and DoesNotExist.
                                type: string
                              values:
                                description: |-
                                  values is an array of string values. If the operator is In or NotIn,
                                  the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                  the values array must be empty. This array is replaced during a strategic
                                  merge patch.
                                items:
                                  type: string
                                type: array
                            required:
                            - key
                            - operator
                            type: object
                          type: array
                        matchLabels:
                          additionalProperties:
                            type: string
                          description: |-
                            matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                            map is equivalent to an element of matchExpressions, whose key field is "key", the
                            operator is "In", and the values array contains only "value". The requirements are ANDed.
                          type: object
                      type: object
                      x-kubernetes-map-type: atomic
                  required:
                  - dscp
                  type: object
                type: array
            required:
            - egress
            type: object
          status:
            description: EgressQoSStatus defines the observed state of EgressQoS
            properties:
              conditions:
                description: An array of condition objects indicating details about
                  status of EgressQoS object.
                items:
                  description: "Condition contains details for one aspect of the current
                    state of this API Resource.\n---\nThis struct is intended for
                    direct use as an array at the field path .status.conditions.  For
                    example,\n\n\n\ttype FooStatus struct{\n\t    // Represents the
                    observations of a foo's current state.\n\t    // Known .status.conditions.type
                    are: \"Available\", \"Progressing\", and \"Degraded\"\n\t    //
                    +patchMergeKey=type\n\t    // +patchStrategy=merge\n\t    // +listType=map\n\t
                    \   // +listMapKey=type\n\t    Conditions []metav1.Condition `json:\"conditions,omitempty\"
                    patchStrategy:\"merge\" patchMergeKey:\"type\" protobuf:\"bytes,1,rep,name=conditions\"`\n\n\n\t
                    \   // other fields\n\t}"
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: |-
                        type of condition in CamelCase or in foo.example.com/CamelCase.
                        ---
                        Many .condition.type values are consistent across resources like Available, but because arbitrary conditions can be
                        useful (see .node.status.conditions), the ability to deconflict is important.
                        The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              status:
                description: A concise indication of whether the EgressQoS resource
                  is applied with success.
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: EgressQoS
    listKind: EgressQoSList
    plural: egressqoses
    singular: egressqos
  conditions:
  - lastTransitionTime: "2025-06-25T14:57:17Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:57:17Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
