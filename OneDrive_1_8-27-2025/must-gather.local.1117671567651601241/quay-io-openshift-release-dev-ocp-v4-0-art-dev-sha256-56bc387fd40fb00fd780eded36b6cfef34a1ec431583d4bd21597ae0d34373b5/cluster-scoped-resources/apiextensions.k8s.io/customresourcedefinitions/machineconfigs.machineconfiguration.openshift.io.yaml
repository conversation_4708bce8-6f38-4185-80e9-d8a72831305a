---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/1453
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:53:56Z"
  generation: 1
  labels:
    openshift.io/operator-managed: ""
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:56Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:labels:
          .: {}
          f:openshift.io/operator-managed: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:58:33Z"
  name: machineconfigs.machineconfiguration.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "161123"
  uid: ad879ba1-95fa-4d9b-8318-71a75e8b7d80
spec:
  conversion:
    strategy: None
  group: machineconfiguration.openshift.io
  names:
    kind: MachineConfig
    listKind: MachineConfigList
    plural: machineconfigs
    shortNames:
    - mc
    singular: machineconfig
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - description: Version of the controller that generated the machineconfig. This
        will be empty if the machineconfig is not managed by a controller.
      jsonPath: .metadata.annotations.machineconfiguration\.openshift\.io/generated-by-controller-version
      name: GeneratedByController
      type: string
    - description: Version of the Ignition Config defined in the machineconfig.
      jsonPath: .spec.config.ignition.version
      name: IgnitionVersion
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1
    schema:
      openAPIV3Schema:
        description: "MachineConfig defines the configuration for a machine \n Compatibility
          level 1: Stable within a major release for a minimum of 12 months or 3 minor
          releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: MachineConfigSpec is the spec for MachineConfig
            properties:
              baseOSExtensionsContainerImage:
                description: BaseOSExtensionsContainerImage specifies the remote location
                  that will be used to fetch the extensions container matching a new-format
                  OS image
                type: string
              config:
                description: Config is a Ignition Config object.
                type: object
                x-kubernetes-preserve-unknown-fields: true
              extensions:
                description: extensions contains a list of additional features that
                  can be enabled on host
                items:
                  type: string
                type: array
                x-kubernetes-list-type: atomic
              fips:
                description: fips controls FIPS mode
                type: boolean
              kernelArguments:
                description: kernelArguments contains a list of kernel arguments to
                  be added
                items:
                  type: string
                nullable: true
                type: array
                x-kubernetes-list-type: atomic
              kernelType:
                description: kernelType contains which kernel we want to be running
                  like default (traditional), realtime, 64k-pages (aarch64 only).
                type: string
              osImageURL:
                description: OSImageURL specifies the remote location that will be
                  used to fetch the OS.
                type: string
            type: object
        type: object
    served: true
    storage: true
status:
  acceptedNames:
    kind: MachineConfig
    listKind: MachineConfigList
    plural: machineconfigs
    shortNames:
    - mc
    singular: machineconfig
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:56Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:56Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
