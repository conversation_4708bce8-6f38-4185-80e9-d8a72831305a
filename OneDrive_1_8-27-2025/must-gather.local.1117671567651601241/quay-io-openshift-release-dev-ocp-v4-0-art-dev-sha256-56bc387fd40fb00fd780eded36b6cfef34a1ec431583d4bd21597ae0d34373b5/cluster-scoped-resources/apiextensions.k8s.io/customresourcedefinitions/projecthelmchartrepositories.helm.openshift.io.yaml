---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/1084
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:53:52Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:52Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:42:24Z"
  name: projecthelmchartrepositories.helm.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "145011"
  uid: 11f342c1-5ed2-4be9-895d-0c439aa04434
spec:
  conversion:
    strategy: None
  group: helm.openshift.io
  names:
    kind: ProjectHelmChartRepository
    listKind: ProjectHelmChartRepositoryList
    plural: projecthelmchartrepositories
    singular: projecthelmchartrepository
  scope: Namespaced
  versions:
  - name: v1beta1
    schema:
      openAPIV3Schema:
        description: "ProjectHelmChartRepository holds namespace-wide configuration
          for proxied Helm chart repository \n Compatibility level 2: Stable within
          a major release for a minimum of 9 months or 3 minor releases (whichever
          is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: spec holds user settable values for configuration
            properties:
              connectionConfig:
                description: Required configuration for connecting to the chart repo
                properties:
                  basicAuthConfig:
                    description: basicAuthConfig is an optional reference to a secret
                      by name that contains the basic authentication credentials to
                      present when connecting to the server. The key "username" is
                      used locate the username. The key "password" is used to locate
                      the password. The namespace for this secret must be same as
                      the namespace where the project helm chart repository is getting
                      instantiated.
                    properties:
                      name:
                        description: name is the metadata.name of the referenced secret
                        type: string
                    required:
                    - name
                    type: object
                  ca:
                    description: ca is an optional reference to a config map by name
                      containing the PEM-encoded CA bundle. It is used as a trust
                      anchor to validate the TLS certificate presented by the remote
                      server. The key "ca-bundle.crt" is used to locate the data.
                      If empty, the default system roots are used. The namespace for
                      this configmap must be same as the namespace where the project
                      helm chart repository is getting instantiated.
                    properties:
                      name:
                        description: name is the metadata.name of the referenced config
                          map
                        type: string
                    required:
                    - name
                    type: object
                  tlsClientConfig:
                    description: tlsClientConfig is an optional reference to a secret
                      by name that contains the PEM-encoded TLS client certificate
                      and private key to present when connecting to the server. The
                      key "tls.crt" is used to locate the client certificate. The
                      key "tls.key" is used to locate the private key. The namespace
                      for this secret must be same as the namespace where the project
                      helm chart repository is getting instantiated.
                    properties:
                      name:
                        description: name is the metadata.name of the referenced secret
                        type: string
                    required:
                    - name
                    type: object
                  url:
                    description: Chart repository URL
                    maxLength: 2048
                    pattern: ^https?:\/\/
                    type: string
                type: object
              description:
                description: Optional human readable repository description, it can
                  be used by UI for displaying purposes
                maxLength: 2048
                minLength: 1
                type: string
              disabled:
                description: If set to true, disable the repo usage in the namespace
                type: boolean
              name:
                description: Optional associated human readable repository name, it
                  can be used by UI for displaying purposes
                maxLength: 100
                minLength: 1
                type: string
            type: object
          status:
            description: Observed status of the repository within the namespace..
            properties:
              conditions:
                description: conditions is a list of conditions and their statuses
                items:
                  description: "Condition contains details for one aspect of the current
                    state of this API Resource. --- This struct is intended for direct
                    use as an array at the field path .status.conditions.  For example,
                    \n type FooStatus struct{ // Represents the observations of a
                    foo's current state. // Known .status.conditions.type are: \"Available\",
                    \"Progressing\", and \"Degraded\" // +patchMergeKey=type // +patchStrategy=merge
                    // +listType=map // +listMapKey=type Conditions []metav1.Condition
                    `json:\"conditions,omitempty\" patchStrategy:\"merge\" patchMergeKey:\"type\"
                    protobuf:\"bytes,1,rep,name=conditions\"` \n // other fields }"
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the last time the condition
                        transitioned from one status to another. This should be when
                        the underlying condition changed.  If that is not known, then
                        using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: message is a human readable message indicating
                        details about the transition. This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: observedGeneration represents the .metadata.generation
                        that the condition was set based upon. For instance, if .metadata.generation
                        is currently 12, but the .status.conditions[x].observedGeneration
                        is 9, the condition is out of date with respect to the current
                        state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: reason contains a programmatic identifier indicating
                        the reason for the condition's last transition. Producers
                        of specific condition types may define expected values and
                        meanings for this field, and whether the values are considered
                        a guaranteed API. The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                        --- Many .condition.type values are consistent across resources
                        like Available, but because arbitrary conditions can be useful
                        (see .node.status.conditions), the ability to deconflict is
                        important. The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ProjectHelmChartRepository
    listKind: ProjectHelmChartRepositoryList
    plural: projecthelmchartrepositories
    singular: projecthelmchartrepository
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:52Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:52Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1beta1
