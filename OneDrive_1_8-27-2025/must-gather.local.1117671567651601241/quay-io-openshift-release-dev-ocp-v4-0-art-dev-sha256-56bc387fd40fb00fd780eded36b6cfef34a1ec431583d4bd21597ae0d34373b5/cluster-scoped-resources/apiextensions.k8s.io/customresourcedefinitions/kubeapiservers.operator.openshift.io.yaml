---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/475
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:54:01Z"
  generation: 2
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:categories: {}
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T11:47:07Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:categories: {}
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-01T11:47:07Z"
  name: kubeapiservers.operator.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21327991"
  uid: 2cdd9238-8e19-4913-915b-04175be06548
spec:
  conversion:
    strategy: None
  group: operator.openshift.io
  names:
    categories:
    - coreoperators
    kind: KubeAPIServer
    listKind: KubeAPIServerList
    plural: kubeapiservers
    singular: kubeapiserver
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "KubeAPIServer provides information to configure an operator
          to manage kube-apiserver. \n Compatibility level 1: Stable within a major
          release for a minimum of 12 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: spec is the specification of the desired behavior of the
              Kubernetes API Server
            properties:
              failedRevisionLimit:
                description: failedRevisionLimit is the number of failed static pod
                  installer revisions to keep on disk and in the api -1 = unlimited,
                  0 or unset = 5 (default)
                format: int32
                type: integer
              forceRedeploymentReason:
                description: forceRedeploymentReason can be used to force the redeployment
                  of the operand by providing a unique string. This provides a mechanism
                  to kick a previously failed deployment and provide a reason why
                  you think it will work this time instead of failing again on the
                  same config.
                type: string
              logLevel:
                default: Normal
                description: "logLevel is an intent based logging for an overall component.
                  \ It does not give fine grained control, but it is a simple way
                  to manage coarse grained logging choices that operators have to
                  interpret for their operands. \n Valid values are: \"Normal\", \"Debug\",
                  \"Trace\", \"TraceAll\". Defaults to \"Normal\"."
                enum:
                - ""
                - Normal
                - Debug
                - Trace
                - TraceAll
                type: string
              managementState:
                description: managementState indicates whether and how the operator
                  should manage the component
                pattern: ^(Managed|Force)$
                type: string
              observedConfig:
                description: observedConfig holds a sparse config that controller
                  has observed from the cluster state.  It exists in spec because
                  it is an input to the level for the operator
                nullable: true
                type: object
                x-kubernetes-preserve-unknown-fields: true
              operatorLogLevel:
                default: Normal
                description: "operatorLogLevel is an intent based logging for the
                  operator itself.  It does not give fine grained control, but it
                  is a simple way to manage coarse grained logging choices that operators
                  have to interpret for themselves. \n Valid values are: \"Normal\",
                  \"Debug\", \"Trace\", \"TraceAll\". Defaults to \"Normal\"."
                enum:
                - ""
                - Normal
                - Debug
                - Trace
                - TraceAll
                type: string
              succeededRevisionLimit:
                description: succeededRevisionLimit is the number of successful static
                  pod installer revisions to keep on disk and in the api -1 = unlimited,
                  0 or unset = 5 (default)
                format: int32
                type: integer
              unsupportedConfigOverrides:
                description: unsupportedConfigOverrides overrides the final configuration
                  that was computed by the operator. Red Hat does not support the
                  use of this field. Misuse of this field could lead to unexpected
                  behavior or conflict with other configuration options. Seek guidance
                  from the Red Hat support before using this field. Use of this property
                  blocks cluster upgrades, it must be removed before upgrading your
                  cluster.
                nullable: true
                type: object
                x-kubernetes-preserve-unknown-fields: true
            type: object
          status:
            description: status is the most recently observed status of the Kubernetes
              API Server
            properties:
              conditions:
                description: conditions is a list of conditions and their status
                items:
                  description: OperatorCondition is just the standard condition fields.
                  properties:
                    lastTransitionTime:
                      format: date-time
                      type: string
                    message:
                      type: string
                    reason:
                      type: string
                    status:
                      type: string
                    type:
                      type: string
                  required:
                  - type
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              generations:
                description: generations are used to determine when an item needs
                  to be reconciled or has changed in a way that needs a reaction.
                items:
                  description: GenerationStatus keeps track of the generation for
                    a given resource so that decisions about forced updates can be
                    made.
                  properties:
                    group:
                      description: group is the group of the thing you're tracking
                      type: string
                    hash:
                      description: hash is an optional field set for resources without
                        generation that are content sensitive like secrets and configmaps
                      type: string
                    lastGeneration:
                      description: lastGeneration is the last generation of the workload
                        controller involved
                      format: int64
                      type: integer
                    name:
                      description: name is the name of the thing you're tracking
                      type: string
                    namespace:
                      description: namespace is where the thing you're tracking is
                      type: string
                    resource:
                      description: resource is the resource type of the thing you're
                        tracking
                      type: string
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              latestAvailableRevision:
                description: latestAvailableRevision is the deploymentID of the most
                  recent deployment
                format: int32
                type: integer
              latestAvailableRevisionReason:
                description: latestAvailableRevisionReason describe the detailed reason
                  for the most recent deployment
                type: string
              nodeStatuses:
                description: nodeStatuses track the deployment values and errors across
                  individual nodes
                items:
                  description: NodeStatus provides information about the current state
                    of a particular node managed by this operator.
                  properties:
                    currentRevision:
                      description: currentRevision is the generation of the most recently
                        successful deployment
                      format: int32
                      type: integer
                    lastFailedCount:
                      description: lastFailedCount is how often the installer pod
                        of the last failed revision failed.
                      type: integer
                    lastFailedReason:
                      description: lastFailedReason is a machine readable failure
                        reason string.
                      type: string
                    lastFailedRevision:
                      description: lastFailedRevision is the generation of the deployment
                        we tried and failed to deploy.
                      format: int32
                      type: integer
                    lastFailedRevisionErrors:
                      description: lastFailedRevisionErrors is a list of human readable
                        errors during the failed deployment referenced in lastFailedRevision.
                      items:
                        type: string
                      type: array
                      x-kubernetes-list-type: atomic
                    lastFailedTime:
                      description: lastFailedTime is the time the last failed revision
                        failed the last time.
                      format: date-time
                      type: string
                    lastFallbackCount:
                      description: lastFallbackCount is how often a fallback to a
                        previous revision happened.
                      type: integer
                    nodeName:
                      description: nodeName is the name of the node
                      type: string
                    targetRevision:
                      description: targetRevision is the generation of the deployment
                        we're trying to apply
                      format: int32
                      type: integer
                  required:
                  - nodeName
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - nodeName
                x-kubernetes-list-type: map
              observedGeneration:
                description: observedGeneration is the last generation change you've
                  dealt with
                format: int64
                type: integer
              readyReplicas:
                description: readyReplicas indicates how many replicas are ready and
                  at the desired state
                format: int32
                type: integer
              serviceAccountIssuers:
                description: 'serviceAccountIssuers tracks history of used service
                  account issuers. The item without expiration time represents the
                  currently used service account issuer. The other items represents
                  service account issuers that were used previously and are still
                  being trusted. The default expiration for the items is set by the
                  platform and it defaults to 24h. see: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/#service-account-token-volume-projection'
                items:
                  properties:
                    expirationTime:
                      description: expirationTime is the time after which this service
                        account issuer will be pruned and removed from the trusted
                        list of service account issuers.
                      format: date-time
                      type: string
                    name:
                      description: name is the name of the service account issuer
                        ---
                      type: string
                  type: object
                type: array
              version:
                description: version is the level this availability applies to
                type: string
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    categories:
    - coreoperators
    kind: KubeAPIServer
    listKind: KubeAPIServerList
    plural: kubeapiservers
    singular: kubeapiserver
  conditions:
  - lastTransitionTime: "2025-06-25T14:54:01Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:54:01Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
