---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.5
  creationTimestamp: "2025-06-25T15:21:59Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:controller-gen.kubebuilder.io/version: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: Go-http-client
    operation: Update
    time: "2025-06-25T15:21:59Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T15:21:59Z"
  name: clusters.aro.openshift.io
  resourceVersion: "41448"
  uid: 00662828-17dc-4370-bca1-3fd0ff2d75e0
spec:
  conversion:
    strategy: None
  group: aro.openshift.io
  names:
    kind: Cluster
    listKind: ClusterList
    plural: clusters
    singular: cluster
  scope: Cluster
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: Cluster is the Schema for the clusters API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: ClusterSpec defines the desired state of Cluster
            properties:
              acrDomain:
                type: string
              apiIntIP:
                type: string
              architectureVersion:
                type: integer
              azEnvironment:
                type: string
              banner:
                description: Banner defines if a Banner should be shown to the customer
                properties:
                  content:
                    type: string
                type: object
              clusterResourceGroupId:
                type: string
              domain:
                type: string
              gatewayDomains:
                items:
                  type: string
                type: array
              gatewayPrivateEndpointIP:
                type: string
              genevaLogging:
                properties:
                  configVersion:
                    pattern: '[0-9]+.[0-9]+'
                    type: string
                  monitoringGCSAccount:
                    enum:
                    - AROClusterLogsINT
                    - AROClusterLogsPROD
                    - AROClusterLogs
                    type: string
                  monitoringGCSEnvironment:
                    enum:
                    - DiagnosticsProd
                    - Test
                    - CaFairfax
                    type: string
                  monitoringGCSNamespace:
                    enum:
                    - AROClusterLogsINT
                    - AROClusterLogsPROD
                    - AROClusterLogs
                    type: string
                type: object
              infraId:
                type: string
              ingressIP:
                type: string
              internetChecker:
                properties:
                  urls:
                    items:
                      type: string
                    type: array
                type: object
              location:
                type: string
              operatorflags:
                additionalProperties:
                  type: string
                description: OperatorFlags defines feature gates for the ARO Operator
                type: object
              resourceId:
                description: ResourceID is the Azure resourceId of the cluster
                type: string
              serviceSubnets:
                items:
                  type: string
                type: array
              storageSuffix:
                type: string
              vnetId:
                type: string
            type: object
          status:
            description: ClusterStatus defines the observed state of Cluster
            properties:
              conditions:
                items:
                  description: OperatorCondition is just the standard condition fields.
                  properties:
                    lastTransitionTime:
                      format: date-time
                      type: string
                    message:
                      type: string
                    reason:
                      type: string
                    status:
                      type: string
                    type:
                      type: string
                  type: object
                type: array
              operatorVersion:
                type: string
              redHatKeysPresent:
                items:
                  type: string
                type: array
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: Cluster
    listKind: ClusterList
    plural: clusters
    singular: cluster
  conditions:
  - lastTransitionTime: "2025-06-25T15:21:59Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T15:21:59Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1alpha1
