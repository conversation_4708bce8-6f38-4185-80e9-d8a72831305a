---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/1406
    api.openshift.io/merged-by-featuregates: "true"
    description: OpenShift Monitoring alert relabel configurations
    include.release.openshift.io/hypershift: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:53:42Z"
  generation: 1
  labels:
    app.kubernetes.io/managed-by: cluster-version-operator
    app.kubernetes.io/part-of: openshift-monitoring
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:42Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:description: {}
          f:include.release.openshift.io/hypershift: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:labels:
          .: {}
          f:app.kubernetes.io/managed-by: {}
          f:app.kubernetes.io/part-of: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:42:16Z"
  name: alertrelabelconfigs.monitoring.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "144756"
  uid: c7a4cb31-0a1d-47c0-aae2-7af91d95e626
spec:
  conversion:
    strategy: None
  group: monitoring.openshift.io
  names:
    kind: AlertRelabelConfig
    listKind: AlertRelabelConfigList
    plural: alertrelabelconfigs
    singular: alertrelabelconfig
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "AlertRelabelConfig defines a set of relabel configs for alerts.
          \n Compatibility level 1: Stable within a major release for a minimum of
          12 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: spec describes the desired state of this AlertRelabelConfig
              object.
            properties:
              configs:
                description: configs is a list of sequentially evaluated alert relabel
                  configs.
                items:
                  description: 'RelabelConfig allows dynamic rewriting of label sets
                    for alerts. See Prometheus documentation: - https://prometheus.io/docs/prometheus/latest/configuration/configuration/#alert_relabel_configs
                    - https://prometheus.io/docs/prometheus/latest/configuration/configuration/#relabel_config'
                  properties:
                    action:
                      default: Replace
                      description: 'action to perform based on regex matching. Must
                        be one of: ''Replace'', ''Keep'', ''Drop'', ''HashMod'', ''LabelMap'',
                        ''LabelDrop'', or ''LabelKeep''. Default is: ''Replace'''
                      enum:
                      - Replace
                      - Keep
                      - Drop
                      - HashMod
                      - LabelMap
                      - LabelDrop
                      - LabelKeep
                      type: string
                    modulus:
                      description: modulus to take of the hash of the source label
                        values.  This can be combined with the 'HashMod' action to
                        set 'target_label' to the 'modulus' of a hash of the concatenated
                        'source_labels'. This is only valid if sourceLabels is not
                        empty and action is not 'LabelKeep' or 'LabelDrop'.
                      format: int64
                      type: integer
                    regex:
                      default: (.*)
                      description: 'regex against which the extracted value is matched.
                        Default is: ''(.*)'' regex is required for all actions except
                        ''HashMod'''
                      maxLength: 2048
                      type: string
                    replacement:
                      description: 'replacement value against which a regex replace
                        is performed if the regular expression matches. This is required
                        if the action is ''Replace'' or ''LabelMap'' and forbidden
                        for actions ''LabelKeep'' and ''LabelDrop''. Regex capture
                        groups are available. Default is: ''$1'''
                      maxLength: 2048
                      type: string
                    separator:
                      description: separator placed between concatenated source label
                        values. When omitted, Prometheus will use its default value
                        of ';'.
                      maxLength: 2048
                      type: string
                    sourceLabels:
                      description: sourceLabels select values from existing labels.
                        Their content is concatenated using the configured separator
                        and matched against the configured regular expression for
                        the 'Replace', 'Keep', and 'Drop' actions. Not allowed for
                        actions 'LabelKeep' and 'LabelDrop'.
                      items:
                        description: LabelName is a valid Prometheus label name which
                          may only contain ASCII letters, numbers, and underscores.
                        maxLength: 2048
                        pattern: ^[a-zA-Z_][a-zA-Z0-9_]*$
                        type: string
                      type: array
                    targetLabel:
                      description: targetLabel to which the resulting value is written
                        in a 'Replace' action. It is required for 'Replace' and 'HashMod'
                        actions and forbidden for actions 'LabelKeep' and 'LabelDrop'.
                        Regex capture groups are available.
                      maxLength: 2048
                      type: string
                  type: object
                  x-kubernetes-validations:
                  - message: relabel action hashmod requires non-zero modulus
                    rule: self.action != 'HashMod' || self.modulus != 0
                  - message: targetLabel is required when action is Replace or HashMod
                    rule: (self.action != 'Replace' && self.action != 'HashMod') ||
                      has(self.targetLabel)
                  - message: LabelKeep and LabelDrop actions require only 'regex',
                      and no other fields (found sourceLabels)
                    rule: (self.action != 'LabelDrop' && self.action != 'LabelKeep')
                      || !has(self.sourceLabels)
                  - message: LabelKeep and LabelDrop actions require only 'regex',
                      and no other fields (found targetLabel)
                    rule: (self.action != 'LabelDrop' && self.action != 'LabelKeep')
                      || !has(self.targetLabel)
                  - message: LabelKeep and LabelDrop actions require only 'regex',
                      and no other fields (found modulus)
                    rule: (self.action != 'LabelDrop' && self.action != 'LabelKeep')
                      || !has(self.modulus)
                  - message: LabelKeep and LabelDrop actions require only 'regex',
                      and no other fields (found separator)
                    rule: (self.action != 'LabelDrop' && self.action != 'LabelKeep')
                      || !has(self.separator)
                  - message: LabelKeep and LabelDrop actions require only 'regex',
                      and no other fields (found replacement)
                    rule: (self.action != 'LabelDrop' && self.action != 'LabelKeep')
                      || !has(self.replacement)
                  - message: modulus requires sourceLabels to be present
                    rule: '!has(self.modulus) || (has(self.modulus) && size(self.sourceLabels)
                      > 0)'
                  - message: sourceLabels is required for actions Replace, Keep, Drop,
                      HashMod and LabelMap
                    rule: (self.action == 'LabelDrop' || self.action == 'LabelKeep')
                      || has(self.sourceLabels)
                  - message: replacement is required for actions Replace and LabelMap
                    rule: (self.action != 'Replace' && self.action != 'LabelMap')
                      || has(self.replacement)
                minItems: 1
                type: array
            required:
            - configs
            type: object
          status:
            description: status describes the current state of this AlertRelabelConfig
              object.
            properties:
              conditions:
                description: conditions contains details on the state of the AlertRelabelConfig,
                  may be empty.
                items:
                  description: "Condition contains details for one aspect of the current
                    state of this API Resource. --- This struct is intended for direct
                    use as an array at the field path .status.conditions.  For example,
                    \n type FooStatus struct{ // Represents the observations of a
                    foo's current state. // Known .status.conditions.type are: \"Available\",
                    \"Progressing\", and \"Degraded\" // +patchMergeKey=type // +patchStrategy=merge
                    // +listType=map // +listMapKey=type Conditions []metav1.Condition
                    `json:\"conditions,omitempty\" patchStrategy:\"merge\" patchMergeKey:\"type\"
                    protobuf:\"bytes,1,rep,name=conditions\"` \n // other fields }"
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the last time the condition
                        transitioned from one status to another. This should be when
                        the underlying condition changed.  If that is not known, then
                        using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: message is a human readable message indicating
                        details about the transition. This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: observedGeneration represents the .metadata.generation
                        that the condition was set based upon. For instance, if .metadata.generation
                        is currently 12, but the .status.conditions[x].observedGeneration
                        is 9, the condition is out of date with respect to the current
                        state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: reason contains a programmatic identifier indicating
                        the reason for the condition's last transition. Producers
                        of specific condition types may define expected values and
                        meanings for this field, and whether the values are considered
                        a guaranteed API. The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                        --- Many .condition.type values are consistent across resources
                        like Available, but because arbitrary conditions can be useful
                        (see .node.status.conditions), the ability to deconflict is
                        important. The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: AlertRelabelConfig
    listKind: AlertRelabelConfigList
    plural: alertrelabelconfigs
    singular: alertrelabelconfig
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:42Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:42Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
