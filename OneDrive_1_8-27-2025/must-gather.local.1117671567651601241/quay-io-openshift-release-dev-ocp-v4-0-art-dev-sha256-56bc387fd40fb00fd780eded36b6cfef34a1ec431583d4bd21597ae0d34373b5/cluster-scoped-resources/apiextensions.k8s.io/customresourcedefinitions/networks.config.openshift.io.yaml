---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/470
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    release.openshift.io/bootstrap-required: "true"
    release.openshift.io/feature-set: Default
  creationTimestamp: "2025-06-25T14:52:56Z"
  generation: 3
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
          f:release.openshift.io/feature-set: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
    manager: cluster-bootstrap
    operation: Update
    time: "2025-06-25T14:52:56Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:52:56Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:api.openshift.io/merged-by-featuregates: {}
          f:release.openshift.io/bootstrap-required: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T11:46:55Z"
  name: networks.config.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21327801"
  uid: 67f3900f-dc2c-4f65-93fc-3018a1c2c026
spec:
  conversion:
    strategy: None
  group: config.openshift.io
  names:
    kind: Network
    listKind: NetworkList
    plural: networks
    singular: network
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "Network holds cluster-wide information about Network. The canonical
          name is `cluster`. It is used to configure the desired network configuration,
          such as: IP address pools for services/pod IPs, network plugin, etc. Please
          view network.spec for an explanation on what applies when configuring this
          resource. \n Compatibility level 1: Stable within a major release for a
          minimum of 12 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: spec holds user settable values for configuration. As a general
              rule, this SHOULD NOT be read directly. Instead, you should consume
              the NetworkStatus, as it indicates the currently deployed configuration.
              Currently, most spec fields are immutable after installation. Please
              view the individual ones for further details on each.
            properties:
              clusterNetwork:
                description: IP address pool to use for pod IPs. This field is immutable
                  after installation.
                items:
                  description: ClusterNetworkEntry is a contiguous block of IP addresses
                    from which pod IPs are allocated.
                  properties:
                    cidr:
                      description: The complete block for pod IPs.
                      type: string
                    hostPrefix:
                      description: The size (prefix) of block to allocate to each
                        node. If this field is not used by the plugin, it can be left
                        unset.
                      format: int32
                      minimum: 0
                      type: integer
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              externalIP:
                description: externalIP defines configuration for controllers that
                  affect Service.ExternalIP. If nil, then ExternalIP is not allowed
                  to be set.
                properties:
                  autoAssignCIDRs:
                    description: autoAssignCIDRs is a list of CIDRs from which to
                      automatically assign Service.ExternalIP. These are assigned
                      when the service is of type LoadBalancer. In general, this is
                      only useful for bare-metal clusters. In Openshift 3.x, this
                      was misleadingly called "IngressIPs". Automatically assigned
                      External IPs are not affected by any ExternalIPPolicy rules.
                      Currently, only one entry may be provided.
                    items:
                      type: string
                    type: array
                    x-kubernetes-list-type: atomic
                  policy:
                    description: policy is a set of restrictions applied to the ExternalIP
                      field. If nil or empty, then ExternalIP is not allowed to be
                      set.
                    properties:
                      allowedCIDRs:
                        description: allowedCIDRs is the list of allowed CIDRs.
                        items:
                          type: string
                        type: array
                        x-kubernetes-list-type: atomic
                      rejectedCIDRs:
                        description: rejectedCIDRs is the list of disallowed CIDRs.
                          These take precedence over allowedCIDRs.
                        items:
                          type: string
                        type: array
                        x-kubernetes-list-type: atomic
                    type: object
                type: object
              networkDiagnostics:
                description: "networkDiagnostics defines network diagnostics configuration.
                  \n Takes precedence over spec.disableNetworkDiagnostics in network.operator.openshift.io.
                  If networkDiagnostics is not specified or is empty, and the spec.disableNetworkDiagnostics
                  flag in network.operator.openshift.io is set to true, the network
                  diagnostics feature will be disabled."
                properties:
                  mode:
                    description: "mode controls the network diagnostics mode \n When
                      omitted, this means the user has no opinion and the platform
                      is left to choose reasonable defaults. These defaults are subject
                      to change over time. The current default is All."
                    enum:
                    - ""
                    - All
                    - Disabled
                    type: string
                  sourcePlacement:
                    description: "sourcePlacement controls the scheduling of network
                      diagnostics source deployment \n See NetworkDiagnosticsSourcePlacement
                      for more details about default values."
                    properties:
                      nodeSelector:
                        additionalProperties:
                          type: string
                        description: "nodeSelector is the node selector applied to
                          network diagnostics components \n When omitted, this means
                          the user has no opinion and the platform is left to choose
                          reasonable defaults. These defaults are subject to change
                          over time. The current default is `kubernetes.io/os: linux`."
                        type: object
                      tolerations:
                        description: "tolerations is a list of tolerations applied
                          to network diagnostics components \n When omitted, this
                          means the user has no opinion and the platform is left to
                          choose reasonable defaults. These defaults are subject to
                          change over time. The current default is an empty list."
                        items:
                          description: The pod this Toleration is attached to tolerates
                            any taint that matches the triple <key,value,effect> using
                            the matching operator <operator>.
                          properties:
                            effect:
                              description: Effect indicates the taint effect to match.
                                Empty means match all taint effects. When specified,
                                allowed values are NoSchedule, PreferNoSchedule and
                                NoExecute.
                              type: string
                            key:
                              description: Key is the taint key that the toleration
                                applies to. Empty means match all taint keys. If the
                                key is empty, operator must be Exists; this combination
                                means to match all values and all keys.
                              type: string
                            operator:
                              description: Operator represents a key's relationship
                                to the value. Valid operators are Exists and Equal.
                                Defaults to Equal. Exists is equivalent to wildcard
                                for value, so that a pod can tolerate all taints of
                                a particular category.
                              type: string
                            tolerationSeconds:
                              description: TolerationSeconds represents the period
                                of time the toleration (which must be of effect NoExecute,
                                otherwise this field is ignored) tolerates the taint.
                                By default, it is not set, which means tolerate the
                                taint forever (do not evict). Zero and negative values
                                will be treated as 0 (evict immediately) by the system.
                              format: int64
                              type: integer
                            value:
                              description: Value is the taint value the toleration
                                matches to. If the operator is Exists, the value should
                                be empty, otherwise just a regular string.
                              type: string
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                    type: object
                  targetPlacement:
                    description: "targetPlacement controls the scheduling of network
                      diagnostics target daemonset \n See NetworkDiagnosticsTargetPlacement
                      for more details about default values."
                    properties:
                      nodeSelector:
                        additionalProperties:
                          type: string
                        description: "nodeSelector is the node selector applied to
                          network diagnostics components \n When omitted, this means
                          the user has no opinion and the platform is left to choose
                          reasonable defaults. These defaults are subject to change
                          over time. The current default is `kubernetes.io/os: linux`."
                        type: object
                      tolerations:
                        description: "tolerations is a list of tolerations applied
                          to network diagnostics components \n When omitted, this
                          means the user has no opinion and the platform is left to
                          choose reasonable defaults. These defaults are subject to
                          change over time. The current default is `- operator: \"Exists\"`
                          which means that all taints are tolerated."
                        items:
                          description: The pod this Toleration is attached to tolerates
                            any taint that matches the triple <key,value,effect> using
                            the matching operator <operator>.
                          properties:
                            effect:
                              description: Effect indicates the taint effect to match.
                                Empty means match all taint effects. When specified,
                                allowed values are NoSchedule, PreferNoSchedule and
                                NoExecute.
                              type: string
                            key:
                              description: Key is the taint key that the toleration
                                applies to. Empty means match all taint keys. If the
                                key is empty, operator must be Exists; this combination
                                means to match all values and all keys.
                              type: string
                            operator:
                              description: Operator represents a key's relationship
                                to the value. Valid operators are Exists and Equal.
                                Defaults to Equal. Exists is equivalent to wildcard
                                for value, so that a pod can tolerate all taints of
                                a particular category.
                              type: string
                            tolerationSeconds:
                              description: TolerationSeconds represents the period
                                of time the toleration (which must be of effect NoExecute,
                                otherwise this field is ignored) tolerates the taint.
                                By default, it is not set, which means tolerate the
                                taint forever (do not evict). Zero and negative values
                                will be treated as 0 (evict immediately) by the system.
                              format: int64
                              type: integer
                            value:
                              description: Value is the taint value the toleration
                                matches to. If the operator is Exists, the value should
                                be empty, otherwise just a regular string.
                              type: string
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                    type: object
                type: object
              networkType:
                description: 'NetworkType is the plugin that is to be deployed (e.g.
                  OVNKubernetes). This should match a value that the cluster-network-operator
                  understands, or else no networking will be installed. Currently
                  supported values are: - OVNKubernetes This field is immutable after
                  installation.'
                type: string
              serviceNetwork:
                description: IP address pool for services. Currently, we only support
                  a single entry here. This field is immutable after installation.
                items:
                  type: string
                type: array
                x-kubernetes-list-type: atomic
              serviceNodePortRange:
                description: The port range allowed for Services of type NodePort.
                  If not specified, the default of 30000-32767 will be used. Such
                  Services without a NodePort specified will have one automatically
                  allocated from this range. This parameter can be updated after the
                  cluster is installed.
                pattern: ^([0-9]{1,4}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])-([0-9]{1,4}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$
                type: string
            type: object
            x-kubernetes-validations:
            - message: cannot set networkDiagnostics.sourcePlacement and networkDiagnostics.targetPlacement
                when networkDiagnostics.mode is Disabled
              rule: '!has(self.networkDiagnostics) || !has(self.networkDiagnostics.mode)
                || self.networkDiagnostics.mode!=''Disabled'' || !has(self.networkDiagnostics.sourcePlacement)
                && !has(self.networkDiagnostics.targetPlacement)'
          status:
            description: status holds observed values from the cluster. They may not
              be overridden.
            properties:
              clusterNetwork:
                description: IP address pool to use for pod IPs.
                items:
                  description: ClusterNetworkEntry is a contiguous block of IP addresses
                    from which pod IPs are allocated.
                  properties:
                    cidr:
                      description: The complete block for pod IPs.
                      type: string
                    hostPrefix:
                      description: The size (prefix) of block to allocate to each
                        node. If this field is not used by the plugin, it can be left
                        unset.
                      format: int32
                      minimum: 0
                      type: integer
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              clusterNetworkMTU:
                description: ClusterNetworkMTU is the MTU for inter-pod networking.
                type: integer
              conditions:
                description: 'conditions represents the observations of a network.config
                  current state. Known .status.conditions.type are: "NetworkDiagnosticsAvailable"'
                items:
                  description: "Condition contains details for one aspect of the current
                    state of this API Resource. --- This struct is intended for direct
                    use as an array at the field path .status.conditions.  For example,
                    \n type FooStatus struct{ // Represents the observations of a
                    foo's current state. // Known .status.conditions.type are: \"Available\",
                    \"Progressing\", and \"Degraded\" // +patchMergeKey=type // +patchStrategy=merge
                    // +listType=map // +listMapKey=type Conditions []metav1.Condition
                    `json:\"conditions,omitempty\" patchStrategy:\"merge\" patchMergeKey:\"type\"
                    protobuf:\"bytes,1,rep,name=conditions\"` \n // other fields }"
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the last time the condition
                        transitioned from one status to another. This should be when
                        the underlying condition changed.  If that is not known, then
                        using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: message is a human readable message indicating
                        details about the transition. This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: observedGeneration represents the .metadata.generation
                        that the condition was set based upon. For instance, if .metadata.generation
                        is currently 12, but the .status.conditions[x].observedGeneration
                        is 9, the condition is out of date with respect to the current
                        state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: reason contains a programmatic identifier indicating
                        the reason for the condition's last transition. Producers
                        of specific condition types may define expected values and
                        meanings for this field, and whether the values are considered
                        a guaranteed API. The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                        --- Many .condition.type values are consistent across resources
                        like Available, but because arbitrary conditions can be useful
                        (see .node.status.conditions), the ability to deconflict is
                        important. The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              migration:
                description: Migration contains the cluster network migration configuration.
                properties:
                  mtu:
                    description: MTU is the MTU configuration that is being deployed.
                    properties:
                      machine:
                        description: Machine contains MTU migration configuration
                          for the machine's uplink.
                        properties:
                          from:
                            description: From is the MTU to migrate from.
                            format: int32
                            minimum: 0
                            type: integer
                          to:
                            description: To is the MTU to migrate to.
                            format: int32
                            minimum: 0
                            type: integer
                        type: object
                      network:
                        description: Network contains MTU migration configuration
                          for the default network.
                        properties:
                          from:
                            description: From is the MTU to migrate from.
                            format: int32
                            minimum: 0
                            type: integer
                          to:
                            description: To is the MTU to migrate to.
                            format: int32
                            minimum: 0
                            type: integer
                        type: object
                    type: object
                  networkType:
                    description: 'NetworkType is the target plugin that is being deployed.
                      DEPRECATED: network type migration is no longer supported, so
                      this should always be unset.'
                    type: string
                type: object
              networkType:
                description: NetworkType is the plugin that is deployed (e.g. OVNKubernetes).
                type: string
              serviceNetwork:
                description: IP address pool for services. Currently, we only support
                  a single entry here.
                items:
                  type: string
                type: array
                x-kubernetes-list-type: atomic
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
status:
  acceptedNames:
    kind: Network
    listKind: NetworkList
    plural: networks
    singular: network
  conditions:
  - lastTransitionTime: "2025-06-25T14:52:56Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:52:56Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
