---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
    exclude.release.openshift.io/internal-openshift-hosted: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T18:39:14Z"
  generation: 2
  labels:
    cluster.x-k8s.io/provider: cluster-api
    clusterctl.cluster.x-k8s.io: ""
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:categories: {}
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T18:39:14Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:controller-gen.kubebuilder.io/version: {}
          f:exclude.release.openshift.io/internal-openshift-hosted: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:labels:
          .: {}
          f:cluster.x-k8s.io/provider: {}
          f:clusterctl.cluster.x-k8s.io: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:categories: {}
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T12:05:32Z"
  name: ipaddresses.ipam.cluster.x-k8s.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21338476"
  uid: 24b1ce21-cc05-4295-9593-81a7efa43ef1
spec:
  conversion:
    strategy: None
  group: ipam.cluster.x-k8s.io
  names:
    categories:
    - cluster-api
    kind: IPAddress
    listKind: IPAddressList
    plural: ipaddresses
    singular: ipaddress
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Address
      jsonPath: .spec.address
      name: Address
      type: string
    - description: Name of the pool the address is from
      jsonPath: .spec.poolRef.name
      name: Pool Name
      type: string
    - description: Kind of the pool the address is from
      jsonPath: .spec.poolRef.kind
      name: Pool Kind
      type: string
    - description: Time duration since creation of IPAdress
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: IPAddress is the Schema for the ipaddress API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: IPAddressSpec is the desired state of an IPAddress.
            properties:
              address:
                description: Address is the IP address.
                type: string
              claimRef:
                description: ClaimRef is a reference to the claim this IPAddress was
                  created for.
                properties:
                  name:
                    description: |-
                      Name of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                      TODO: Add other useful fields. apiVersion, kind, uid?
                    type: string
                type: object
                x-kubernetes-map-type: atomic
              gateway:
                description: Gateway is the network gateway of the network the address
                  is from.
                type: string
              poolRef:
                description: PoolRef is a reference to the pool that this IPAddress
                  was created from.
                properties:
                  apiGroup:
                    description: |-
                      APIGroup is the group for the resource being referenced.
                      If APIGroup is not specified, the specified Kind must be in the core API group.
                      For any other third-party types, APIGroup is required.
                    type: string
                  kind:
                    description: Kind is the type of resource being referenced
                    type: string
                  name:
                    description: Name is the name of resource being referenced
                    type: string
                required:
                - kind
                - name
                type: object
                x-kubernetes-map-type: atomic
              prefix:
                description: Prefix is the prefix of the address.
                type: integer
            required:
            - address
            - claimRef
            - poolRef
            - prefix
            type: object
        type: object
    served: true
    storage: false
    subresources: {}
  - additionalPrinterColumns:
    - description: Address
      jsonPath: .spec.address
      name: Address
      type: string
    - description: Name of the pool the address is from
      jsonPath: .spec.poolRef.name
      name: Pool Name
      type: string
    - description: Kind of the pool the address is from
      jsonPath: .spec.poolRef.kind
      name: Pool Kind
      type: string
    - description: Time duration since creation of IPAdress
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1beta1
    schema:
      openAPIV3Schema:
        description: IPAddress is the Schema for the ipaddress API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: IPAddressSpec is the desired state of an IPAddress.
            properties:
              address:
                description: Address is the IP address.
                type: string
              claimRef:
                description: ClaimRef is a reference to the claim this IPAddress was
                  created for.
                properties:
                  name:
                    description: |-
                      Name of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                      TODO: Add other useful fields. apiVersion, kind, uid?
                    type: string
                type: object
                x-kubernetes-map-type: atomic
              gateway:
                description: Gateway is the network gateway of the network the address
                  is from.
                type: string
              poolRef:
                description: PoolRef is a reference to the pool that this IPAddress
                  was created from.
                properties:
                  apiGroup:
                    description: |-
                      APIGroup is the group for the resource being referenced.
                      If APIGroup is not specified, the specified Kind must be in the core API group.
                      For any other third-party types, APIGroup is required.
                    type: string
                  kind:
                    description: Kind is the type of resource being referenced
                    type: string
                  name:
                    description: Name is the name of resource being referenced
                    type: string
                required:
                - kind
                - name
                type: object
                x-kubernetes-map-type: atomic
              prefix:
                description: Prefix is the prefix of the address.
                type: integer
            required:
            - address
            - claimRef
            - poolRef
            - prefix
            type: object
        type: object
    served: true
    storage: true
    subresources: {}
status:
  acceptedNames:
    categories:
    - cluster-api
    kind: IPAddress
    listKind: IPAddressList
    plural: ipaddresses
    singular: ipaddress
  conditions:
  - lastTransitionTime: "2025-06-25T18:39:14Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T18:39:14Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1beta1
