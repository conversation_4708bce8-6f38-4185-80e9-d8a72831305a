---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
    exclude.release.openshift.io/internal-openshift-hosted: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T18:39:24Z"
  generation: 2
  labels:
    cluster.x-k8s.io/provider: cluster-api
    clusterctl.cluster.x-k8s.io: ""
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:categories: {}
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T18:39:24Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:controller-gen.kubebuilder.io/version: {}
          f:exclude.release.openshift.io/internal-openshift-hosted: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:labels:
          .: {}
          f:cluster.x-k8s.io/provider: {}
          f:clusterctl.cluster.x-k8s.io: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:categories: {}
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T12:05:33Z"
  name: ipaddressclaims.ipam.cluster.x-k8s.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21338487"
  uid: bc5d4b0e-71b0-419c-b514-a4c8b53515a4
spec:
  conversion:
    strategy: None
  group: ipam.cluster.x-k8s.io
  names:
    categories:
    - cluster-api
    kind: IPAddressClaim
    listKind: IPAddressClaimList
    plural: ipaddressclaims
    singular: ipaddressclaim
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Name of the pool to allocate an address from
      jsonPath: .spec.poolRef.name
      name: Pool Name
      type: string
    - description: Kind of the pool to allocate an address from
      jsonPath: .spec.poolRef.kind
      name: Pool Kind
      type: string
    - description: Time duration since creation of IPAdressClaim
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: IPAddressClaim is the Schema for the ipaddressclaim API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: IPAddressClaimSpec is the desired state of an IPAddressClaim.
            properties:
              poolRef:
                description: PoolRef is a reference to the pool from which an IP address
                  should be created.
                properties:
                  apiGroup:
                    description: |-
                      APIGroup is the group for the resource being referenced.
                      If APIGroup is not specified, the specified Kind must be in the core API group.
                      For any other third-party types, APIGroup is required.
                    type: string
                  kind:
                    description: Kind is the type of resource being referenced
                    type: string
                  name:
                    description: Name is the name of resource being referenced
                    type: string
                required:
                - kind
                - name
                type: object
                x-kubernetes-map-type: atomic
            required:
            - poolRef
            type: object
          status:
            description: IPAddressClaimStatus is the observed status of a IPAddressClaim.
            properties:
              addressRef:
                description: AddressRef is a reference to the address that was created
                  for this claim.
                properties:
                  name:
                    description: |-
                      Name of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                      TODO: Add other useful fields. apiVersion, kind, uid?
                    type: string
                type: object
                x-kubernetes-map-type: atomic
              conditions:
                description: Conditions summarises the current state of the IPAddressClaim
                items:
                  description: Condition defines an observation of a Cluster API resource
                    operational state.
                  properties:
                    lastTransitionTime:
                      description: |-
                        Last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed. If that is not known, then using the time when
                        the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        A human readable message indicating details about the transition.
                        This field may be empty.
                      type: string
                    reason:
                      description: |-
                        The reason for the condition's last transition in CamelCase.
                        The specific API may choose whether or not this field is considered a guaranteed API.
                        This field may not be empty.
                      type: string
                    severity:
                      description: |-
                        Severity provides an explicit classification of Reason code, so the users or machines can immediately
                        understand the current situation and act accordingly.
                        The Severity field MUST be set only when Status=False.
                      type: string
                    status:
                      description: Status of the condition, one of True, False, Unknown.
                      type: string
                    type:
                      description: |-
                        Type of condition in CamelCase or in foo.example.com/CamelCase.
                        Many .condition.type values are consistent across resources like Available, but because arbitrary conditions
                        can be useful (see .node.status.conditions), the ability to deconflict is important.
                      type: string
                  required:
                  - lastTransitionTime
                  - status
                  - type
                  type: object
                type: array
            type: object
        type: object
    served: true
    storage: false
    subresources:
      status: {}
  - additionalPrinterColumns:
    - description: Name of the pool to allocate an address from
      jsonPath: .spec.poolRef.name
      name: Pool Name
      type: string
    - description: Kind of the pool to allocate an address from
      jsonPath: .spec.poolRef.kind
      name: Pool Kind
      type: string
    - description: Time duration since creation of IPAdressClaim
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1beta1
    schema:
      openAPIV3Schema:
        description: IPAddressClaim is the Schema for the ipaddressclaim API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: IPAddressClaimSpec is the desired state of an IPAddressClaim.
            properties:
              poolRef:
                description: PoolRef is a reference to the pool from which an IP address
                  should be created.
                properties:
                  apiGroup:
                    description: |-
                      APIGroup is the group for the resource being referenced.
                      If APIGroup is not specified, the specified Kind must be in the core API group.
                      For any other third-party types, APIGroup is required.
                    type: string
                  kind:
                    description: Kind is the type of resource being referenced
                    type: string
                  name:
                    description: Name is the name of resource being referenced
                    type: string
                required:
                - kind
                - name
                type: object
                x-kubernetes-map-type: atomic
            required:
            - poolRef
            type: object
          status:
            description: IPAddressClaimStatus is the observed status of a IPAddressClaim.
            properties:
              addressRef:
                description: AddressRef is a reference to the address that was created
                  for this claim.
                properties:
                  name:
                    description: |-
                      Name of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                      TODO: Add other useful fields. apiVersion, kind, uid?
                    type: string
                type: object
                x-kubernetes-map-type: atomic
              conditions:
                description: Conditions summarises the current state of the IPAddressClaim
                items:
                  description: Condition defines an observation of a Cluster API resource
                    operational state.
                  properties:
                    lastTransitionTime:
                      description: |-
                        Last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed. If that is not known, then using the time when
                        the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        A human readable message indicating details about the transition.
                        This field may be empty.
                      type: string
                    reason:
                      description: |-
                        The reason for the condition's last transition in CamelCase.
                        The specific API may choose whether or not this field is considered a guaranteed API.
                        This field may not be empty.
                      type: string
                    severity:
                      description: |-
                        Severity provides an explicit classification of Reason code, so the users or machines can immediately
                        understand the current situation and act accordingly.
                        The Severity field MUST be set only when Status=False.
                      type: string
                    status:
                      description: Status of the condition, one of True, False, Unknown.
                      type: string
                    type:
                      description: |-
                        Type of condition in CamelCase or in foo.example.com/CamelCase.
                        Many .condition.type values are consistent across resources like Available, but because arbitrary conditions
                        can be useful (see .node.status.conditions), the ability to deconflict is important.
                      type: string
                  required:
                  - lastTransitionTime
                  - status
                  - type
                  type: object
                type: array
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    categories:
    - cluster-api
    kind: IPAddressClaim
    listKind: IPAddressClaimList
    plural: ipaddressclaims
    singular: ipaddressclaim
  conditions:
  - lastTransitionTime: "2025-06-25T18:39:24Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T18:39:24Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1beta1
