---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.5
  creationTimestamp: "2025-06-25T15:21:58Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:controller-gen.kubebuilder.io/version: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: Go-http-client
    operation: Update
    time: "2025-06-25T15:21:58Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T15:21:58Z"
  name: previewfeatures.preview.aro.openshift.io
  resourceVersion: "41440"
  uid: 5c49421e-2494-49cd-93c1-53d9dc9df322
spec:
  conversion:
    strategy: None
  group: preview.aro.openshift.io
  names:
    kind: PreviewFeature
    listKind: PreviewFeatureList
    plural: previewfeatures
    singular: previewfeature
  scope: Cluster
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: PreviewFeature is the Schema for the preview feature API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: PreviewFeatureSpec defines the preview feature for ARO
            properties:
              nsgFlowLogs:
                description: |-
                  NSGFlowLogs contains configuration for NSG flow logs.
                  Omit the configuration if you don't want the controller
                  to reconcile NSG flow logs.
                properties:
                  enabled:
                    description: |-
                      Enabled defines the behaviour of the reconciler:
                      when true the controller will try to reach the desired configuration,
                      when false it will try to disable the flow logs.
                    type: boolean
                  networkWatcherID:
                    description: NetworkWatcherID specifies the ID of the network
                      watcher.
                    type: string
                  retentionDays:
                    default: 90
                    description: RetentionDays specifies how many days the flowlogs
                      should be retained.
                    format: int32
                    type: integer
                  storageAccountResourceId:
                    description: |-
                      StorageAccountResourceID of the storage account used for collecting the flow logs.
                      Must be in the same region of flow log.
                    type: string
                  trafficAnalyticsInterval:
                    default: 60m
                    description: 'Interval at which to conduct flow analytics. Values:
                      60m, 10m. Default: 60m.'
                    enum:
                    - 60m
                    - 10m
                    type: string
                  trafficAnalyticsLogAnalyticsWorkspaceId:
                    description: |-
                      Required for TrafficAnalytics.
                      Must be in the same region of flow log.
                    type: string
                  version:
                    default: 2
                    description: Version defines version of NSG flow log.
                    enum:
                    - 1
                    - 2
                    type: integer
                required:
                - enabled
                type: object
            type: object
          status:
            description: PreviewFeatureStatus defines the observed state of PreviewFeature
            properties:
              conditions:
                items:
                  description: OperatorCondition is just the standard condition fields.
                  properties:
                    lastTransitionTime:
                      format: date-time
                      type: string
                    message:
                      type: string
                    reason:
                      type: string
                    status:
                      type: string
                    type:
                      type: string
                  type: object
                type: array
              operatorVersion:
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: PreviewFeature
    listKind: PreviewFeatureList
    plural: previewfeatures
    singular: previewfeature
  conditions:
  - lastTransitionTime: "2025-06-25T15:21:58Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T15:21:58Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1alpha1
