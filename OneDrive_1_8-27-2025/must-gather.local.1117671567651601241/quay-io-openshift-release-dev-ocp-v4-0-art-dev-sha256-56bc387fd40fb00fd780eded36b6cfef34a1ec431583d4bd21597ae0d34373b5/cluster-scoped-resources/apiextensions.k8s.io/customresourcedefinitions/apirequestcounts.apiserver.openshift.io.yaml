---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/897
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:53:20Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-bootstrap
    operation: Update
    time: "2025-06-25T14:53:20Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:20Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:api.openshift.io/merged-by-featuregates: {}
    manager: cluster-kube-apiserver-operator
    operation: Update
    time: "2025-08-01T11:47:22Z"
  name: apirequestcounts.apiserver.openshift.io
  resourceVersion: "21328308"
  uid: 8efa5ad3-af4e-4205-85b3-49499ec6230b
spec:
  conversion:
    strategy: None
  group: apiserver.openshift.io
  names:
    kind: APIRequestCount
    listKind: APIRequestCountList
    plural: apirequestcounts
    singular: apirequestcount
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - description: Release in which an API will be removed.
      jsonPath: .status.removedInRelease
      name: RemovedInRelease
      type: string
    - description: Number of requests in the current hour.
      jsonPath: .status.currentHour.requestCount
      name: RequestsInCurrentHour
      type: integer
    - description: Number of requests in the last 24h.
      jsonPath: .status.requestCount
      name: RequestsInLast24h
      type: integer
    name: v1
    schema:
      openAPIV3Schema:
        description: "APIRequestCount tracks requests made to an API. The instance
          name must be of the form `resource.version.group`, matching the resource.
          \n Compatibility level 1: Stable within a major release for a minimum of
          12 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: spec defines the characteristics of the resource.
            properties:
              numberOfUsersToReport:
                default: 10
                description: numberOfUsersToReport is the number of users to include
                  in the report. If unspecified or zero, the default is ten.  This
                  is default is subject to change.
                format: int64
                maximum: 100
                minimum: 0
                type: integer
            type: object
          status:
            description: status contains the observed state of the resource.
            properties:
              conditions:
                description: conditions contains details of the current status of
                  this API Resource.
                items:
                  description: "Condition contains details for one aspect of the current
                    state of this API Resource. --- This struct is intended for direct
                    use as an array at the field path .status.conditions.  For example,
                    \n type FooStatus struct{ // Represents the observations of a
                    foo's current state. // Known .status.conditions.type are: \"Available\",
                    \"Progressing\", and \"Degraded\" // +patchMergeKey=type // +patchStrategy=merge
                    // +listType=map // +listMapKey=type Conditions []metav1.Condition
                    `json:\"conditions,omitempty\" patchStrategy:\"merge\" patchMergeKey:\"type\"
                    protobuf:\"bytes,1,rep,name=conditions\"` \n // other fields }"
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the last time the condition
                        transitioned from one status to another. This should be when
                        the underlying condition changed.  If that is not known, then
                        using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: message is a human readable message indicating
                        details about the transition. This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: observedGeneration represents the .metadata.generation
                        that the condition was set based upon. For instance, if .metadata.generation
                        is currently 12, but the .status.conditions[x].observedGeneration
                        is 9, the condition is out of date with respect to the current
                        state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: reason contains a programmatic identifier indicating
                        the reason for the condition's last transition. Producers
                        of specific condition types may define expected values and
                        meanings for this field, and whether the values are considered
                        a guaranteed API. The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                        --- Many .condition.type values are consistent across resources
                        like Available, but because arbitrary conditions can be useful
                        (see .node.status.conditions), the ability to deconflict is
                        important. The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              currentHour:
                description: currentHour contains request history for the current
                  hour. This is porcelain to make the API easier to read by humans
                  seeing if they addressed a problem. This field is reset on the hour.
                properties:
                  byNode:
                    description: byNode contains logs of requests per node.
                    items:
                      description: PerNodeAPIRequestLog contains logs of requests
                        to a certain node.
                      properties:
                        byUser:
                          description: byUser contains request details by top .spec.numberOfUsersToReport
                            users. Note that because in the case of an apiserver,
                            restart the list of top users is determined on a best-effort
                            basis, the list might be imprecise. In addition, some
                            system users may be explicitly included in the list.
                          items:
                            description: PerUserAPIRequestCount contains logs of a
                              user's requests.
                            properties:
                              byVerb:
                                description: byVerb details by verb.
                                items:
                                  description: PerVerbAPIRequestCount requestCounts
                                    requests by API request verb.
                                  properties:
                                    requestCount:
                                      description: requestCount of requests for verb.
                                      format: int64
                                      minimum: 0
                                      type: integer
                                    verb:
                                      description: verb of API request (get, list,
                                        create, etc...)
                                      maxLength: 20
                                      type: string
                                  type: object
                                maxItems: 10
                                type: array
                              requestCount:
                                description: requestCount of requests by the user
                                  across all verbs.
                                format: int64
                                minimum: 0
                                type: integer
                              userAgent:
                                description: userAgent that made the request. The
                                  same user often has multiple binaries which connect
                                  (pods with many containers).  The different binaries
                                  will have different userAgents, but the same user.  In
                                  addition, we have userAgents with version information
                                  embedded and the userName isn't likely to change.
                                maxLength: 1024
                                type: string
                              username:
                                description: userName that made the request.
                                maxLength: 512
                                type: string
                            type: object
                          maxItems: 500
                          type: array
                        nodeName:
                          description: nodeName where the request are being handled.
                          maxLength: 512
                          minLength: 1
                          type: string
                        requestCount:
                          description: requestCount is a sum of all requestCounts
                            across all users, even those outside of the top 10 users.
                          format: int64
                          minimum: 0
                          type: integer
                      type: object
                    maxItems: 512
                    type: array
                  requestCount:
                    description: requestCount is a sum of all requestCounts across
                      nodes.
                    format: int64
                    minimum: 0
                    type: integer
                type: object
              last24h:
                description: last24h contains request history for the last 24 hours,
                  indexed by the hour, so 12:00AM-12:59 is in index 0, 6am-6:59am
                  is index 6, etc. The index of the current hour is updated live and
                  then duplicated into the requestsLastHour field.
                items:
                  description: PerResourceAPIRequestLog logs request for various nodes.
                  properties:
                    byNode:
                      description: byNode contains logs of requests per node.
                      items:
                        description: PerNodeAPIRequestLog contains logs of requests
                          to a certain node.
                        properties:
                          byUser:
                            description: byUser contains request details by top .spec.numberOfUsersToReport
                              users. Note that because in the case of an apiserver,
                              restart the list of top users is determined on a best-effort
                              basis, the list might be imprecise. In addition, some
                              system users may be explicitly included in the list.
                            items:
                              description: PerUserAPIRequestCount contains logs of
                                a user's requests.
                              properties:
                                byVerb:
                                  description: byVerb details by verb.
                                  items:
                                    description: PerVerbAPIRequestCount requestCounts
                                      requests by API request verb.
                                    properties:
                                      requestCount:
                                        description: requestCount of requests for
                                          verb.
                                        format: int64
                                        minimum: 0
                                        type: integer
                                      verb:
                                        description: verb of API request (get, list,
                                          create, etc...)
                                        maxLength: 20
                                        type: string
                                    type: object
                                  maxItems: 10
                                  type: array
                                requestCount:
                                  description: requestCount of requests by the user
                                    across all verbs.
                                  format: int64
                                  minimum: 0
                                  type: integer
                                userAgent:
                                  description: userAgent that made the request. The
                                    same user often has multiple binaries which connect
                                    (pods with many containers).  The different binaries
                                    will have different userAgents, but the same user.  In
                                    addition, we have userAgents with version information
                                    embedded and the userName isn't likely to change.
                                  maxLength: 1024
                                  type: string
                                username:
                                  description: userName that made the request.
                                  maxLength: 512
                                  type: string
                              type: object
                            maxItems: 500
                            type: array
                          nodeName:
                            description: nodeName where the request are being handled.
                            maxLength: 512
                            minLength: 1
                            type: string
                          requestCount:
                            description: requestCount is a sum of all requestCounts
                              across all users, even those outside of the top 10 users.
                            format: int64
                            minimum: 0
                            type: integer
                        type: object
                      maxItems: 512
                      type: array
                    requestCount:
                      description: requestCount is a sum of all requestCounts across
                        nodes.
                      format: int64
                      minimum: 0
                      type: integer
                  type: object
                maxItems: 24
                type: array
              removedInRelease:
                description: removedInRelease is when the API will be removed.
                maxLength: 64
                minLength: 0
                pattern: ^[0-9][0-9]*\.[0-9][0-9]*$
                type: string
              requestCount:
                description: requestCount is a sum of all requestCounts across all
                  current hours, nodes, and users.
                format: int64
                minimum: 0
                type: integer
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: APIRequestCount
    listKind: APIRequestCountList
    plural: apirequestcounts
    singular: apirequestcount
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:20Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:20Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
