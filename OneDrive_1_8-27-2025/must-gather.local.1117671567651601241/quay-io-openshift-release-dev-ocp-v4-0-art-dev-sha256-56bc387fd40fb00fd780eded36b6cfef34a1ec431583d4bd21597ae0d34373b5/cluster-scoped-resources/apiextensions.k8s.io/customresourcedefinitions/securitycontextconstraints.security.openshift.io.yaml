---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/470
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    release.openshift.io/bootstrap-required: "true"
  creationTimestamp: "2025-06-25T14:52:51Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-bootstrap
    operation: Update
    time: "2025-06-25T14:52:51Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:52:51Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:api.openshift.io/merged-by-featuregates: {}
          f:release.openshift.io/bootstrap-required: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:19:45Z"
  name: securitycontextconstraints.security.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "133562"
  uid: e24a1ce2-06fa-454e-8aff-6e4f37db049d
spec:
  conversion:
    strategy: None
  group: security.openshift.io
  names:
    kind: SecurityContextConstraints
    listKind: SecurityContextConstraintsList
    plural: securitycontextconstraints
    singular: securitycontextconstraints
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - description: Determines if a container can request to be run as privileged
      jsonPath: .allowPrivilegedContainer
      name: Priv
      type: string
    - description: A list of capabilities that can be requested to add to the container
      jsonPath: .allowedCapabilities
      name: Caps
      type: string
    - description: Strategy that will dictate what labels will be set in the SecurityContext
      jsonPath: .seLinuxContext.type
      name: SELinux
      type: string
    - description: Strategy that will dictate what RunAsUser is used in the SecurityContext
      jsonPath: .runAsUser.type
      name: RunAsUser
      type: string
    - description: Strategy that will dictate what fs group is used by the SecurityContext
      jsonPath: .fsGroup.type
      name: FSGroup
      type: string
    - description: Strategy that will dictate what supplemental groups are used by
        the SecurityContext
      jsonPath: .supplementalGroups.type
      name: SupGroup
      type: string
    - description: Sort order of SCCs
      jsonPath: .priority
      name: Priority
      type: string
    - description: Force containers to run with a read only root file system
      jsonPath: .readOnlyRootFilesystem
      name: ReadOnlyRootFS
      type: string
    - description: White list of allowed volume plugins
      jsonPath: .volumes
      name: Volumes
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: "SecurityContextConstraints governs the ability to make requests
          that affect the SecurityContext that will be applied to a container. For
          historical reasons SCC was exposed under the core Kubernetes API group.
          That exposure is deprecated and will be removed in a future release - users
          should instead use the security.openshift.io group to manage SecurityContextConstraints.
          \n Compatibility level 1: Stable within a major release for a minimum of
          12 months or 3 minor releases (whichever is longer)."
        properties:
          allowHostDirVolumePlugin:
            description: AllowHostDirVolumePlugin determines if the policy allow containers
              to use the HostDir volume plugin
            type: boolean
          allowHostIPC:
            description: AllowHostIPC determines if the policy allows host ipc in
              the containers.
            type: boolean
          allowHostNetwork:
            description: AllowHostNetwork determines if the policy allows the use
              of HostNetwork in the pod spec.
            type: boolean
          allowHostPID:
            description: AllowHostPID determines if the policy allows host pid in
              the containers.
            type: boolean
          allowHostPorts:
            description: AllowHostPorts determines if the policy allows host ports
              in the containers.
            type: boolean
          allowPrivilegeEscalation:
            description: AllowPrivilegeEscalation determines if a pod can request
              to allow privilege escalation. If unspecified, defaults to true.
            nullable: true
            type: boolean
          allowPrivilegedContainer:
            description: AllowPrivilegedContainer determines if a container can request
              to be run as privileged.
            type: boolean
          allowedCapabilities:
            description: AllowedCapabilities is a list of capabilities that can be
              requested to add to the container. Capabilities in this field maybe
              added at the pod author's discretion. You must not list a capability
              in both AllowedCapabilities and RequiredDropCapabilities. To allow all
              capabilities you may use '*'.
            items:
              description: Capability represent POSIX capabilities type
              type: string
            nullable: true
            type: array
          allowedFlexVolumes:
            description: AllowedFlexVolumes is a whitelist of allowed Flexvolumes.  Empty
              or nil indicates that all Flexvolumes may be used.  This parameter is
              effective only when the usage of the Flexvolumes is allowed in the "Volumes"
              field.
            items:
              description: AllowedFlexVolume represents a single Flexvolume that is
                allowed to be used.
              properties:
                driver:
                  description: Driver is the name of the Flexvolume driver.
                  type: string
              required:
              - driver
              type: object
            nullable: true
            type: array
          allowedUnsafeSysctls:
            description: "AllowedUnsafeSysctls is a list of explicitly allowed unsafe
              sysctls, defaults to none. Each entry is either a plain sysctl name
              or ends in \"*\" in which case it is considered as a prefix of allowed
              sysctls. Single * means all unsafe sysctls are allowed. Kubelet has
              to whitelist all allowed unsafe sysctls explicitly to avoid rejection.
              \n Examples: e.g. \"foo/*\" allows \"foo/bar\", \"foo/baz\", etc. e.g.
              \"foo.*\" allows \"foo.bar\", \"foo.baz\", etc."
            items:
              type: string
            nullable: true
            type: array
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          defaultAddCapabilities:
            description: DefaultAddCapabilities is the default set of capabilities
              that will be added to the container unless the pod spec specifically
              drops the capability.  You may not list a capabiility in both DefaultAddCapabilities
              and RequiredDropCapabilities.
            items:
              description: Capability represent POSIX capabilities type
              type: string
            nullable: true
            type: array
          defaultAllowPrivilegeEscalation:
            description: DefaultAllowPrivilegeEscalation controls the default setting
              for whether a process can gain more privileges than its parent process.
            nullable: true
            type: boolean
          forbiddenSysctls:
            description: "ForbiddenSysctls is a list of explicitly forbidden sysctls,
              defaults to none. Each entry is either a plain sysctl name or ends in
              \"*\" in which case it is considered as a prefix of forbidden sysctls.
              Single * means all sysctls are forbidden. \n Examples: e.g. \"foo/*\"
              forbids \"foo/bar\", \"foo/baz\", etc. e.g. \"foo.*\" forbids \"foo.bar\",
              \"foo.baz\", etc."
            items:
              type: string
            nullable: true
            type: array
          fsGroup:
            description: FSGroup is the strategy that will dictate what fs group is
              used by the SecurityContext.
            nullable: true
            properties:
              ranges:
                description: Ranges are the allowed ranges of fs groups.  If you would
                  like to force a single fs group then supply a single range with
                  the same start and end.
                items:
                  description: 'IDRange provides a min/max of an allowed range of
                    IDs. TODO: this could be reused for UIDs.'
                  properties:
                    max:
                      description: Max is the end of the range, inclusive.
                      format: int64
                      type: integer
                    min:
                      description: Min is the start of the range, inclusive.
                      format: int64
                      type: integer
                  type: object
                type: array
              type:
                description: Type is the strategy that will dictate what FSGroup is
                  used in the SecurityContext.
                type: string
            type: object
          groups:
            description: The groups that have permission to use this security context
              constraints
            items:
              type: string
            nullable: true
            type: array
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          priority:
            description: Priority influences the sort order of SCCs when evaluating
              which SCCs to try first for a given pod request based on access in the
              Users and Groups fields.  The higher the int, the higher priority. An
              unset value is considered a 0 priority. If scores for multiple SCCs
              are equal they will be sorted from most restrictive to least restrictive.
              If both priorities and restrictions are equal the SCCs will be sorted
              by name.
            format: int32
            nullable: true
            type: integer
          readOnlyRootFilesystem:
            description: ReadOnlyRootFilesystem when set to true will force containers
              to run with a read only root file system.  If the container specifically
              requests to run with a non-read only root file system the SCC should
              deny the pod. If set to false the container may run with a read only
              root file system if it wishes but it will not be forced to.
            type: boolean
          requiredDropCapabilities:
            description: RequiredDropCapabilities are the capabilities that will be
              dropped from the container.  These are required to be dropped and cannot
              be added.
            items:
              description: Capability represent POSIX capabilities type
              type: string
            nullable: true
            type: array
          runAsUser:
            description: RunAsUser is the strategy that will dictate what RunAsUser
              is used in the SecurityContext.
            nullable: true
            properties:
              type:
                description: Type is the strategy that will dictate what RunAsUser
                  is used in the SecurityContext.
                type: string
              uid:
                description: UID is the user id that containers must run as.  Required
                  for the MustRunAs strategy if not using namespace/service account
                  allocated uids.
                format: int64
                type: integer
              uidRangeMax:
                description: UIDRangeMax defines the max value for a strategy that
                  allocates by range.
                format: int64
                type: integer
              uidRangeMin:
                description: UIDRangeMin defines the min value for a strategy that
                  allocates by range.
                format: int64
                type: integer
            type: object
          seLinuxContext:
            description: SELinuxContext is the strategy that will dictate what labels
              will be set in the SecurityContext.
            nullable: true
            properties:
              seLinuxOptions:
                description: seLinuxOptions required to run as; required for MustRunAs
                properties:
                  level:
                    description: Level is SELinux level label that applies to the
                      container.
                    type: string
                  role:
                    description: Role is a SELinux role label that applies to the
                      container.
                    type: string
                  type:
                    description: Type is a SELinux type label that applies to the
                      container.
                    type: string
                  user:
                    description: User is a SELinux user label that applies to the
                      container.
                    type: string
                type: object
              type:
                description: Type is the strategy that will dictate what SELinux context
                  is used in the SecurityContext.
                type: string
            type: object
          seccompProfiles:
            description: "SeccompProfiles lists the allowed profiles that may be set
              for the pod or container's seccomp annotations.  An unset (nil) or empty
              value means that no profiles may be specifid by the pod or container.\tThe
              wildcard '*' may be used to allow all profiles.  When used to generate
              a value for a pod the first non-wildcard profile will be used as the
              default."
            items:
              type: string
            nullable: true
            type: array
          supplementalGroups:
            description: SupplementalGroups is the strategy that will dictate what
              supplemental groups are used by the SecurityContext.
            nullable: true
            properties:
              ranges:
                description: Ranges are the allowed ranges of supplemental groups.  If
                  you would like to force a single supplemental group then supply
                  a single range with the same start and end.
                items:
                  description: 'IDRange provides a min/max of an allowed range of
                    IDs. TODO: this could be reused for UIDs.'
                  properties:
                    max:
                      description: Max is the end of the range, inclusive.
                      format: int64
                      type: integer
                    min:
                      description: Min is the start of the range, inclusive.
                      format: int64
                      type: integer
                  type: object
                type: array
              type:
                description: Type is the strategy that will dictate what supplemental
                  groups is used in the SecurityContext.
                type: string
            type: object
          users:
            description: The users who have permissions to use this security context
              constraints
            items:
              type: string
            nullable: true
            type: array
          volumes:
            description: Volumes is a white list of allowed volume plugins.  FSType
              corresponds directly with the field names of a VolumeSource (azureFile,
              configMap, emptyDir).  To allow all volumes you may use "*". To allow
              no volumes, set to ["none"].
            items:
              description: FS Type gives strong typing to different file systems that
                are used by volumes.
              type: string
            nullable: true
            type: array
        required:
        - allowHostDirVolumePlugin
        - allowHostIPC
        - allowHostNetwork
        - allowHostPID
        - allowHostPorts
        - allowPrivilegedContainer
        - allowedCapabilities
        - defaultAddCapabilities
        - priority
        - readOnlyRootFilesystem
        - requiredDropCapabilities
        - volumes
        type: object
    served: true
    storage: true
status:
  acceptedNames:
    kind: SecurityContextConstraints
    listKind: SecurityContextConstraintsList
    plural: securitycontextconstraints
    singular: securitycontextconstraints
  conditions:
  - lastTransitionTime: "2025-06-25T14:52:51Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:52:51Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
