---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/470
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    release.openshift.io/bootstrap-required: "true"
    release.openshift.io/feature-set: Default
  creationTimestamp: "2025-06-25T14:52:52Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
          f:release.openshift.io/feature-set: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-bootstrap
    operation: Update
    time: "2025-06-25T14:52:52Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:52:52Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:api.openshift.io/merged-by-featuregates: {}
          f:release.openshift.io/bootstrap-required: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:19:46Z"
  name: dnses.config.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "133575"
  uid: afde3480-dc7b-4555-bb58-78bf3cdd74e3
spec:
  conversion:
    strategy: None
  group: config.openshift.io
  names:
    kind: DNS
    listKind: DNSList
    plural: dnses
    singular: dns
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "DNS holds cluster-wide information about DNS. The canonical
          name is `cluster` \n Compatibility level 1: Stable within a major release
          for a minimum of 12 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: spec holds user settable values for configuration
            properties:
              baseDomain:
                description: "baseDomain is the base domain of the cluster. All managed
                  DNS records will be sub-domains of this base. \n For example, given
                  the base domain `openshift.example.com`, an API server DNS record
                  may be created for `cluster-api.openshift.example.com`. \n Once
                  set, this field cannot be changed."
                type: string
              platform:
                description: platform holds configuration specific to the underlying
                  infrastructure provider for DNS. When omitted, this means the user
                  has no opinion and the platform is left to choose reasonable defaults.
                  These defaults are subject to change over time.
                properties:
                  aws:
                    description: aws contains DNS configuration specific to the Amazon
                      Web Services cloud provider.
                    properties:
                      privateZoneIAMRole:
                        description: privateZoneIAMRole contains the ARN of an IAM
                          role that should be assumed when performing operations on
                          the cluster's private hosted zone specified in the cluster
                          DNS config. When left empty, no role should be assumed.
                        pattern: ^arn:(aws|aws-cn|aws-us-gov):iam::[0-9]{12}:role\/.*$
                        type: string
                    type: object
                  type:
                    description: "type is the underlying infrastructure provider for
                      the cluster. Allowed values: \"\", \"AWS\". \n Individual components
                      may not support all platforms, and must handle unrecognized
                      platforms with best-effort defaults."
                    enum:
                    - ""
                    - AWS
                    - Azure
                    - BareMetal
                    - GCP
                    - Libvirt
                    - OpenStack
                    - None
                    - VSphere
                    - oVirt
                    - IBMCloud
                    - KubeVirt
                    - EquinixMetal
                    - PowerVS
                    - AlibabaCloud
                    - Nutanix
                    - External
                    type: string
                    x-kubernetes-validations:
                    - message: allowed values are '' and 'AWS'
                      rule: self in ['','AWS']
                required:
                - type
                type: object
                x-kubernetes-validations:
                - message: aws configuration is required when platform is AWS, and
                    forbidden otherwise
                  rule: 'has(self.type) && self.type == ''AWS'' ?  has(self.aws) :
                    !has(self.aws)'
              privateZone:
                description: "privateZone is the location where all the DNS records
                  that are only available internally to the cluster exist. \n If this
                  field is nil, no private records should be created. \n Once set,
                  this field cannot be changed."
                properties:
                  id:
                    description: "id is the identifier that can be used to find the
                      DNS hosted zone. \n on AWS zone can be fetched using `ID` as
                      id in [1] on Azure zone can be fetched using `ID` as a pre-determined
                      name in [2], on GCP zone can be fetched using `ID` as a pre-determined
                      name in [3]. \n [1]: https://docs.aws.amazon.com/cli/latest/reference/route53/get-hosted-zone.html#options
                      [2]: https://docs.microsoft.com/en-us/cli/azure/network/dns/zone?view=azure-cli-latest#az-network-dns-zone-show
                      [3]: https://cloud.google.com/dns/docs/reference/v1/managedZones/get"
                    type: string
                  tags:
                    additionalProperties:
                      type: string
                    description: "tags can be used to query the DNS hosted zone. \n
                      on AWS, resourcegroupstaggingapi [1] can be used to fetch a
                      zone using `Tags` as tag-filters, \n [1]: https://docs.aws.amazon.com/cli/latest/reference/resourcegroupstaggingapi/get-resources.html#options"
                    type: object
                type: object
              publicZone:
                description: "publicZone is the location where all the DNS records
                  that are publicly accessible to the internet exist. \n If this field
                  is nil, no public records should be created. \n Once set, this field
                  cannot be changed."
                properties:
                  id:
                    description: "id is the identifier that can be used to find the
                      DNS hosted zone. \n on AWS zone can be fetched using `ID` as
                      id in [1] on Azure zone can be fetched using `ID` as a pre-determined
                      name in [2], on GCP zone can be fetched using `ID` as a pre-determined
                      name in [3]. \n [1]: https://docs.aws.amazon.com/cli/latest/reference/route53/get-hosted-zone.html#options
                      [2]: https://docs.microsoft.com/en-us/cli/azure/network/dns/zone?view=azure-cli-latest#az-network-dns-zone-show
                      [3]: https://cloud.google.com/dns/docs/reference/v1/managedZones/get"
                    type: string
                  tags:
                    additionalProperties:
                      type: string
                    description: "tags can be used to query the DNS hosted zone. \n
                      on AWS, resourcegroupstaggingapi [1] can be used to fetch a
                      zone using `Tags` as tag-filters, \n [1]: https://docs.aws.amazon.com/cli/latest/reference/resourcegroupstaggingapi/get-resources.html#options"
                    type: object
                type: object
            type: object
          status:
            description: status holds observed values from the cluster. They may not
              be overridden.
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: DNS
    listKind: DNSList
    plural: dnses
    singular: dns
  conditions:
  - lastTransitionTime: "2025-06-25T14:52:52Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:52:52Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
