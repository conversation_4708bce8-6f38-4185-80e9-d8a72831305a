---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/948
    api.openshift.io/merged-by-featuregates: "true"
    capability.openshift.io/name: MachineAPI
    exclude.release.openshift.io/internal-openshift-hosted: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    release.openshift.io/feature-set: Default
  creationTimestamp: "2025-06-25T14:54:01Z"
  generation: 2
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:54:01Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:capability.openshift.io/name: {}
          f:exclude.release.openshift.io/internal-openshift-hosted: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
          f:release.openshift.io/feature-set: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T12:05:34Z"
  name: machines.machine.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21338533"
  uid: b5a6f4fa-4520-4916-a89a-a90b47a44e46
spec:
  conversion:
    strategy: None
  group: machine.openshift.io
  names:
    kind: Machine
    listKind: MachineList
    plural: machines
    singular: machine
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Phase of machine
      jsonPath: .status.phase
      name: Phase
      type: string
    - description: Type of instance
      jsonPath: .metadata.labels['machine\.openshift\.io/instance-type']
      name: Type
      type: string
    - description: Region associated with machine
      jsonPath: .metadata.labels['machine\.openshift\.io/region']
      name: Region
      type: string
    - description: Zone associated with machine
      jsonPath: .metadata.labels['machine\.openshift\.io/zone']
      name: Zone
      type: string
    - description: Machine age
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - description: Node associated with machine
      jsonPath: .status.nodeRef.name
      name: Node
      priority: 1
      type: string
    - description: Provider ID of machine created in cloud provider
      jsonPath: .spec.providerID
      name: ProviderID
      priority: 1
      type: string
    - description: State of instance
      jsonPath: .metadata.annotations['machine\.openshift\.io/instance-state']
      name: State
      priority: 1
      type: string
    name: v1beta1
    schema:
      openAPIV3Schema:
        description: 'Machine is the Schema for the machines API Compatibility level
          2: Stable within a major release for a minimum of 9 months or 3 minor releases
          (whichever is longer).'
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: MachineSpec defines the desired state of Machine
            properties:
              lifecycleHooks:
                description: LifecycleHooks allow users to pause operations on the
                  machine at certain predefined points within the machine lifecycle.
                properties:
                  preDrain:
                    description: PreDrain hooks prevent the machine from being drained.
                      This also blocks further lifecycle events, such as termination.
                    items:
                      description: LifecycleHook represents a single instance of a
                        lifecycle hook
                      properties:
                        name:
                          description: Name defines a unique name for the lifcycle
                            hook. The name should be unique and descriptive, ideally
                            1-3 words, in CamelCase or it may be namespaced, eg. foo.example.com/CamelCase.
                            Names must be unique and should only be managed by a single
                            entity.
                          maxLength: 256
                          minLength: 3
                          pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                          type: string
                        owner:
                          description: Owner defines the owner of the lifecycle hook.
                            This should be descriptive enough so that users can identify
                            who/what is responsible for blocking the lifecycle. This
                            could be the name of a controller (e.g. clusteroperator/etcd)
                            or an administrator managing the hook.
                          maxLength: 512
                          minLength: 3
                          type: string
                      required:
                      - name
                      - owner
                      type: object
                    type: array
                    x-kubernetes-list-map-keys:
                    - name
                    x-kubernetes-list-type: map
                  preTerminate:
                    description: PreTerminate hooks prevent the machine from being
                      terminated. PreTerminate hooks be actioned after the Machine
                      has been drained.
                    items:
                      description: LifecycleHook represents a single instance of a
                        lifecycle hook
                      properties:
                        name:
                          description: Name defines a unique name for the lifcycle
                            hook. The name should be unique and descriptive, ideally
                            1-3 words, in CamelCase or it may be namespaced, eg. foo.example.com/CamelCase.
                            Names must be unique and should only be managed by a single
                            entity.
                          maxLength: 256
                          minLength: 3
                          pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                          type: string
                        owner:
                          description: Owner defines the owner of the lifecycle hook.
                            This should be descriptive enough so that users can identify
                            who/what is responsible for blocking the lifecycle. This
                            could be the name of a controller (e.g. clusteroperator/etcd)
                            or an administrator managing the hook.
                          maxLength: 512
                          minLength: 3
                          type: string
                      required:
                      - name
                      - owner
                      type: object
                    type: array
                    x-kubernetes-list-map-keys:
                    - name
                    x-kubernetes-list-type: map
                type: object
              metadata:
                description: ObjectMeta will autopopulate the Node created. Use this
                  to indicate what labels, annotations, name prefix, etc., should
                  be used when creating the Node.
                properties:
                  annotations:
                    additionalProperties:
                      type: string
                    description: 'Annotations is an unstructured key value map stored
                      with a resource that may be set by external tools to store and
                      retrieve arbitrary metadata. They are not queryable and should
                      be preserved when modifying objects. More info: http://kubernetes.io/docs/user-guide/annotations'
                    type: object
                  generateName:
                    description: "GenerateName is an optional prefix, used by the
                      server, to generate a unique name ONLY IF the Name field has
                      not been provided. If this field is used, the name returned
                      to the client will be different than the name passed. This value
                      will also be combined with a unique suffix. The provided value
                      has the same validation rules as the Name field, and may be
                      truncated by the length of the suffix required to make the value
                      unique on the server. \n If this field is specified and the
                      generated name exists, the server will NOT return a 409 - instead,
                      it will either return 201 Created or 500 with Reason ServerTimeout
                      indicating a unique name could not be found in the time allotted,
                      and the client should retry (optionally after the time indicated
                      in the Retry-After header). \n Applied only if Name is not specified.
                      More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#idempotency"
                    type: string
                  labels:
                    additionalProperties:
                      type: string
                    description: 'Map of string keys and values that can be used to
                      organize and categorize (scope and select) objects. May match
                      selectors of replication controllers and services. More info:
                      http://kubernetes.io/docs/user-guide/labels'
                    type: object
                  name:
                    description: 'Name must be unique within a namespace. Is required
                      when creating resources, although some resources may allow a
                      client to request the generation of an appropriate name automatically.
                      Name is primarily intended for creation idempotence and configuration
                      definition. Cannot be updated. More info: http://kubernetes.io/docs/user-guide/identifiers#names'
                    type: string
                  namespace:
                    description: "Namespace defines the space within each name must
                      be unique. An empty namespace is equivalent to the \"default\"
                      namespace, but \"default\" is the canonical representation.
                      Not all objects are required to be scoped to a namespace - the
                      value of this field for those objects will be empty. \n Must
                      be a DNS_LABEL. Cannot be updated. More info: http://kubernetes.io/docs/user-guide/namespaces"
                    type: string
                  ownerReferences:
                    description: List of objects depended by this object. If ALL objects
                      in the list have been deleted, this object will be garbage collected.
                      If this object is managed by a controller, then an entry in
                      this list will point to this controller, with the controller
                      field set to true. There cannot be more than one managing controller.
                    items:
                      description: OwnerReference contains enough information to let
                        you identify an owning object. An owning object must be in
                        the same namespace as the dependent, or be cluster-scoped,
                        so there is no namespace field.
                      properties:
                        apiVersion:
                          description: API version of the referent.
                          type: string
                        blockOwnerDeletion:
                          description: If true, AND if the owner has the "foregroundDeletion"
                            finalizer, then the owner cannot be deleted from the key-value
                            store until this reference is removed. See https://kubernetes.io/docs/concepts/architecture/garbage-collection/#foreground-deletion
                            for how the garbage collector interacts with this field
                            and enforces the foreground deletion. Defaults to false.
                            To set this field, a user needs "delete" permission of
                            the owner, otherwise 422 (Unprocessable Entity) will be
                            returned.
                          type: boolean
                        controller:
                          description: If true, this reference points to the managing
                            controller.
                          type: boolean
                        kind:
                          description: 'Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
                          type: string
                        name:
                          description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#names'
                          type: string
                        uid:
                          description: 'UID of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#uids'
                          type: string
                      required:
                      - apiVersion
                      - kind
                      - name
                      - uid
                      type: object
                      x-kubernetes-map-type: atomic
                    type: array
                    x-kubernetes-list-map-keys:
                    - uid
                    x-kubernetes-list-type: map
                type: object
              providerID:
                description: ProviderID is the identification ID of the machine provided
                  by the provider. This field must match the provider ID as seen on
                  the node object corresponding to this machine. This field is required
                  by higher level consumers of cluster-api. Example use case is cluster
                  autoscaler with cluster-api as provider. Clean-up logic in the autoscaler
                  compares machines to nodes to find out machines at provider which
                  could not get registered as Kubernetes nodes. With cluster-api as
                  a generic out-of-tree provider for autoscaler, this field is required
                  by autoscaler to be able to have a provider view of the list of
                  machines. Another list of nodes is queried from the k8s apiserver
                  and then a comparison is done to find out unregistered machines
                  and are marked for delete. This field will be set by the actuators
                  and consumed by higher level entities like autoscaler that will
                  be interfacing with cluster-api as generic provider.
                type: string
              providerSpec:
                description: ProviderSpec details Provider-specific configuration
                  to use during node creation.
                properties:
                  value:
                    description: Value is an inlined, serialized representation of
                      the resource configuration. It is recommended that providers
                      maintain their own versioned API types that should be serialized/deserialized
                      from this field, akin to component config.
                    type: object
                    x-kubernetes-preserve-unknown-fields: true
                type: object
              taints:
                description: The list of the taints to be applied to the corresponding
                  Node in additive manner. This list will not overwrite any other
                  taints added to the Node on an ongoing basis by other entities.
                  These taints should be actively reconciled e.g. if you ask the machine
                  controller to apply a taint and then manually remove the taint the
                  machine controller will put it back) but not have the machine controller
                  remove any taints
                items:
                  description: The node this Taint is attached to has the "effect"
                    on any pod that does not tolerate the Taint.
                  properties:
                    effect:
                      description: Required. The effect of the taint on pods that
                        do not tolerate the taint. Valid effects are NoSchedule, PreferNoSchedule
                        and NoExecute.
                      type: string
                    key:
                      description: Required. The taint key to be applied to a node.
                      type: string
                    timeAdded:
                      description: TimeAdded represents the time at which the taint
                        was added. It is only written for NoExecute taints.
                      format: date-time
                      type: string
                    value:
                      description: The taint value corresponding to the taint key.
                      type: string
                  required:
                  - effect
                  - key
                  type: object
                type: array
                x-kubernetes-list-type: atomic
            type: object
          status:
            description: MachineStatus defines the observed state of Machine
            properties:
              addresses:
                description: Addresses is a list of addresses assigned to the machine.
                  Queried from cloud provider, if available.
                items:
                  description: NodeAddress contains information for the node's address.
                  properties:
                    address:
                      description: The node address.
                      type: string
                    type:
                      description: Node address type, one of Hostname, ExternalIP
                        or InternalIP.
                      type: string
                  required:
                  - address
                  - type
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              conditions:
                description: Conditions defines the current state of the Machine
                items:
                  description: Condition defines an observation of a Machine API resource
                    operational state.
                  properties:
                    lastTransitionTime:
                      description: Last time the condition transitioned from one status
                        to another. This should be when the underlying condition changed.
                        If that is not known, then using the time when the API field
                        changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: A human readable message indicating details about
                        the transition. This field may be empty.
                      type: string
                    reason:
                      description: The reason for the condition's last transition
                        in CamelCase. The specific API may choose whether or not this
                        field is considered a guaranteed API. This field may not be
                        empty.
                      type: string
                    severity:
                      description: Severity provides an explicit classification of
                        Reason code, so the users or machines can immediately understand
                        the current situation and act accordingly. The Severity field
                        MUST be set only when Status=False.
                      type: string
                    status:
                      description: Status of the condition, one of True, False, Unknown.
                      type: string
                    type:
                      description: Type of condition in CamelCase or in foo.example.com/CamelCase.
                        Many .condition.type values are consistent across resources
                        like Available, but because arbitrary conditions can be useful
                        (see .node.status.conditions), the ability to deconflict is
                        important.
                      type: string
                  required:
                  - type
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              errorMessage:
                description: "ErrorMessage will be set in the event that there is
                  a terminal problem reconciling the Machine and will contain a more
                  verbose string suitable for logging and human consumption. \n This
                  field should not be set for transitive errors that a controller
                  faces that are expected to be fixed automatically over time (like
                  service outages), but instead indicate that something is fundamentally
                  wrong with the Machine's spec or the configuration of the controller,
                  and that manual intervention is required. Examples of terminal errors
                  would be invalid combinations of settings in the spec, values that
                  are unsupported by the controller, or the responsible controller
                  itself being critically misconfigured. \n Any transient errors that
                  occur during the reconciliation of Machines can be added as events
                  to the Machine object and/or logged in the controller's output."
                type: string
              errorReason:
                description: "ErrorReason will be set in the event that there is a
                  terminal problem reconciling the Machine and will contain a succinct
                  value suitable for machine interpretation. \n This field should
                  not be set for transitive errors that a controller faces that are
                  expected to be fixed automatically over time (like service outages),
                  but instead indicate that something is fundamentally wrong with
                  the Machine's spec or the configuration of the controller, and that
                  manual intervention is required. Examples of terminal errors would
                  be invalid combinations of settings in the spec, values that are
                  unsupported by the controller, or the responsible controller itself
                  being critically misconfigured. \n Any transient errors that occur
                  during the reconciliation of Machines can be added as events to
                  the Machine object and/or logged in the controller's output."
                type: string
              lastOperation:
                description: LastOperation describes the last-operation performed
                  by the machine-controller. This API should be useful as a history
                  in terms of the latest operation performed on the specific machine.
                  It should also convey the state of the latest-operation for example
                  if it is still on-going, failed or completed successfully.
                properties:
                  description:
                    description: Description is the human-readable description of
                      the last operation.
                    type: string
                  lastUpdated:
                    description: LastUpdated is the timestamp at which LastOperation
                      API was last-updated.
                    format: date-time
                    type: string
                  state:
                    description: State is the current status of the last performed
                      operation. E.g. Processing, Failed, Successful etc
                    type: string
                  type:
                    description: Type is the type of operation which was last performed.
                      E.g. Create, Delete, Update etc
                    type: string
                type: object
              lastUpdated:
                description: LastUpdated identifies when this status was last observed.
                format: date-time
                type: string
              nodeRef:
                description: NodeRef will point to the corresponding Node if it exists.
                properties:
                  apiVersion:
                    description: API version of the referent.
                    type: string
                  fieldPath:
                    description: 'If referring to a piece of an object instead of
                      an entire object, this string should contain a valid JSON/Go
                      field access statement, such as desiredState.manifest.containers[2].
                      For example, if the object reference is to a container within
                      a pod, this would take on a value like: "spec.containers{name}"
                      (where "name" refers to the name of the container that triggered
                      the event) or if no container name is specified "spec.containers[2]"
                      (container with index 2 in this pod). This syntax is chosen
                      only to have some well-defined way of referencing a part of
                      an object. TODO: this design is not final and this field is
                      subject to change in the future.'
                    type: string
                  kind:
                    description: 'Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
                    type: string
                  name:
                    description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names'
                    type: string
                  namespace:
                    description: 'Namespace of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/'
                    type: string
                  resourceVersion:
                    description: 'Specific resourceVersion to which this reference
                      is made, if any. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency'
                    type: string
                  uid:
                    description: 'UID of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids'
                    type: string
                type: object
                x-kubernetes-map-type: atomic
              phase:
                description: 'Phase represents the current phase of machine actuation.
                  One of: Failed, Provisioning, Provisioned, Running, Deleting'
                type: string
              providerStatus:
                description: ProviderStatus details a Provider-specific status. It
                  is recommended that providers maintain their own versioned API types
                  that should be serialized/deserialized from this field.
                type: object
                x-kubernetes-preserve-unknown-fields: true
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: Machine
    listKind: MachineList
    plural: machines
    singular: machine
  conditions:
  - lastTransitionTime: "2025-06-25T14:54:01Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:54:01Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1beta1
