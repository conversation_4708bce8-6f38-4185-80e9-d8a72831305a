---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
  creationTimestamp: "2025-08-01T12:18:06Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:controller-gen.kubebuilder.io/version: {}
        f:ownerReferences:
          k:{"uid":"e8b7620a-d5d2-448e-b2bf-6f6768b9b1dc"}: {}
      f:spec:
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-network-operator/operconfig
    operation: Apply
    time: "2025-08-01T12:18:06Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-01T12:18:06Z"
  name: nodeslicepools.whereabouts.cni.cncf.io
  ownerReferences:
  - apiVersion: operator.openshift.io/v1
    blockOwnerDeletion: true
    controller: true
    kind: Network
    name: cluster
    uid: e8b7620a-d5d2-448e-b2bf-6f6768b9b1dc
  resourceVersion: "21352414"
  uid: 1690ee84-4d71-4a14-9abd-b1ce77d0f707
spec:
  conversion:
    strategy: None
  group: whereabouts.cni.cncf.io
  names:
    kind: NodeSlicePool
    listKind: NodeSlicePoolList
    plural: nodeslicepools
    singular: nodeslicepool
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: NodeSlicePool is the Schema for the nodesliceippools API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: NodeSlicePoolSpec defines the desired state of NodeSlicePool
            properties:
              range:
                description: |-
                  Range is a RFC 4632/4291-style string that represents an IP address and prefix length in CIDR notation
                  this refers to the entire range where the node is allocated a subset
                type: string
              sliceSize:
                description: SliceSize is the size of subnets or slices of the range
                  that each node will be assigned
                type: string
            required:
            - range
            - sliceSize
            type: object
          status:
            description: NodeSlicePoolStatus defines the desired state of NodeSlicePool
            properties:
              allocations:
                description: Allocations holds the allocations of nodes to slices
                items:
                  properties:
                    nodeName:
                      description: NodeName is the name of the node assigned to this
                        slice, empty node name is an available slice for assignment
                      type: string
                    sliceRange:
                      description: SliceRange is the subnet of this slice
                      type: string
                  required:
                  - nodeName
                  - sliceRange
                  type: object
                type: array
            required:
            - allocations
            type: object
        type: object
    served: true
    storage: true
status:
  acceptedNames:
    kind: NodeSlicePool
    listKind: NodeSlicePoolList
    plural: nodeslicepools
    singular: nodeslicepool
  conditions:
  - lastTransitionTime: "2025-08-01T12:18:06Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-08-01T12:18:06Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1alpha1
