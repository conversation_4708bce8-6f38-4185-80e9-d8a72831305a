---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/1596
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    release.openshift.io/feature-set: TechPreviewNoUpgrade
  creationTimestamp: "2025-06-25T14:59:09Z"
  generation: 1
  labels:
    openshift.io/operator-managed: ""
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:59:09Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
          f:release.openshift.io/feature-set: {}
        f:labels:
          .: {}
          f:openshift.io/operator-managed: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: machine-config-operator
    operation: Update
    time: "2025-06-25T14:59:09Z"
  name: machineconfignodes.machineconfiguration.openshift.io
  resourceVersion: "9933"
  uid: a69fc810-354f-4c4b-8525-13102d34465b
spec:
  conversion:
    strategy: None
  group: machineconfiguration.openshift.io
  names:
    kind: MachineConfigNode
    listKind: MachineConfigNodeList
    plural: machineconfignodes
    singular: machineconfignode
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.conditions[?(@.type=="Updated")].status
      name: Updated
      type: string
    - jsonPath: .status.conditions[?(@.type=="UpdatePrepared")].status
      name: UpdatePrepared
      type: string
    - jsonPath: .status.conditions[?(@.type=="UpdateExecuted")].status
      name: UpdateExecuted
      type: string
    - jsonPath: .status.conditions[?(@.type=="UpdatePostActionComplete")].status
      name: UpdatePostActionComplete
      type: string
    - jsonPath: .status.conditions[?(@.type=="UpdateComplete")].status
      name: UpdateComplete
      type: string
    - jsonPath: .status.conditions[?(@.type=="Resumed")].status
      name: Resumed
      type: string
    - jsonPath: .status.conditions[?(@.type=="UpdateCompatible")].status
      name: UpdateCompatible
      priority: 1
      type: string
    - jsonPath: .status.conditions[?(@.type=="AppliedFilesAndOS")].status
      name: UpdatedFilesAndOS
      priority: 1
      type: string
    - jsonPath: .status.conditions[?(@.type=="Cordoned")].status
      name: CordonedNode
      priority: 1
      type: string
    - jsonPath: .status.conditions[?(@.type=="Drained")].status
      name: DrainedNode
      priority: 1
      type: string
    - jsonPath: .status.conditions[?(@.type=="RebootedNode")].status
      name: RebootedNode
      priority: 1
      type: string
    - jsonPath: .status.conditions[?(@.type=="ReloadedCRIO")].status
      name: ReloadedCRIO
      priority: 1
      type: string
    - jsonPath: .status.conditions[?(@.type=="Uncordoned")].status
      name: UncordonedNode
      priority: 1
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: 'MachineConfigNode describes the health of the Machines on the
          system Compatibility level 4: No compatibility is provided, the API can
          change at any point for any reason. These capabilities should not be used
          by applications needing long term support.'
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: spec describes the configuration of the machine config node.
            properties:
              configVersion:
                description: configVersion holds the desired config version for the
                  node targeted by this machine config node resource. The desired
                  version represents the machine config the node will attempt to update
                  to. This gets set before the machine config operator validates the
                  new machine config against the current machine config.
                properties:
                  desired:
                    description: desired is the name of the machine config that the
                      the node should be upgraded to. This value is set when the machine
                      config pool generates a new version of its rendered configuration.
                      When this value is changed, the machine config daemon starts
                      the node upgrade process. This value gets set in the machine
                      config node spec once the machine config has been targeted for
                      upgrade and before it is validated. Must be a lowercase RFC-1123
                      hostname (https://tools.ietf.org/html/rfc1123) It may consist
                      of only alphanumeric characters, hyphens (-) and periods (.)
                      and must be at most 253 characters in length.
                    maxLength: 253
                    pattern: ^([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])(\.([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]{0,61}[a-zA-Z0-9]))*$
                    type: string
                required:
                - desired
                type: object
              node:
                description: node contains a reference to the node for this machine
                  config node.
                properties:
                  name:
                    description: name is the object name. Must be a lowercase RFC-1123
                      hostname (https://tools.ietf.org/html/rfc1123) It may consist
                      of only alphanumeric characters, hyphens (-) and periods (.)
                      and must be at most 253 characters in length.
                    maxLength: 253
                    pattern: ^([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])(\.([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]{0,61}[a-zA-Z0-9]))*$
                    type: string
                required:
                - name
                type: object
              pool:
                description: pool contains a reference to the machine config pool
                  that this machine config node's referenced node belongs to.
                properties:
                  name:
                    description: name is the object name. Must be a lowercase RFC-1123
                      hostname (https://tools.ietf.org/html/rfc1123) It may consist
                      of only alphanumeric characters, hyphens (-) and periods (.)
                      and must be at most 253 characters in length.
                    maxLength: 253
                    pattern: ^([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])(\.([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]{0,61}[a-zA-Z0-9]))*$
                    type: string
                required:
                - name
                type: object
            required:
            - configVersion
            - node
            - pool
            type: object
          status:
            description: status describes the last observed state of this machine
              config node.
            properties:
              conditions:
                description: conditions represent the observations of a machine config
                  node's current state.
                items:
                  description: "Condition contains details for one aspect of the current
                    state of this API Resource. --- This struct is intended for direct
                    use as an array at the field path .status.conditions.  For example,
                    \n type FooStatus struct{ // Represents the observations of a
                    foo's current state. // Known .status.conditions.type are: \"Available\",
                    \"Progressing\", and \"Degraded\" // +patchMergeKey=type // +patchStrategy=merge
                    // +listType=map // +listMapKey=type Conditions []metav1.Condition
                    `json:\"conditions,omitempty\" patchStrategy:\"merge\" patchMergeKey:\"type\"
                    protobuf:\"bytes,1,rep,name=conditions\"` \n // other fields }"
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the last time the condition
                        transitioned from one status to another. This should be when
                        the underlying condition changed.  If that is not known, then
                        using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: message is a human readable message indicating
                        details about the transition. This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: observedGeneration represents the .metadata.generation
                        that the condition was set based upon. For instance, if .metadata.generation
                        is currently 12, but the .status.conditions[x].observedGeneration
                        is 9, the condition is out of date with respect to the current
                        state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: reason contains a programmatic identifier indicating
                        the reason for the condition's last transition. Producers
                        of specific condition types may define expected values and
                        meanings for this field, and whether the values are considered
                        a guaranteed API. The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                        --- Many .condition.type values are consistent across resources
                        like Available, but because arbitrary conditions can be useful
                        (see .node.status.conditions), the ability to deconflict is
                        important. The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              configVersion:
                description: configVersion describes the current and desired machine
                  config for this node. The current version represents the current
                  machine config for the node and is updated after a successful update.
                  The desired version represents the machine config the node will
                  attempt to update to. This desired machine config has been compared
                  to the current machine config and has been validated by the machine
                  config operator as one that is valid and that exists.
                properties:
                  current:
                    description: current is the name of the machine config currently
                      in use on the node. This value is updated once the machine config
                      daemon has completed the update of the configuration for the
                      node. This value should match the desired version unless an
                      upgrade is in progress. Must be a lowercase RFC-1123 hostname
                      (https://tools.ietf.org/html/rfc1123) It may consist of only
                      alphanumeric characters, hyphens (-) and periods (.) and must
                      be at most 253 characters in length.
                    maxLength: 253
                    pattern: ^([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])(\.([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]{0,61}[a-zA-Z0-9]))*$
                    type: string
                  desired:
                    description: desired is the MachineConfig the node wants to upgrade
                      to. This value gets set in the machine config node status once
                      the machine config has been validated against the current machine
                      config. Must be a lowercase RFC-1123 hostname (https://tools.ietf.org/html/rfc1123)
                      It may consist of only alphanumeric characters, hyphens (-)
                      and periods (.) and must be at most 253 characters in length.
                    maxLength: 253
                    pattern: ^([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])(\.([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]{0,61}[a-zA-Z0-9]))*$
                    type: string
                required:
                - desired
                type: object
              observedGeneration:
                description: observedGeneration represents the generation observed
                  by the controller. This field is updated when the controller observes
                  a change to the desiredConfig in the configVersion of the machine
                  config node spec.
                format: int64
                type: integer
            required:
            - configVersion
            type: object
        required:
        - spec
        type: object
        x-kubernetes-validations:
        - message: spec.node.name should match metadata.name
          rule: self.metadata.name == self.spec.node.name
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: MachineConfigNode
    listKind: MachineConfigNodeList
    plural: machineconfignodes
    singular: machineconfignode
  conditions:
  - lastTransitionTime: "2025-06-25T14:59:09Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:59:09Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1alpha1
