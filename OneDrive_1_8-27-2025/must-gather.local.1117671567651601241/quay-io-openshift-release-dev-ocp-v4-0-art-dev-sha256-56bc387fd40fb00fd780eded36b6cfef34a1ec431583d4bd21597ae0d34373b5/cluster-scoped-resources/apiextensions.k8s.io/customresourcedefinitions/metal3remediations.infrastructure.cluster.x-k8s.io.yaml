---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    exclude.release.openshift.io/internal-openshift-hosted: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:54:02Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:exclude.release.openshift.io/internal-openshift-hosted: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:categories: {}
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T14:54:02Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:categories: {}
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:54:02Z"
  name: metal3remediations.infrastructure.cluster.x-k8s.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "1912"
  uid: 07933aab-4b22-4c5e-8144-6c5c9c1db78e
spec:
  conversion:
    strategy: None
  group: infrastructure.cluster.x-k8s.io
  names:
    categories:
    - cluster-api
    kind: Metal3Remediation
    listKind: Metal3RemediationList
    plural: metal3remediations
    shortNames:
    - m3r
    - m3remediation
    singular: metal3remediation
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: How many times remediation controller should attempt to remediate
        the host
      jsonPath: .spec.strategy.retryLimit
      name: Retry limit
      type: string
    - description: How many times remediation controller has tried to remediate the
        node
      jsonPath: .status.retryCount
      name: Retry count
      type: string
    - description: Timestamp of the last remediation attempt
      jsonPath: .status.lastRemediated
      name: Last Remediated
      type: string
    - description: Type of the remediation strategy
      jsonPath: .spec.strategy.type
      name: Strategy
      type: string
    - description: Phase of the remediation
      jsonPath: .status.phase
      name: Phase
      type: string
    name: v1alpha5
    schema:
      openAPIV3Schema:
        description: Metal3Remediation is the Schema for the metal3remediations API.
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: Metal3RemediationSpec defines the desired state of Metal3Remediation.
            properties:
              strategy:
                description: Strategy field defines remediation strategy.
                properties:
                  retryLimit:
                    description: Sets maximum number of remediation retries.
                    type: integer
                  timeout:
                    description: Sets the timeout between remediation retries.
                    type: string
                  type:
                    description: Type of remediation.
                    type: string
                type: object
            type: object
          status:
            description: Metal3RemediationStatus defines the observed state of Metal3Remediation.
            properties:
              lastRemediated:
                description: LastRemediated identifies when the host was last remediated
                format: date-time
                type: string
              phase:
                description: Phase represents the current phase of machine remediation.
                  E.g. Pending, Running, Done etc.
                type: string
              retryCount:
                description: RetryCount can be used as a counter during the remediation.
                  Field can hold number of reboots etc.
                type: integer
            type: object
        type: object
    served: true
    storage: false
    subresources:
      status: {}
  - additionalPrinterColumns:
    - description: How many times remediation controller should attempt to remediate
        the host
      jsonPath: .spec.strategy.retryLimit
      name: Retry limit
      type: string
    - description: How many times remediation controller has tried to remediate the
        node
      jsonPath: .status.retryCount
      name: Retry count
      type: string
    - description: Timestamp of the last remediation attempt
      jsonPath: .status.lastRemediated
      name: Last Remediated
      type: string
    - description: Type of the remediation strategy
      jsonPath: .spec.strategy.type
      name: Strategy
      type: string
    - description: Phase of the remediation
      jsonPath: .status.phase
      name: Phase
      type: string
    name: v1beta1
    schema:
      openAPIV3Schema:
        description: Metal3Remediation is the Schema for the metal3remediations API.
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: Metal3RemediationSpec defines the desired state of Metal3Remediation.
            properties:
              strategy:
                description: Strategy field defines remediation strategy.
                properties:
                  retryLimit:
                    description: Sets maximum number of remediation retries.
                    type: integer
                  timeout:
                    description: Sets the timeout between remediation retries.
                    type: string
                  type:
                    description: Type of remediation.
                    type: string
                type: object
            type: object
          status:
            description: Metal3RemediationStatus defines the observed state of Metal3Remediation.
            properties:
              lastRemediated:
                description: LastRemediated identifies when the host was last remediated
                format: date-time
                type: string
              phase:
                description: Phase represents the current phase of machine remediation.
                  E.g. Pending, Running, Done etc.
                type: string
              retryCount:
                description: RetryCount can be used as a counter during the remediation.
                  Field can hold number of reboots etc.
                type: integer
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    categories:
    - cluster-api
    kind: Metal3Remediation
    listKind: Metal3RemediationList
    plural: metal3remediations
    shortNames:
    - m3r
    - m3remediation
    singular: metal3remediation
  conditions:
  - lastTransitionTime: "2025-06-25T14:54:02Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:54:02Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1beta1
