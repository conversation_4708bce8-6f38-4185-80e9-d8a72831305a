---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/481
    api.openshift.io/merged-by-featuregates: "true"
    capability.openshift.io/name: Console
    description: Extension for customizing OpenShift web console links
    displayName: ConsoleLinks
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:53:44Z"
  generation: 2
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:44Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:capability.openshift.io/name: {}
          f:description: {}
          f:displayName: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T12:08:53Z"
  name: consolelinks.console.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21340280"
  uid: 50cd8c4f-60a6-4fe5-a1e7-2500d449562e
spec:
  conversion:
    strategy: None
  group: console.openshift.io
  names:
    kind: ConsoleLink
    listKind: ConsoleLinkList
    plural: consolelinks
    singular: consolelink
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.text
      name: Text
      type: string
    - jsonPath: .spec.href
      name: URL
      type: string
    - jsonPath: .spec.menu
      name: Menu
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1
    schema:
      openAPIV3Schema:
        description: "ConsoleLink is an extension for customizing OpenShift web console
          links. \n Compatibility level 2: Stable within a major release for a minimum
          of 9 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: ConsoleLinkSpec is the desired console link configuration.
            properties:
              applicationMenu:
                description: applicationMenu holds information about section and icon
                  used for the link in the application menu, and it is applicable
                  only when location is set to ApplicationMenu.
                properties:
                  imageURL:
                    description: imageUrl is the URL for the icon used in front of
                      the link in the application menu. The URL must be an HTTPS URL
                      or a Data URI. The image should be square and will be shown
                      at 24x24 pixels.
                    type: string
                  section:
                    description: section is the section of the application menu in
                      which the link should appear. This can be any text that will
                      appear as a subheading in the application menu dropdown. A new
                      section will be created if the text does not match text of an
                      existing section.
                    type: string
                required:
                - section
                type: object
              href:
                description: href is the absolute secure URL for the link (must use
                  https)
                pattern: ^https://
                type: string
              location:
                description: location determines which location in the console the
                  link will be appended to (ApplicationMenu, HelpMenu, UserMenu, NamespaceDashboard).
                pattern: ^(ApplicationMenu|HelpMenu|UserMenu|NamespaceDashboard)$
                type: string
              namespaceDashboard:
                description: namespaceDashboard holds information about namespaces
                  in which the dashboard link should appear, and it is applicable
                  only when location is set to NamespaceDashboard. If not specified,
                  the link will appear in all namespaces.
                properties:
                  namespaceSelector:
                    description: namespaceSelector is used to select the Namespaces
                      that should contain dashboard link by label. If the namespace
                      labels match, dashboard link will be shown for the namespaces.
                    properties:
                      matchExpressions:
                        description: matchExpressions is a list of label selector
                          requirements. The requirements are ANDed.
                        items:
                          description: A label selector requirement is a selector
                            that contains values, a key, and an operator that relates
                            the key and values.
                          properties:
                            key:
                              description: key is the label key that the selector
                                applies to.
                              type: string
                            operator:
                              description: operator represents a key's relationship
                                to a set of values. Valid operators are In, NotIn,
                                Exists and DoesNotExist.
                              type: string
                            values:
                              description: values is an array of string values. If
                                the operator is In or NotIn, the values array must
                                be non-empty. If the operator is Exists or DoesNotExist,
                                the values array must be empty. This array is replaced
                                during a strategic merge patch.
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          required:
                          - key
                          - operator
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      matchLabels:
                        additionalProperties:
                          type: string
                        description: matchLabels is a map of {key,value} pairs. A
                          single {key,value} in the matchLabels map is equivalent
                          to an element of matchExpressions, whose key field is "key",
                          the operator is "In", and the values array contains only
                          "value". The requirements are ANDed.
                        type: object
                    type: object
                    x-kubernetes-map-type: atomic
                  namespaces:
                    description: namespaces is an array of namespace names in which
                      the dashboard link should appear.
                    items:
                      type: string
                    type: array
                type: object
              text:
                description: text is the display text for the link
                type: string
            required:
            - href
            - location
            - text
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ConsoleLink
    listKind: ConsoleLinkList
    plural: consolelinks
    singular: consolelink
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:44Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:44Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
