---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    capability.openshift.io/name: OperatorLifecycleManager
    controller-gen.kubebuilder.io/version: v0.15.0
    include.release.openshift.io/hypershift: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
  creationTimestamp: "2025-06-25T14:53:43Z"
  generation: 2
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:categories: {}
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:43Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:capability.openshift.io/name: {}
          f:controller-gen.kubebuilder.io/version: {}
          f:include.release.openshift.io/hypershift: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:categories: {}
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T12:09:05Z"
  name: installplans.operators.coreos.com
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21340786"
  uid: 05110e7a-3176-4649-9b36-f0e61ce10a22
spec:
  conversion:
    strategy: None
  group: operators.coreos.com
  names:
    categories:
    - olm
    kind: InstallPlan
    listKind: InstallPlanList
    plural: installplans
    shortNames:
    - ip
    singular: installplan
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: The first CSV in the list of clusterServiceVersionNames
      jsonPath: .spec.clusterServiceVersionNames[0]
      name: CSV
      type: string
    - description: The approval mode
      jsonPath: .spec.approval
      name: Approval
      type: string
    - jsonPath: .spec.approved
      name: Approved
      type: boolean
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: InstallPlan defines the installation of a set of operators.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: InstallPlanSpec defines a set of Application resources to
              be installed
            properties:
              approval:
                description: |-
                  Approval is the user approval policy for an InstallPlan.
                  It must be one of "Automatic" or "Manual".
                type: string
              approved:
                type: boolean
              clusterServiceVersionNames:
                items:
                  type: string
                type: array
              generation:
                type: integer
              source:
                type: string
              sourceNamespace:
                type: string
            required:
            - approval
            - approved
            - clusterServiceVersionNames
            type: object
          status:
            description: |-
              InstallPlanStatus represents the information about the status of
              steps required to complete installation.


              Status may trail the actual state of a system.
            properties:
              attenuatedServiceAccountRef:
                description: |-
                  AttenuatedServiceAccountRef references the service account that is used
                  to do scoped operator install.
                properties:
                  apiVersion:
                    description: API version of the referent.
                    type: string
                  fieldPath:
                    description: |-
                      If referring to a piece of an object instead of an entire object, this string
                      should contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].
                      For example, if the object reference is to a container within a pod, this would take on a value like:
                      "spec.containers{name}" (where "name" refers to the name of the container that triggered
                      the event) or if no container name is specified "spec.containers[2]" (container with
                      index 2 in this pod). This syntax is chosen only to have some well-defined way of
                      referencing a part of an object.
                      TODO: this design is not final and this field is subject to change in the future.
                    type: string
                  kind:
                    description: |-
                      Kind of the referent.
                      More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
                    type: string
                  name:
                    description: |-
                      Name of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                    type: string
                  namespace:
                    description: |-
                      Namespace of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/
                    type: string
                  resourceVersion:
                    description: |-
                      Specific resourceVersion to which this reference is made, if any.
                      More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
                    type: string
                  uid:
                    description: |-
                      UID of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids
                    type: string
                type: object
                x-kubernetes-map-type: atomic
              bundleLookups:
                description: BundleLookups is the set of in-progress requests to pull
                  and unpackage bundle content to the cluster.
                items:
                  description: BundleLookup is a request to pull and unpackage the
                    content of a bundle to the cluster.
                  properties:
                    catalogSourceRef:
                      description: CatalogSourceRef is a reference to the CatalogSource
                        the bundle path was resolved from.
                      properties:
                        apiVersion:
                          description: API version of the referent.
                          type: string
                        fieldPath:
                          description: |-
                            If referring to a piece of an object instead of an entire object, this string
                            should contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].
                            For example, if the object reference is to a container within a pod, this would take on a value like:
                            "spec.containers{name}" (where "name" refers to the name of the container that triggered
                            the event) or if no container name is specified "spec.containers[2]" (container with
                            index 2 in this pod). This syntax is chosen only to have some well-defined way of
                            referencing a part of an object.
                            TODO: this design is not final and this field is subject to change in the future.
                          type: string
                        kind:
                          description: |-
                            Kind of the referent.
                            More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
                          type: string
                        name:
                          description: |-
                            Name of the referent.
                            More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                          type: string
                        namespace:
                          description: |-
                            Namespace of the referent.
                            More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/
                          type: string
                        resourceVersion:
                          description: |-
                            Specific resourceVersion to which this reference is made, if any.
                            More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
                          type: string
                        uid:
                          description: |-
                            UID of the referent.
                            More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids
                          type: string
                      type: object
                      x-kubernetes-map-type: atomic
                    conditions:
                      description: Conditions represents the overall state of a BundleLookup.
                      items:
                        properties:
                          lastTransitionTime:
                            description: Last time the condition transitioned from
                              one status to another.
                            format: date-time
                            type: string
                          lastUpdateTime:
                            description: Last time the condition was probed.
                            format: date-time
                            type: string
                          message:
                            description: A human readable message indicating details
                              about the transition.
                            type: string
                          reason:
                            description: The reason for the condition's last transition.
                            type: string
                          status:
                            description: Status of the condition, one of True, False,
                              Unknown.
                            type: string
                          type:
                            description: Type of condition.
                            type: string
                        required:
                        - status
                        - type
                        type: object
                      type: array
                    identifier:
                      description: Identifier is the catalog-unique name of the operator
                        (the name of the CSV for bundles that contain CSVs)
                      type: string
                    path:
                      description: |-
                        Path refers to the location of a bundle to pull.
                        It's typically an image reference.
                      type: string
                    properties:
                      description: The effective properties of the unpacked bundle.
                      type: string
                    replaces:
                      description: Replaces is the name of the bundle to replace with
                        the one found at Path.
                      type: string
                  required:
                  - catalogSourceRef
                  - identifier
                  - path
                  - replaces
                  type: object
                type: array
              catalogSources:
                items:
                  type: string
                type: array
              conditions:
                items:
                  description: |-
                    InstallPlanCondition represents the overall status of the execution of
                    an InstallPlan.
                  properties:
                    lastTransitionTime:
                      format: date-time
                      type: string
                    lastUpdateTime:
                      format: date-time
                      type: string
                    message:
                      type: string
                    reason:
                      description: ConditionReason is a camelcased reason for the
                        state transition.
                      type: string
                    status:
                      type: string
                    type:
                      description: InstallPlanConditionType describes the state of
                        an InstallPlan at a certain point as a whole.
                      type: string
                  type: object
                type: array
              message:
                description: |-
                  Message is a human-readable message containing detailed
                  information that may be important to understanding why the
                  plan has its current status.
                type: string
              phase:
                description: InstallPlanPhase is the current status of a InstallPlan
                  as a whole.
                type: string
              plan:
                items:
                  description: Step represents the status of an individual step in
                    an InstallPlan.
                  properties:
                    optional:
                      type: boolean
                    resolving:
                      type: string
                    resource:
                      description: |-
                        StepResource represents the status of a resource to be tracked by an
                        InstallPlan.
                      properties:
                        group:
                          type: string
                        kind:
                          type: string
                        manifest:
                          type: string
                        name:
                          type: string
                        sourceName:
                          type: string
                        sourceNamespace:
                          type: string
                        version:
                          type: string
                      required:
                      - group
                      - kind
                      - name
                      - sourceName
                      - sourceNamespace
                      - version
                      type: object
                    status:
                      description: |-
                        StepStatus is the current status of a particular resource an in
                        InstallPlan
                      type: string
                  required:
                  - resolving
                  - resource
                  - status
                  type: object
                type: array
              startTime:
                description: |-
                  StartTime is the time when the controller began applying
                  the resources listed in the plan to the cluster.
                format: date-time
                type: string
            required:
            - catalogSources
            - phase
            type: object
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    categories:
    - olm
    kind: InstallPlan
    listKind: InstallPlanList
    plural: installplans
    shortNames:
    - ip
    singular: installplan
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:43Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:43Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1alpha1
