---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/1112
    api.openshift.io/merged-by-featuregates: "true"
    capability.openshift.io/name: MachineAPI
    exclude.release.openshift.io/internal-openshift-hosted: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    release.openshift.io/feature-set: Default
  creationTimestamp: "2025-06-25T14:53:36Z"
  generation: 3
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:36Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:capability.openshift.io/name: {}
          f:exclude.release.openshift.io/internal-openshift-hosted: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:release.openshift.io/feature-set: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T11:46:54Z"
  name: controlplanemachinesets.machine.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21327788"
  uid: 30b571b4-cd59-4e19-b892-0782a8a608e7
spec:
  conversion:
    strategy: None
  group: machine.openshift.io
  names:
    kind: ControlPlaneMachineSet
    listKind: ControlPlaneMachineSetList
    plural: controlplanemachinesets
    singular: controlplanemachineset
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Desired Replicas
      jsonPath: .spec.replicas
      name: Desired
      type: integer
    - description: Current Replicas
      jsonPath: .status.replicas
      name: Current
      type: integer
    - description: Ready Replicas
      jsonPath: .status.readyReplicas
      name: Ready
      type: integer
    - description: Updated Replicas
      jsonPath: .status.updatedReplicas
      name: Updated
      type: integer
    - description: Observed number of unavailable replicas
      jsonPath: .status.unavailableReplicas
      name: Unavailable
      type: integer
    - description: ControlPlaneMachineSet state
      jsonPath: .spec.state
      name: State
      type: string
    - description: ControlPlaneMachineSet age
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1
    schema:
      openAPIV3Schema:
        description: 'ControlPlaneMachineSet ensures that a specified number of control
          plane machine replicas are running at any given time. Compatibility level
          1: Stable within a major release for a minimum of 12 months or 3 minor releases
          (whichever is longer).'
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: ControlPlaneMachineSet represents the configuration of the
              ControlPlaneMachineSet.
            properties:
              replicas:
                default: 3
                description: Replicas defines how many Control Plane Machines should
                  be created by this ControlPlaneMachineSet. This field is immutable
                  and cannot be changed after cluster installation. The ControlPlaneMachineSet
                  only operates with 3 or 5 node control planes, 3 and 5 are the only
                  valid values for this field.
                enum:
                - 3
                - 5
                format: int32
                type: integer
                x-kubernetes-validations:
                - message: replicas is immutable
                  rule: self == oldSelf
              selector:
                description: Label selector for Machines. Existing Machines selected
                  by this selector will be the ones affected by this ControlPlaneMachineSet.
                  It must match the template's labels. This field is considered immutable
                  after creation of the resource.
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: A label selector requirement is a selector that
                        contains values, a key, and an operator that relates the key
                        and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: operator represents a key's relationship to
                            a set of values. Valid operators are In, NotIn, Exists
                            and DoesNotExist.
                          type: string
                        values:
                          description: values is an array of string values. If the
                            operator is In or NotIn, the values array must be non-empty.
                            If the operator is Exists or DoesNotExist, the values
                            array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: matchLabels is a map of {key,value} pairs. A single
                      {key,value} in the matchLabels map is equivalent to an element
                      of matchExpressions, whose key field is "key", the operator
                      is "In", and the values array contains only "value". The requirements
                      are ANDed.
                    type: object
                type: object
                x-kubernetes-map-type: atomic
                x-kubernetes-validations:
                - message: selector is immutable
                  rule: self == oldSelf
              state:
                default: Inactive
                description: State defines whether the ControlPlaneMachineSet is Active
                  or Inactive. When Inactive, the ControlPlaneMachineSet will not
                  take any action on the state of the Machines within the cluster.
                  When Active, the ControlPlaneMachineSet will reconcile the Machines
                  and will update the Machines as necessary. Once Active, a ControlPlaneMachineSet
                  cannot be made Inactive. To prevent further action please remove
                  the ControlPlaneMachineSet.
                enum:
                - Active
                - Inactive
                type: string
                x-kubernetes-validations:
                - message: state cannot be changed once Active
                  rule: oldSelf != 'Active' || self == oldSelf
              strategy:
                default:
                  type: RollingUpdate
                description: Strategy defines how the ControlPlaneMachineSet will
                  update Machines when it detects a change to the ProviderSpec.
                properties:
                  type:
                    default: RollingUpdate
                    description: Type defines the type of update strategy that should
                      be used when updating Machines owned by the ControlPlaneMachineSet.
                      Valid values are "RollingUpdate" and "OnDelete". The current
                      default value is "RollingUpdate".
                    enum:
                    - RollingUpdate
                    - OnDelete
                    type: string
                type: object
              template:
                description: Template describes the Control Plane Machines that will
                  be created by this ControlPlaneMachineSet.
                properties:
                  machineType:
                    description: MachineType determines the type of Machines that
                      should be managed by the ControlPlaneMachineSet. Currently,
                      the only valid value is machines_v1beta1_machine_openshift_io.
                    enum:
                    - machines_v1beta1_machine_openshift_io
                    type: string
                  machines_v1beta1_machine_openshift_io:
                    description: OpenShiftMachineV1Beta1Machine defines the template
                      for creating Machines from the v1beta1.machine.openshift.io
                      API group.
                    properties:
                      failureDomains:
                        description: FailureDomains is the list of failure domains
                          (sometimes called availability zones) in which the ControlPlaneMachineSet
                          should balance the Control Plane Machines. This will be
                          merged into the ProviderSpec given in the template. This
                          field is optional on platforms that do not require placement
                          information.
                        properties:
                          aws:
                            description: AWS configures failure domain information
                              for the AWS platform.
                            items:
                              description: AWSFailureDomain configures failure domain
                                information for the AWS platform.
                              minProperties: 1
                              properties:
                                placement:
                                  description: Placement configures the placement
                                    information for this instance.
                                  properties:
                                    availabilityZone:
                                      description: AvailabilityZone is the availability
                                        zone of the instance.
                                      type: string
                                  required:
                                  - availabilityZone
                                  type: object
                                subnet:
                                  description: Subnet is a reference to the subnet
                                    to use for this instance.
                                  properties:
                                    arn:
                                      description: ARN of resource.
                                      type: string
                                    filters:
                                      description: Filters is a set of filters used
                                        to identify a resource.
                                      items:
                                        description: AWSResourceFilter is a filter
                                          used to identify an AWS resource
                                        properties:
                                          name:
                                            description: Name of the filter. Filter
                                              names are case-sensitive.
                                            type: string
                                          values:
                                            description: Values includes one or more
                                              filter values. Filter values are case-sensitive.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - name
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    id:
                                      description: ID of resource.
                                      type: string
                                    type:
                                      description: Type determines how the reference
                                        will fetch the AWS resource.
                                      enum:
                                      - ID
                                      - ARN
                                      - Filters
                                      type: string
                                  required:
                                  - type
                                  type: object
                                  x-kubernetes-validations:
                                  - message: id is required when type is ID, and forbidden
                                      otherwise
                                    rule: 'has(self.type) && self.type == ''ID'' ?  has(self.id)
                                      : !has(self.id)'
                                  - message: arn is required when type is ARN, and
                                      forbidden otherwise
                                    rule: 'has(self.type) && self.type == ''ARN''
                                      ?  has(self.arn) : !has(self.arn)'
                                  - message: filters is required when type is Filters,
                                      and forbidden otherwise
                                    rule: 'has(self.type) && self.type == ''Filters''
                                      ?  has(self.filters) : !has(self.filters)'
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          azure:
                            description: Azure configures failure domain information
                              for the Azure platform.
                            items:
                              description: AzureFailureDomain configures failure domain
                                information for the Azure platform.
                              properties:
                                subnet:
                                  description: subnet is the name of the network subnet
                                    in which the VM will be created. When omitted,
                                    the subnet value from the machine providerSpec
                                    template will be used.
                                  maxLength: 80
                                  pattern: ^[a-zA-Z0-9](?:[a-zA-Z0-9._-]*[a-zA-Z0-9_])?$
                                  type: string
                                zone:
                                  description: Availability Zone for the virtual machine.
                                    If nil, the virtual machine should be deployed
                                    to no zone.
                                  type: string
                              required:
                              - zone
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          gcp:
                            description: GCP configures failure domain information
                              for the GCP platform.
                            items:
                              description: GCPFailureDomain configures failure domain
                                information for the GCP platform
                              properties:
                                zone:
                                  description: Zone is the zone in which the GCP machine
                                    provider will create the VM.
                                  type: string
                              required:
                              - zone
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          nutanix:
                            description: nutanix configures failure domain information
                              for the Nutanix platform.
                            items:
                              description: NutanixFailureDomainReference refers to
                                the failure domain of the Nutanix platform.
                              properties:
                                name:
                                  description: name of the failure domain in which
                                    the nutanix machine provider will create the VM.
                                    Failure domains are defined in a cluster's config.openshift.io/Infrastructure
                                    resource.
                                  maxLength: 64
                                  minLength: 1
                                  pattern: '[a-z0-9]([-a-z0-9]*[a-z0-9])?'
                                  type: string
                              required:
                              - name
                              type: object
                            type: array
                            x-kubernetes-list-map-keys:
                            - name
                            x-kubernetes-list-type: map
                          openstack:
                            description: OpenStack configures failure domain information
                              for the OpenStack platform.
                            items:
                              description: OpenStackFailureDomain configures failure
                                domain information for the OpenStack platform.
                              minProperties: 1
                              properties:
                                availabilityZone:
                                  description: 'availabilityZone is the nova availability
                                    zone in which the OpenStack machine provider will
                                    create the VM. If not specified, the VM will be
                                    created in the default availability zone specified
                                    in the nova configuration. Availability zone names
                                    must NOT contain : since it is used by admin users
                                    to specify hosts where instances are launched
                                    in server creation. Also, it must not contain
                                    spaces otherwise it will lead to node that belongs
                                    to this availability zone register failure, see
                                    kubernetes/cloud-provider-openstack#1379 for further
                                    information. The maximum length of availability
                                    zone name is 63 as per labels limits.'
                                  maxLength: 63
                                  minLength: 1
                                  pattern: '^[^: ]*$'
                                  type: string
                                rootVolume:
                                  description: rootVolume contains settings that will
                                    be used by the OpenStack machine provider to create
                                    the root volume attached to the VM. If not specified,
                                    no root volume will be created.
                                  properties:
                                    availabilityZone:
                                      description: availabilityZone specifies the
                                        Cinder availability zone where the root volume
                                        will be created. If not specifified, the root
                                        volume will be created in the availability
                                        zone specified by the volume type in the cinder
                                        configuration. If the volume type (configured
                                        in the OpenStack cluster) does not specify
                                        an availability zone, the root volume will
                                        be created in the default availability zone
                                        specified in the cinder configuration. See
                                        https://docs.openstack.org/cinder/latest/admin/availability-zone-type.html
                                        for more details. If the OpenStack cluster
                                        is deployed with the cross_az_attach configuration
                                        option set to false, the root volume will
                                        have to be in the same availability zone as
                                        the VM (defined by OpenStackFailureDomain.AvailabilityZone).
                                        Availability zone names must NOT contain spaces
                                        otherwise it will lead to volume that belongs
                                        to this availability zone register failure,
                                        see kubernetes/cloud-provider-openstack#1379
                                        for further information. The maximum length
                                        of availability zone name is 63 as per labels
                                        limits.
                                      maxLength: 63
                                      minLength: 1
                                      pattern: ^[^ ]*$
                                      type: string
                                    volumeType:
                                      description: volumeType specifies the type of
                                        the root volume that will be provisioned.
                                        The maximum length of a volume type name is
                                        255 characters, as per the OpenStack limit.
                                      maxLength: 255
                                      minLength: 1
                                      type: string
                                  required:
                                  - volumeType
                                  type: object
                              type: object
                              x-kubernetes-validations:
                              - message: rootVolume.availabilityZone is required when
                                  availabilityZone is set
                                rule: '!has(self.availabilityZone) || !has(self.rootVolume)
                                  || has(self.rootVolume.availabilityZone)'
                            type: array
                            x-kubernetes-list-type: atomic
                          platform:
                            description: Platform identifies the platform for which
                              the FailureDomain represents. Currently supported values
                              are AWS, Azure, GCP, OpenStack, VSphere and Nutanix.
                            enum:
                            - ""
                            - AWS
                            - Azure
                            - BareMetal
                            - GCP
                            - Libvirt
                            - OpenStack
                            - None
                            - VSphere
                            - oVirt
                            - IBMCloud
                            - KubeVirt
                            - EquinixMetal
                            - PowerVS
                            - AlibabaCloud
                            - Nutanix
                            - External
                            type: string
                          vsphere:
                            description: vsphere configures failure domain information
                              for the VSphere platform.
                            items:
                              description: VSphereFailureDomain configures failure
                                domain information for the vSphere platform
                              properties:
                                name:
                                  description: name of the failure domain in which
                                    the vSphere machine provider will create the VM.
                                    Failure domains are defined in a cluster's config.openshift.io/Infrastructure
                                    resource. When balancing machines across failure
                                    domains, the control plane machine set will inject
                                    configuration from the Infrastructure resource
                                    into the machine providerSpec to allocate the
                                    machine to a failure domain.
                                  type: string
                              required:
                              - name
                              type: object
                            type: array
                            x-kubernetes-list-map-keys:
                            - name
                            x-kubernetes-list-type: map
                        required:
                        - platform
                        type: object
                        x-kubernetes-validations:
                        - message: aws configuration is required when platform is
                            AWS, and forbidden otherwise
                          rule: 'has(self.platform) && self.platform == ''AWS'' ?  has(self.aws)
                            : !has(self.aws)'
                        - message: azure configuration is required when platform is
                            Azure, and forbidden otherwise
                          rule: 'has(self.platform) && self.platform == ''Azure''
                            ?  has(self.azure) : !has(self.azure)'
                        - message: gcp configuration is required when platform is
                            GCP, and forbidden otherwise
                          rule: 'has(self.platform) && self.platform == ''GCP'' ?  has(self.gcp)
                            : !has(self.gcp)'
                        - message: openstack configuration is required when platform
                            is OpenStack, and forbidden otherwise
                          rule: 'has(self.platform) && self.platform == ''OpenStack''
                            ?  has(self.openstack) : !has(self.openstack)'
                        - message: vsphere configuration is required when platform
                            is VSphere, and forbidden otherwise
                          rule: 'has(self.platform) && self.platform == ''VSphere''
                            ?  has(self.vsphere) : !has(self.vsphere)'
                        - message: nutanix configuration is required when platform
                            is Nutanix, and forbidden otherwise
                          rule: 'has(self.platform) && self.platform == ''Nutanix''
                            ?  has(self.nutanix) : !has(self.nutanix)'
                      metadata:
                        description: 'ObjectMeta is the standard object metadata More
                          info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
                          Labels are required to match the ControlPlaneMachineSet
                          selector.'
                        properties:
                          annotations:
                            additionalProperties:
                              type: string
                            description: 'Annotations is an unstructured key value
                              map stored with a resource that may be set by external
                              tools to store and retrieve arbitrary metadata. They
                              are not queryable and should be preserved when modifying
                              objects. More info: http://kubernetes.io/docs/user-guide/annotations'
                            type: object
                          labels:
                            additionalProperties:
                              type: string
                            description: 'Map of string keys and values that can be
                              used to organize and categorize (scope and select) objects.
                              May match selectors of replication controllers and services.
                              More info: http://kubernetes.io/docs/user-guide/labels.
                              This field must contain both the ''machine.openshift.io/cluster-api-machine-role''
                              and ''machine.openshift.io/cluster-api-machine-type''
                              labels, both with a value of ''master''. It must also
                              contain a label with the key ''machine.openshift.io/cluster-api-cluster''.'
                            type: object
                            x-kubernetes-validations:
                            - message: label 'machine.openshift.io/cluster-api-machine-role'
                                is required, and must have value 'master'
                              rule: '''machine.openshift.io/cluster-api-machine-role''
                                in self && self[''machine.openshift.io/cluster-api-machine-role'']
                                == ''master'''
                            - message: label 'machine.openshift.io/cluster-api-machine-type'
                                is required, and must have value 'master'
                              rule: '''machine.openshift.io/cluster-api-machine-type''
                                in self && self[''machine.openshift.io/cluster-api-machine-type'']
                                == ''master'''
                            - message: label 'machine.openshift.io/cluster-api-cluster'
                                is required
                              rule: '''machine.openshift.io/cluster-api-cluster''
                                in self'
                        required:
                        - labels
                        type: object
                      spec:
                        description: Spec contains the desired configuration of the
                          Control Plane Machines. The ProviderSpec within contains
                          platform specific details for creating the Control Plane
                          Machines. The ProviderSe should be complete apart from the
                          platform specific failure domain field. This will be overriden
                          when the Machines are created based on the FailureDomains
                          field.
                        properties:
                          lifecycleHooks:
                            description: LifecycleHooks allow users to pause operations
                              on the machine at certain predefined points within the
                              machine lifecycle.
                            properties:
                              preDrain:
                                description: PreDrain hooks prevent the machine from
                                  being drained. This also blocks further lifecycle
                                  events, such as termination.
                                items:
                                  description: LifecycleHook represents a single instance
                                    of a lifecycle hook
                                  properties:
                                    name:
                                      description: Name defines a unique name for
                                        the lifcycle hook. The name should be unique
                                        and descriptive, ideally 1-3 words, in CamelCase
                                        or it may be namespaced, eg. foo.example.com/CamelCase.
                                        Names must be unique and should only be managed
                                        by a single entity.
                                      maxLength: 256
                                      minLength: 3
                                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                                      type: string
                                    owner:
                                      description: Owner defines the owner of the
                                        lifecycle hook. This should be descriptive
                                        enough so that users can identify who/what
                                        is responsible for blocking the lifecycle.
                                        This could be the name of a controller (e.g.
                                        clusteroperator/etcd) or an administrator
                                        managing the hook.
                                      maxLength: 512
                                      minLength: 3
                                      type: string
                                  required:
                                  - name
                                  - owner
                                  type: object
                                type: array
                                x-kubernetes-list-map-keys:
                                - name
                                x-kubernetes-list-type: map
                              preTerminate:
                                description: PreTerminate hooks prevent the machine
                                  from being terminated. PreTerminate hooks be actioned
                                  after the Machine has been drained.
                                items:
                                  description: LifecycleHook represents a single instance
                                    of a lifecycle hook
                                  properties:
                                    name:
                                      description: Name defines a unique name for
                                        the lifcycle hook. The name should be unique
                                        and descriptive, ideally 1-3 words, in CamelCase
                                        or it may be namespaced, eg. foo.example.com/CamelCase.
                                        Names must be unique and should only be managed
                                        by a single entity.
                                      maxLength: 256
                                      minLength: 3
                                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                                      type: string
                                    owner:
                                      description: Owner defines the owner of the
                                        lifecycle hook. This should be descriptive
                                        enough so that users can identify who/what
                                        is responsible for blocking the lifecycle.
                                        This could be the name of a controller (e.g.
                                        clusteroperator/etcd) or an administrator
                                        managing the hook.
                                      maxLength: 512
                                      minLength: 3
                                      type: string
                                  required:
                                  - name
                                  - owner
                                  type: object
                                type: array
                                x-kubernetes-list-map-keys:
                                - name
                                x-kubernetes-list-type: map
                            type: object
                          metadata:
                            description: ObjectMeta will autopopulate the Node created.
                              Use this to indicate what labels, annotations, name
                              prefix, etc., should be used when creating the Node.
                            properties:
                              annotations:
                                additionalProperties:
                                  type: string
                                description: 'Annotations is an unstructured key value
                                  map stored with a resource that may be set by external
                                  tools to store and retrieve arbitrary metadata.
                                  They are not queryable and should be preserved when
                                  modifying objects. More info: http://kubernetes.io/docs/user-guide/annotations'
                                type: object
                              generateName:
                                description: "GenerateName is an optional prefix,
                                  used by the server, to generate a unique name ONLY
                                  IF the Name field has not been provided. If this
                                  field is used, the name returned to the client will
                                  be different than the name passed. This value will
                                  also be combined with a unique suffix. The provided
                                  value has the same validation rules as the Name
                                  field, and may be truncated by the length of the
                                  suffix required to make the value unique on the
                                  server. \n If this field is specified and the generated
                                  name exists, the server will NOT return a 409 -
                                  instead, it will either return 201 Created or 500
                                  with Reason ServerTimeout indicating a unique name
                                  could not be found in the time allotted, and the
                                  client should retry (optionally after the time indicated
                                  in the Retry-After header). \n Applied only if Name
                                  is not specified. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#idempotency"
                                type: string
                              labels:
                                additionalProperties:
                                  type: string
                                description: 'Map of string keys and values that can
                                  be used to organize and categorize (scope and select)
                                  objects. May match selectors of replication controllers
                                  and services. More info: http://kubernetes.io/docs/user-guide/labels'
                                type: object
                              name:
                                description: 'Name must be unique within a namespace.
                                  Is required when creating resources, although some
                                  resources may allow a client to request the generation
                                  of an appropriate name automatically. Name is primarily
                                  intended for creation idempotence and configuration
                                  definition. Cannot be updated. More info: http://kubernetes.io/docs/user-guide/identifiers#names'
                                type: string
                              namespace:
                                description: "Namespace defines the space within each
                                  name must be unique. An empty namespace is equivalent
                                  to the \"default\" namespace, but \"default\" is
                                  the canonical representation. Not all objects are
                                  required to be scoped to a namespace - the value
                                  of this field for those objects will be empty. \n
                                  Must be a DNS_LABEL. Cannot be updated. More info:
                                  http://kubernetes.io/docs/user-guide/namespaces"
                                type: string
                              ownerReferences:
                                description: List of objects depended by this object.
                                  If ALL objects in the list have been deleted, this
                                  object will be garbage collected. If this object
                                  is managed by a controller, then an entry in this
                                  list will point to this controller, with the controller
                                  field set to true. There cannot be more than one
                                  managing controller.
                                items:
                                  description: OwnerReference contains enough information
                                    to let you identify an owning object. An owning
                                    object must be in the same namespace as the dependent,
                                    or be cluster-scoped, so there is no namespace
                                    field.
                                  properties:
                                    apiVersion:
                                      description: API version of the referent.
                                      type: string
                                    blockOwnerDeletion:
                                      description: If true, AND if the owner has the
                                        "foregroundDeletion" finalizer, then the owner
                                        cannot be deleted from the key-value store
                                        until this reference is removed. See https://kubernetes.io/docs/concepts/architecture/garbage-collection/#foreground-deletion
                                        for how the garbage collector interacts with
                                        this field and enforces the foreground deletion.
                                        Defaults to false. To set this field, a user
                                        needs "delete" permission of the owner, otherwise
                                        422 (Unprocessable Entity) will be returned.
                                      type: boolean
                                    controller:
                                      description: If true, this reference points
                                        to the managing controller.
                                      type: boolean
                                    kind:
                                      description: 'Kind of the referent. More info:
                                        https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
                                      type: string
                                    name:
                                      description: 'Name of the referent. More info:
                                        https://kubernetes.io/docs/concepts/overview/working-with-objects/names#names'
                                      type: string
                                    uid:
                                      description: 'UID of the referent. More info:
                                        https://kubernetes.io/docs/concepts/overview/working-with-objects/names#uids'
                                      type: string
                                  required:
                                  - apiVersion
                                  - kind
                                  - name
                                  - uid
                                  type: object
                                type: array
                                x-kubernetes-list-map-keys:
                                - uid
                                x-kubernetes-list-type: map
                            type: object
                          providerID:
                            description: ProviderID is the identification ID of the
                              machine provided by the provider. This field must match
                              the provider ID as seen on the node object corresponding
                              to this machine. This field is required by higher level
                              consumers of cluster-api. Example use case is cluster
                              autoscaler with cluster-api as provider. Clean-up logic
                              in the autoscaler compares machines to nodes to find
                              out machines at provider which could not get registered
                              as Kubernetes nodes. With cluster-api as a generic out-of-tree
                              provider for autoscaler, this field is required by autoscaler
                              to be able to have a provider view of the list of machines.
                              Another list of nodes is queried from the k8s apiserver
                              and then a comparison is done to find out unregistered
                              machines and are marked for delete. This field will
                              be set by the actuators and consumed by higher level
                              entities like autoscaler that will be interfacing with
                              cluster-api as generic provider.
                            type: string
                          providerSpec:
                            description: ProviderSpec details Provider-specific configuration
                              to use during node creation.
                            properties:
                              value:
                                description: Value is an inlined, serialized representation
                                  of the resource configuration. It is recommended
                                  that providers maintain their own versioned API
                                  types that should be serialized/deserialized from
                                  this field, akin to component config.
                                type: object
                                x-kubernetes-preserve-unknown-fields: true
                            type: object
                          taints:
                            description: The list of the taints to be applied to the
                              corresponding Node in additive manner. This list will
                              not overwrite any other taints added to the Node on
                              an ongoing basis by other entities. These taints should
                              be actively reconciled e.g. if you ask the machine controller
                              to apply a taint and then manually remove the taint
                              the machine controller will put it back) but not have
                              the machine controller remove any taints
                            items:
                              description: The node this Taint is attached to has
                                the "effect" on any pod that does not tolerate the
                                Taint.
                              properties:
                                effect:
                                  description: Required. The effect of the taint on
                                    pods that do not tolerate the taint. Valid effects
                                    are NoSchedule, PreferNoSchedule and NoExecute.
                                  type: string
                                key:
                                  description: Required. The taint key to be applied
                                    to a node.
                                  type: string
                                timeAdded:
                                  description: TimeAdded represents the time at which
                                    the taint was added. It is only written for NoExecute
                                    taints.
                                  format: date-time
                                  type: string
                                value:
                                  description: The taint value corresponding to the
                                    taint key.
                                  type: string
                              required:
                              - effect
                              - key
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                        type: object
                    required:
                    - metadata
                    - spec
                    type: object
                required:
                - machineType
                type: object
                x-kubernetes-validations:
                - message: machines_v1beta1_machine_openshift_io configuration is
                    required when machineType is machines_v1beta1_machine_openshift_io,
                    and forbidden otherwise
                  rule: 'has(self.machineType) && self.machineType == ''machines_v1beta1_machine_openshift_io''
                    ?  has(self.machines_v1beta1_machine_openshift_io) : !has(self.machines_v1beta1_machine_openshift_io)'
            required:
            - replicas
            - selector
            - template
            type: object
          status:
            description: ControlPlaneMachineSetStatus represents the status of the
              ControlPlaneMachineSet CRD.
            properties:
              conditions:
                description: 'Conditions represents the observations of the ControlPlaneMachineSet''s
                  current state. Known .status.conditions.type are: Available, Degraded
                  and Progressing.'
                items:
                  description: "Condition contains details for one aspect of the current
                    state of this API Resource. --- This struct is intended for direct
                    use as an array at the field path .status.conditions.  For example,
                    \n type FooStatus struct{ // Represents the observations of a
                    foo's current state. // Known .status.conditions.type are: \"Available\",
                    \"Progressing\", and \"Degraded\" // +patchMergeKey=type // +patchStrategy=merge
                    // +listType=map // +listMapKey=type Conditions []metav1.Condition
                    `json:\"conditions,omitempty\" patchStrategy:\"merge\" patchMergeKey:\"type\"
                    protobuf:\"bytes,1,rep,name=conditions\"` \n // other fields }"
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the last time the condition
                        transitioned from one status to another. This should be when
                        the underlying condition changed.  If that is not known, then
                        using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: message is a human readable message indicating
                        details about the transition. This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: observedGeneration represents the .metadata.generation
                        that the condition was set based upon. For instance, if .metadata.generation
                        is currently 12, but the .status.conditions[x].observedGeneration
                        is 9, the condition is out of date with respect to the current
                        state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: reason contains a programmatic identifier indicating
                        the reason for the condition's last transition. Producers
                        of specific condition types may define expected values and
                        meanings for this field, and whether the values are considered
                        a guaranteed API. The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                        --- Many .condition.type values are consistent across resources
                        like Available, but because arbitrary conditions can be useful
                        (see .node.status.conditions), the ability to deconflict is
                        important. The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              observedGeneration:
                description: ObservedGeneration is the most recent generation observed
                  for this ControlPlaneMachineSet. It corresponds to the ControlPlaneMachineSets's
                  generation, which is updated on mutation by the API Server.
                format: int64
                type: integer
              readyReplicas:
                description: ReadyReplicas is the number of Control Plane Machines
                  created by the ControlPlaneMachineSet controller which are ready.
                  Note that this value may be higher than the desired number of replicas
                  while rolling updates are in-progress.
                format: int32
                type: integer
              replicas:
                description: Replicas is the number of Control Plane Machines created
                  by the ControlPlaneMachineSet controller. Note that during update
                  operations this value may differ from the desired replica count.
                format: int32
                type: integer
              unavailableReplicas:
                description: UnavailableReplicas is the number of Control Plane Machines
                  that are still required before the ControlPlaneMachineSet reaches
                  the desired available capacity. When this value is non-zero, the
                  number of ReadyReplicas is less than the desired Replicas.
                format: int32
                type: integer
              updatedReplicas:
                description: UpdatedReplicas is the number of non-terminated Control
                  Plane Machines created by the ControlPlaneMachineSet controller
                  that have the desired provider spec and are ready. This value is
                  set to 0 when a change is detected to the desired spec. When the
                  update strategy is RollingUpdate, this will also coincide with starting
                  the process of updating the Machines. When the update strategy is
                  OnDelete, this value will remain at 0 until a user deletes an existing
                  replica and its replacement has become ready.
                format: int32
                type: integer
            type: object
        type: object
    served: true
    storage: true
    subresources:
      scale:
        labelSelectorPath: .status.labelSelector
        specReplicasPath: .spec.replicas
        statusReplicasPath: .status.replicas
      status: {}
status:
  acceptedNames:
    kind: ControlPlaneMachineSet
    listKind: ControlPlaneMachineSetList
    plural: controlplanemachinesets
    singular: controlplanemachineset
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:36Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:36Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
