---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/639
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T15:18:06Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-network-operator
    operation: Update
    time: "2025-06-25T15:18:06Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T15:18:06Z"
  name: podnetworkconnectivitychecks.controlplane.operator.openshift.io
  resourceVersion: "40032"
  uid: a5a87494-3ffa-4ba0-8919-5b1492659557
spec:
  conversion:
    strategy: None
  group: controlplane.operator.openshift.io
  names:
    kind: PodNetworkConnectivityCheck
    listKind: PodNetworkConnectivityCheckList
    plural: podnetworkconnectivitychecks
    singular: podnetworkconnectivitycheck
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: "PodNetworkConnectivityCheck \n Compatibility level 4: No compatibility
          is provided, the API can change at any point for any reason. These capabilities
          should not be used by applications needing long term support."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: Spec defines the source and target of the connectivity check
            properties:
              sourcePod:
                description: SourcePod names the pod from which the condition will
                  be checked
                pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                type: string
              targetEndpoint:
                description: EndpointAddress to check. A TCP address of the form host:port.
                  Note that if host is a DNS name, then the check would fail if the
                  DNS name cannot be resolved. Specify an IP address for host to bypass
                  DNS name lookup.
                pattern: ^\S+:\d*$
                type: string
              tlsClientCert:
                description: TLSClientCert, if specified, references a kubernetes.io/tls
                  type secret with 'tls.crt' and 'tls.key' entries containing an optional
                  TLS client certificate and key to be used when checking endpoints
                  that require a client certificate in order to gracefully preform
                  the scan without causing excessive logging in the endpoint process.
                  The secret must exist in the same namespace as this resource.
                properties:
                  name:
                    description: name is the metadata.name of the referenced secret
                    type: string
                required:
                - name
                type: object
            required:
            - sourcePod
            - targetEndpoint
            type: object
          status:
            description: Status contains the observed status of the connectivity check
            properties:
              conditions:
                description: Conditions summarize the status of the check
                items:
                  description: PodNetworkConnectivityCheckCondition represents the
                    overall status of the pod network connectivity.
                  properties:
                    lastTransitionTime:
                      description: Last time the condition transitioned from one status
                        to another.
                      format: date-time
                      nullable: true
                      type: string
                    message:
                      description: Message indicating details about last transition
                        in a human readable format.
                      type: string
                    reason:
                      description: Reason for the condition's last status transition
                        in a machine readable format.
                      type: string
                    status:
                      description: Status of the condition
                      type: string
                    type:
                      description: Type of the condition
                      type: string
                  required:
                  - lastTransitionTime
                  - status
                  - type
                  type: object
                type: array
              failures:
                description: Failures contains logs of unsuccessful check actions
                items:
                  description: LogEntry records events
                  properties:
                    latency:
                      description: Latency records how long the action mentioned in
                        the entry took.
                      nullable: true
                      type: string
                    message:
                      description: Message explaining status in a human readable format.
                      type: string
                    reason:
                      description: Reason for status in a machine readable format.
                      type: string
                    success:
                      description: Success indicates if the log entry indicates a
                        success or failure.
                      type: boolean
                    time:
                      description: Start time of check action.
                      format: date-time
                      nullable: true
                      type: string
                  required:
                  - success
                  - time
                  type: object
                type: array
              outages:
                description: Outages contains logs of time periods of outages
                items:
                  description: OutageEntry records time period of an outage
                  properties:
                    end:
                      description: End of outage detected
                      format: date-time
                      nullable: true
                      type: string
                    endLogs:
                      description: EndLogs contains log entries related to the end
                        of this outage. Should contain the success entry that resolved
                        the outage and possibly a few of the failure log entries that
                        preceded it.
                      items:
                        description: LogEntry records events
                        properties:
                          latency:
                            description: Latency records how long the action mentioned
                              in the entry took.
                            nullable: true
                            type: string
                          message:
                            description: Message explaining status in a human readable
                              format.
                            type: string
                          reason:
                            description: Reason for status in a machine readable format.
                            type: string
                          success:
                            description: Success indicates if the log entry indicates
                              a success or failure.
                            type: boolean
                          time:
                            description: Start time of check action.
                            format: date-time
                            nullable: true
                            type: string
                        required:
                        - success
                        - time
                        type: object
                      type: array
                    message:
                      description: Message summarizes outage details in a human readable
                        format.
                      type: string
                    start:
                      description: Start of outage detected
                      format: date-time
                      nullable: true
                      type: string
                    startLogs:
                      description: StartLogs contains log entries related to the start
                        of this outage. Should contain the original failure, any entries
                        where the failure mode changed.
                      items:
                        description: LogEntry records events
                        properties:
                          latency:
                            description: Latency records how long the action mentioned
                              in the entry took.
                            nullable: true
                            type: string
                          message:
                            description: Message explaining status in a human readable
                              format.
                            type: string
                          reason:
                            description: Reason for status in a machine readable format.
                            type: string
                          success:
                            description: Success indicates if the log entry indicates
                              a success or failure.
                            type: boolean
                          time:
                            description: Start time of check action.
                            format: date-time
                            nullable: true
                            type: string
                        required:
                        - success
                        - time
                        type: object
                      type: array
                  required:
                  - start
                  type: object
                type: array
              successes:
                description: Successes contains logs successful check actions
                items:
                  description: LogEntry records events
                  properties:
                    latency:
                      description: Latency records how long the action mentioned in
                        the entry took.
                      nullable: true
                      type: string
                    message:
                      description: Message explaining status in a human readable format.
                      type: string
                    reason:
                      description: Reason for status in a machine readable format.
                      type: string
                    success:
                      description: Success indicates if the log entry indicates a
                        success or failure.
                      type: boolean
                    time:
                      description: Start time of check action.
                      format: date-time
                      nullable: true
                      type: string
                  required:
                  - success
                  - time
                  type: object
                type: array
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: PodNetworkConnectivityCheck
    listKind: PodNetworkConnectivityCheckList
    plural: podnetworkconnectivitychecks
    singular: podnetworkconnectivitycheck
  conditions:
  - lastTransitionTime: "2025-06-25T15:18:06Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T15:18:06Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1alpha1
