---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/481
    api.openshift.io/merged-by-featuregates: "true"
    capability.openshift.io/name: Console
    description: ConsoleSample is an extension to customizing OpenShift web console
      by adding samples.
    displayName: ConsoleSample
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:53:48Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:48Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:capability.openshift.io/name: {}
          f:description: {}
          f:displayName: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:42:20Z"
  name: consolesamples.console.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "144805"
  uid: 8db942e4-9438-4ed6-9e5d-58dfc0ba20b4
spec:
  conversion:
    strategy: None
  group: console.openshift.io
  names:
    kind: ConsoleSample
    listKind: ConsoleSampleList
    plural: consolesamples
    singular: consolesample
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "ConsoleSample is an extension to customizing OpenShift web console
          by adding samples. \n Compatibility level 1: Stable within a major release
          for a minimum of 12 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: spec contains configuration for a console sample.
            properties:
              abstract:
                description: "abstract is a short introduction to the sample. \n It
                  is required and must be no more than 100 characters in length. \n
                  The abstract is shown on the sample card tile below the title and
                  provider and is limited to three lines of content."
                maxLength: 100
                type: string
              description:
                description: "description is a long form explanation of the sample.
                  \n It is required and can have a maximum length of **4096** characters.
                  \n It is a README.md-like content for additional information, links,
                  pre-conditions, and other instructions. It will be rendered as Markdown
                  so that it can contain line breaks, links, and other simple formatting."
                maxLength: 4096
                type: string
              icon:
                description: "icon is an optional base64 encoded image and shown beside
                  the sample title. \n The format must follow the data: URL format
                  and can have a maximum size of **10 KB**. \n data:[<mediatype>][;base64],<base64
                  encoded image> \n For example: \n data:image;base64,             plus
                  the base64 encoded image. \n Vector images can also be used. SVG
                  icons must start with: \n data:image/svg+xml;base64,     plus the
                  base64 encoded SVG image. \n All sample catalog icons will be shown
                  on a white background (also when the dark theme is used). The web
                  console ensures that different aspect ratios work correctly. Currently,
                  the surface of the icon is at most 40x100px. \n For more information
                  on the data URL format, please visit https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/Data_URLs."
                maxLength: 14000
                pattern: ^data:([a-z/\.+0-9]*;(([-a-zA-Z0-9=])*;)?)?base64,
                type: string
              provider:
                description: "provider is an optional label to honor who provides
                  the sample. \n It is optional and must be no more than 50 characters
                  in length. \n A provider can be a company like \"Red Hat\" or an
                  organization like \"CNCF\" or \"Knative\". \n Currently, the provider
                  is only shown on the sample card tile below the title with the prefix
                  \"Provided by \""
                maxLength: 50
                type: string
              source:
                description: source defines where to deploy the sample service from.
                  The sample may be sourced from an external git repository or container
                  image.
                properties:
                  containerImport:
                    description: containerImport allows the user import a container
                      image.
                    properties:
                      image:
                        description: "reference to a container image that provides
                          a HTTP service. The service must be exposed on the default
                          port (8080) unless otherwise configured with the port field.
                          \n Supported formats: - <repository-name>/<image-name> -
                          docker.io/<repository-name>/<image-name> - quay.io/<repository-name>/<image-name>
                          - quay.io/<repository-name>/<image-name>@sha256:<image hash>
                          - quay.io/<repository-name>/<image-name>:<tag>"
                        maxLength: 256
                        minLength: 1
                        type: string
                      service:
                        default:
                          targetPort: 8080
                        description: service contains configuration for the Service
                          resource created for this sample.
                        properties:
                          targetPort:
                            default: 8080
                            description: targetPort is the port that the service listens
                              on for HTTP requests. This port will be used for Service
                              and Route created for this sample. Port must be in the
                              range 1 to 65535. Default port is 8080.
                            format: int32
                            maximum: 65535
                            minimum: 1
                            type: integer
                        type: object
                    required:
                    - image
                    type: object
                  gitImport:
                    description: gitImport allows the user to import code from a git
                      repository.
                    properties:
                      repository:
                        description: repository contains the reference to the actual
                          Git repository.
                        properties:
                          contextDir:
                            description: contextDir is used to specify a directory
                              within the repository to build the component. Must start
                              with `/` and have a maximum length of 256 characters.
                              When omitted, the default value is to build from the
                              root of the repository.
                            maxLength: 256
                            pattern: ^/
                            type: string
                          revision:
                            description: revision is the git revision at which to
                              clone the git repository Can be used to clone a specific
                              branch, tag or commit SHA. Must be at most 256 characters
                              in length. When omitted the repository's default branch
                              is used.
                            maxLength: 256
                            type: string
                          url:
                            description: "url of the Git repository that contains
                              a HTTP service. The HTTP service must be exposed on
                              the default port (8080) unless otherwise configured
                              with the port field. \n Only public repositories on
                              GitHub, GitLab and Bitbucket are currently supported:
                              \n - https://github.com/<org>/<repository> - https://gitlab.com/<org>/<repository>
                              - https://bitbucket.org/<org>/<repository> \n The url
                              must have a maximum length of 256 characters."
                            maxLength: 256
                            minLength: 1
                            pattern: ^https:\/\/(github.com|gitlab.com|bitbucket.org)\/[a-zA-Z0-9-]+\/[a-zA-Z0-9-]+(.git)?$
                            type: string
                        required:
                        - url
                        type: object
                      service:
                        default:
                          targetPort: 8080
                        description: service contains configuration for the Service
                          resource created for this sample.
                        properties:
                          targetPort:
                            default: 8080
                            description: targetPort is the port that the service listens
                              on for HTTP requests. This port will be used for Service
                              created for this sample. Port must be in the range 1
                              to 65535. Default port is 8080.
                            format: int32
                            maximum: 65535
                            minimum: 1
                            type: integer
                        type: object
                    required:
                    - repository
                    type: object
                  type:
                    allOf:
                    - enum:
                      - GitImport
                      - ContainerImport
                    - enum:
                      - GitImport
                      - ContainerImport
                    description: 'type of the sample, currently supported: "GitImport";"ContainerImport"'
                    type: string
                required:
                - type
                type: object
                x-kubernetes-validations:
                - message: source.gitImport is required when source.type is GitImport,
                    and forbidden otherwise
                  rule: 'self.type == ''GitImport'' ? has(self.gitImport) : !has(self.gitImport)'
                - message: source.containerImport is required when source.type is
                    ContainerImport, and forbidden otherwise
                  rule: 'self.type == ''ContainerImport'' ? has(self.containerImport)
                    : !has(self.containerImport)'
              tags:
                description: "tags are optional string values that can be used to
                  find samples in the samples catalog. \n Examples of common tags
                  may be \"Java\", \"Quarkus\", etc. \n They will be displayed on
                  the samples details page."
                items:
                  type: string
                maxItems: 10
                type: array
                x-kubernetes-list-type: set
              title:
                description: "title is the display name of the sample. \n It is required
                  and must be no more than 50 characters in length."
                maxLength: 50
                minLength: 1
                type: string
              type:
                description: "type is an optional label to group multiple samples.
                  \n It is optional and must be no more than 20 characters in length.
                  \n Recommendation is a singular term like \"Builder Image\", \"Devfile\"
                  or \"Serverless Function\". \n Currently, the type is shown a badge
                  on the sample card tile in the top right corner."
                maxLength: 20
                type: string
            required:
            - abstract
            - description
            - source
            - title
            type: object
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true
status:
  acceptedNames:
    kind: ConsoleSample
    listKind: ConsoleSampleList
    plural: consolesamples
    singular: consolesample
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:48Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:48Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
