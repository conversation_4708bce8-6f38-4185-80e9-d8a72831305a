---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.11.3
  creationTimestamp: "2025-06-25T14:57:17Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:controller-gen.kubebuilder.io/version: {}
        f:ownerReferences:
          k:{"uid":"e8b7620a-d5d2-448e-b2bf-6f6768b9b1dc"}: {}
      f:spec:
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-network-operator/operconfig
    operation: Apply
    time: "2025-06-25T14:57:17Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:57:17Z"
  name: egressservices.k8s.ovn.org
  ownerReferences:
  - apiVersion: operator.openshift.io/v1
    blockOwnerDeletion: true
    controller: true
    kind: Network
    name: cluster
    uid: e8b7620a-d5d2-448e-b2bf-6f6768b9b1dc
  resourceVersion: "3901"
  uid: b2ef071e-8d9a-4dc5-acf6-a63595a2a604
spec:
  conversion:
    strategy: None
  group: k8s.ovn.org
  names:
    kind: EgressService
    listKind: EgressServiceList
    plural: egressservices
    singular: egressservice
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: EgressService is a CRD that allows the user to request that the
          source IP of egress packets originating from all of the pods that are endpoints
          of the corresponding LoadBalancer Service would be its ingress IP. In addition,
          it allows the user to request that egress packets originating from all of
          the pods that are endpoints of the LoadBalancer service would use a different
          network than the main one.
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: EgressServiceSpec defines the desired state of EgressService
            properties:
              network:
                description: The network which this service should send egress and
                  corresponding ingress replies to. This is typically implemented
                  as VRF mapping, representing a numeric id or string name of a routing
                  table which by omission uses the default host routing.
                type: string
              nodeSelector:
                description: Allows limiting the nodes that can be selected to handle
                  the service's traffic when sourceIPBy=LoadBalancerIP. When present
                  only a node whose labels match the specified selectors can be selected
                  for handling the service's traffic. When it is not specified any
                  node in the cluster can be chosen to manage the service's traffic.
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: A label selector requirement is a selector that
                        contains values, a key, and an operator that relates the key
                        and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: operator represents a key's relationship to
                            a set of values. Valid operators are In, NotIn, Exists
                            and DoesNotExist.
                          type: string
                        values:
                          description: values is an array of string values. If the
                            operator is In or NotIn, the values array must be non-empty.
                            If the operator is Exists or DoesNotExist, the values
                            array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: matchLabels is a map of {key,value} pairs. A single
                      {key,value} in the matchLabels map is equivalent to an element
                      of matchExpressions, whose key field is "key", the operator
                      is "In", and the values array contains only "value". The requirements
                      are ANDed.
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              sourceIPBy:
                description: Determines the source IP of egress traffic originating
                  from the pods backing the LoadBalancer Service. When `LoadBalancerIP`
                  the source IP is set to its LoadBalancer ingress IP. When `Network`
                  the source IP is set according to the interface of the Network,
                  leveraging the masquerade rules that are already in place. Typically
                  these rules specify SNAT to the IP of the outgoing interface, which
                  means the packet will typically leave with the IP of the node.
                enum:
                - LoadBalancerIP
                - Network
                type: string
            type: object
          status:
            description: EgressServiceStatus defines the observed state of EgressService
            properties:
              host:
                description: The name of the node selected to handle the service's
                  traffic. In case sourceIPBy=Network the field will be set to "ALL".
                type: string
            required:
            - host
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: EgressService
    listKind: EgressServiceList
    plural: egressservices
    singular: egressservice
  conditions:
  - lastTransitionTime: "2025-06-25T14:57:17Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:57:17Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
