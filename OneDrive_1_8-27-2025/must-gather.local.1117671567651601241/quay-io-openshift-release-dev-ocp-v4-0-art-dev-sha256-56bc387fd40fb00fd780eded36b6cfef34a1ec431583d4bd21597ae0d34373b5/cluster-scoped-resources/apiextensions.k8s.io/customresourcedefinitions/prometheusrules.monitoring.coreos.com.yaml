---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.15.0
    include.release.openshift.io/hypershift: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    operator.prometheus.io/version: 0.75.1
  creationTimestamp: "2025-06-25T14:53:48Z"
  generation: 2
  labels:
    app.kubernetes.io/managed-by: cluster-version-operator
    app.kubernetes.io/part-of: openshift-monitoring
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:categories: {}
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:48Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:controller-gen.kubebuilder.io/version: {}
          f:include.release.openshift.io/hypershift: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
          f:operator.prometheus.io/version: {}
        f:labels:
          .: {}
          f:app.kubernetes.io/managed-by: {}
          f:app.kubernetes.io/part-of: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:categories: {}
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T12:09:01Z"
  name: prometheusrules.monitoring.coreos.com
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21340462"
  uid: 5bc32d8d-6a8d-4cbb-befe-4c74a0b7ee72
spec:
  conversion:
    strategy: None
  group: monitoring.coreos.com
  names:
    categories:
    - prometheus-operator
    kind: PrometheusRule
    listKind: PrometheusRuleList
    plural: prometheusrules
    shortNames:
    - promrule
    singular: prometheusrule
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: PrometheusRule defines recording and alerting rules for a Prometheus
          instance
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Specification of desired alerting rule definitions for Prometheus.
            properties:
              groups:
                description: Content of Prometheus rule file
                items:
                  description: RuleGroup is a list of sequentially evaluated recording
                    and alerting rules.
                  properties:
                    interval:
                      description: Interval determines how often rules in the group
                        are evaluated.
                      pattern: ^(0|(([0-9]+)y)?(([0-9]+)w)?(([0-9]+)d)?(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$
                      type: string
                    limit:
                      description: |-
                        Limit the number of alerts an alerting rule and series a recording
                        rule can produce.
                        Limit is supported starting with Prometheus >= 2.31 and Thanos Ruler >= 0.24.
                      type: integer
                    name:
                      description: Name of the rule group.
                      minLength: 1
                      type: string
                    partial_response_strategy:
                      description: |-
                        PartialResponseStrategy is only used by ThanosRuler and will
                        be ignored by Prometheus instances.
                        More info: https://github.com/thanos-io/thanos/blob/main/docs/components/rule.md#partial-response
                      pattern: ^(?i)(abort|warn)?$
                      type: string
                    rules:
                      description: List of alerting and recording rules.
                      items:
                        description: |-
                          Rule describes an alerting or recording rule
                          See Prometheus documentation: [alerting](https://www.prometheus.io/docs/prometheus/latest/configuration/alerting_rules/) or [recording](https://www.prometheus.io/docs/prometheus/latest/configuration/recording_rules/#recording-rules) rule
                        properties:
                          alert:
                            description: |-
                              Name of the alert. Must be a valid label value.
                              Only one of `record` and `alert` must be set.
                            type: string
                          annotations:
                            additionalProperties:
                              type: string
                            description: |-
                              Annotations to add to each alert.
                              Only valid for alerting rules.
                            type: object
                          expr:
                            anyOf:
                            - type: integer
                            - type: string
                            description: PromQL expression to evaluate.
                            x-kubernetes-int-or-string: true
                          for:
                            description: Alerts are considered firing once they have
                              been returned for this long.
                            pattern: ^(0|(([0-9]+)y)?(([0-9]+)w)?(([0-9]+)d)?(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$
                            type: string
                          keep_firing_for:
                            description: KeepFiringFor defines how long an alert will
                              continue firing after the condition that triggered it
                              has cleared.
                            minLength: 1
                            pattern: ^(0|(([0-9]+)y)?(([0-9]+)w)?(([0-9]+)d)?(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$
                            type: string
                          labels:
                            additionalProperties:
                              type: string
                            description: Labels to add or overwrite.
                            type: object
                          record:
                            description: |-
                              Name of the time series to output to. Must be a valid metric name.
                              Only one of `record` and `alert` must be set.
                            type: string
                        required:
                        - expr
                        type: object
                      type: array
                  required:
                  - name
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - name
                x-kubernetes-list-type: map
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
status:
  acceptedNames:
    categories:
    - prometheus-operator
    kind: PrometheusRule
    listKind: PrometheusRuleList
    plural: prometheusrules
    shortNames:
    - promrule
    singular: prometheusrule
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:48Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:48Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
