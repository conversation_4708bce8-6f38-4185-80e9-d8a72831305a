---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.kubernetes.io: https://github.com/kubernetes-csi/external-snapshotter/pull/955
    controller-gen.kubebuilder.io/version: v0.15.0
  creationTimestamp: "2025-06-25T14:58:12Z"
  generation: 3
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"KubernetesAPIApprovalPolicyConformant"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T18:42:43Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.kubernetes.io: {}
          f:controller-gen.kubebuilder.io/version: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: csi-snapshot-controller-operator
    operation: Update
    time: "2025-08-01T12:09:20Z"
  name: volumesnapshotcontents.snapshot.storage.k8s.io
  resourceVersion: "21341589"
  uid: 97855dd8-8326-46c6-ba1e-713eb9d61976
spec:
  conversion:
    strategy: None
  group: snapshot.storage.k8s.io
  names:
    kind: VolumeSnapshotContent
    listKind: VolumeSnapshotContentList
    plural: volumesnapshotcontents
    shortNames:
    - vsc
    - vscs
    singular: volumesnapshotcontent
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - description: Indicates if the snapshot is ready to be used to restore a volume.
      jsonPath: .status.readyToUse
      name: ReadyToUse
      type: boolean
    - description: Represents the complete size of the snapshot in bytes
      jsonPath: .status.restoreSize
      name: RestoreSize
      type: integer
    - description: Determines whether this VolumeSnapshotContent and its physical
        snapshot on the underlying storage system should be deleted when its bound
        VolumeSnapshot is deleted.
      jsonPath: .spec.deletionPolicy
      name: DeletionPolicy
      type: string
    - description: Name of the CSI driver used to create the physical snapshot on
        the underlying storage system.
      jsonPath: .spec.driver
      name: Driver
      type: string
    - description: Name of the VolumeSnapshotClass to which this snapshot belongs.
      jsonPath: .spec.volumeSnapshotClassName
      name: VolumeSnapshotClass
      type: string
    - description: Name of the VolumeSnapshot object to which this VolumeSnapshotContent
        object is bound.
      jsonPath: .spec.volumeSnapshotRef.name
      name: VolumeSnapshot
      type: string
    - description: Namespace of the VolumeSnapshot object to which this VolumeSnapshotContent
        object is bound.
      jsonPath: .spec.volumeSnapshotRef.namespace
      name: VolumeSnapshotNamespace
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1
    schema:
      openAPIV3Schema:
        description: |-
          VolumeSnapshotContent represents the actual "on-disk" snapshot object in the
          underlying storage system
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: |-
              spec defines properties of a VolumeSnapshotContent created by the underlying storage system.
              Required.
            properties:
              deletionPolicy:
                description: |-
                  deletionPolicy determines whether this VolumeSnapshotContent and its physical snapshot on
                  the underlying storage system should be deleted when its bound VolumeSnapshot is deleted.
                  Supported values are "Retain" and "Delete".
                  "Retain" means that the VolumeSnapshotContent and its physical snapshot on underlying storage system are kept.
                  "Delete" means that the VolumeSnapshotContent and its physical snapshot on underlying storage system are deleted.
                  For dynamically provisioned snapshots, this field will automatically be filled in by the
                  CSI snapshotter sidecar with the "DeletionPolicy" field defined in the corresponding
                  VolumeSnapshotClass.
                  For pre-existing snapshots, users MUST specify this field when creating the
                   VolumeSnapshotContent object.
                  Required.
                enum:
                - Delete
                - Retain
                type: string
              driver:
                description: |-
                  driver is the name of the CSI driver used to create the physical snapshot on
                  the underlying storage system.
                  This MUST be the same as the name returned by the CSI GetPluginName() call for
                  that driver.
                  Required.
                type: string
              source:
                description: |-
                  source specifies whether the snapshot is (or should be) dynamically provisioned
                  or already exists, and just requires a Kubernetes object representation.
                  This field is immutable after creation.
                  Required.
                properties:
                  snapshotHandle:
                    description: |-
                      snapshotHandle specifies the CSI "snapshot_id" of a pre-existing snapshot on
                      the underlying storage system for which a Kubernetes object representation
                      was (or should be) created.
                      This field is immutable.
                    type: string
                    x-kubernetes-validations:
                    - message: snapshotHandle is immutable
                      rule: self == oldSelf
                  volumeHandle:
                    description: |-
                      volumeHandle specifies the CSI "volume_id" of the volume from which a snapshot
                      should be dynamically taken from.
                      This field is immutable.
                    type: string
                    x-kubernetes-validations:
                    - message: volumeHandle is immutable
                      rule: self == oldSelf
                type: object
                x-kubernetes-validations:
                - message: volumeHandle is required once set
                  rule: '!has(oldSelf.volumeHandle) || has(self.volumeHandle)'
                - message: snapshotHandle is required once set
                  rule: '!has(oldSelf.snapshotHandle) || has(self.snapshotHandle)'
                - message: exactly one of volumeHandle and snapshotHandle must be
                    set
                  rule: (has(self.volumeHandle) && !has(self.snapshotHandle)) || (!has(self.volumeHandle)
                    && has(self.snapshotHandle))
              sourceVolumeMode:
                description: |-
                  SourceVolumeMode is the mode of the volume whose snapshot is taken.
                  Can be either “Filesystem” or “Block”.
                  If not specified, it indicates the source volume's mode is unknown.
                  This field is immutable.
                  This field is an alpha field.
                type: string
                x-kubernetes-validations:
                - message: sourceVolumeMode is immutable
                  rule: self == oldSelf
              volumeSnapshotClassName:
                description: |-
                  name of the VolumeSnapshotClass from which this snapshot was (or will be)
                  created.
                  Note that after provisioning, the VolumeSnapshotClass may be deleted or
                  recreated with different set of values, and as such, should not be referenced
                  post-snapshot creation.
                type: string
              volumeSnapshotRef:
                description: |-
                  volumeSnapshotRef specifies the VolumeSnapshot object to which this
                  VolumeSnapshotContent object is bound.
                  VolumeSnapshot.Spec.VolumeSnapshotContentName field must reference to
                  this VolumeSnapshotContent's name for the bidirectional binding to be valid.
                  For a pre-existing VolumeSnapshotContent object, name and namespace of the
                  VolumeSnapshot object MUST be provided for binding to happen.
                  This field is immutable after creation.
                  Required.
                properties:
                  apiVersion:
                    description: API version of the referent.
                    type: string
                  fieldPath:
                    description: |-
                      If referring to a piece of an object instead of an entire object, this string
                      should contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].
                      For example, if the object reference is to a container within a pod, this would take on a value like:
                      "spec.containers{name}" (where "name" refers to the name of the container that triggered
                      the event) or if no container name is specified "spec.containers[2]" (container with
                      index 2 in this pod). This syntax is chosen only to have some well-defined way of
                      referencing a part of an object.
                      TODO: this design is not final and this field is subject to change in the future.
                    type: string
                  kind:
                    description: |-
                      Kind of the referent.
                      More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
                    type: string
                  name:
                    description: |-
                      Name of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                    type: string
                  namespace:
                    description: |-
                      Namespace of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/
                    type: string
                  resourceVersion:
                    description: |-
                      Specific resourceVersion to which this reference is made, if any.
                      More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
                    type: string
                  uid:
                    description: |-
                      UID of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids
                    type: string
                type: object
                x-kubernetes-map-type: atomic
                x-kubernetes-validations:
                - message: both spec.volumeSnapshotRef.name and spec.volumeSnapshotRef.namespace
                    must be set
                  rule: has(self.name) && has(self.__namespace__)
            required:
            - deletionPolicy
            - driver
            - source
            - volumeSnapshotRef
            type: object
            x-kubernetes-validations:
            - message: sourceVolumeMode is required once set
              rule: '!has(oldSelf.sourceVolumeMode) || has(self.sourceVolumeMode)'
          status:
            description: status represents the current information of a snapshot.
            properties:
              creationTime:
                description: |-
                  creationTime is the timestamp when the point-in-time snapshot is taken
                  by the underlying storage system.
                  In dynamic snapshot creation case, this field will be filled in by the
                  CSI snapshotter sidecar with the "creation_time" value returned from CSI
                  "CreateSnapshot" gRPC call.
                  For a pre-existing snapshot, this field will be filled with the "creation_time"
                  value returned from the CSI "ListSnapshots" gRPC call if the driver supports it.
                  If not specified, it indicates the creation time is unknown.
                  The format of this field is a Unix nanoseconds time encoded as an int64.
                  On Unix, the command `date +%s%N` returns the current time in nanoseconds
                  since 1970-01-01 00:00:00 UTC.
                format: int64
                type: integer
              error:
                description: |-
                  error is the last observed error during snapshot creation, if any.
                  Upon success after retry, this error field will be cleared.
                properties:
                  message:
                    description: |-
                      message is a string detailing the encountered error during snapshot
                      creation if specified.
                      NOTE: message may be logged, and it should not contain sensitive
                      information.
                    type: string
                  time:
                    description: time is the timestamp when the error was encountered.
                    format: date-time
                    type: string
                type: object
              readyToUse:
                description: |-
                  readyToUse indicates if a snapshot is ready to be used to restore a volume.
                  In dynamic snapshot creation case, this field will be filled in by the
                  CSI snapshotter sidecar with the "ready_to_use" value returned from CSI
                  "CreateSnapshot" gRPC call.
                  For a pre-existing snapshot, this field will be filled with the "ready_to_use"
                  value returned from the CSI "ListSnapshots" gRPC call if the driver supports it,
                  otherwise, this field will be set to "True".
                  If not specified, it means the readiness of a snapshot is unknown.
                type: boolean
              restoreSize:
                description: |-
                  restoreSize represents the complete size of the snapshot in bytes.
                  In dynamic snapshot creation case, this field will be filled in by the
                  CSI snapshotter sidecar with the "size_bytes" value returned from CSI
                  "CreateSnapshot" gRPC call.
                  For a pre-existing snapshot, this field will be filled with the "size_bytes"
                  value returned from the CSI "ListSnapshots" gRPC call if the driver supports it.
                  When restoring a volume from this snapshot, the size of the volume MUST NOT
                  be smaller than the restoreSize if it is specified, otherwise the restoration will fail.
                  If not specified, it indicates that the size is unknown.
                format: int64
                minimum: 0
                type: integer
              snapshotHandle:
                description: |-
                  snapshotHandle is the CSI "snapshot_id" of a snapshot on the underlying storage system.
                  If not specified, it indicates that dynamic snapshot creation has either failed
                  or it is still in progress.
                type: string
              volumeGroupSnapshotHandle:
                description: |-
                  VolumeGroupSnapshotHandle is the CSI "group_snapshot_id" of a group snapshot
                  on the underlying storage system.
                type: string
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
  - additionalPrinterColumns:
    - description: Indicates if the snapshot is ready to be used to restore a volume.
      jsonPath: .status.readyToUse
      name: ReadyToUse
      type: boolean
    - description: Represents the complete size of the snapshot in bytes
      jsonPath: .status.restoreSize
      name: RestoreSize
      type: integer
    - description: Determines whether this VolumeSnapshotContent and its physical
        snapshot on the underlying storage system should be deleted when its bound
        VolumeSnapshot is deleted.
      jsonPath: .spec.deletionPolicy
      name: DeletionPolicy
      type: string
    - description: Name of the CSI driver used to create the physical snapshot on
        the underlying storage system.
      jsonPath: .spec.driver
      name: Driver
      type: string
    - description: Name of the VolumeSnapshotClass to which this snapshot belongs.
      jsonPath: .spec.volumeSnapshotClassName
      name: VolumeSnapshotClass
      type: string
    - description: Name of the VolumeSnapshot object to which this VolumeSnapshotContent
        object is bound.
      jsonPath: .spec.volumeSnapshotRef.name
      name: VolumeSnapshot
      type: string
    - description: Namespace of the VolumeSnapshot object to which this VolumeSnapshotContent
        object is bound.
      jsonPath: .spec.volumeSnapshotRef.namespace
      name: VolumeSnapshotNamespace
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    deprecated: true
    deprecationWarning: snapshot.storage.k8s.io/v1beta1 VolumeSnapshotContent is deprecated;
      use snapshot.storage.k8s.io/v1 VolumeSnapshotContent
    name: v1beta1
    schema:
      openAPIV3Schema:
        description: VolumeSnapshotContent represents the actual "on-disk" snapshot
          object in the underlying storage system
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          spec:
            description: spec defines properties of a VolumeSnapshotContent created
              by the underlying storage system. Required.
            properties:
              deletionPolicy:
                description: deletionPolicy determines whether this VolumeSnapshotContent
                  and its physical snapshot on the underlying storage system should
                  be deleted when its bound VolumeSnapshot is deleted. Supported values
                  are "Retain" and "Delete". "Retain" means that the VolumeSnapshotContent
                  and its physical snapshot on underlying storage system are kept.
                  "Delete" means that the VolumeSnapshotContent and its physical snapshot
                  on underlying storage system are deleted. For dynamically provisioned
                  snapshots, this field will automatically be filled in by the CSI
                  snapshotter sidecar with the "DeletionPolicy" field defined in the
                  corresponding VolumeSnapshotClass. For pre-existing snapshots, users
                  MUST specify this field when creating the  VolumeSnapshotContent
                  object. Required.
                enum:
                - Delete
                - Retain
                type: string
              driver:
                description: driver is the name of the CSI driver used to create the
                  physical snapshot on the underlying storage system. This MUST be
                  the same as the name returned by the CSI GetPluginName() call for
                  that driver. Required.
                type: string
              source:
                description: source specifies whether the snapshot is (or should be)
                  dynamically provisioned or already exists, and just requires a Kubernetes
                  object representation. This field is immutable after creation. Required.
                properties:
                  snapshotHandle:
                    description: snapshotHandle specifies the CSI "snapshot_id" of
                      a pre-existing snapshot on the underlying storage system for
                      which a Kubernetes object representation was (or should be)
                      created. This field is immutable.
                    type: string
                  volumeHandle:
                    description: volumeHandle specifies the CSI "volume_id" of the
                      volume from which a snapshot should be dynamically taken from.
                      This field is immutable.
                    type: string
                type: object
              volumeSnapshotClassName:
                description: name of the VolumeSnapshotClass from which this snapshot
                  was (or will be) created. Note that after provisioning, the VolumeSnapshotClass
                  may be deleted or recreated with different set of values, and as
                  such, should not be referenced post-snapshot creation.
                type: string
              volumeSnapshotRef:
                description: volumeSnapshotRef specifies the VolumeSnapshot object
                  to which this VolumeSnapshotContent object is bound. VolumeSnapshot.Spec.VolumeSnapshotContentName
                  field must reference to this VolumeSnapshotContent's name for the
                  bidirectional binding to be valid. For a pre-existing VolumeSnapshotContent
                  object, name and namespace of the VolumeSnapshot object MUST be
                  provided for binding to happen. This field is immutable after creation.
                  Required.
                properties:
                  apiVersion:
                    description: API version of the referent.
                    type: string
                  fieldPath:
                    description: 'If referring to a piece of an object instead of
                      an entire object, this string should contain a valid JSON/Go
                      field access statement, such as desiredState.manifest.containers[2].
                      For example, if the object reference is to a container within
                      a pod, this would take on a value like: "spec.containers{name}"
                      (where "name" refers to the name of the container that triggered
                      the event) or if no container name is specified "spec.containers[2]"
                      (container with index 2 in this pod). This syntax is chosen
                      only to have some well-defined way of referencing a part of
                      an object. TODO: this design is not final and this field is
                      subject to change in the future.'
                    type: string
                  kind:
                    description: 'Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
                    type: string
                  name:
                    description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names'
                    type: string
                  namespace:
                    description: 'Namespace of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/'
                    type: string
                  resourceVersion:
                    description: 'Specific resourceVersion to which this reference
                      is made, if any. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency'
                    type: string
                  uid:
                    description: 'UID of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids'
                    type: string
                type: object
            required:
            - deletionPolicy
            - driver
            - source
            - volumeSnapshotRef
            type: object
          status:
            description: status represents the current information of a snapshot.
            properties:
              creationTime:
                description: creationTime is the timestamp when the point-in-time
                  snapshot is taken by the underlying storage system. In dynamic snapshot
                  creation case, this field will be filled in by the CSI snapshotter
                  sidecar with the "creation_time" value returned from CSI "CreateSnapshot"
                  gRPC call. For a pre-existing snapshot, this field will be filled
                  with the "creation_time" value returned from the CSI "ListSnapshots"
                  gRPC call if the driver supports it. If not specified, it indicates
                  the creation time is unknown. The format of this field is a Unix
                  nanoseconds time encoded as an int64. On Unix, the command `date
                  +%s%N` returns the current time in nanoseconds since 1970-01-01
                  00:00:00 UTC.
                format: int64
                type: integer
              error:
                description: error is the last observed error during snapshot creation,
                  if any. Upon success after retry, this error field will be cleared.
                properties:
                  message:
                    description: 'message is a string detailing the encountered error
                      during snapshot creation if specified. NOTE: message may be
                      logged, and it should not contain sensitive information.'
                    type: string
                  time:
                    description: time is the timestamp when the error was encountered.
                    format: date-time
                    type: string
                type: object
              readyToUse:
                description: readyToUse indicates if a snapshot is ready to be used
                  to restore a volume. In dynamic snapshot creation case, this field
                  will be filled in by the CSI snapshotter sidecar with the "ready_to_use"
                  value returned from CSI "CreateSnapshot" gRPC call. For a pre-existing
                  snapshot, this field will be filled with the "ready_to_use" value
                  returned from the CSI "ListSnapshots" gRPC call if the driver supports
                  it, otherwise, this field will be set to "True". If not specified,
                  it means the readiness of a snapshot is unknown.
                type: boolean
              restoreSize:
                description: restoreSize represents the complete size of the snapshot
                  in bytes. In dynamic snapshot creation case, this field will be
                  filled in by the CSI snapshotter sidecar with the "size_bytes" value
                  returned from CSI "CreateSnapshot" gRPC call. For a pre-existing
                  snapshot, this field will be filled with the "size_bytes" value
                  returned from the CSI "ListSnapshots" gRPC call if the driver supports
                  it. When restoring a volume from this snapshot, the size of the
                  volume MUST NOT be smaller than the restoreSize if it is specified,
                  otherwise the restoration will fail. If not specified, it indicates
                  that the size is unknown.
                format: int64
                minimum: 0
                type: integer
              snapshotHandle:
                description: snapshotHandle is the CSI "snapshot_id" of a snapshot
                  on the underlying storage system. If not specified, it indicates
                  that dynamic snapshot creation has either failed or it is still
                  in progress.
                type: string
            type: object
        required:
        - spec
        type: object
    served: false
    storage: false
    subresources:
      status: {}
status:
  acceptedNames:
    kind: VolumeSnapshotContent
    listKind: VolumeSnapshotContentList
    plural: volumesnapshotcontents
    shortNames:
    - vsc
    - vscs
    singular: volumesnapshotcontent
  conditions:
  - lastTransitionTime: "2025-06-25T14:58:12Z"
    message: approved in https://github.com/kubernetes-csi/external-snapshotter/pull/955
    reason: ApprovedAnnotation
    status: "True"
    type: KubernetesAPIApprovalPolicyConformant
  - lastTransitionTime: "2025-06-25T14:58:12Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:58:12Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
