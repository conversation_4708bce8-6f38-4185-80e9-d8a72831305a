---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/1453
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    release.openshift.io/feature-set: Default
  creationTimestamp: "2025-06-25T14:53:53Z"
  generation: 3
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:53Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
          f:release.openshift.io/feature-set: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T12:25:54Z"
  name: machineconfigurations.operator.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21360003"
  uid: e184834f-97d4-4f87-aec3-f5da28e8e21b
spec:
  conversion:
    strategy: None
  group: operator.openshift.io
  names:
    kind: MachineConfiguration
    listKind: MachineConfigurationList
    plural: machineconfigurations
    singular: machineconfiguration
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "MachineConfiguration provides information to configure an operator
          to manage Machine Configuration. \n Compatibility level 1: Stable within
          a major release for a minimum of 12 months or 3 minor releases (whichever
          is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: spec is the specification of the desired behavior of the
              Machine Config Operator
            properties:
              failedRevisionLimit:
                description: failedRevisionLimit is the number of failed static pod
                  installer revisions to keep on disk and in the api -1 = unlimited,
                  0 or unset = 5 (default)
                format: int32
                type: integer
              forceRedeploymentReason:
                description: forceRedeploymentReason can be used to force the redeployment
                  of the operand by providing a unique string. This provides a mechanism
                  to kick a previously failed deployment and provide a reason why
                  you think it will work this time instead of failing again on the
                  same config.
                type: string
              logLevel:
                default: Normal
                description: "logLevel is an intent based logging for an overall component.
                  \ It does not give fine grained control, but it is a simple way
                  to manage coarse grained logging choices that operators have to
                  interpret for their operands. \n Valid values are: \"Normal\", \"Debug\",
                  \"Trace\", \"TraceAll\". Defaults to \"Normal\"."
                enum:
                - ""
                - Normal
                - Debug
                - Trace
                - TraceAll
                type: string
              managedBootImages:
                description: managedBootImages allows configuration for the management
                  of boot images for machine resources within the cluster. This configuration
                  allows users to select resources that should be updated to the latest
                  boot images during cluster upgrades, ensuring that new machines
                  always boot with the current cluster version's boot image. When
                  omitted, no boot images will be updated.
                properties:
                  machineManagers:
                    description: machineManagers can be used to register machine management
                      resources for boot image updates. The Machine Config Operator
                      will watch for changes to this list. Only one entry is permitted
                      per type of machine management resource.
                    items:
                      description: MachineManager describes a target machine resource
                        that is registered for boot image updates. It stores identifying
                        information such as the resource type and the API Group of
                        the resource. It also provides granular control via the selection
                        field.
                      properties:
                        apiGroup:
                          description: apiGroup is name of the APIGroup that the machine
                            management resource belongs to. The only current valid
                            value is machine.openshift.io. machine.openshift.io means
                            that the machine manager will only register resources
                            that belong to OpenShift machine API group.
                          enum:
                          - machine.openshift.io
                          type: string
                        resource:
                          description: resource is the machine management resource's
                            type. The only current valid value is machinesets. machinesets
                            means that the machine manager will only register resources
                            of the kind MachineSet.
                          enum:
                          - machinesets
                          type: string
                        selection:
                          description: selection allows granular control of the machine
                            management resources that will be registered for boot
                            image updates.
                          properties:
                            mode:
                              description: mode determines how machine managers will
                                be selected for updates. Valid values are All and
                                Partial. All means that every resource matched by
                                the machine manager will be updated. Partial requires
                                specified selector(s) and allows customisation of
                                which resources matched by the machine manager will
                                be updated.
                              enum:
                              - All
                              - Partial
                              type: string
                            partial:
                              description: partial provides label selector(s) that
                                can be used to match machine management resources.
                                Only permitted when mode is set to "Partial".
                              properties:
                                machineResourceSelector:
                                  description: machineResourceSelector is a label
                                    selector that can be used to select machine resources
                                    like MachineSets.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: A label selector requirement
                                          is a selector that contains values, a key,
                                          and an operator that relates the key and
                                          values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: operator represents a key's
                                              relationship to a set of values. Valid
                                              operators are In, NotIn, Exists and
                                              DoesNotExist.
                                            type: string
                                          values:
                                            description: values is an array of string
                                              values. If the operator is In or NotIn,
                                              the values array must be non-empty.
                                              If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This
                                              array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: matchLabels is a map of {key,value}
                                        pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions,
                                        whose key field is "key", the operator is
                                        "In", and the values array contains only "value".
                                        The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                              required:
                              - machineResourceSelector
                              type: object
                          required:
                          - mode
                          type: object
                          x-kubernetes-validations:
                          - message: Partial is required when type is partial, and
                              forbidden otherwise
                            rule: 'has(self.mode) && self.mode == ''Partial'' ?  has(self.partial)
                              : !has(self.partial)'
                      required:
                      - apiGroup
                      - resource
                      - selection
                      type: object
                    type: array
                    x-kubernetes-list-map-keys:
                    - resource
                    - apiGroup
                    x-kubernetes-list-type: map
                type: object
              managementState:
                description: managementState indicates whether and how the operator
                  should manage the component
                pattern: ^(Managed|Unmanaged|Force|Removed)$
                type: string
              nodeDisruptionPolicy:
                description: nodeDisruptionPolicy allows an admin to set granular
                  node disruption actions for MachineConfig-based updates, such as
                  drains, service reloads, etc. Specifying this will allow for less
                  downtime when doing small configuration updates to the cluster.
                  This configuration has no effect on cluster upgrades which will
                  still incur node disruption where required.
                properties:
                  files:
                    description: files is a list of MachineConfig file definitions
                      and actions to take to changes on those paths This list supports
                      a maximum of 50 entries.
                    items:
                      description: NodeDisruptionPolicySpecFile is a file entry and
                        corresponding actions to take and is used in the NodeDisruptionPolicyConfig
                        object
                      properties:
                        actions:
                          description: actions represents the series of commands to
                            be executed on changes to the file at the corresponding
                            file path. Actions will be applied in the order that they
                            are set in this list. If there are other incoming changes
                            to other MachineConfig entries in the same update that
                            require a reboot, the reboot will supercede these actions.
                            Valid actions are Reboot, Drain, Reload, DaemonReload
                            and None. The Reboot action and the None action cannot
                            be used in conjunction with any of the other actions.
                            This list supports a maximum of 10 entries.
                          items:
                            properties:
                              reload:
                                description: reload specifies the service to reload,
                                  only valid if type is reload
                                properties:
                                  serviceName:
                                    description: serviceName is the full name (e.g.
                                      crio.service) of the service to be reloaded
                                      Service names should be of the format ${NAME}${SERVICETYPE}
                                      and can up to 255 characters long. ${NAME} must
                                      be atleast 1 character long and can only consist
                                      of alphabets, digits, ":", "-", "_", ".", and
                                      "\". ${SERVICETYPE} must be one of ".service",
                                      ".socket", ".device", ".mount", ".automount",
                                      ".swap", ".target", ".path", ".timer", ".snapshot",
                                      ".slice" or ".scope".
                                    maxLength: 255
                                    type: string
                                    x-kubernetes-validations:
                                    - message: Invalid ${SERVICETYPE} in service name.
                                        Expected format is ${NAME}${SERVICETYPE},
                                        where ${SERVICETYPE} must be one of ".service",
                                        ".socket", ".device", ".mount", ".automount",
                                        ".swap", ".target", ".path", ".timer",".snapshot",
                                        ".slice" or ".scope".
                                      rule: self.matches('\\.(service|socket|device|mount|automount|swap|target|path|timer|snapshot|slice|scope)$')
                                    - message: Invalid ${NAME} in service name. Expected
                                        format is ${NAME}${SERVICETYPE}, where {NAME}
                                        must be atleast 1 character long and can only
                                        consist of alphabets, digits, ":", "-", "_",
                                        ".", and "\"
                                      rule: self.matches('^[a-zA-Z0-9:._\\\\-]+\\..')
                                required:
                                - serviceName
                                type: object
                              restart:
                                description: restart specifies the service to restart,
                                  only valid if type is restart
                                properties:
                                  serviceName:
                                    description: serviceName is the full name (e.g.
                                      crio.service) of the service to be restarted
                                      Service names should be of the format ${NAME}${SERVICETYPE}
                                      and can up to 255 characters long. ${NAME} must
                                      be atleast 1 character long and can only consist
                                      of alphabets, digits, ":", "-", "_", ".", and
                                      "\". ${SERVICETYPE} must be one of ".service",
                                      ".socket", ".device", ".mount", ".automount",
                                      ".swap", ".target", ".path", ".timer", ".snapshot",
                                      ".slice" or ".scope".
                                    maxLength: 255
                                    type: string
                                    x-kubernetes-validations:
                                    - message: Invalid ${SERVICETYPE} in service name.
                                        Expected format is ${NAME}${SERVICETYPE},
                                        where ${SERVICETYPE} must be one of ".service",
                                        ".socket", ".device", ".mount", ".automount",
                                        ".swap", ".target", ".path", ".timer",".snapshot",
                                        ".slice" or ".scope".
                                      rule: self.matches('\\.(service|socket|device|mount|automount|swap|target|path|timer|snapshot|slice|scope)$')
                                    - message: Invalid ${NAME} in service name. Expected
                                        format is ${NAME}${SERVICETYPE}, where {NAME}
                                        must be atleast 1 character long and can only
                                        consist of alphabets, digits, ":", "-", "_",
                                        ".", and "\"
                                      rule: self.matches('^[a-zA-Z0-9:._\\\\-]+\\..')
                                required:
                                - serviceName
                                type: object
                              type:
                                description: type represents the commands that will
                                  be carried out if this NodeDisruptionPolicySpecActionType
                                  is executed Valid values are Reboot, Drain, Reload,
                                  Restart, DaemonReload and None. reload/restart requires
                                  a corresponding service target specified in the
                                  reload/restart field. Other values require no further
                                  configuration
                                enum:
                                - Reboot
                                - Drain
                                - Reload
                                - Restart
                                - DaemonReload
                                - None
                                type: string
                            required:
                            - type
                            type: object
                            x-kubernetes-validations:
                            - message: reload is required when type is Reload, and
                                forbidden otherwise
                              rule: 'has(self.type) && self.type == ''Reload'' ? has(self.reload)
                                : !has(self.reload)'
                            - message: restart is required when type is Restart, and
                                forbidden otherwise
                              rule: 'has(self.type) && self.type == ''Restart'' ?
                                has(self.restart) : !has(self.restart)'
                          maxItems: 10
                          type: array
                          x-kubernetes-list-type: atomic
                          x-kubernetes-validations:
                          - message: Reboot action can only be specified standalone,
                              as it will override any other actions
                            rule: 'self.exists(x, x.type==''Reboot'') ? size(self)
                              == 1 : true'
                          - message: None action can only be specified standalone,
                              as it will override any other actions
                            rule: 'self.exists(x, x.type==''None'') ? size(self) ==
                              1 : true'
                        path:
                          description: path is the location of a file being managed
                            through a MachineConfig. The Actions in the policy will
                            apply to changes to the file at this path.
                          type: string
                      required:
                      - actions
                      - path
                      type: object
                    maxItems: 50
                    type: array
                    x-kubernetes-list-map-keys:
                    - path
                    x-kubernetes-list-type: map
                  sshkey:
                    description: sshkey maps to the ignition.sshkeys field in the
                      MachineConfig object, definition an action for this will apply
                      to all sshkey changes in the cluster
                    properties:
                      actions:
                        description: actions represents the series of commands to
                          be executed on changes to the file at the corresponding
                          file path. Actions will be applied in the order that they
                          are set in this list. If there are other incoming changes
                          to other MachineConfig entries in the same update that require
                          a reboot, the reboot will supercede these actions. Valid
                          actions are Reboot, Drain, Reload, DaemonReload and None.
                          The Reboot action and the None action cannot be used in
                          conjunction with any of the other actions. This list supports
                          a maximum of 10 entries.
                        items:
                          properties:
                            reload:
                              description: reload specifies the service to reload,
                                only valid if type is reload
                              properties:
                                serviceName:
                                  description: serviceName is the full name (e.g.
                                    crio.service) of the service to be reloaded Service
                                    names should be of the format ${NAME}${SERVICETYPE}
                                    and can up to 255 characters long. ${NAME} must
                                    be atleast 1 character long and can only consist
                                    of alphabets, digits, ":", "-", "_", ".", and
                                    "\". ${SERVICETYPE} must be one of ".service",
                                    ".socket", ".device", ".mount", ".automount",
                                    ".swap", ".target", ".path", ".timer", ".snapshot",
                                    ".slice" or ".scope".
                                  maxLength: 255
                                  type: string
                                  x-kubernetes-validations:
                                  - message: Invalid ${SERVICETYPE} in service name.
                                      Expected format is ${NAME}${SERVICETYPE}, where
                                      ${SERVICETYPE} must be one of ".service", ".socket",
                                      ".device", ".mount", ".automount", ".swap",
                                      ".target", ".path", ".timer",".snapshot", ".slice"
                                      or ".scope".
                                    rule: self.matches('\\.(service|socket|device|mount|automount|swap|target|path|timer|snapshot|slice|scope)$')
                                  - message: Invalid ${NAME} in service name. Expected
                                      format is ${NAME}${SERVICETYPE}, where {NAME}
                                      must be atleast 1 character long and can only
                                      consist of alphabets, digits, ":", "-", "_",
                                      ".", and "\"
                                    rule: self.matches('^[a-zA-Z0-9:._\\\\-]+\\..')
                              required:
                              - serviceName
                              type: object
                            restart:
                              description: restart specifies the service to restart,
                                only valid if type is restart
                              properties:
                                serviceName:
                                  description: serviceName is the full name (e.g.
                                    crio.service) of the service to be restarted Service
                                    names should be of the format ${NAME}${SERVICETYPE}
                                    and can up to 255 characters long. ${NAME} must
                                    be atleast 1 character long and can only consist
                                    of alphabets, digits, ":", "-", "_", ".", and
                                    "\". ${SERVICETYPE} must be one of ".service",
                                    ".socket", ".device", ".mount", ".automount",
                                    ".swap", ".target", ".path", ".timer", ".snapshot",
                                    ".slice" or ".scope".
                                  maxLength: 255
                                  type: string
                                  x-kubernetes-validations:
                                  - message: Invalid ${SERVICETYPE} in service name.
                                      Expected format is ${NAME}${SERVICETYPE}, where
                                      ${SERVICETYPE} must be one of ".service", ".socket",
                                      ".device", ".mount", ".automount", ".swap",
                                      ".target", ".path", ".timer",".snapshot", ".slice"
                                      or ".scope".
                                    rule: self.matches('\\.(service|socket|device|mount|automount|swap|target|path|timer|snapshot|slice|scope)$')
                                  - message: Invalid ${NAME} in service name. Expected
                                      format is ${NAME}${SERVICETYPE}, where {NAME}
                                      must be atleast 1 character long and can only
                                      consist of alphabets, digits, ":", "-", "_",
                                      ".", and "\"
                                    rule: self.matches('^[a-zA-Z0-9:._\\\\-]+\\..')
                              required:
                              - serviceName
                              type: object
                            type:
                              description: type represents the commands that will
                                be carried out if this NodeDisruptionPolicySpecActionType
                                is executed Valid values are Reboot, Drain, Reload,
                                Restart, DaemonReload and None. reload/restart requires
                                a corresponding service target specified in the reload/restart
                                field. Other values require no further configuration
                              enum:
                              - Reboot
                              - Drain
                              - Reload
                              - Restart
                              - DaemonReload
                              - None
                              type: string
                          required:
                          - type
                          type: object
                          x-kubernetes-validations:
                          - message: reload is required when type is Reload, and forbidden
                              otherwise
                            rule: 'has(self.type) && self.type == ''Reload'' ? has(self.reload)
                              : !has(self.reload)'
                          - message: restart is required when type is Restart, and
                              forbidden otherwise
                            rule: 'has(self.type) && self.type == ''Restart'' ? has(self.restart)
                              : !has(self.restart)'
                        maxItems: 10
                        type: array
                        x-kubernetes-list-type: atomic
                        x-kubernetes-validations:
                        - message: Reboot action can only be specified standalone,
                            as it will override any other actions
                          rule: 'self.exists(x, x.type==''Reboot'') ? size(self) ==
                            1 : true'
                        - message: None action can only be specified standalone, as
                            it will override any other actions
                          rule: 'self.exists(x, x.type==''None'') ? size(self) ==
                            1 : true'
                    required:
                    - actions
                    type: object
                  units:
                    description: units is a list MachineConfig unit definitions and
                      actions to take on changes to those services This list supports
                      a maximum of 50 entries.
                    items:
                      description: NodeDisruptionPolicySpecUnit is a systemd unit
                        name and corresponding actions to take and is used in the
                        NodeDisruptionPolicyConfig object
                      properties:
                        actions:
                          description: actions represents the series of commands to
                            be executed on changes to the file at the corresponding
                            file path. Actions will be applied in the order that they
                            are set in this list. If there are other incoming changes
                            to other MachineConfig entries in the same update that
                            require a reboot, the reboot will supercede these actions.
                            Valid actions are Reboot, Drain, Reload, DaemonReload
                            and None. The Reboot action and the None action cannot
                            be used in conjunction with any of the other actions.
                            This list supports a maximum of 10 entries.
                          items:
                            properties:
                              reload:
                                description: reload specifies the service to reload,
                                  only valid if type is reload
                                properties:
                                  serviceName:
                                    description: serviceName is the full name (e.g.
                                      crio.service) of the service to be reloaded
                                      Service names should be of the format ${NAME}${SERVICETYPE}
                                      and can up to 255 characters long. ${NAME} must
                                      be atleast 1 character long and can only consist
                                      of alphabets, digits, ":", "-", "_", ".", and
                                      "\". ${SERVICETYPE} must be one of ".service",
                                      ".socket", ".device", ".mount", ".automount",
                                      ".swap", ".target", ".path", ".timer", ".snapshot",
                                      ".slice" or ".scope".
                                    maxLength: 255
                                    type: string
                                    x-kubernetes-validations:
                                    - message: Invalid ${SERVICETYPE} in service name.
                                        Expected format is ${NAME}${SERVICETYPE},
                                        where ${SERVICETYPE} must be one of ".service",
                                        ".socket", ".device", ".mount", ".automount",
                                        ".swap", ".target", ".path", ".timer",".snapshot",
                                        ".slice" or ".scope".
                                      rule: self.matches('\\.(service|socket|device|mount|automount|swap|target|path|timer|snapshot|slice|scope)$')
                                    - message: Invalid ${NAME} in service name. Expected
                                        format is ${NAME}${SERVICETYPE}, where {NAME}
                                        must be atleast 1 character long and can only
                                        consist of alphabets, digits, ":", "-", "_",
                                        ".", and "\"
                                      rule: self.matches('^[a-zA-Z0-9:._\\\\-]+\\..')
                                required:
                                - serviceName
                                type: object
                              restart:
                                description: restart specifies the service to restart,
                                  only valid if type is restart
                                properties:
                                  serviceName:
                                    description: serviceName is the full name (e.g.
                                      crio.service) of the service to be restarted
                                      Service names should be of the format ${NAME}${SERVICETYPE}
                                      and can up to 255 characters long. ${NAME} must
                                      be atleast 1 character long and can only consist
                                      of alphabets, digits, ":", "-", "_", ".", and
                                      "\". ${SERVICETYPE} must be one of ".service",
                                      ".socket", ".device", ".mount", ".automount",
                                      ".swap", ".target", ".path", ".timer", ".snapshot",
                                      ".slice" or ".scope".
                                    maxLength: 255
                                    type: string
                                    x-kubernetes-validations:
                                    - message: Invalid ${SERVICETYPE} in service name.
                                        Expected format is ${NAME}${SERVICETYPE},
                                        where ${SERVICETYPE} must be one of ".service",
                                        ".socket", ".device", ".mount", ".automount",
                                        ".swap", ".target", ".path", ".timer",".snapshot",
                                        ".slice" or ".scope".
                                      rule: self.matches('\\.(service|socket|device|mount|automount|swap|target|path|timer|snapshot|slice|scope)$')
                                    - message: Invalid ${NAME} in service name. Expected
                                        format is ${NAME}${SERVICETYPE}, where {NAME}
                                        must be atleast 1 character long and can only
                                        consist of alphabets, digits, ":", "-", "_",
                                        ".", and "\"
                                      rule: self.matches('^[a-zA-Z0-9:._\\\\-]+\\..')
                                required:
                                - serviceName
                                type: object
                              type:
                                description: type represents the commands that will
                                  be carried out if this NodeDisruptionPolicySpecActionType
                                  is executed Valid values are Reboot, Drain, Reload,
                                  Restart, DaemonReload and None. reload/restart requires
                                  a corresponding service target specified in the
                                  reload/restart field. Other values require no further
                                  configuration
                                enum:
                                - Reboot
                                - Drain
                                - Reload
                                - Restart
                                - DaemonReload
                                - None
                                type: string
                            required:
                            - type
                            type: object
                            x-kubernetes-validations:
                            - message: reload is required when type is Reload, and
                                forbidden otherwise
                              rule: 'has(self.type) && self.type == ''Reload'' ? has(self.reload)
                                : !has(self.reload)'
                            - message: restart is required when type is Restart, and
                                forbidden otherwise
                              rule: 'has(self.type) && self.type == ''Restart'' ?
                                has(self.restart) : !has(self.restart)'
                          maxItems: 10
                          type: array
                          x-kubernetes-list-type: atomic
                          x-kubernetes-validations:
                          - message: Reboot action can only be specified standalone,
                              as it will override any other actions
                            rule: 'self.exists(x, x.type==''Reboot'') ? size(self)
                              == 1 : true'
                          - message: None action can only be specified standalone,
                              as it will override any other actions
                            rule: 'self.exists(x, x.type==''None'') ? size(self) ==
                              1 : true'
                        name:
                          description: name represents the service name of a systemd
                            service managed through a MachineConfig Actions specified
                            will be applied for changes to the named service. Service
                            names should be of the format ${NAME}${SERVICETYPE} and
                            can up to 255 characters long. ${NAME} must be atleast
                            1 character long and can only consist of alphabets, digits,
                            ":", "-", "_", ".", and "\". ${SERVICETYPE} must be one
                            of ".service", ".socket", ".device", ".mount", ".automount",
                            ".swap", ".target", ".path", ".timer", ".snapshot", ".slice"
                            or ".scope".
                          maxLength: 255
                          type: string
                          x-kubernetes-validations:
                          - message: Invalid ${SERVICETYPE} in service name. Expected
                              format is ${NAME}${SERVICETYPE}, where ${SERVICETYPE}
                              must be one of ".service", ".socket", ".device", ".mount",
                              ".automount", ".swap", ".target", ".path", ".timer",".snapshot",
                              ".slice" or ".scope".
                            rule: self.matches('\\.(service|socket|device|mount|automount|swap|target|path|timer|snapshot|slice|scope)$')
                          - message: Invalid ${NAME} in service name. Expected format
                              is ${NAME}${SERVICETYPE}, where {NAME} must be atleast
                              1 character long and can only consist of alphabets,
                              digits, ":", "-", "_", ".", and "\"
                            rule: self.matches('^[a-zA-Z0-9:._\\\\-]+\\..')
                      required:
                      - actions
                      - name
                      type: object
                    maxItems: 50
                    type: array
                    x-kubernetes-list-map-keys:
                    - name
                    x-kubernetes-list-type: map
                type: object
              observedConfig:
                description: observedConfig holds a sparse config that controller
                  has observed from the cluster state.  It exists in spec because
                  it is an input to the level for the operator
                nullable: true
                type: object
                x-kubernetes-preserve-unknown-fields: true
              operatorLogLevel:
                default: Normal
                description: "operatorLogLevel is an intent based logging for the
                  operator itself.  It does not give fine grained control, but it
                  is a simple way to manage coarse grained logging choices that operators
                  have to interpret for themselves. \n Valid values are: \"Normal\",
                  \"Debug\", \"Trace\", \"TraceAll\". Defaults to \"Normal\"."
                enum:
                - ""
                - Normal
                - Debug
                - Trace
                - TraceAll
                type: string
              succeededRevisionLimit:
                description: succeededRevisionLimit is the number of successful static
                  pod installer revisions to keep on disk and in the api -1 = unlimited,
                  0 or unset = 5 (default)
                format: int32
                type: integer
              unsupportedConfigOverrides:
                description: unsupportedConfigOverrides overrides the final configuration
                  that was computed by the operator. Red Hat does not support the
                  use of this field. Misuse of this field could lead to unexpected
                  behavior or conflict with other configuration options. Seek guidance
                  from the Red Hat support before using this field. Use of this property
                  blocks cluster upgrades, it must be removed before upgrading your
                  cluster.
                nullable: true
                type: object
                x-kubernetes-preserve-unknown-fields: true
            type: object
          status:
            description: status is the most recently observed status of the Machine
              Config Operator
            properties:
              conditions:
                description: conditions is a list of conditions and their status
                items:
                  description: "Condition contains details for one aspect of the current
                    state of this API Resource. --- This struct is intended for direct
                    use as an array at the field path .status.conditions.  For example,
                    \n type FooStatus struct{ // Represents the observations of a
                    foo's current state. // Known .status.conditions.type are: \"Available\",
                    \"Progressing\", and \"Degraded\" // +patchMergeKey=type // +patchStrategy=merge
                    // +listType=map // +listMapKey=type Conditions []metav1.Condition
                    `json:\"conditions,omitempty\" patchStrategy:\"merge\" patchMergeKey:\"type\"
                    protobuf:\"bytes,1,rep,name=conditions\"` \n // other fields }"
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the last time the condition
                        transitioned from one status to another. This should be when
                        the underlying condition changed.  If that is not known, then
                        using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: message is a human readable message indicating
                        details about the transition. This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: observedGeneration represents the .metadata.generation
                        that the condition was set based upon. For instance, if .metadata.generation
                        is currently 12, but the .status.conditions[x].observedGeneration
                        is 9, the condition is out of date with respect to the current
                        state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: reason contains a programmatic identifier indicating
                        the reason for the condition's last transition. Producers
                        of specific condition types may define expected values and
                        meanings for this field, and whether the values are considered
                        a guaranteed API. The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                        --- Many .condition.type values are consistent across resources
                        like Available, but because arbitrary conditions can be useful
                        (see .node.status.conditions), the ability to deconflict is
                        important. The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              nodeDisruptionPolicyStatus:
                description: nodeDisruptionPolicyStatus status reflects what the latest
                  cluster-validated policies are, and will be used by the Machine
                  Config Daemon during future node updates.
                properties:
                  clusterPolicies:
                    description: clusterPolicies is a merge of cluster default and
                      user provided node disruption policies.
                    properties:
                      files:
                        description: files is a list of MachineConfig file definitions
                          and actions to take to changes on those paths
                        items:
                          description: NodeDisruptionPolicyStatusFile is a file entry
                            and corresponding actions to take and is used in the NodeDisruptionPolicyClusterStatus
                            object
                          properties:
                            actions:
                              description: actions represents the series of commands
                                to be executed on changes to the file at the corresponding
                                file path. Actions will be applied in the order that
                                they are set in this list. If there are other incoming
                                changes to other MachineConfig entries in the same
                                update that require a reboot, the reboot will supercede
                                these actions. Valid actions are Reboot, Drain, Reload,
                                DaemonReload and None. The Reboot action and the None
                                action cannot be used in conjunction with any of the
                                other actions. This list supports a maximum of 10
                                entries.
                              items:
                                properties:
                                  reload:
                                    description: reload specifies the service to reload,
                                      only valid if type is reload
                                    properties:
                                      serviceName:
                                        description: serviceName is the full name
                                          (e.g. crio.service) of the service to be
                                          reloaded Service names should be of the
                                          format ${NAME}${SERVICETYPE} and can up
                                          to 255 characters long. ${NAME} must be
                                          atleast 1 character long and can only consist
                                          of alphabets, digits, ":", "-", "_", ".",
                                          and "\". ${SERVICETYPE} must be one of ".service",
                                          ".socket", ".device", ".mount", ".automount",
                                          ".swap", ".target", ".path", ".timer", ".snapshot",
                                          ".slice" or ".scope".
                                        maxLength: 255
                                        type: string
                                        x-kubernetes-validations:
                                        - message: Invalid ${SERVICETYPE} in service
                                            name. Expected format is ${NAME}${SERVICETYPE},
                                            where ${SERVICETYPE} must be one of ".service",
                                            ".socket", ".device", ".mount", ".automount",
                                            ".swap", ".target", ".path", ".timer",".snapshot",
                                            ".slice" or ".scope".
                                          rule: self.matches('\\.(service|socket|device|mount|automount|swap|target|path|timer|snapshot|slice|scope)$')
                                        - message: Invalid ${NAME} in service name.
                                            Expected format is ${NAME}${SERVICETYPE},
                                            where {NAME} must be atleast 1 character
                                            long and can only consist of alphabets,
                                            digits, ":", "-", "_", ".", and "\"
                                          rule: self.matches('^[a-zA-Z0-9:._\\\\-]+\\..')
                                    required:
                                    - serviceName
                                    type: object
                                  restart:
                                    description: restart specifies the service to
                                      restart, only valid if type is restart
                                    properties:
                                      serviceName:
                                        description: serviceName is the full name
                                          (e.g. crio.service) of the service to be
                                          restarted Service names should be of the
                                          format ${NAME}${SERVICETYPE} and can up
                                          to 255 characters long. ${NAME} must be
                                          atleast 1 character long and can only consist
                                          of alphabets, digits, ":", "-", "_", ".",
                                          and "\". ${SERVICETYPE} must be one of ".service",
                                          ".socket", ".device", ".mount", ".automount",
                                          ".swap", ".target", ".path", ".timer", ".snapshot",
                                          ".slice" or ".scope".
                                        maxLength: 255
                                        type: string
                                        x-kubernetes-validations:
                                        - message: Invalid ${SERVICETYPE} in service
                                            name. Expected format is ${NAME}${SERVICETYPE},
                                            where ${SERVICETYPE} must be one of ".service",
                                            ".socket", ".device", ".mount", ".automount",
                                            ".swap", ".target", ".path", ".timer",".snapshot",
                                            ".slice" or ".scope".
                                          rule: self.matches('\\.(service|socket|device|mount|automount|swap|target|path|timer|snapshot|slice|scope)$')
                                        - message: Invalid ${NAME} in service name.
                                            Expected format is ${NAME}${SERVICETYPE},
                                            where {NAME} must be atleast 1 character
                                            long and can only consist of alphabets,
                                            digits, ":", "-", "_", ".", and "\"
                                          rule: self.matches('^[a-zA-Z0-9:._\\\\-]+\\..')
                                    required:
                                    - serviceName
                                    type: object
                                  type:
                                    description: type represents the commands that
                                      will be carried out if this NodeDisruptionPolicyStatusActionType
                                      is executed Valid values are Reboot, Drain,
                                      Reload, Restart, DaemonReload, None and Special.
                                      reload/restart requires a corresponding service
                                      target specified in the reload/restart field.
                                      Other values require no further configuration
                                    enum:
                                    - Reboot
                                    - Drain
                                    - Reload
                                    - Restart
                                    - DaemonReload
                                    - None
                                    - Special
                                    type: string
                                required:
                                - type
                                type: object
                                x-kubernetes-validations:
                                - message: reload is required when type is Reload,
                                    and forbidden otherwise
                                  rule: 'has(self.type) && self.type == ''Reload''
                                    ? has(self.reload) : !has(self.reload)'
                                - message: restart is required when type is Restart,
                                    and forbidden otherwise
                                  rule: 'has(self.type) && self.type == ''Restart''
                                    ? has(self.restart) : !has(self.restart)'
                              maxItems: 10
                              type: array
                              x-kubernetes-list-type: atomic
                              x-kubernetes-validations:
                              - message: Reboot action can only be specified standalone,
                                  as it will override any other actions
                                rule: 'self.exists(x, x.type==''Reboot'') ? size(self)
                                  == 1 : true'
                              - message: None action can only be specified standalone,
                                  as it will override any other actions
                                rule: 'self.exists(x, x.type==''None'') ? size(self)
                                  == 1 : true'
                            path:
                              description: path is the location of a file being managed
                                through a MachineConfig. The Actions in the policy
                                will apply to changes to the file at this path.
                              type: string
                          required:
                          - actions
                          - path
                          type: object
                        maxItems: 100
                        type: array
                        x-kubernetes-list-map-keys:
                        - path
                        x-kubernetes-list-type: map
                      sshkey:
                        description: sshkey is the overall sshkey MachineConfig definition
                        properties:
                          actions:
                            description: actions represents the series of commands
                              to be executed on changes to the file at the corresponding
                              file path. Actions will be applied in the order that
                              they are set in this list. If there are other incoming
                              changes to other MachineConfig entries in the same update
                              that require a reboot, the reboot will supercede these
                              actions. Valid actions are Reboot, Drain, Reload, DaemonReload
                              and None. The Reboot action and the None action cannot
                              be used in conjunction with any of the other actions.
                              This list supports a maximum of 10 entries.
                            items:
                              properties:
                                reload:
                                  description: reload specifies the service to reload,
                                    only valid if type is reload
                                  properties:
                                    serviceName:
                                      description: serviceName is the full name (e.g.
                                        crio.service) of the service to be reloaded
                                        Service names should be of the format ${NAME}${SERVICETYPE}
                                        and can up to 255 characters long. ${NAME}
                                        must be atleast 1 character long and can only
                                        consist of alphabets, digits, ":", "-", "_",
                                        ".", and "\". ${SERVICETYPE} must be one of
                                        ".service", ".socket", ".device", ".mount",
                                        ".automount", ".swap", ".target", ".path",
                                        ".timer", ".snapshot", ".slice" or ".scope".
                                      maxLength: 255
                                      type: string
                                      x-kubernetes-validations:
                                      - message: Invalid ${SERVICETYPE} in service
                                          name. Expected format is ${NAME}${SERVICETYPE},
                                          where ${SERVICETYPE} must be one of ".service",
                                          ".socket", ".device", ".mount", ".automount",
                                          ".swap", ".target", ".path", ".timer",".snapshot",
                                          ".slice" or ".scope".
                                        rule: self.matches('\\.(service|socket|device|mount|automount|swap|target|path|timer|snapshot|slice|scope)$')
                                      - message: Invalid ${NAME} in service name.
                                          Expected format is ${NAME}${SERVICETYPE},
                                          where {NAME} must be atleast 1 character
                                          long and can only consist of alphabets,
                                          digits, ":", "-", "_", ".", and "\"
                                        rule: self.matches('^[a-zA-Z0-9:._\\\\-]+\\..')
                                  required:
                                  - serviceName
                                  type: object
                                restart:
                                  description: restart specifies the service to restart,
                                    only valid if type is restart
                                  properties:
                                    serviceName:
                                      description: serviceName is the full name (e.g.
                                        crio.service) of the service to be restarted
                                        Service names should be of the format ${NAME}${SERVICETYPE}
                                        and can up to 255 characters long. ${NAME}
                                        must be atleast 1 character long and can only
                                        consist of alphabets, digits, ":", "-", "_",
                                        ".", and "\". ${SERVICETYPE} must be one of
                                        ".service", ".socket", ".device", ".mount",
                                        ".automount", ".swap", ".target", ".path",
                                        ".timer", ".snapshot", ".slice" or ".scope".
                                      maxLength: 255
                                      type: string
                                      x-kubernetes-validations:
                                      - message: Invalid ${SERVICETYPE} in service
                                          name. Expected format is ${NAME}${SERVICETYPE},
                                          where ${SERVICETYPE} must be one of ".service",
                                          ".socket", ".device", ".mount", ".automount",
                                          ".swap", ".target", ".path", ".timer",".snapshot",
                                          ".slice" or ".scope".
                                        rule: self.matches('\\.(service|socket|device|mount|automount|swap|target|path|timer|snapshot|slice|scope)$')
                                      - message: Invalid ${NAME} in service name.
                                          Expected format is ${NAME}${SERVICETYPE},
                                          where {NAME} must be atleast 1 character
                                          long and can only consist of alphabets,
                                          digits, ":", "-", "_", ".", and "\"
                                        rule: self.matches('^[a-zA-Z0-9:._\\\\-]+\\..')
                                  required:
                                  - serviceName
                                  type: object
                                type:
                                  description: type represents the commands that will
                                    be carried out if this NodeDisruptionPolicyStatusActionType
                                    is executed Valid values are Reboot, Drain, Reload,
                                    Restart, DaemonReload, None and Special. reload/restart
                                    requires a corresponding service target specified
                                    in the reload/restart field. Other values require
                                    no further configuration
                                  enum:
                                  - Reboot
                                  - Drain
                                  - Reload
                                  - Restart
                                  - DaemonReload
                                  - None
                                  - Special
                                  type: string
                              required:
                              - type
                              type: object
                              x-kubernetes-validations:
                              - message: reload is required when type is Reload, and
                                  forbidden otherwise
                                rule: 'has(self.type) && self.type == ''Reload'' ?
                                  has(self.reload) : !has(self.reload)'
                              - message: restart is required when type is Restart,
                                  and forbidden otherwise
                                rule: 'has(self.type) && self.type == ''Restart''
                                  ? has(self.restart) : !has(self.restart)'
                            maxItems: 10
                            type: array
                            x-kubernetes-list-type: atomic
                            x-kubernetes-validations:
                            - message: Reboot action can only be specified standalone,
                                as it will override any other actions
                              rule: 'self.exists(x, x.type==''Reboot'') ? size(self)
                                == 1 : true'
                            - message: None action can only be specified standalone,
                                as it will override any other actions
                              rule: 'self.exists(x, x.type==''None'') ? size(self)
                                == 1 : true'
                        required:
                        - actions
                        type: object
                      units:
                        description: units is a list MachineConfig unit definitions
                          and actions to take on changes to those services
                        items:
                          description: NodeDisruptionPolicyStatusUnit is a systemd
                            unit name and corresponding actions to take and is used
                            in the NodeDisruptionPolicyClusterStatus object
                          properties:
                            actions:
                              description: actions represents the series of commands
                                to be executed on changes to the file at the corresponding
                                file path. Actions will be applied in the order that
                                they are set in this list. If there are other incoming
                                changes to other MachineConfig entries in the same
                                update that require a reboot, the reboot will supercede
                                these actions. Valid actions are Reboot, Drain, Reload,
                                DaemonReload and None. The Reboot action and the None
                                action cannot be used in conjunction with any of the
                                other actions. This list supports a maximum of 10
                                entries.
                              items:
                                properties:
                                  reload:
                                    description: reload specifies the service to reload,
                                      only valid if type is reload
                                    properties:
                                      serviceName:
                                        description: serviceName is the full name
                                          (e.g. crio.service) of the service to be
                                          reloaded Service names should be of the
                                          format ${NAME}${SERVICETYPE} and can up
                                          to 255 characters long. ${NAME} must be
                                          atleast 1 character long and can only consist
                                          of alphabets, digits, ":", "-", "_", ".",
                                          and "\". ${SERVICETYPE} must be one of ".service",
                                          ".socket", ".device", ".mount", ".automount",
                                          ".swap", ".target", ".path", ".timer", ".snapshot",
                                          ".slice" or ".scope".
                                        maxLength: 255
                                        type: string
                                        x-kubernetes-validations:
                                        - message: Invalid ${SERVICETYPE} in service
                                            name. Expected format is ${NAME}${SERVICETYPE},
                                            where ${SERVICETYPE} must be one of ".service",
                                            ".socket", ".device", ".mount", ".automount",
                                            ".swap", ".target", ".path", ".timer",".snapshot",
                                            ".slice" or ".scope".
                                          rule: self.matches('\\.(service|socket|device|mount|automount|swap|target|path|timer|snapshot|slice|scope)$')
                                        - message: Invalid ${NAME} in service name.
                                            Expected format is ${NAME}${SERVICETYPE},
                                            where {NAME} must be atleast 1 character
                                            long and can only consist of alphabets,
                                            digits, ":", "-", "_", ".", and "\"
                                          rule: self.matches('^[a-zA-Z0-9:._\\\\-]+\\..')
                                    required:
                                    - serviceName
                                    type: object
                                  restart:
                                    description: restart specifies the service to
                                      restart, only valid if type is restart
                                    properties:
                                      serviceName:
                                        description: serviceName is the full name
                                          (e.g. crio.service) of the service to be
                                          restarted Service names should be of the
                                          format ${NAME}${SERVICETYPE} and can up
                                          to 255 characters long. ${NAME} must be
                                          atleast 1 character long and can only consist
                                          of alphabets, digits, ":", "-", "_", ".",
                                          and "\". ${SERVICETYPE} must be one of ".service",
                                          ".socket", ".device", ".mount", ".automount",
                                          ".swap", ".target", ".path", ".timer", ".snapshot",
                                          ".slice" or ".scope".
                                        maxLength: 255
                                        type: string
                                        x-kubernetes-validations:
                                        - message: Invalid ${SERVICETYPE} in service
                                            name. Expected format is ${NAME}${SERVICETYPE},
                                            where ${SERVICETYPE} must be one of ".service",
                                            ".socket", ".device", ".mount", ".automount",
                                            ".swap", ".target", ".path", ".timer",".snapshot",
                                            ".slice" or ".scope".
                                          rule: self.matches('\\.(service|socket|device|mount|automount|swap|target|path|timer|snapshot|slice|scope)$')
                                        - message: Invalid ${NAME} in service name.
                                            Expected format is ${NAME}${SERVICETYPE},
                                            where {NAME} must be atleast 1 character
                                            long and can only consist of alphabets,
                                            digits, ":", "-", "_", ".", and "\"
                                          rule: self.matches('^[a-zA-Z0-9:._\\\\-]+\\..')
                                    required:
                                    - serviceName
                                    type: object
                                  type:
                                    description: type represents the commands that
                                      will be carried out if this NodeDisruptionPolicyStatusActionType
                                      is executed Valid values are Reboot, Drain,
                                      Reload, Restart, DaemonReload, None and Special.
                                      reload/restart requires a corresponding service
                                      target specified in the reload/restart field.
                                      Other values require no further configuration
                                    enum:
                                    - Reboot
                                    - Drain
                                    - Reload
                                    - Restart
                                    - DaemonReload
                                    - None
                                    - Special
                                    type: string
                                required:
                                - type
                                type: object
                                x-kubernetes-validations:
                                - message: reload is required when type is Reload,
                                    and forbidden otherwise
                                  rule: 'has(self.type) && self.type == ''Reload''
                                    ? has(self.reload) : !has(self.reload)'
                                - message: restart is required when type is Restart,
                                    and forbidden otherwise
                                  rule: 'has(self.type) && self.type == ''Restart''
                                    ? has(self.restart) : !has(self.restart)'
                              maxItems: 10
                              type: array
                              x-kubernetes-list-type: atomic
                              x-kubernetes-validations:
                              - message: Reboot action can only be specified standalone,
                                  as it will override any other actions
                                rule: 'self.exists(x, x.type==''Reboot'') ? size(self)
                                  == 1 : true'
                              - message: None action can only be specified standalone,
                                  as it will override any other actions
                                rule: 'self.exists(x, x.type==''None'') ? size(self)
                                  == 1 : true'
                            name:
                              description: name represents the service name of a systemd
                                service managed through a MachineConfig Actions specified
                                will be applied for changes to the named service.
                                Service names should be of the format ${NAME}${SERVICETYPE}
                                and can up to 255 characters long. ${NAME} must be
                                atleast 1 character long and can only consist of alphabets,
                                digits, ":", "-", "_", ".", and "\". ${SERVICETYPE}
                                must be one of ".service", ".socket", ".device", ".mount",
                                ".automount", ".swap", ".target", ".path", ".timer",
                                ".snapshot", ".slice" or ".scope".
                              maxLength: 255
                              type: string
                              x-kubernetes-validations:
                              - message: Invalid ${SERVICETYPE} in service name. Expected
                                  format is ${NAME}${SERVICETYPE}, where ${SERVICETYPE}
                                  must be one of ".service", ".socket", ".device",
                                  ".mount", ".automount", ".swap", ".target", ".path",
                                  ".timer",".snapshot", ".slice" or ".scope".
                                rule: self.matches('\\.(service|socket|device|mount|automount|swap|target|path|timer|snapshot|slice|scope)$')
                              - message: Invalid ${NAME} in service name. Expected
                                  format is ${NAME}${SERVICETYPE}, where {NAME} must
                                  be atleast 1 character long and can only consist
                                  of alphabets, digits, ":", "-", "_", ".", and "\"
                                rule: self.matches('^[a-zA-Z0-9:._\\\\-]+\\..')
                          required:
                          - actions
                          - name
                          type: object
                        maxItems: 100
                        type: array
                        x-kubernetes-list-map-keys:
                        - name
                        x-kubernetes-list-type: map
                    type: object
                type: object
              observedGeneration:
                description: observedGeneration is the last generation change you've
                  dealt with
                format: int64
                type: integer
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: MachineConfiguration
    listKind: MachineConfigurationList
    plural: machineconfigurations
    singular: machineconfiguration
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:53Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:53Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
