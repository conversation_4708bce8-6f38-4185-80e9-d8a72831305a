---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/497
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:52:34Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-bootstrap
    operation: Update
    time: "2025-06-25T14:52:34Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:52:34Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:api.openshift.io/merged-by-featuregates: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:19:36Z"
  name: clusteroperators.config.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "133445"
  uid: b9c44ac6-a683-401f-8a38-8feb3e6b2031
spec:
  conversion:
    strategy: None
  group: config.openshift.io
  names:
    kind: ClusterOperator
    listKind: ClusterOperatorList
    plural: clusteroperators
    shortNames:
    - co
    singular: clusteroperator
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - description: The version the operator is at.
      jsonPath: .status.versions[?(@.name=="operator")].version
      name: Version
      type: string
    - description: Whether the operator is running and stable.
      jsonPath: .status.conditions[?(@.type=="Available")].status
      name: Available
      type: string
    - description: Whether the operator is processing changes.
      jsonPath: .status.conditions[?(@.type=="Progressing")].status
      name: Progressing
      type: string
    - description: Whether the operator is degraded.
      jsonPath: .status.conditions[?(@.type=="Degraded")].status
      name: Degraded
      type: string
    - description: The time the operator's Available status last changed.
      jsonPath: .status.conditions[?(@.type=="Available")].lastTransitionTime
      name: Since
      type: date
    name: v1
    schema:
      openAPIV3Schema:
        description: "ClusterOperator is the Custom Resource object which holds the
          current state of an operator. This object is used by operators to convey
          their state to the rest of the cluster. \n Compatibility level 1: Stable
          within a major release for a minimum of 12 months or 3 minor releases (whichever
          is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: spec holds configuration that could apply to any operator.
            type: object
          status:
            description: status holds the information about the state of an operator.  It
              is consistent with status information across the Kubernetes ecosystem.
            properties:
              conditions:
                description: conditions describes the state of the operator's managed
                  and monitored components.
                items:
                  description: ClusterOperatorStatusCondition represents the state
                    of the operator's managed and monitored components.
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the time of the last update
                        to the current status property.
                      format: date-time
                      type: string
                    message:
                      description: message provides additional information about the
                        current condition. This is only to be consumed by humans.  It
                        may contain Line Feed characters (U+000A), which should be
                        rendered as new lines.
                      type: string
                    reason:
                      description: reason is the CamelCase reason for the condition's
                        current status.
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      type: string
                    type:
                      description: type specifies the aspect reported by this condition.
                      type: string
                  required:
                  - lastTransitionTime
                  - status
                  - type
                  type: object
                type: array
              extension:
                description: extension contains any additional status information
                  specific to the operator which owns this status object.
                nullable: true
                type: object
                x-kubernetes-preserve-unknown-fields: true
              relatedObjects:
                description: 'relatedObjects is a list of objects that are "interesting"
                  or related to this operator.  Common uses are: 1. the detailed resource
                  driving the operator 2. operator namespaces 3. operand namespaces'
                items:
                  description: ObjectReference contains enough information to let
                    you inspect or modify the referred object.
                  properties:
                    group:
                      description: group of the referent.
                      type: string
                    name:
                      description: name of the referent.
                      type: string
                    namespace:
                      description: namespace of the referent.
                      type: string
                    resource:
                      description: resource of the referent.
                      type: string
                  required:
                  - group
                  - name
                  - resource
                  type: object
                type: array
              versions:
                description: versions is a slice of operator and operand version tuples.  Operators
                  which manage multiple operands will have multiple operand entries
                  in the array.  Available operators must report the version of the
                  operator itself with the name "operator". An operator reports a
                  new "operator" version when it has rolled out the new version to
                  all of its operands.
                items:
                  properties:
                    name:
                      description: name is the name of the particular operand this
                        version is for.  It usually matches container images, not
                        operators.
                      type: string
                    version:
                      description: version indicates which version of a particular
                        operand is currently being managed.  It must always match
                        the Available operand.  If 1.0.0 is Available, then this must
                        indicate 1.0.0 even if the operator is trying to rollout 1.1.0
                      type: string
                  required:
                  - name
                  - version
                  type: object
                type: array
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ClusterOperator
    listKind: ClusterOperatorList
    plural: clusteroperators
    shortNames:
    - co
    singular: clusteroperator
  conditions:
  - lastTransitionTime: "2025-06-25T14:52:34Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:52:34Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
