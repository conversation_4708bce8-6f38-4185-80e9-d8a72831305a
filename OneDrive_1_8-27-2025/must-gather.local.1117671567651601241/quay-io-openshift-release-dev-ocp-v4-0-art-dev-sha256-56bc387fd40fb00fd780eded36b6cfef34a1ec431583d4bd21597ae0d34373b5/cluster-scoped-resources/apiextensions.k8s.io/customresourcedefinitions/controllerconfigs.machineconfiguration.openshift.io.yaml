---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/1453
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    release.openshift.io/feature-set: Default
  creationTimestamp: "2025-06-25T14:59:08Z"
  generation: 5
  labels:
    openshift.io/operator-managed: ""
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:59:08Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:labels:
          .: {}
          f:openshift.io/operator-managed: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
    manager: machine-config-operator
    operation: Update
    time: "2025-06-25T18:58:33Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:api.openshift.io/merged-by-featuregates: {}
          f:release.openshift.io/feature-set: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T12:25:54Z"
  name: controllerconfigs.machineconfiguration.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21359999"
  uid: cb2dd634-ae17-4439-828c-9673afe269ce
spec:
  conversion:
    strategy: None
  group: machineconfiguration.openshift.io
  names:
    kind: ControllerConfig
    listKind: ControllerConfigList
    plural: controllerconfigs
    singular: controllerconfig
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "ControllerConfig describes configuration for MachineConfigController.
          This is currently only used to drive the MachineConfig objects generated
          by the TemplateController. \n Compatibility level 1: Stable within a major
          release for a minimum of 12 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: ControllerConfigSpec is the spec for ControllerConfig resource.
            properties:
              additionalTrustBundle:
                description: additionalTrustBundle is a certificate bundle that will
                  be added to the nodes trusted certificate store.
                format: byte
                nullable: true
                type: string
              baseOSContainerImage:
                description: BaseOSContainerImage is the new-format container image
                  for operating system updates.
                type: string
              baseOSExtensionsContainerImage:
                description: BaseOSExtensionsContainerImage is the matching extensions
                  container for the new-format container
                type: string
              cloudProviderCAData:
                description: cloudProvider specifies the cloud provider CA data
                format: byte
                nullable: true
                type: string
              cloudProviderConfig:
                description: cloudProviderConfig is the configuration for the given
                  cloud provider
                type: string
              clusterDNSIP:
                description: clusterDNSIP is the cluster DNS IP address
                type: string
              dns:
                description: dns holds the cluster dns details
                nullable: true
                properties:
                  apiVersion:
                    description: 'APIVersion defines the versioned schema of this
                      representation of an object. Servers should convert recognized
                      schemas to the latest internal value, and may reject unrecognized
                      values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
                    type: string
                  kind:
                    description: 'Kind is a string value representing the REST resource
                      this object represents. Servers may infer this from the endpoint
                      the client submits requests to. Cannot be updated. In CamelCase.
                      More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
                    type: string
                  metadata:
                    description: 'metadata is the standard object''s metadata. More
                      info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata'
                    type: object
                  spec:
                    description: spec holds user settable values for configuration
                    properties:
                      baseDomain:
                        description: "baseDomain is the base domain of the cluster.
                          All managed DNS records will be sub-domains of this base.
                          \n For example, given the base domain `openshift.example.com`,
                          an API server DNS record may be created for `cluster-api.openshift.example.com`.
                          \n Once set, this field cannot be changed."
                        type: string
                      platform:
                        description: platform holds configuration specific to the
                          underlying infrastructure provider for DNS. When omitted,
                          this means the user has no opinion and the platform is left
                          to choose reasonable defaults. These defaults are subject
                          to change over time.
                        properties:
                          aws:
                            description: aws contains DNS configuration specific to
                              the Amazon Web Services cloud provider.
                            properties:
                              privateZoneIAMRole:
                                description: privateZoneIAMRole contains the ARN of
                                  an IAM role that should be assumed when performing
                                  operations on the cluster's private hosted zone
                                  specified in the cluster DNS config. When left empty,
                                  no role should be assumed.
                                pattern: ^arn:(aws|aws-cn|aws-us-gov):iam::[0-9]{12}:role\/.*$
                                type: string
                            type: object
                          type:
                            description: "type is the underlying infrastructure provider
                              for the cluster. Allowed values: \"\", \"AWS\". \n Individual
                              components may not support all platforms, and must handle
                              unrecognized platforms with best-effort defaults."
                            enum:
                            - ""
                            - AWS
                            - Azure
                            - BareMetal
                            - GCP
                            - Libvirt
                            - OpenStack
                            - None
                            - VSphere
                            - oVirt
                            - IBMCloud
                            - KubeVirt
                            - EquinixMetal
                            - PowerVS
                            - AlibabaCloud
                            - Nutanix
                            - External
                            type: string
                            x-kubernetes-validations:
                            - message: allowed values are '' and 'AWS'
                              rule: self in ['','AWS']
                        required:
                        - type
                        type: object
                        x-kubernetes-validations:
                        - message: aws configuration is required when platform is
                            AWS, and forbidden otherwise
                          rule: 'has(self.type) && self.type == ''AWS'' ?  has(self.aws)
                            : !has(self.aws)'
                      privateZone:
                        description: "privateZone is the location where all the DNS
                          records that are only available internally to the cluster
                          exist. \n If this field is nil, no private records should
                          be created. \n Once set, this field cannot be changed."
                        properties:
                          id:
                            description: "id is the identifier that can be used to
                              find the DNS hosted zone. \n on AWS zone can be fetched
                              using `ID` as id in [1] on Azure zone can be fetched
                              using `ID` as a pre-determined name in [2], on GCP zone
                              can be fetched using `ID` as a pre-determined name in
                              [3]. \n [1]: https://docs.aws.amazon.com/cli/latest/reference/route53/get-hosted-zone.html#options
                              [2]: https://docs.microsoft.com/en-us/cli/azure/network/dns/zone?view=azure-cli-latest#az-network-dns-zone-show
                              [3]: https://cloud.google.com/dns/docs/reference/v1/managedZones/get"
                            type: string
                          tags:
                            additionalProperties:
                              type: string
                            description: "tags can be used to query the DNS hosted
                              zone. \n on AWS, resourcegroupstaggingapi [1] can be
                              used to fetch a zone using `Tags` as tag-filters, \n
                              [1]: https://docs.aws.amazon.com/cli/latest/reference/resourcegroupstaggingapi/get-resources.html#options"
                            type: object
                        type: object
                      publicZone:
                        description: "publicZone is the location where all the DNS
                          records that are publicly accessible to the internet exist.
                          \n If this field is nil, no public records should be created.
                          \n Once set, this field cannot be changed."
                        properties:
                          id:
                            description: "id is the identifier that can be used to
                              find the DNS hosted zone. \n on AWS zone can be fetched
                              using `ID` as id in [1] on Azure zone can be fetched
                              using `ID` as a pre-determined name in [2], on GCP zone
                              can be fetched using `ID` as a pre-determined name in
                              [3]. \n [1]: https://docs.aws.amazon.com/cli/latest/reference/route53/get-hosted-zone.html#options
                              [2]: https://docs.microsoft.com/en-us/cli/azure/network/dns/zone?view=azure-cli-latest#az-network-dns-zone-show
                              [3]: https://cloud.google.com/dns/docs/reference/v1/managedZones/get"
                            type: string
                          tags:
                            additionalProperties:
                              type: string
                            description: "tags can be used to query the DNS hosted
                              zone. \n on AWS, resourcegroupstaggingapi [1] can be
                              used to fetch a zone using `Tags` as tag-filters, \n
                              [1]: https://docs.aws.amazon.com/cli/latest/reference/resourcegroupstaggingapi/get-resources.html#options"
                            type: object
                        type: object
                    type: object
                  status:
                    description: status holds observed values from the cluster. They
                      may not be overridden.
                    type: object
                required:
                - spec
                type: object
                x-kubernetes-embedded-resource: true
              etcdDiscoveryDomain:
                description: etcdDiscoveryDomain is deprecated, use Infra.Status.EtcdDiscoveryDomain
                  instead
                type: string
              imageRegistryBundleData:
                description: imageRegistryBundleData is the ImageRegistryData
                items:
                  description: ImageRegistryBundle contains information for writing
                    image registry certificates
                  properties:
                    data:
                      description: data holds the contents of the bundle that will
                        be written to the file location
                      format: byte
                      type: string
                    file:
                      description: file holds the name of the file where the bundle
                        will be written to disk
                      type: string
                  required:
                  - data
                  - file
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              imageRegistryBundleUserData:
                description: imageRegistryBundleUserData is Image Registry Data provided
                  by the user
                items:
                  description: ImageRegistryBundle contains information for writing
                    image registry certificates
                  properties:
                    data:
                      description: data holds the contents of the bundle that will
                        be written to the file location
                      format: byte
                      type: string
                    file:
                      description: file holds the name of the file where the bundle
                        will be written to disk
                      type: string
                  required:
                  - data
                  - file
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              images:
                additionalProperties:
                  type: string
                description: images is map of images that are used by the controller
                  to render templates under ./templates/
                type: object
              infra:
                description: infra holds the infrastructure details
                nullable: true
                properties:
                  apiVersion:
                    description: 'APIVersion defines the versioned schema of this
                      representation of an object. Servers should convert recognized
                      schemas to the latest internal value, and may reject unrecognized
                      values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
                    type: string
                  kind:
                    description: 'Kind is a string value representing the REST resource
                      this object represents. Servers may infer this from the endpoint
                      the client submits requests to. Cannot be updated. In CamelCase.
                      More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
                    type: string
                  metadata:
                    description: 'metadata is the standard object''s metadata. More
                      info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata'
                    type: object
                  spec:
                    description: spec holds user settable values for configuration
                    properties:
                      cloudConfig:
                        description: "cloudConfig is a reference to a ConfigMap containing
                          the cloud provider configuration file. This configuration
                          file is used to configure the Kubernetes cloud provider
                          integration when using the built-in cloud provider integration
                          or the external cloud controller manager. The namespace
                          for this config map is openshift-config. \n cloudConfig
                          should only be consumed by the kube_cloud_config controller.
                          The controller is responsible for using the user configuration
                          in the spec for various platforms and combining that with
                          the user provided ConfigMap in this field to create a stitched
                          kube cloud config. The controller generates a ConfigMap
                          `kube-cloud-config` in `openshift-config-managed` namespace
                          with the kube cloud config is stored in `cloud.conf` key.
                          All the clients are expected to use the generated ConfigMap
                          only."
                        properties:
                          key:
                            description: Key allows pointing to a specific key/value
                              inside of the configmap.  This is useful for logical
                              file references.
                            type: string
                          name:
                            type: string
                        type: object
                      platformSpec:
                        description: platformSpec holds desired information specific
                          to the underlying infrastructure provider.
                        properties:
                          alibabaCloud:
                            description: AlibabaCloud contains settings specific to
                              the Alibaba Cloud infrastructure provider.
                            type: object
                          aws:
                            description: AWS contains settings specific to the Amazon
                              Web Services infrastructure provider.
                            properties:
                              serviceEndpoints:
                                description: serviceEndpoints list contains custom
                                  endpoints which will override default service endpoint
                                  of AWS Services. There must be only one ServiceEndpoint
                                  for a service.
                                items:
                                  description: AWSServiceEndpoint store the configuration
                                    of a custom url to override existing defaults
                                    of AWS Services.
                                  properties:
                                    name:
                                      description: name is the name of the AWS service.
                                        The list of all the service names can be found
                                        at https://docs.aws.amazon.com/general/latest/gr/aws-service-information.html
                                        This must be provided and cannot be empty.
                                      pattern: ^[a-z0-9-]+$
                                      type: string
                                    url:
                                      description: url is fully qualified URI with
                                        scheme https, that overrides the default generated
                                        endpoint for a client. This must be provided
                                        and cannot be empty.
                                      pattern: ^https://
                                      type: string
                                  type: object
                                type: array
                                x-kubernetes-list-type: atomic
                            type: object
                          azure:
                            description: Azure contains settings specific to the Azure
                              infrastructure provider.
                            type: object
                          baremetal:
                            description: BareMetal contains settings specific to the
                              BareMetal platform.
                            properties:
                              apiServerInternalIPs:
                                description: apiServerInternalIPs are the IP addresses
                                  to contact the Kubernetes API server that can be
                                  used by components inside the cluster, like kubelets
                                  using the infrastructure rather than Kubernetes
                                  networking. These are the IPs for a self-hosted
                                  load balancer in front of the API servers. In dual
                                  stack clusters this list contains two IP addresses,
                                  one from IPv4 family and one from IPv6. In single
                                  stack clusters a single IP address is expected.
                                  When omitted, values from the status.apiServerInternalIPs
                                  will be used. Once set, the list cannot be completely
                                  removed (but its second entry can).
                                items:
                                  description: IP is an IP address (for example, "10.0.0.0"
                                    or "fd00::").
                                  maxLength: 39
                                  minLength: 1
                                  type: string
                                  x-kubernetes-validations:
                                  - message: value must be a valid IP address
                                    rule: isIP(self)
                                maxItems: 2
                                type: array
                                x-kubernetes-list-type: atomic
                                x-kubernetes-validations:
                                - message: apiServerInternalIPs must contain at most
                                    one IPv4 address and at most one IPv6 address
                                  rule: 'size(self) == 2 && isIP(self[0]) && isIP(self[1])
                                    ? ip(self[0]).family() != ip(self[1]).family()
                                    : true'
                              ingressIPs:
                                description: ingressIPs are the external IPs which
                                  route to the default ingress controller. The IPs
                                  are suitable targets of a wildcard DNS record used
                                  to resolve default route host names. In dual stack
                                  clusters this list contains two IP addresses, one
                                  from IPv4 family and one from IPv6. In single stack
                                  clusters a single IP address is expected. When omitted,
                                  values from the status.ingressIPs will be used.
                                  Once set, the list cannot be completely removed
                                  (but its second entry can).
                                items:
                                  description: IP is an IP address (for example, "10.0.0.0"
                                    or "fd00::").
                                  maxLength: 39
                                  minLength: 1
                                  type: string
                                  x-kubernetes-validations:
                                  - message: value must be a valid IP address
                                    rule: isIP(self)
                                maxItems: 2
                                type: array
                                x-kubernetes-list-type: atomic
                                x-kubernetes-validations:
                                - message: ingressIPs must contain at most one IPv4
                                    address and at most one IPv6 address
                                  rule: 'size(self) == 2 && isIP(self[0]) && isIP(self[1])
                                    ? ip(self[0]).family() != ip(self[1]).family()
                                    : true'
                              machineNetworks:
                                description: machineNetworks are IP networks used
                                  to connect all the OpenShift cluster nodes. Each
                                  network is provided in the CIDR format and should
                                  be IPv4 or IPv6, for example "10.0.0.0/8" or "fd00::/8".
                                items:
                                  description: CIDR is an IP address range in CIDR
                                    notation (for example, "10.0.0.0/8" or "fd00::/8").
                                  maxLength: 43
                                  minLength: 1
                                  type: string
                                  x-kubernetes-validations:
                                  - message: value must be a valid CIDR network address
                                    rule: isCIDR(self)
                                maxItems: 32
                                type: array
                                x-kubernetes-list-type: atomic
                                x-kubernetes-validations:
                                - rule: self.all(x, self.exists_one(y, x == y))
                            type: object
                            x-kubernetes-validations:
                            - message: apiServerInternalIPs list is required once
                                set
                              rule: '!has(oldSelf.apiServerInternalIPs) || has(self.apiServerInternalIPs)'
                            - message: ingressIPs list is required once set
                              rule: '!has(oldSelf.ingressIPs) || has(self.ingressIPs)'
                          equinixMetal:
                            description: EquinixMetal contains settings specific to
                              the Equinix Metal infrastructure provider.
                            type: object
                          external:
                            description: ExternalPlatformType represents generic infrastructure
                              provider. Platform-specific components should be supplemented
                              separately.
                            properties:
                              platformName:
                                default: Unknown
                                description: PlatformName holds the arbitrary string
                                  representing the infrastructure provider name, expected
                                  to be set at the installation time. This field is
                                  solely for informational and reporting purposes
                                  and is not expected to be used for decision-making.
                                type: string
                                x-kubernetes-validations:
                                - message: platform name cannot be changed once set
                                  rule: oldSelf == 'Unknown' || self == oldSelf
                            type: object
                          gcp:
                            description: GCP contains settings specific to the Google
                              Cloud Platform infrastructure provider.
                            type: object
                          ibmcloud:
                            description: IBMCloud contains settings specific to the
                              IBMCloud infrastructure provider.
                            type: object
                          kubevirt:
                            description: Kubevirt contains settings specific to the
                              kubevirt infrastructure provider.
                            type: object
                          nutanix:
                            description: Nutanix contains settings specific to the
                              Nutanix infrastructure provider.
                            properties:
                              failureDomains:
                                description: failureDomains configures failure domains
                                  information for the Nutanix platform. When set,
                                  the failure domains defined here may be used to
                                  spread Machines across prism element clusters to
                                  improve fault tolerance of the cluster.
                                items:
                                  description: NutanixFailureDomain configures failure
                                    domain information for the Nutanix platform.
                                  properties:
                                    cluster:
                                      description: cluster is to identify the cluster
                                        (the Prism Element under management of the
                                        Prism Central), in which the Machine's VM
                                        will be created. The cluster identifier (uuid
                                        or name) can be obtained from the Prism Central
                                        console or using the prism_central API.
                                      properties:
                                        name:
                                          description: name is the resource name in
                                            the PC. It cannot be empty if the type
                                            is Name.
                                          type: string
                                        type:
                                          description: type is the identifier type
                                            to use for this resource.
                                          enum:
                                          - UUID
                                          - Name
                                          type: string
                                        uuid:
                                          description: uuid is the UUID of the resource
                                            in the PC. It cannot be empty if the type
                                            is UUID.
                                          type: string
                                      required:
                                      - type
                                      type: object
                                      x-kubernetes-validations:
                                      - message: uuid configuration is required when
                                          type is UUID, and forbidden otherwise
                                        rule: 'has(self.type) && self.type == ''UUID''
                                          ?  has(self.uuid) : !has(self.uuid)'
                                      - message: name configuration is required when
                                          type is Name, and forbidden otherwise
                                        rule: 'has(self.type) && self.type == ''Name''
                                          ?  has(self.name) : !has(self.name)'
                                    name:
                                      description: name defines the unique name of
                                        a failure domain. Name is required and must
                                        be at most 64 characters in length. It must
                                        consist of only lower case alphanumeric characters
                                        and hyphens (-). It must start and end with
                                        an alphanumeric character. This value is arbitrary
                                        and is used to identify the failure domain
                                        within the platform.
                                      maxLength: 64
                                      minLength: 1
                                      pattern: '[a-z0-9]([-a-z0-9]*[a-z0-9])?'
                                      type: string
                                    subnets:
                                      description: subnets holds a list of identifiers
                                        (one or more) of the cluster's network subnets
                                        for the Machine's VM to connect to. The subnet
                                        identifiers (uuid or name) can be obtained
                                        from the Prism Central console or using the
                                        prism_central API.
                                      items:
                                        description: NutanixResourceIdentifier holds
                                          the identity of a Nutanix PC resource (cluster,
                                          image, subnet, etc.)
                                        properties:
                                          name:
                                            description: name is the resource name
                                              in the PC. It cannot be empty if the
                                              type is Name.
                                            type: string
                                          type:
                                            description: type is the identifier type
                                              to use for this resource.
                                            enum:
                                            - UUID
                                            - Name
                                            type: string
                                          uuid:
                                            description: uuid is the UUID of the resource
                                              in the PC. It cannot be empty if the
                                              type is UUID.
                                            type: string
                                        required:
                                        - type
                                        type: object
                                        x-kubernetes-validations:
                                        - message: uuid configuration is required
                                            when type is UUID, and forbidden otherwise
                                          rule: 'has(self.type) && self.type == ''UUID''
                                            ?  has(self.uuid) : !has(self.uuid)'
                                        - message: name configuration is required
                                            when type is Name, and forbidden otherwise
                                          rule: 'has(self.type) && self.type == ''Name''
                                            ?  has(self.name) : !has(self.name)'
                                      maxItems: 1
                                      minItems: 1
                                      type: array
                                      x-kubernetes-list-map-keys:
                                      - type
                                      x-kubernetes-list-type: map
                                  required:
                                  - cluster
                                  - name
                                  - subnets
                                  type: object
                                type: array
                                x-kubernetes-list-map-keys:
                                - name
                                x-kubernetes-list-type: map
                              prismCentral:
                                description: prismCentral holds the endpoint address
                                  and port to access the Nutanix Prism Central. When
                                  a cluster-wide proxy is installed, by default, this
                                  endpoint will be accessed via the proxy. Should
                                  you wish for communication with this endpoint not
                                  to be proxied, please add the endpoint to the proxy
                                  spec.noProxy list.
                                properties:
                                  address:
                                    description: address is the endpoint address (DNS
                                      name or IP address) of the Nutanix Prism Central
                                      or Element (cluster)
                                    maxLength: 256
                                    type: string
                                  port:
                                    description: port is the port number to access
                                      the Nutanix Prism Central or Element (cluster)
                                    format: int32
                                    maximum: 65535
                                    minimum: 1
                                    type: integer
                                required:
                                - address
                                - port
                                type: object
                              prismElements:
                                description: prismElements holds one or more endpoint
                                  address and port data to access the Nutanix Prism
                                  Elements (clusters) of the Nutanix Prism Central.
                                  Currently we only support one Prism Element (cluster)
                                  for an OpenShift cluster, where all the Nutanix
                                  resources (VMs, subnets, volumes, etc.) used in
                                  the OpenShift cluster are located. In the future,
                                  we may support Nutanix resources (VMs, etc.) spread
                                  over multiple Prism Elements (clusters) of the Prism
                                  Central.
                                items:
                                  description: NutanixPrismElementEndpoint holds the
                                    name and endpoint data for a Prism Element (cluster)
                                  properties:
                                    endpoint:
                                      description: endpoint holds the endpoint address
                                        and port data of the Prism Element (cluster).
                                        When a cluster-wide proxy is installed, by
                                        default, this endpoint will be accessed via
                                        the proxy. Should you wish for communication
                                        with this endpoint not to be proxied, please
                                        add the endpoint to the proxy spec.noProxy
                                        list.
                                      properties:
                                        address:
                                          description: address is the endpoint address
                                            (DNS name or IP address) of the Nutanix
                                            Prism Central or Element (cluster)
                                          maxLength: 256
                                          type: string
                                        port:
                                          description: port is the port number to
                                            access the Nutanix Prism Central or Element
                                            (cluster)
                                          format: int32
                                          maximum: 65535
                                          minimum: 1
                                          type: integer
                                      required:
                                      - address
                                      - port
                                      type: object
                                    name:
                                      description: name is the name of the Prism Element
                                        (cluster). This value will correspond with
                                        the cluster field configured on other resources
                                        (eg Machines, PVCs, etc).
                                      maxLength: 256
                                      type: string
                                  required:
                                  - endpoint
                                  - name
                                  type: object
                                type: array
                                x-kubernetes-list-map-keys:
                                - name
                                x-kubernetes-list-type: map
                            required:
                            - prismCentral
                            - prismElements
                            type: object
                          openstack:
                            description: OpenStack contains settings specific to the
                              OpenStack infrastructure provider.
                            properties:
                              apiServerInternalIPs:
                                description: apiServerInternalIPs are the IP addresses
                                  to contact the Kubernetes API server that can be
                                  used by components inside the cluster, like kubelets
                                  using the infrastructure rather than Kubernetes
                                  networking. These are the IPs for a self-hosted
                                  load balancer in front of the API servers. In dual
                                  stack clusters this list contains two IP addresses,
                                  one from IPv4 family and one from IPv6. In single
                                  stack clusters a single IP address is expected.
                                  When omitted, values from the status.apiServerInternalIPs
                                  will be used. Once set, the list cannot be completely
                                  removed (but its second entry can).
                                items:
                                  description: IP is an IP address (for example, "10.0.0.0"
                                    or "fd00::").
                                  maxLength: 39
                                  minLength: 1
                                  type: string
                                  x-kubernetes-validations:
                                  - message: value must be a valid IP address
                                    rule: isIP(self)
                                maxItems: 2
                                type: array
                                x-kubernetes-list-type: atomic
                                x-kubernetes-validations:
                                - message: apiServerInternalIPs must contain at most
                                    one IPv4 address and at most one IPv6 address
                                  rule: 'size(self) == 2 && isIP(self[0]) && isIP(self[1])
                                    ? ip(self[0]).family() != ip(self[1]).family()
                                    : true'
                              ingressIPs:
                                description: ingressIPs are the external IPs which
                                  route to the default ingress controller. The IPs
                                  are suitable targets of a wildcard DNS record used
                                  to resolve default route host names. In dual stack
                                  clusters this list contains two IP addresses, one
                                  from IPv4 family and one from IPv6. In single stack
                                  clusters a single IP address is expected. When omitted,
                                  values from the status.ingressIPs will be used.
                                  Once set, the list cannot be completely removed
                                  (but its second entry can).
                                items:
                                  description: IP is an IP address (for example, "10.0.0.0"
                                    or "fd00::").
                                  maxLength: 39
                                  minLength: 1
                                  type: string
                                  x-kubernetes-validations:
                                  - message: value must be a valid IP address
                                    rule: isIP(self)
                                maxItems: 2
                                type: array
                                x-kubernetes-list-type: atomic
                                x-kubernetes-validations:
                                - message: ingressIPs must contain at most one IPv4
                                    address and at most one IPv6 address
                                  rule: 'size(self) == 2 && isIP(self[0]) && isIP(self[1])
                                    ? ip(self[0]).family() != ip(self[1]).family()
                                    : true'
                              machineNetworks:
                                description: machineNetworks are IP networks used
                                  to connect all the OpenShift cluster nodes. Each
                                  network is provided in the CIDR format and should
                                  be IPv4 or IPv6, for example "10.0.0.0/8" or "fd00::/8".
                                items:
                                  description: CIDR is an IP address range in CIDR
                                    notation (for example, "10.0.0.0/8" or "fd00::/8").
                                  maxLength: 43
                                  minLength: 1
                                  type: string
                                  x-kubernetes-validations:
                                  - message: value must be a valid CIDR network address
                                    rule: isCIDR(self)
                                maxItems: 32
                                type: array
                                x-kubernetes-list-type: atomic
                                x-kubernetes-validations:
                                - rule: self.all(x, self.exists_one(y, x == y))
                            type: object
                            x-kubernetes-validations:
                            - message: apiServerInternalIPs list is required once
                                set
                              rule: '!has(oldSelf.apiServerInternalIPs) || has(self.apiServerInternalIPs)'
                            - message: ingressIPs list is required once set
                              rule: '!has(oldSelf.ingressIPs) || has(self.ingressIPs)'
                          ovirt:
                            description: Ovirt contains settings specific to the oVirt
                              infrastructure provider.
                            type: object
                          powervs:
                            description: PowerVS contains settings specific to the
                              IBM Power Systems Virtual Servers infrastructure provider.
                            properties:
                              serviceEndpoints:
                                description: serviceEndpoints is a list of custom
                                  endpoints which will override the default service
                                  endpoints of a Power VS service.
                                items:
                                  description: PowervsServiceEndpoint stores the configuration
                                    of a custom url to override existing defaults
                                    of PowerVS Services.
                                  properties:
                                    name:
                                      description: name is the name of the Power VS
                                        service. Few of the services are IAM - https://cloud.ibm.com/apidocs/iam-identity-token-api
                                        ResourceController - https://cloud.ibm.com/apidocs/resource-controller/resource-controller
                                        Power Cloud - https://cloud.ibm.com/apidocs/power-cloud
                                      pattern: ^[a-z0-9-]+$
                                      type: string
                                    url:
                                      description: url is fully qualified URI with
                                        scheme https, that overrides the default generated
                                        endpoint for a client. This must be provided
                                        and cannot be empty.
                                      format: uri
                                      pattern: ^https://
                                      type: string
                                  required:
                                  - name
                                  - url
                                  type: object
                                type: array
                                x-kubernetes-list-map-keys:
                                - name
                                x-kubernetes-list-type: map
                            type: object
                          type:
                            description: type is the underlying infrastructure provider
                              for the cluster. This value controls whether infrastructure
                              automation such as service load balancers, dynamic volume
                              provisioning, machine creation and deletion, and other
                              integrations are enabled. If None, no infrastructure
                              automation is enabled. Allowed values are "AWS", "Azure",
                              "BareMetal", "GCP", "Libvirt", "OpenStack", "VSphere",
                              "oVirt", "KubeVirt", "EquinixMetal", "PowerVS", "AlibabaCloud",
                              "Nutanix" and "None". Individual components may not
                              support all platforms, and must handle unrecognized
                              platforms as None if they do not support that platform.
                            enum:
                            - ""
                            - AWS
                            - Azure
                            - BareMetal
                            - GCP
                            - Libvirt
                            - OpenStack
                            - None
                            - VSphere
                            - oVirt
                            - IBMCloud
                            - KubeVirt
                            - EquinixMetal
                            - PowerVS
                            - AlibabaCloud
                            - Nutanix
                            - External
                            type: string
                          vsphere:
                            description: VSphere contains settings specific to the
                              VSphere infrastructure provider.
                            properties:
                              apiServerInternalIPs:
                                description: apiServerInternalIPs are the IP addresses
                                  to contact the Kubernetes API server that can be
                                  used by components inside the cluster, like kubelets
                                  using the infrastructure rather than Kubernetes
                                  networking. These are the IPs for a self-hosted
                                  load balancer in front of the API servers. In dual
                                  stack clusters this list contains two IP addresses,
                                  one from IPv4 family and one from IPv6. In single
                                  stack clusters a single IP address is expected.
                                  When omitted, values from the status.apiServerInternalIPs
                                  will be used. Once set, the list cannot be completely
                                  removed (but its second entry can).
                                items:
                                  description: IP is an IP address (for example, "10.0.0.0"
                                    or "fd00::").
                                  maxLength: 39
                                  minLength: 1
                                  type: string
                                  x-kubernetes-validations:
                                  - message: value must be a valid IP address
                                    rule: isIP(self)
                                maxItems: 2
                                type: array
                                x-kubernetes-list-type: atomic
                                x-kubernetes-validations:
                                - message: apiServerInternalIPs must contain at most
                                    one IPv4 address and at most one IPv6 address
                                  rule: 'size(self) == 2 && isIP(self[0]) && isIP(self[1])
                                    ? ip(self[0]).family() != ip(self[1]).family()
                                    : true'
                              failureDomains:
                                description: failureDomains contains the definition
                                  of region, zone and the vCenter topology. If this
                                  is omitted failure domains (regions and zones) will
                                  not be used.
                                items:
                                  description: VSpherePlatformFailureDomainSpec holds
                                    the region and zone failure domain and the vCenter
                                    topology of that failure domain.
                                  properties:
                                    name:
                                      description: name defines the arbitrary but
                                        unique name of a failure domain.
                                      maxLength: 256
                                      minLength: 1
                                      type: string
                                    region:
                                      description: region defines the name of a region
                                        tag that will be attached to a vCenter datacenter.
                                        The tag category in vCenter must be named
                                        openshift-region.
                                      maxLength: 80
                                      minLength: 1
                                      type: string
                                    server:
                                      description: server is the fully-qualified domain
                                        name or the IP address of the vCenter server.
                                        ---
                                      maxLength: 255
                                      minLength: 1
                                      type: string
                                    topology:
                                      description: Topology describes a given failure
                                        domain using vSphere constructs
                                      properties:
                                        computeCluster:
                                          description: computeCluster the absolute
                                            path of the vCenter cluster in which virtual
                                            machine will be located. The absolute
                                            path is of the form /<datacenter>/host/<cluster>.
                                            The maximum length of the path is 2048
                                            characters.
                                          maxLength: 2048
                                          pattern: ^/.*?/host/.*?
                                          type: string
                                        datacenter:
                                          description: datacenter is the name of vCenter
                                            datacenter in which virtual machines will
                                            be located. The maximum length of the
                                            datacenter name is 80 characters.
                                          maxLength: 80
                                          type: string
                                        datastore:
                                          description: datastore is the absolute path
                                            of the datastore in which the virtual
                                            machine is located. The absolute path
                                            is of the form /<datacenter>/datastore/<datastore>
                                            The maximum length of the path is 2048
                                            characters.
                                          maxLength: 2048
                                          pattern: ^/.*?/datastore/.*?
                                          type: string
                                        folder:
                                          description: folder is the absolute path
                                            of the folder where virtual machines are
                                            located. The absolute path is of the form
                                            /<datacenter>/vm/<folder>. The maximum
                                            length of the path is 2048 characters.
                                          maxLength: 2048
                                          pattern: ^/.*?/vm/.*?
                                          type: string
                                        networks:
                                          description: networks is the list of port
                                            group network names within this failure
                                            domain. Currently, we only support a single
                                            interface per RHCOS virtual machine. The
                                            available networks (port groups) can be
                                            listed using `govc ls 'network/*'` The
                                            single interface should be the absolute
                                            path of the form /<datacenter>/network/<portgroup>.
                                          items:
                                            type: string
                                          maxItems: 1
                                          minItems: 1
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        resourcePool:
                                          description: resourcePool is the absolute
                                            path of the resource pool where virtual
                                            machines will be created. The absolute
                                            path is of the form /<datacenter>/host/<cluster>/Resources/<resourcepool>.
                                            The maximum length of the path is 2048
                                            characters.
                                          maxLength: 2048
                                          pattern: ^/.*?/host/.*?/Resources.*
                                          type: string
                                        template:
                                          description: "template is the full inventory
                                            path of the virtual machine or template
                                            that will be cloned when creating new
                                            machines in this failure domain. The maximum
                                            length of the path is 2048 characters.
                                            \n When omitted, the template will be
                                            calculated by the control plane machineset
                                            operator based on the region and zone
                                            defined in VSpherePlatformFailureDomainSpec.
                                            For example, for zone=zonea, region=region1,
                                            and infrastructure name=test, the template
                                            path would be calculated as /<datacenter>/vm/test-rhcos-region1-zonea."
                                          maxLength: 2048
                                          minLength: 1
                                          pattern: ^/.*?/vm/.*?
                                          type: string
                                      required:
                                      - computeCluster
                                      - datacenter
                                      - datastore
                                      - networks
                                      type: object
                                    zone:
                                      description: zone defines the name of a zone
                                        tag that will be attached to a vCenter cluster.
                                        The tag category in vCenter must be named
                                        openshift-zone.
                                      maxLength: 80
                                      minLength: 1
                                      type: string
                                  required:
                                  - name
                                  - region
                                  - server
                                  - topology
                                  - zone
                                  type: object
                                type: array
                                x-kubernetes-list-map-keys:
                                - name
                                x-kubernetes-list-type: map
                              ingressIPs:
                                description: ingressIPs are the external IPs which
                                  route to the default ingress controller. The IPs
                                  are suitable targets of a wildcard DNS record used
                                  to resolve default route host names. In dual stack
                                  clusters this list contains two IP addresses, one
                                  from IPv4 family and one from IPv6. In single stack
                                  clusters a single IP address is expected. When omitted,
                                  values from the status.ingressIPs will be used.
                                  Once set, the list cannot be completely removed
                                  (but its second entry can).
                                items:
                                  description: IP is an IP address (for example, "10.0.0.0"
                                    or "fd00::").
                                  maxLength: 39
                                  minLength: 1
                                  type: string
                                  x-kubernetes-validations:
                                  - message: value must be a valid IP address
                                    rule: isIP(self)
                                maxItems: 2
                                type: array
                                x-kubernetes-list-type: atomic
                                x-kubernetes-validations:
                                - message: ingressIPs must contain at most one IPv4
                                    address and at most one IPv6 address
                                  rule: 'size(self) == 2 && isIP(self[0]) && isIP(self[1])
                                    ? ip(self[0]).family() != ip(self[1]).family()
                                    : true'
                              machineNetworks:
                                description: machineNetworks are IP networks used
                                  to connect all the OpenShift cluster nodes. Each
                                  network is provided in the CIDR format and should
                                  be IPv4 or IPv6, for example "10.0.0.0/8" or "fd00::/8".
                                items:
                                  description: CIDR is an IP address range in CIDR
                                    notation (for example, "10.0.0.0/8" or "fd00::/8").
                                  maxLength: 43
                                  minLength: 1
                                  type: string
                                  x-kubernetes-validations:
                                  - message: value must be a valid CIDR network address
                                    rule: isCIDR(self)
                                maxItems: 32
                                type: array
                                x-kubernetes-list-type: atomic
                                x-kubernetes-validations:
                                - rule: self.all(x, self.exists_one(y, x == y))
                              nodeNetworking:
                                description: nodeNetworking contains the definition
                                  of internal and external network constraints for
                                  assigning the node's networking. If this field is
                                  omitted, networking defaults to the legacy address
                                  selection behavior which is to only support a single
                                  address and return the first one found.
                                properties:
                                  external:
                                    description: external represents the network configuration
                                      of the node that is externally routable.
                                    properties:
                                      excludeNetworkSubnetCidr:
                                        description: excludeNetworkSubnetCidr IP addresses
                                          in subnet ranges will be excluded when selecting
                                          the IP address from the VirtualMachine's
                                          VM for use in the status.addresses fields.
                                          ---
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                      network:
                                        description: network VirtualMachine's VM Network
                                          names that will be used to when searching
                                          for status.addresses fields. Note that if
                                          internal.networkSubnetCIDR and external.networkSubnetCIDR
                                          are not set, then the vNIC associated to
                                          this network must only have a single IP
                                          address assigned to it. The available networks
                                          (port groups) can be listed using `govc
                                          ls 'network/*'`
                                        type: string
                                      networkSubnetCidr:
                                        description: networkSubnetCidr IP address
                                          on VirtualMachine's network interfaces included
                                          in the fields' CIDRs that will be used in
                                          respective status.addresses fields. ---
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: set
                                    type: object
                                  internal:
                                    description: internal represents the network configuration
                                      of the node that is routable only within the
                                      cluster.
                                    properties:
                                      excludeNetworkSubnetCidr:
                                        description: excludeNetworkSubnetCidr IP addresses
                                          in subnet ranges will be excluded when selecting
                                          the IP address from the VirtualMachine's
                                          VM for use in the status.addresses fields.
                                          ---
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                      network:
                                        description: network VirtualMachine's VM Network
                                          names that will be used to when searching
                                          for status.addresses fields. Note that if
                                          internal.networkSubnetCIDR and external.networkSubnetCIDR
                                          are not set, then the vNIC associated to
                                          this network must only have a single IP
                                          address assigned to it. The available networks
                                          (port groups) can be listed using `govc
                                          ls 'network/*'`
                                        type: string
                                      networkSubnetCidr:
                                        description: networkSubnetCidr IP address
                                          on VirtualMachine's network interfaces included
                                          in the fields' CIDRs that will be used in
                                          respective status.addresses fields. ---
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: set
                                    type: object
                                type: object
                              vcenters:
                                description: vcenters holds the connection details
                                  for services to communicate with vCenter. Currently,
                                  only a single vCenter is supported. ---
                                items:
                                  description: VSpherePlatformVCenterSpec stores the
                                    vCenter connection fields. This is used by the
                                    vSphere CCM.
                                  properties:
                                    datacenters:
                                      description: The vCenter Datacenters in which
                                        the RHCOS vm guests are located. This field
                                        will be used by the Cloud Controller Manager.
                                        Each datacenter listed here should be used
                                        within a topology.
                                      items:
                                        type: string
                                      minItems: 1
                                      type: array
                                      x-kubernetes-list-type: set
                                    port:
                                      description: port is the TCP port that will
                                        be used to communicate to the vCenter endpoint.
                                        When omitted, this means the user has no opinion
                                        and it is up to the platform to choose a sensible
                                        default, which is subject to change over time.
                                      format: int32
                                      maximum: 32767
                                      minimum: 1
                                      type: integer
                                    server:
                                      description: server is the fully-qualified domain
                                        name or the IP address of the vCenter server.
                                        ---
                                      maxLength: 255
                                      type: string
                                  required:
                                  - datacenters
                                  - server
                                  type: object
                                maxItems: 1
                                minItems: 0
                                type: array
                                x-kubernetes-list-type: atomic
                            type: object
                            x-kubernetes-validations:
                            - message: apiServerInternalIPs list is required once
                                set
                              rule: '!has(oldSelf.apiServerInternalIPs) || has(self.apiServerInternalIPs)'
                            - message: ingressIPs list is required once set
                              rule: '!has(oldSelf.ingressIPs) || has(self.ingressIPs)'
                        type: object
                    type: object
                  status:
                    description: status holds observed values from the cluster. They
                      may not be overridden.
                    properties:
                      apiServerInternalURI:
                        description: apiServerInternalURL is a valid URI with scheme
                          'https', address and optionally a port (defaulting to 443).  apiServerInternalURL
                          can be used by components like kubelets, to contact the
                          Kubernetes API server using the infrastructure provider
                          rather than Kubernetes networking.
                        type: string
                      apiServerURL:
                        description: apiServerURL is a valid URI with scheme 'https',
                          address and optionally a port (defaulting to 443).  apiServerURL
                          can be used by components like the web console to tell users
                          where to find the Kubernetes API.
                        type: string
                      controlPlaneTopology:
                        default: HighlyAvailable
                        description: controlPlaneTopology expresses the expectations
                          for operands that normally run on control nodes. The default
                          is 'HighlyAvailable', which represents the behavior operators
                          have in a "normal" cluster. The 'SingleReplica' mode will
                          be used in single-node deployments and the operators should
                          not configure the operand for highly-available operation
                          The 'External' mode indicates that the control plane is
                          hosted externally to the cluster and that its components
                          are not visible within the cluster.
                        enum:
                        - HighlyAvailable
                        - SingleReplica
                        - External
                        type: string
                      cpuPartitioning:
                        default: None
                        description: cpuPartitioning expresses if CPU partitioning
                          is a currently enabled feature in the cluster. CPU Partitioning
                          means that this cluster can support partitioning workloads
                          to specific CPU Sets. Valid values are "None" and "AllNodes".
                          When omitted, the default value is "None". The default value
                          of "None" indicates that no nodes will be setup with CPU
                          partitioning. The "AllNodes" value indicates that all nodes
                          have been setup with CPU partitioning, and can then be further
                          configured via the PerformanceProfile API.
                        enum:
                        - None
                        - AllNodes
                        type: string
                      etcdDiscoveryDomain:
                        description: 'etcdDiscoveryDomain is the domain used to fetch
                          the SRV records for discovering etcd servers and clients.
                          For more info: https://github.com/etcd-io/etcd/blob/329be66e8b3f9e2e6af83c123ff89297e49ebd15/Documentation/op-guide/clustering.md#dns-discovery
                          deprecated: as of 4.7, this field is no longer set or honored.  It
                          will be removed in a future release.'
                        type: string
                      infrastructureName:
                        description: infrastructureName uniquely identifies a cluster
                          with a human friendly name. Once set it should not be changed.
                          Must be of max length 27 and must have only alphanumeric
                          or hyphen characters.
                        type: string
                      infrastructureTopology:
                        default: HighlyAvailable
                        description: 'infrastructureTopology expresses the expectations
                          for infrastructure services that do not run on control plane
                          nodes, usually indicated by a node selector for a `role`
                          value other than `master`. The default is ''HighlyAvailable'',
                          which represents the behavior operators have in a "normal"
                          cluster. The ''SingleReplica'' mode will be used in single-node
                          deployments and the operators should not configure the operand
                          for highly-available operation NOTE: External topology mode
                          is not applicable for this field.'
                        enum:
                        - HighlyAvailable
                        - SingleReplica
                        type: string
                      platform:
                        description: "platform is the underlying infrastructure provider
                          for the cluster. \n Deprecated: Use platformStatus.type
                          instead."
                        enum:
                        - ""
                        - AWS
                        - Azure
                        - BareMetal
                        - GCP
                        - Libvirt
                        - OpenStack
                        - None
                        - VSphere
                        - oVirt
                        - IBMCloud
                        - KubeVirt
                        - EquinixMetal
                        - PowerVS
                        - AlibabaCloud
                        - Nutanix
                        - External
                        type: string
                      platformStatus:
                        description: platformStatus holds status information specific
                          to the underlying infrastructure provider.
                        properties:
                          alibabaCloud:
                            description: AlibabaCloud contains settings specific to
                              the Alibaba Cloud infrastructure provider.
                            properties:
                              region:
                                description: region specifies the region for Alibaba
                                  Cloud resources created for the cluster.
                                pattern: ^[0-9A-Za-z-]+$
                                type: string
                              resourceGroupID:
                                description: resourceGroupID is the ID of the resource
                                  group for the cluster.
                                pattern: ^(rg-[0-9A-Za-z]+)?$
                                type: string
                              resourceTags:
                                description: resourceTags is a list of additional
                                  tags to apply to Alibaba Cloud resources created
                                  for the cluster.
                                items:
                                  description: AlibabaCloudResourceTag is the set
                                    of tags to add to apply to resources.
                                  properties:
                                    key:
                                      description: key is the key of the tag.
                                      maxLength: 128
                                      minLength: 1
                                      type: string
                                    value:
                                      description: value is the value of the tag.
                                      maxLength: 128
                                      minLength: 1
                                      type: string
                                  required:
                                  - key
                                  - value
                                  type: object
                                maxItems: 20
                                type: array
                                x-kubernetes-list-map-keys:
                                - key
                                x-kubernetes-list-type: map
                            required:
                            - region
                            type: object
                          aws:
                            description: AWS contains settings specific to the Amazon
                              Web Services infrastructure provider.
                            properties:
                              region:
                                description: region holds the default AWS region for
                                  new AWS resources created by the cluster.
                                type: string
                              resourceTags:
                                description: resourceTags is a list of additional
                                  tags to apply to AWS resources created for the cluster.
                                  See https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html
                                  for information on tagging AWS resources. AWS supports
                                  a maximum of 50 tags per resource. OpenShift reserves
                                  25 tags for its use, leaving 25 tags available for
                                  the user.
                                items:
                                  description: AWSResourceTag is a tag to apply to
                                    AWS resources created for the cluster.
                                  properties:
                                    key:
                                      description: key is the key of the tag
                                      maxLength: 128
                                      minLength: 1
                                      pattern: ^[0-9A-Za-z_.:/=+-@]+$
                                      type: string
                                    value:
                                      description: value is the value of the tag.
                                        Some AWS service do not support empty values.
                                        Since tags are added to resources in many
                                        services, the length of the tag value must
                                        meet the requirements of all services.
                                      maxLength: 256
                                      minLength: 1
                                      pattern: ^[0-9A-Za-z_.:/=+-@]+$
                                      type: string
                                  required:
                                  - key
                                  - value
                                  type: object
                                maxItems: 25
                                type: array
                                x-kubernetes-list-type: atomic
                              serviceEndpoints:
                                description: ServiceEndpoints list contains custom
                                  endpoints which will override default service endpoint
                                  of AWS Services. There must be only one ServiceEndpoint
                                  for a service.
                                items:
                                  description: AWSServiceEndpoint store the configuration
                                    of a custom url to override existing defaults
                                    of AWS Services.
                                  properties:
                                    name:
                                      description: name is the name of the AWS service.
                                        The list of all the service names can be found
                                        at https://docs.aws.amazon.com/general/latest/gr/aws-service-information.html
                                        This must be provided and cannot be empty.
                                      pattern: ^[a-z0-9-]+$
                                      type: string
                                    url:
                                      description: url is fully qualified URI with
                                        scheme https, that overrides the default generated
                                        endpoint for a client. This must be provided
                                        and cannot be empty.
                                      pattern: ^https://
                                      type: string
                                  type: object
                                type: array
                                x-kubernetes-list-type: atomic
                            type: object
                          azure:
                            description: Azure contains settings specific to the Azure
                              infrastructure provider.
                            properties:
                              armEndpoint:
                                description: armEndpoint specifies a URL to use for
                                  resource management in non-soverign clouds such
                                  as Azure Stack.
                                type: string
                              cloudName:
                                description: cloudName is the name of the Azure cloud
                                  environment which can be used to configure the Azure
                                  SDK with the appropriate Azure API endpoints. If
                                  empty, the value is equal to `AzurePublicCloud`.
                                enum:
                                - ""
                                - AzurePublicCloud
                                - AzureUSGovernmentCloud
                                - AzureChinaCloud
                                - AzureGermanCloud
                                - AzureStackCloud
                                type: string
                              networkResourceGroupName:
                                description: networkResourceGroupName is the Resource
                                  Group for network resources like the Virtual Network
                                  and Subnets used by the cluster. If empty, the value
                                  is same as ResourceGroupName.
                                type: string
                              resourceGroupName:
                                description: resourceGroupName is the Resource Group
                                  for new Azure resources created for the cluster.
                                type: string
                              resourceTags:
                                description: resourceTags is a list of additional
                                  tags to apply to Azure resources created for the
                                  cluster. See https://docs.microsoft.com/en-us/rest/api/resources/tags
                                  for information on tagging Azure resources. Due
                                  to limitations on Automation, Content Delivery Network,
                                  DNS Azure resources, a maximum of 15 tags may be
                                  applied. OpenShift reserves 5 tags for internal
                                  use, allowing 10 tags for user configuration.
                                items:
                                  description: AzureResourceTag is a tag to apply
                                    to Azure resources created for the cluster.
                                  properties:
                                    key:
                                      description: key is the key part of the tag.
                                        A tag key can have a maximum of 128 characters
                                        and cannot be empty. Key must begin with a
                                        letter, end with a letter, number or underscore,
                                        and must contain only alphanumeric characters
                                        and the following special characters `_ .
                                        -`.
                                      maxLength: 128
                                      minLength: 1
                                      pattern: ^[a-zA-Z]([0-9A-Za-z_.-]*[0-9A-Za-z_])?$
                                      type: string
                                    value:
                                      description: 'value is the value part of the
                                        tag. A tag value can have a maximum of 256
                                        characters and cannot be empty. Value must
                                        contain only alphanumeric characters and the
                                        following special characters `_ + , - . /
                                        : ; < = > ? @`.'
                                      maxLength: 256
                                      minLength: 1
                                      pattern: ^[0-9A-Za-z_.=+-@]+$
                                      type: string
                                  required:
                                  - key
                                  - value
                                  type: object
                                maxItems: 10
                                type: array
                                x-kubernetes-list-type: atomic
                                x-kubernetes-validations:
                                - message: resourceTags are immutable and may only
                                    be configured during installation
                                  rule: self.all(x, x in oldSelf) && oldSelf.all(x,
                                    x in self)
                            type: object
                            x-kubernetes-validations:
                            - message: resourceTags may only be configured during
                                installation
                              rule: '!has(oldSelf.resourceTags) && !has(self.resourceTags)
                                || has(oldSelf.resourceTags) && has(self.resourceTags)'
                          baremetal:
                            description: BareMetal contains settings specific to the
                              BareMetal platform.
                            properties:
                              apiServerInternalIP:
                                description: "apiServerInternalIP is an IP address
                                  to contact the Kubernetes API server that can be
                                  used by components inside the cluster, like kubelets
                                  using the infrastructure rather than Kubernetes
                                  networking. It is the IP that the Infrastructure.status.apiServerInternalURI
                                  points to. It is the IP for a self-hosted load balancer
                                  in front of the API servers. \n Deprecated: Use
                                  APIServerInternalIPs instead."
                                type: string
                              apiServerInternalIPs:
                                description: apiServerInternalIPs are the IP addresses
                                  to contact the Kubernetes API server that can be
                                  used by components inside the cluster, like kubelets
                                  using the infrastructure rather than Kubernetes
                                  networking. These are the IPs for a self-hosted
                                  load balancer in front of the API servers. In dual
                                  stack clusters this list contains two IPs otherwise
                                  only one.
                                format: ip
                                items:
                                  type: string
                                maxItems: 2
                                type: array
                                x-kubernetes-list-type: atomic
                                x-kubernetes-validations:
                                - message: apiServerInternalIPs must contain at most
                                    one IPv4 address and at most one IPv6 address
                                  rule: 'self == oldSelf || (size(self) == 2 && isIP(self[0])
                                    && isIP(self[1]) ? ip(self[0]).family() != ip(self[1]).family()
                                    : true)'
                              ingressIP:
                                description: "ingressIP is an external IP which routes
                                  to the default ingress controller. The IP is a suitable
                                  target of a wildcard DNS record used to resolve
                                  default route host names. \n Deprecated: Use IngressIPs
                                  instead."
                                type: string
                              ingressIPs:
                                description: ingressIPs are the external IPs which
                                  route to the default ingress controller. The IPs
                                  are suitable targets of a wildcard DNS record used
                                  to resolve default route host names. In dual stack
                                  clusters this list contains two IPs otherwise only
                                  one.
                                format: ip
                                items:
                                  type: string
                                maxItems: 2
                                type: array
                                x-kubernetes-list-type: atomic
                                x-kubernetes-validations:
                                - message: ingressIPs must contain at most one IPv4
                                    address and at most one IPv6 address
                                  rule: 'self == oldSelf || (size(self) == 2 && isIP(self[0])
                                    && isIP(self[1]) ? ip(self[0]).family() != ip(self[1]).family()
                                    : true)'
                              loadBalancer:
                                default:
                                  type: OpenShiftManagedDefault
                                description: loadBalancer defines how the load balancer
                                  used by the cluster is configured.
                                properties:
                                  type:
                                    default: OpenShiftManagedDefault
                                    description: type defines the type of load balancer
                                      used by the cluster on BareMetal platform which
                                      can be a user-managed or openshift-managed load
                                      balancer that is to be used for the OpenShift
                                      API and Ingress endpoints. When set to OpenShiftManagedDefault
                                      the static pods in charge of API and Ingress
                                      traffic load-balancing defined in the machine
                                      config operator will be deployed. When set to
                                      UserManaged these static pods will not be deployed
                                      and it is expected that the load balancer is
                                      configured out of band by the deployer. When
                                      omitted, this means no opinion and the platform
                                      is left to choose a reasonable default. The
                                      default value is OpenShiftManagedDefault.
                                    enum:
                                    - OpenShiftManagedDefault
                                    - UserManaged
                                    type: string
                                    x-kubernetes-validations:
                                    - message: type is immutable once set
                                      rule: oldSelf == '' || self == oldSelf
                                type: object
                              machineNetworks:
                                description: machineNetworks are IP networks used
                                  to connect all the OpenShift cluster nodes.
                                items:
                                  description: CIDR is an IP address range in CIDR
                                    notation (for example, "10.0.0.0/8" or "fd00::/8").
                                  maxLength: 43
                                  minLength: 1
                                  type: string
                                  x-kubernetes-validations:
                                  - message: value must be a valid CIDR network address
                                    rule: isCIDR(self)
                                maxItems: 32
                                type: array
                                x-kubernetes-list-type: atomic
                                x-kubernetes-validations:
                                - rule: self.all(x, self.exists_one(y, x == y))
                              nodeDNSIP:
                                description: nodeDNSIP is the IP address for the internal
                                  DNS used by the nodes. Unlike the one managed by
                                  the DNS operator, `NodeDNSIP` provides name resolution
                                  for the nodes themselves. There is no DNS-as-a-service
                                  for BareMetal deployments. In order to minimize
                                  necessary changes to the datacenter DNS, a DNS service
                                  is hosted as a static pod to serve those hostnames
                                  to the nodes in the cluster.
                                type: string
                            type: object
                          equinixMetal:
                            description: EquinixMetal contains settings specific to
                              the Equinix Metal infrastructure provider.
                            properties:
                              apiServerInternalIP:
                                description: apiServerInternalIP is an IP address
                                  to contact the Kubernetes API server that can be
                                  used by components inside the cluster, like kubelets
                                  using the infrastructure rather than Kubernetes
                                  networking. It is the IP that the Infrastructure.status.apiServerInternalURI
                                  points to. It is the IP for a self-hosted load balancer
                                  in front of the API servers.
                                type: string
                              ingressIP:
                                description: ingressIP is an external IP which routes
                                  to the default ingress controller. The IP is a suitable
                                  target of a wildcard DNS record used to resolve
                                  default route host names.
                                type: string
                            type: object
                          external:
                            description: External contains settings specific to the
                              generic External infrastructure provider.
                            properties:
                              cloudControllerManager:
                                description: cloudControllerManager contains settings
                                  specific to the external Cloud Controller Manager
                                  (a.k.a. CCM or CPI). When omitted, new nodes will
                                  be not tainted and no extra initialization from
                                  the cloud controller manager is expected.
                                properties:
                                  state:
                                    description: "state determines whether or not
                                      an external Cloud Controller Manager is expected
                                      to be installed within the cluster. https://kubernetes.io/docs/tasks/administer-cluster/running-cloud-controller/#running-cloud-controller-manager
                                      \n Valid values are \"External\", \"None\" and
                                      omitted. When set to \"External\", new nodes
                                      will be tainted as uninitialized when created,
                                      preventing them from running workloads until
                                      they are initialized by the cloud controller
                                      manager. When omitted or set to \"None\", new
                                      nodes will be not tainted and no extra initialization
                                      from the cloud controller manager is expected."
                                    enum:
                                    - ""
                                    - External
                                    - None
                                    type: string
                                    x-kubernetes-validations:
                                    - message: state is immutable once set
                                      rule: self == oldSelf
                                type: object
                                x-kubernetes-validations:
                                - message: state may not be added or removed once
                                    set
                                  rule: (has(self.state) == has(oldSelf.state)) ||
                                    (!has(oldSelf.state) && self.state != "External")
                            type: object
                            x-kubernetes-validations:
                            - message: cloudControllerManager may not be added or
                                removed once set
                              rule: has(self.cloudControllerManager) == has(oldSelf.cloudControllerManager)
                          gcp:
                            description: GCP contains settings specific to the Google
                              Cloud Platform infrastructure provider.
                            properties:
                              projectID:
                                description: resourceGroupName is the Project ID for
                                  new GCP resources created for the cluster.
                                type: string
                              region:
                                description: region holds the region for new GCP resources
                                  created for the cluster.
                                type: string
                            type: object
                          ibmcloud:
                            description: IBMCloud contains settings specific to the
                              IBMCloud infrastructure provider.
                            properties:
                              cisInstanceCRN:
                                description: CISInstanceCRN is the CRN of the Cloud
                                  Internet Services instance managing the DNS zone
                                  for the cluster's base domain
                                type: string
                              dnsInstanceCRN:
                                description: DNSInstanceCRN is the CRN of the DNS
                                  Services instance managing the DNS zone for the
                                  cluster's base domain
                                type: string
                              location:
                                description: Location is where the cluster has been
                                  deployed
                                type: string
                              providerType:
                                description: ProviderType indicates the type of cluster
                                  that was created
                                type: string
                              resourceGroupName:
                                description: ResourceGroupName is the Resource Group
                                  for new IBMCloud resources created for the cluster.
                                type: string
                              serviceEndpoints:
                                description: serviceEndpoints is a list of custom
                                  endpoints which will override the default service
                                  endpoints of an IBM Cloud service. These endpoints
                                  are consumed by components within the cluster to
                                  reach the respective IBM Cloud Services.
                                items:
                                  description: IBMCloudServiceEndpoint stores the
                                    configuration of a custom url to override existing
                                    defaults of IBM Cloud Services.
                                  properties:
                                    name:
                                      description: 'name is the name of the IBM Cloud
                                        service. Possible values are: CIS, COS, COSConfig,
                                        DNSServices, GlobalCatalog, GlobalSearch,
                                        GlobalTagging, HyperProtect, IAM, KeyProtect,
                                        ResourceController, ResourceManager, or VPC.
                                        For example, the IBM Cloud Private IAM service
                                        could be configured with the service `name`
                                        of `IAM` and `url` of `https://private.iam.cloud.ibm.com`
                                        Whereas the IBM Cloud Private VPC service
                                        for US South (Dallas) could be configured
                                        with the service `name` of `VPC` and `url`
                                        of `https://us.south.private.iaas.cloud.ibm.com`'
                                      enum:
                                      - CIS
                                      - COS
                                      - COSConfig
                                      - DNSServices
                                      - GlobalCatalog
                                      - GlobalSearch
                                      - GlobalTagging
                                      - HyperProtect
                                      - IAM
                                      - KeyProtect
                                      - ResourceController
                                      - ResourceManager
                                      - VPC
                                      type: string
                                    url:
                                      description: url is fully qualified URI with
                                        scheme https, that overrides the default generated
                                        endpoint for a client. This must be provided
                                        and cannot be empty.
                                      type: string
                                      x-kubernetes-validations:
                                      - message: url must be a valid absolute URL
                                        rule: isURL(self)
                                  required:
                                  - name
                                  - url
                                  type: object
                                type: array
                                x-kubernetes-list-map-keys:
                                - name
                                x-kubernetes-list-type: map
                            type: object
                          kubevirt:
                            description: Kubevirt contains settings specific to the
                              kubevirt infrastructure provider.
                            properties:
                              apiServerInternalIP:
                                description: apiServerInternalIP is an IP address
                                  to contact the Kubernetes API server that can be
                                  used by components inside the cluster, like kubelets
                                  using the infrastructure rather than Kubernetes
                                  networking. It is the IP that the Infrastructure.status.apiServerInternalURI
                                  points to. It is the IP for a self-hosted load balancer
                                  in front of the API servers.
                                type: string
                              ingressIP:
                                description: ingressIP is an external IP which routes
                                  to the default ingress controller. The IP is a suitable
                                  target of a wildcard DNS record used to resolve
                                  default route host names.
                                type: string
                            type: object
                          nutanix:
                            description: Nutanix contains settings specific to the
                              Nutanix infrastructure provider.
                            properties:
                              apiServerInternalIP:
                                description: "apiServerInternalIP is an IP address
                                  to contact the Kubernetes API server that can be
                                  used by components inside the cluster, like kubelets
                                  using the infrastructure rather than Kubernetes
                                  networking. It is the IP that the Infrastructure.status.apiServerInternalURI
                                  points to. It is the IP for a self-hosted load balancer
                                  in front of the API servers. \n Deprecated: Use
                                  APIServerInternalIPs instead."
                                type: string
                              apiServerInternalIPs:
                                description: apiServerInternalIPs are the IP addresses
                                  to contact the Kubernetes API server that can be
                                  used by components inside the cluster, like kubelets
                                  using the infrastructure rather than Kubernetes
                                  networking. These are the IPs for a self-hosted
                                  load balancer in front of the API servers. In dual
                                  stack clusters this list contains two IPs otherwise
                                  only one.
                                format: ip
                                items:
                                  type: string
                                maxItems: 2
                                type: array
                                x-kubernetes-list-type: set
                                x-kubernetes-validations:
                                - message: apiServerInternalIPs must contain at most
                                    one IPv4 address and at most one IPv6 address
                                  rule: 'self == oldSelf || (size(self) == 2 && isIP(self[0])
                                    && isIP(self[1]) ? ip(self[0]).family() != ip(self[1]).family()
                                    : true)'
                              ingressIP:
                                description: "ingressIP is an external IP which routes
                                  to the default ingress controller. The IP is a suitable
                                  target of a wildcard DNS record used to resolve
                                  default route host names. \n Deprecated: Use IngressIPs
                                  instead."
                                type: string
                              ingressIPs:
                                description: ingressIPs are the external IPs which
                                  route to the default ingress controller. The IPs
                                  are suitable targets of a wildcard DNS record used
                                  to resolve default route host names. In dual stack
                                  clusters this list contains two IPs otherwise only
                                  one.
                                format: ip
                                items:
                                  type: string
                                maxItems: 2
                                type: array
                                x-kubernetes-list-type: set
                                x-kubernetes-validations:
                                - message: ingressIPs must contain at most one IPv4
                                    address and at most one IPv6 address
                                  rule: 'self == oldSelf || (size(self) == 2 && isIP(self[0])
                                    && isIP(self[1]) ? ip(self[0]).family() != ip(self[1]).family()
                                    : true)'
                              loadBalancer:
                                default:
                                  type: OpenShiftManagedDefault
                                description: loadBalancer defines how the load balancer
                                  used by the cluster is configured.
                                properties:
                                  type:
                                    default: OpenShiftManagedDefault
                                    description: type defines the type of load balancer
                                      used by the cluster on Nutanix platform which
                                      can be a user-managed or openshift-managed load
                                      balancer that is to be used for the OpenShift
                                      API and Ingress endpoints. When set to OpenShiftManagedDefault
                                      the static pods in charge of API and Ingress
                                      traffic load-balancing defined in the machine
                                      config operator will be deployed. When set to
                                      UserManaged these static pods will not be deployed
                                      and it is expected that the load balancer is
                                      configured out of band by the deployer. When
                                      omitted, this means no opinion and the platform
                                      is left to choose a reasonable default. The
                                      default value is OpenShiftManagedDefault.
                                    enum:
                                    - OpenShiftManagedDefault
                                    - UserManaged
                                    type: string
                                    x-kubernetes-validations:
                                    - message: type is immutable once set
                                      rule: oldSelf == '' || self == oldSelf
                                type: object
                            type: object
                          openstack:
                            description: OpenStack contains settings specific to the
                              OpenStack infrastructure provider.
                            properties:
                              apiServerInternalIP:
                                description: "apiServerInternalIP is an IP address
                                  to contact the Kubernetes API server that can be
                                  used by components inside the cluster, like kubelets
                                  using the infrastructure rather than Kubernetes
                                  networking. It is the IP that the Infrastructure.status.apiServerInternalURI
                                  points to. It is the IP for a self-hosted load balancer
                                  in front of the API servers. \n Deprecated: Use
                                  APIServerInternalIPs instead."
                                type: string
                              apiServerInternalIPs:
                                description: apiServerInternalIPs are the IP addresses
                                  to contact the Kubernetes API server that can be
                                  used by components inside the cluster, like kubelets
                                  using the infrastructure rather than Kubernetes
                                  networking. These are the IPs for a self-hosted
                                  load balancer in front of the API servers. In dual
                                  stack clusters this list contains two IPs otherwise
                                  only one.
                                format: ip
                                items:
                                  type: string
                                maxItems: 2
                                type: array
                                x-kubernetes-list-type: atomic
                                x-kubernetes-validations:
                                - message: apiServerInternalIPs must contain at most
                                    one IPv4 address and at most one IPv6 address
                                  rule: 'self == oldSelf || (size(self) == 2 && isIP(self[0])
                                    && isIP(self[1]) ? ip(self[0]).family() != ip(self[1]).family()
                                    : true)'
                              cloudName:
                                description: cloudName is the name of the desired
                                  OpenStack cloud in the client configuration file
                                  (`clouds.yaml`).
                                type: string
                              ingressIP:
                                description: "ingressIP is an external IP which routes
                                  to the default ingress controller. The IP is a suitable
                                  target of a wildcard DNS record used to resolve
                                  default route host names. \n Deprecated: Use IngressIPs
                                  instead."
                                type: string
                              ingressIPs:
                                description: ingressIPs are the external IPs which
                                  route to the default ingress controller. The IPs
                                  are suitable targets of a wildcard DNS record used
                                  to resolve default route host names. In dual stack
                                  clusters this list contains two IPs otherwise only
                                  one.
                                format: ip
                                items:
                                  type: string
                                maxItems: 2
                                type: array
                                x-kubernetes-list-type: atomic
                                x-kubernetes-validations:
                                - message: ingressIPs must contain at most one IPv4
                                    address and at most one IPv6 address
                                  rule: 'self == oldSelf || (size(self) == 2 && isIP(self[0])
                                    && isIP(self[1]) ? ip(self[0]).family() != ip(self[1]).family()
                                    : true)'
                              loadBalancer:
                                default:
                                  type: OpenShiftManagedDefault
                                description: loadBalancer defines how the load balancer
                                  used by the cluster is configured.
                                properties:
                                  type:
                                    default: OpenShiftManagedDefault
                                    description: type defines the type of load balancer
                                      used by the cluster on OpenStack platform which
                                      can be a user-managed or openshift-managed load
                                      balancer that is to be used for the OpenShift
                                      API and Ingress endpoints. When set to OpenShiftManagedDefault
                                      the static pods in charge of API and Ingress
                                      traffic load-balancing defined in the machine
                                      config operator will be deployed. When set to
                                      UserManaged these static pods will not be deployed
                                      and it is expected that the load balancer is
                                      configured out of band by the deployer. When
                                      omitted, this means no opinion and the platform
                                      is left to choose a reasonable default. The
                                      default value is OpenShiftManagedDefault.
                                    enum:
                                    - OpenShiftManagedDefault
                                    - UserManaged
                                    type: string
                                    x-kubernetes-validations:
                                    - message: type is immutable once set
                                      rule: oldSelf == '' || self == oldSelf
                                type: object
                              machineNetworks:
                                description: machineNetworks are IP networks used
                                  to connect all the OpenShift cluster nodes.
                                items:
                                  description: CIDR is an IP address range in CIDR
                                    notation (for example, "10.0.0.0/8" or "fd00::/8").
                                  maxLength: 43
                                  minLength: 1
                                  type: string
                                  x-kubernetes-validations:
                                  - message: value must be a valid CIDR network address
                                    rule: isCIDR(self)
                                maxItems: 32
                                type: array
                                x-kubernetes-list-type: atomic
                                x-kubernetes-validations:
                                - rule: self.all(x, self.exists_one(y, x == y))
                              nodeDNSIP:
                                description: nodeDNSIP is the IP address for the internal
                                  DNS used by the nodes. Unlike the one managed by
                                  the DNS operator, `NodeDNSIP` provides name resolution
                                  for the nodes themselves. There is no DNS-as-a-service
                                  for OpenStack deployments. In order to minimize
                                  necessary changes to the datacenter DNS, a DNS service
                                  is hosted as a static pod to serve those hostnames
                                  to the nodes in the cluster.
                                type: string
                            type: object
                          ovirt:
                            description: Ovirt contains settings specific to the oVirt
                              infrastructure provider.
                            properties:
                              apiServerInternalIP:
                                description: "apiServerInternalIP is an IP address
                                  to contact the Kubernetes API server that can be
                                  used by components inside the cluster, like kubelets
                                  using the infrastructure rather than Kubernetes
                                  networking. It is the IP that the Infrastructure.status.apiServerInternalURI
                                  points to. It is the IP for a self-hosted load balancer
                                  in front of the API servers. \n Deprecated: Use
                                  APIServerInternalIPs instead."
                                type: string
                              apiServerInternalIPs:
                                description: apiServerInternalIPs are the IP addresses
                                  to contact the Kubernetes API server that can be
                                  used by components inside the cluster, like kubelets
                                  using the infrastructure rather than Kubernetes
                                  networking. These are the IPs for a self-hosted
                                  load balancer in front of the API servers. In dual
                                  stack clusters this list contains two IPs otherwise
                                  only one.
                                format: ip
                                items:
                                  type: string
                                maxItems: 2
                                type: array
                                x-kubernetes-list-type: set
                                x-kubernetes-validations:
                                - message: apiServerInternalIPs must contain at most
                                    one IPv4 address and at most one IPv6 address
                                  rule: 'self == oldSelf || (size(self) == 2 && isIP(self[0])
                                    && isIP(self[1]) ? ip(self[0]).family() != ip(self[1]).family()
                                    : true)'
                              ingressIP:
                                description: "ingressIP is an external IP which routes
                                  to the default ingress controller. The IP is a suitable
                                  target of a wildcard DNS record used to resolve
                                  default route host names. \n Deprecated: Use IngressIPs
                                  instead."
                                type: string
                              ingressIPs:
                                description: ingressIPs are the external IPs which
                                  route to the default ingress controller. The IPs
                                  are suitable targets of a wildcard DNS record used
                                  to resolve default route host names. In dual stack
                                  clusters this list contains two IPs otherwise only
                                  one.
                                format: ip
                                items:
                                  type: string
                                maxItems: 2
                                type: array
                                x-kubernetes-list-type: set
                                x-kubernetes-validations:
                                - message: ingressIPs must contain at most one IPv4
                                    address and at most one IPv6 address
                                  rule: 'self == oldSelf || (size(self) == 2 && isIP(self[0])
                                    && isIP(self[1]) ? ip(self[0]).family() != ip(self[1]).family()
                                    : true)'
                              loadBalancer:
                                default:
                                  type: OpenShiftManagedDefault
                                description: loadBalancer defines how the load balancer
                                  used by the cluster is configured.
                                properties:
                                  type:
                                    default: OpenShiftManagedDefault
                                    description: type defines the type of load balancer
                                      used by the cluster on Ovirt platform which
                                      can be a user-managed or openshift-managed load
                                      balancer that is to be used for the OpenShift
                                      API and Ingress endpoints. When set to OpenShiftManagedDefault
                                      the static pods in charge of API and Ingress
                                      traffic load-balancing defined in the machine
                                      config operator will be deployed. When set to
                                      UserManaged these static pods will not be deployed
                                      and it is expected that the load balancer is
                                      configured out of band by the deployer. When
                                      omitted, this means no opinion and the platform
                                      is left to choose a reasonable default. The
                                      default value is OpenShiftManagedDefault.
                                    enum:
                                    - OpenShiftManagedDefault
                                    - UserManaged
                                    type: string
                                    x-kubernetes-validations:
                                    - message: type is immutable once set
                                      rule: oldSelf == '' || self == oldSelf
                                type: object
                              nodeDNSIP:
                                description: 'deprecated: as of 4.6, this field is
                                  no longer set or honored.  It will be removed in
                                  a future release.'
                                type: string
                            type: object
                          powervs:
                            description: PowerVS contains settings specific to the
                              Power Systems Virtual Servers infrastructure provider.
                            properties:
                              cisInstanceCRN:
                                description: CISInstanceCRN is the CRN of the Cloud
                                  Internet Services instance managing the DNS zone
                                  for the cluster's base domain
                                type: string
                              dnsInstanceCRN:
                                description: DNSInstanceCRN is the CRN of the DNS
                                  Services instance managing the DNS zone for the
                                  cluster's base domain
                                type: string
                              region:
                                description: region holds the default Power VS region
                                  for new Power VS resources created by the cluster.
                                type: string
                              resourceGroup:
                                description: 'resourceGroup is the resource group
                                  name for new IBMCloud resources created for a cluster.
                                  The resource group specified here will be used by
                                  cluster-image-registry-operator to set up a COS
                                  Instance in IBMCloud for the cluster registry. More
                                  about resource groups can be found here: https://cloud.ibm.com/docs/account?topic=account-rgs.
                                  When omitted, the image registry operator won''t
                                  be able to configure storage, which results in the
                                  image registry cluster operator not being in an
                                  available state.'
                                maxLength: 40
                                pattern: ^[a-zA-Z0-9-_ ]+$
                                type: string
                                x-kubernetes-validations:
                                - message: resourceGroup is immutable once set
                                  rule: oldSelf == '' || self == oldSelf
                              serviceEndpoints:
                                description: serviceEndpoints is a list of custom
                                  endpoints which will override the default service
                                  endpoints of a Power VS service.
                                items:
                                  description: PowervsServiceEndpoint stores the configuration
                                    of a custom url to override existing defaults
                                    of PowerVS Services.
                                  properties:
                                    name:
                                      description: name is the name of the Power VS
                                        service. Few of the services are IAM - https://cloud.ibm.com/apidocs/iam-identity-token-api
                                        ResourceController - https://cloud.ibm.com/apidocs/resource-controller/resource-controller
                                        Power Cloud - https://cloud.ibm.com/apidocs/power-cloud
                                      pattern: ^[a-z0-9-]+$
                                      type: string
                                    url:
                                      description: url is fully qualified URI with
                                        scheme https, that overrides the default generated
                                        endpoint for a client. This must be provided
                                        and cannot be empty.
                                      format: uri
                                      pattern: ^https://
                                      type: string
                                  required:
                                  - name
                                  - url
                                  type: object
                                type: array
                                x-kubernetes-list-map-keys:
                                - name
                                x-kubernetes-list-type: map
                              zone:
                                description: 'zone holds the default zone for the
                                  new Power VS resources created by the cluster. Note:
                                  Currently only single-zone OCP clusters are supported'
                                type: string
                            type: object
                            x-kubernetes-validations:
                            - message: cannot unset resourceGroup once set
                              rule: '!has(oldSelf.resourceGroup) || has(self.resourceGroup)'
                          type:
                            description: "type is the underlying infrastructure provider
                              for the cluster. This value controls whether infrastructure
                              automation such as service load balancers, dynamic volume
                              provisioning, machine creation and deletion, and other
                              integrations are enabled. If None, no infrastructure
                              automation is enabled. Allowed values are \"AWS\", \"Azure\",
                              \"BareMetal\", \"GCP\", \"Libvirt\", \"OpenStack\",
                              \"VSphere\", \"oVirt\", \"EquinixMetal\", \"PowerVS\",
                              \"AlibabaCloud\", \"Nutanix\" and \"None\". Individual
                              components may not support all platforms, and must handle
                              unrecognized platforms as None if they do not support
                              that platform. \n This value will be synced with to
                              the `status.platform` and `status.platformStatus.type`.
                              Currently this value cannot be changed once set."
                            enum:
                            - ""
                            - AWS
                            - Azure
                            - BareMetal
                            - GCP
                            - Libvirt
                            - OpenStack
                            - None
                            - VSphere
                            - oVirt
                            - IBMCloud
                            - KubeVirt
                            - EquinixMetal
                            - PowerVS
                            - AlibabaCloud
                            - Nutanix
                            - External
                            type: string
                          vsphere:
                            description: VSphere contains settings specific to the
                              VSphere infrastructure provider.
                            properties:
                              apiServerInternalIP:
                                description: "apiServerInternalIP is an IP address
                                  to contact the Kubernetes API server that can be
                                  used by components inside the cluster, like kubelets
                                  using the infrastructure rather than Kubernetes
                                  networking. It is the IP that the Infrastructure.status.apiServerInternalURI
                                  points to. It is the IP for a self-hosted load balancer
                                  in front of the API servers. \n Deprecated: Use
                                  APIServerInternalIPs instead."
                                type: string
                              apiServerInternalIPs:
                                description: apiServerInternalIPs are the IP addresses
                                  to contact the Kubernetes API server that can be
                                  used by components inside the cluster, like kubelets
                                  using the infrastructure rather than Kubernetes
                                  networking. These are the IPs for a self-hosted
                                  load balancer in front of the API servers. In dual
                                  stack clusters this list contains two IPs otherwise
                                  only one.
                                format: ip
                                items:
                                  type: string
                                maxItems: 2
                                type: array
                                x-kubernetes-list-type: atomic
                                x-kubernetes-validations:
                                - message: apiServerInternalIPs must contain at most
                                    one IPv4 address and at most one IPv6 address
                                  rule: 'self == oldSelf || (size(self) == 2 && isIP(self[0])
                                    && isIP(self[1]) ? ip(self[0]).family() != ip(self[1]).family()
                                    : true)'
                              ingressIP:
                                description: "ingressIP is an external IP which routes
                                  to the default ingress controller. The IP is a suitable
                                  target of a wildcard DNS record used to resolve
                                  default route host names. \n Deprecated: Use IngressIPs
                                  instead."
                                type: string
                              ingressIPs:
                                description: ingressIPs are the external IPs which
                                  route to the default ingress controller. The IPs
                                  are suitable targets of a wildcard DNS record used
                                  to resolve default route host names. In dual stack
                                  clusters this list contains two IPs otherwise only
                                  one.
                                format: ip
                                items:
                                  type: string
                                maxItems: 2
                                type: array
                                x-kubernetes-list-type: atomic
                                x-kubernetes-validations:
                                - message: ingressIPs must contain at most one IPv4
                                    address and at most one IPv6 address
                                  rule: 'self == oldSelf || (size(self) == 2 && isIP(self[0])
                                    && isIP(self[1]) ? ip(self[0]).family() != ip(self[1]).family()
                                    : true)'
                              loadBalancer:
                                default:
                                  type: OpenShiftManagedDefault
                                description: loadBalancer defines how the load balancer
                                  used by the cluster is configured.
                                properties:
                                  type:
                                    default: OpenShiftManagedDefault
                                    description: type defines the type of load balancer
                                      used by the cluster on VSphere platform which
                                      can be a user-managed or openshift-managed load
                                      balancer that is to be used for the OpenShift
                                      API and Ingress endpoints. When set to OpenShiftManagedDefault
                                      the static pods in charge of API and Ingress
                                      traffic load-balancing defined in the machine
                                      config operator will be deployed. When set to
                                      UserManaged these static pods will not be deployed
                                      and it is expected that the load balancer is
                                      configured out of band by the deployer. When
                                      omitted, this means no opinion and the platform
                                      is left to choose a reasonable default. The
                                      default value is OpenShiftManagedDefault.
                                    enum:
                                    - OpenShiftManagedDefault
                                    - UserManaged
                                    type: string
                                    x-kubernetes-validations:
                                    - message: type is immutable once set
                                      rule: oldSelf == '' || self == oldSelf
                                type: object
                              machineNetworks:
                                description: machineNetworks are IP networks used
                                  to connect all the OpenShift cluster nodes.
                                items:
                                  description: CIDR is an IP address range in CIDR
                                    notation (for example, "10.0.0.0/8" or "fd00::/8").
                                  maxLength: 43
                                  minLength: 1
                                  type: string
                                  x-kubernetes-validations:
                                  - message: value must be a valid CIDR network address
                                    rule: isCIDR(self)
                                maxItems: 32
                                type: array
                                x-kubernetes-list-type: atomic
                                x-kubernetes-validations:
                                - rule: self.all(x, self.exists_one(y, x == y))
                              nodeDNSIP:
                                description: nodeDNSIP is the IP address for the internal
                                  DNS used by the nodes. Unlike the one managed by
                                  the DNS operator, `NodeDNSIP` provides name resolution
                                  for the nodes themselves. There is no DNS-as-a-service
                                  for vSphere deployments. In order to minimize necessary
                                  changes to the datacenter DNS, a DNS service is
                                  hosted as a static pod to serve those hostnames
                                  to the nodes in the cluster.
                                type: string
                            type: object
                        type: object
                    type: object
                required:
                - spec
                type: object
                x-kubernetes-embedded-resource: true
              internalRegistryPullSecret:
                description: internalRegistryPullSecret is the pull secret for the
                  internal registry, used by rpm-ostree to pull images from the internal
                  registry if present
                format: byte
                nullable: true
                type: string
              ipFamilies:
                description: ipFamilies indicates the IP families in use by the cluster
                  network
                type: string
              kubeAPIServerServingCAData:
                description: kubeAPIServerServingCAData managed Kubelet to API Server
                  Cert... Rotated automatically
                format: byte
                type: string
              network:
                description: Network contains additional network related information
                nullable: true
                properties:
                  mtuMigration:
                    description: MTUMigration contains the MTU migration configuration.
                    nullable: true
                    properties:
                      machine:
                        description: Machine contains MTU migration configuration
                          for the machine's uplink.
                        properties:
                          from:
                            description: From is the MTU to migrate from.
                            format: int32
                            minimum: 0
                            type: integer
                          to:
                            description: To is the MTU to migrate to.
                            format: int32
                            minimum: 0
                            type: integer
                        type: object
                      network:
                        description: Network contains MTU migration configuration
                          for the default network.
                        properties:
                          from:
                            description: From is the MTU to migrate from.
                            format: int32
                            minimum: 0
                            type: integer
                          to:
                            description: To is the MTU to migrate to.
                            format: int32
                            minimum: 0
                            type: integer
                        type: object
                    type: object
                required:
                - mtuMigration
                type: object
              networkType:
                description: 'networkType holds the type of network the cluster is
                  using XXX: this is temporary and will be dropped as soon as possible
                  in favor of a better support to start network related services the
                  proper way. Nobody is also changing this once the cluster is up
                  and running the first time, so, disallow regeneration if this changes.'
                type: string
              osImageURL:
                description: OSImageURL is the old-format container image that contains
                  the OS update payload.
                type: string
              platform:
                description: platform is deprecated, use Infra.Status.PlatformStatus.Type
                  instead
                type: string
              proxy:
                description: proxy holds the current proxy configuration for the nodes
                nullable: true
                properties:
                  httpProxy:
                    description: httpProxy is the URL of the proxy for HTTP requests.
                    type: string
                  httpsProxy:
                    description: httpsProxy is the URL of the proxy for HTTPS requests.
                    type: string
                  noProxy:
                    description: noProxy is a comma-separated list of hostnames and/or
                      CIDRs for which the proxy should not be used.
                    type: string
                type: object
              pullSecret:
                description: pullSecret is the default pull secret that needs to be
                  installed on all machines.
                properties:
                  apiVersion:
                    description: API version of the referent.
                    type: string
                  fieldPath:
                    description: 'If referring to a piece of an object instead of
                      an entire object, this string should contain a valid JSON/Go
                      field access statement, such as desiredState.manifest.containers[2].
                      For example, if the object reference is to a container within
                      a pod, this would take on a value like: "spec.containers{name}"
                      (where "name" refers to the name of the container that triggered
                      the event) or if no container name is specified "spec.containers[2]"
                      (container with index 2 in this pod). This syntax is chosen
                      only to have some well-defined way of referencing a part of
                      an object. TODO: this design is not final and this field is
                      subject to change in the future.'
                    type: string
                  kind:
                    description: 'Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
                    type: string
                  name:
                    description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names'
                    type: string
                  namespace:
                    description: 'Namespace of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/'
                    type: string
                  resourceVersion:
                    description: 'Specific resourceVersion to which this reference
                      is made, if any. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency'
                    type: string
                  uid:
                    description: 'UID of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids'
                    type: string
                type: object
                x-kubernetes-map-type: atomic
              releaseImage:
                description: releaseImage is the image used when installing the cluster
                type: string
              rootCAData:
                description: rootCAData specifies the root CA data
                format: byte
                type: string
            required:
            - additionalTrustBundle
            - baseOSContainerImage
            - cloudProviderCAData
            - cloudProviderConfig
            - clusterDNSIP
            - dns
            - images
            - infra
            - ipFamilies
            - kubeAPIServerServingCAData
            - network
            - proxy
            - releaseImage
            - rootCAData
            type: object
          status:
            description: ControllerConfigStatus is the status for ControllerConfig
            properties:
              conditions:
                description: conditions represents the latest available observations
                  of current state.
                items:
                  description: ControllerConfigStatusCondition contains condition
                    information for ControllerConfigStatus
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the time of the last update
                        to the current status object.
                      format: date-time
                      nullable: true
                      type: string
                    message:
                      description: message provides additional information about the
                        current condition. This is only to be consumed by humans.
                      type: string
                    reason:
                      description: reason is the reason for the condition's last transition.  Reasons
                        are PascalCase
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      type: string
                    type:
                      description: type specifies the state of the operator's reconciliation
                        functionality.
                      type: string
                  required:
                  - lastTransitionTime
                  - status
                  - type
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              controllerCertificates:
                description: controllerCertificates represents the latest available
                  observations of the automatically rotating certificates in the MCO.
                items:
                  description: ControllerCertificate contains info about a specific
                    cert.
                  properties:
                    bundleFile:
                      description: bundleFile is the larger bundle a cert comes from
                      type: string
                    notAfter:
                      description: notAfter is the upper boundary for validity
                      format: date-time
                      type: string
                    notBefore:
                      description: notBefore is the lower boundary for validity
                      format: date-time
                      type: string
                    signer:
                      description: signer is the  cert Issuer
                      type: string
                    subject:
                      description: subject is the cert subject
                      type: string
                  required:
                  - bundleFile
                  - signer
                  - subject
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              observedGeneration:
                description: observedGeneration represents the generation observed
                  by the controller.
                format: int64
                type: integer
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ControllerConfig
    listKind: ControllerConfigList
    plural: controllerconfigs
    singular: controllerconfig
  conditions:
  - lastTransitionTime: "2025-06-25T14:59:08Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:59:08Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
