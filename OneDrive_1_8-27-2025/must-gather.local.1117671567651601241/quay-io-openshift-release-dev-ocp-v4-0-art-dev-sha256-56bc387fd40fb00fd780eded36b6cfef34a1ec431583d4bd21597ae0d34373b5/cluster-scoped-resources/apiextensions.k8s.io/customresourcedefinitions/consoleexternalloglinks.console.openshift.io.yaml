---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/481
    api.openshift.io/merged-by-featuregates: "true"
    capability.openshift.io/name: Console
    description: ConsoleExternalLogLink is an extension for customizing OpenShift
      web console log links.
    displayName: ConsoleExternalLogLinks
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:53:42Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:42Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:capability.openshift.io/name: {}
          f:description: {}
          f:displayName: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:42:13Z"
  name: consoleexternalloglinks.console.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "144727"
  uid: 79b2fb35-b89a-456d-9c3b-516d99905cbb
spec:
  conversion:
    strategy: None
  group: console.openshift.io
  names:
    kind: ConsoleExternalLogLink
    listKind: ConsoleExternalLogLinkList
    plural: consoleexternalloglinks
    singular: consoleexternalloglink
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.text
      name: Text
      type: string
    - jsonPath: .spec.hrefTemplate
      name: HrefTemplate
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1
    schema:
      openAPIV3Schema:
        description: "ConsoleExternalLogLink is an extension for customizing OpenShift
          web console log links. \n Compatibility level 2: Stable within a major release
          for a minimum of 9 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: ConsoleExternalLogLinkSpec is the desired log link configuration.
              The log link will appear on the logs tab of the pod details page.
            properties:
              hrefTemplate:
                description: "hrefTemplate is an absolute secure URL (must use https)
                  for the log link including variables to be replaced. Variables are
                  specified in the URL with the format ${variableName}, for instance,
                  ${containerName} and will be replaced with the corresponding values
                  from the resource. Resource is a pod. Supported variables are: -
                  ${resourceName} - name of the resource which containes the logs
                  - ${resourceUID} - UID of the resource which contains the logs -
                  e.g. `11111111-**************-************` - ${containerName} -
                  name of the resource's container that contains the logs - ${resourceNamespace}
                  - namespace of the resource that contains the logs - ${resourceNamespaceUID}
                  - namespace UID of the resource that contains the logs - ${podLabels}
                  - JSON representation of labels matching the pod with the logs -
                  e.g. `{\"key1\":\"value1\",\"key2\":\"value2\"}` \n e.g., https://example.com/logs?resourceName=${resourceName}&containerName=${containerName}&resourceNamespace=${resourceNamespace}&podLabels=${podLabels}"
                pattern: ^https://
                type: string
              namespaceFilter:
                description: namespaceFilter is a regular expression used to restrict
                  a log link to a matching set of namespaces (e.g., `^openshift-`).
                  The string is converted into a regular expression using the JavaScript
                  RegExp constructor. If not specified, links will be displayed for
                  all the namespaces.
                type: string
              text:
                description: text is the display text for the link
                type: string
            required:
            - hrefTemplate
            - text
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ConsoleExternalLogLink
    listKind: ConsoleExternalLogLinkList
    plural: consoleexternalloglinks
    singular: consoleexternalloglink
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:42Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:42Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
