---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    capability.openshift.io/name: NodeTuning
    include.release.openshift.io/hypershift: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    service.beta.openshift.io/inject-cabundle: "true"
  creationTimestamp: "2025-06-25T14:53:40Z"
  generation: 3
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:40Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        f:conversion:
          f:webhook:
            f:clientConfig:
              f:caBundle: {}
    manager: service-ca-operator
    operation: Update
    time: "2025-06-25T14:58:21Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:capability.openshift.io/name: {}
          f:include.release.openshift.io/hypershift: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
          f:service.beta.openshift.io/inject-cabundle: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
          f:webhook:
            .: {}
            f:clientConfig:
              .: {}
              f:service:
                .: {}
                f:name: {}
                f:namespace: {}
                f:path: {}
                f:port: {}
            f:conversionReviewVersions: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:42:13Z"
  name: performanceprofiles.performance.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "144722"
  uid: b94df358-a4de-49f2-80d3-4caf5bca94dc
spec:
  conversion:
    strategy: Webhook
    webhook:
      clientConfig:
        caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURVVENDQWptZ0F3SUJBZ0lJZHpKblVydFl3TVV3RFFZSktvWklodmNOQVFFTEJRQXdOakUwTURJR0ExVUUKQXd3cmIzQmxibk5vYVdaMExYTmxjblpwWTJVdGMyVnlkbWx1WnkxemFXZHVaWEpBTVRjMU1EZzJNelE1TkRBZQpGdzB5TlRBMk1qVXhORFU0TVROYUZ3MHlOekE0TWpReE5EVTRNVFJhTURZeE5EQXlCZ05WQkFNTUsyOXdaVzV6CmFHbG1kQzF6WlhKMmFXTmxMWE5sY25acGJtY3RjMmxuYm1WeVFERTNOVEE0TmpNME9UUXdnZ0VpTUEwR0NTcUcKU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRRFNsSjQwa1FtblRTK245U1lCd2QwYW8zRDlUVXlPK1BFQgp0R3FjbGh0L3pmemk5SVRtRmVZeHNOcVZMUDlLQ1hNY0J5L0I2Nk9qV1l5SzJlYU1QbGhCUXp2cmhmU3lCaU5DClpwSFVkYXEzSjZrVVlSUGpGWHRNa1o5YUNVa016UTNqYm42Sm9zbTBxcXFvd0F2Vk1YL21jaWFJWXJNVTFVMEkKbWNhd0VBK3BqeS9ZKzEzSWtlRm9FbitUK0dwUjB6RVFLRnJaK05OM2dTOHBIQ3dpbW9odS9NVHA0WjdEQXI3RApJQzVqcklzU0JoTXQzdVFMU1dhb0VmcHoxemRsSmVFamNwbVdiWEIzbTB6d3FDekNPQm9YZTh3RlZYVGdTK0ZFCjEwNXp4cTBsYzIxS21BRVRDSml4cEQzK1p6NXA5dk1Pa3VMTmsxdXF5c0xhMU5lWlExaVJBZ01CQUFHall6QmgKTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQQmdOVkhSTUJBZjhFQlRBREFRSC9NQjBHQTFVZERnUVdCQlQrU081SwpBTkxudVZ6eG5CZStMS3hyL3dMb2JUQWZCZ05WSFNNRUdEQVdnQlQrU081S0FOTG51Vnp4bkJlK0xLeHIvd0xvCmJUQU5CZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUFTcWc0cG5LdWQrUmN3NUFVcjdVQXBPZTVaOXcyTUhKS3R6MmoKK3BuUHg3dituNWZJdDZoV3BxYlNzREovenZaeDhjSW9mcWgrNTFUOTQ3bmdHQjhxdThtOEprOHFnMncxcENJYgpqWDhNRThBTnVwM3diYVZEYXdGQXJrTE5HbmwzTFB0akV5amkyZGpTUjlUK3pZWHVUa3JvSVQ1Mmg3NUY1bFI1CmFvelJmVjE3bmQvcGpEcHhSNkYycUpMVVh4eGt2elZnQjdBMGs0M1RYSitCTkdrSmNHQXVuQjBPd2JTN2E1T2QKRFRBSGk3NVM5QmN5akhLVk1iNVg0L3FLS25yNlVqSHgxZHp1bC9xWHBYWmFhWndJRHFURHZSZzczRStrTC9xdgpyMUFCcWdxZ0RNb0J6bUpLd1lVa29lbzNmYS9EaThid2NWVnJEekJSeG8rclZWYlc5QT09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
        service:
          name: performance-addon-operator-service
          namespace: openshift-cluster-node-tuning-operator
          path: /convert
          port: 443
      conversionReviewVersions:
      - v1
      - v1alpha1
  group: performance.openshift.io
  names:
    kind: PerformanceProfile
    listKind: PerformanceProfileList
    plural: performanceprofiles
    singular: performanceprofile
  scope: Cluster
  versions:
  - deprecated: true
    deprecationWarning: v1 is deprecated and should be removed in next three releases,
      use v2 instead
    name: v1
    schema:
      openAPIV3Schema:
        description: PerformanceProfile is the Schema for the performanceprofiles
          API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: PerformanceProfileSpec defines the desired state of PerformanceProfile.
            properties:
              additionalKernelArgs:
                description: Additional kernel arguments.
                items:
                  type: string
                type: array
              cpu:
                description: CPU defines a set of CPU related parameters.
                properties:
                  balanceIsolated:
                    description: BalanceIsolated toggles whether or not the Isolated
                      CPU set is eligible for load balancing work loads. When this
                      option is set to "false", the Isolated CPU set will be static,
                      meaning workloads have to explicitly assign each thread to a
                      specific cpu in order to work across multiple CPUs. Setting
                      this to "true" allows workloads to be balanced across CPUs.
                      Setting this to "false" offers the most predictable performance
                      for guaranteed workloads, but it offloads the complexity of
                      cpu load balancing to the application. Defaults to "true"
                    type: boolean
                  isolated:
                    description: 'Isolated defines a set of CPUs that will be used
                      to give to application threads the most execution time possible,
                      which means removing as many extraneous tasks off a CPU as possible.
                      It is important to notice the CPU manager can choose any CPU
                      to run the workload except the reserved CPUs. In order to guarantee
                      that your workload will run on the isolated CPU:   1. The union
                      of reserved CPUs and isolated CPUs should include all online
                      CPUs   2. The isolated CPUs field should be the complementary
                      to reserved CPUs field'
                    type: string
                  offlined:
                    description: Offline defines a set of CPUs that will be unused
                      and set offline
                    type: string
                  reserved:
                    description: Reserved defines a set of CPUs that will not be used
                      for any container workloads initiated by kubelet.
                    type: string
                required:
                - isolated
                type: object
              globallyDisableIrqLoadBalancing:
                description: GloballyDisableIrqLoadBalancing toggles whether IRQ load
                  balancing will be disabled for the Isolated CPU set. When the option
                  is set to "true" it disables IRQs load balancing for the Isolated
                  CPU set. Setting the option to "false" allows the IRQs to be balanced
                  across all CPUs, however the IRQs load balancing can be disabled
                  per pod CPUs when using irq-load-balancing.crio.io/cpu-quota.crio.io
                  annotations. Defaults to "false"
                type: boolean
              hardwareTuning:
                description: HardwareTuning defines a set of CPU frequencies for isolated
                  and reserved cpus. It is an optional parameter and requires vendor
                  recommendation to find suitable frequencies. The intention is to
                  set higher frequency for reserved cpus where platform application
                  is running while setting isolated cpus frequency to match vendor
                  recommendation.
                properties:
                  isolatedCpuFreq:
                    description: IsolatedCpuFreq defines a minimum frequency to be
                      set across isolated cpus
                    type: integer
                  reservedCpuFreq:
                    description: ReservedCpuFreq defines a maximum frequency to be
                      set across reserved cpus
                    type: integer
                type: object
              hugepages:
                description: HugePages defines a set of huge pages related parameters.
                  It is possible to set huge pages with multiple size values at the
                  same time. For example, hugepages can be set with 1G and 2M, both
                  values will be set on the node by the performance-addon-operator.
                  It is important to notice that setting hugepages default size to
                  1G will remove all 2M related folders from the node and it will
                  be impossible to configure 2M hugepages under the node.
                properties:
                  defaultHugepagesSize:
                    description: DefaultHugePagesSize defines huge pages default size
                      under kernel boot parameters.
                    type: string
                  pages:
                    description: Pages defines huge pages that we want to allocate
                      at boot time.
                    items:
                      description: HugePage defines the number of allocated huge pages
                        of the specific size.
                      properties:
                        count:
                          description: Count defines amount of huge pages, maps to
                            the 'hugepages' kernel boot parameter.
                          format: int32
                          type: integer
                        node:
                          description: Node defines the NUMA node where hugepages
                            will be allocated, if not specified, pages will be allocated
                            equally between NUMA nodes
                          format: int32
                          type: integer
                        size:
                          description: Size defines huge page size, maps to the 'hugepagesz'
                            kernel boot parameter.
                          type: string
                      type: object
                    type: array
                type: object
              machineConfigLabel:
                additionalProperties:
                  type: string
                description: MachineConfigLabel defines the label to add to the MachineConfigs
                  the operator creates. It has to be used in the MachineConfigSelector
                  of the MachineConfigPool which targets this performance profile.
                  Defaults to "machineconfiguration.openshift.io/role=<same role as
                  in NodeSelector label key>"
                type: object
              machineConfigPoolSelector:
                additionalProperties:
                  type: string
                description: MachineConfigPoolSelector defines the MachineConfigPool
                  label to use in the MachineConfigPoolSelector of resources like
                  KubeletConfigs created by the operator. Defaults to "machineconfiguration.openshift.io/role=<same
                  role as in NodeSelector label key>"
                type: object
              net:
                description: Net defines a set of network related features
                properties:
                  devices:
                    description: Devices contains a list of network device representations
                      that will be set with a netqueue count equal to CPU.Reserved
                      . If no devices are specified then the default is all devices.
                    items:
                      description: 'Device defines a way to represent a network device
                        in several options: device name, vendor ID, model ID, PCI
                        path and MAC address'
                      properties:
                        deviceID:
                          description: Network device ID (model) represnted as a 16
                            bit hexmadecimal number.
                          type: string
                        interfaceName:
                          description: Network device name to be matched. It uses
                            a syntax of shell-style wildcards which are either positive
                            or negative.
                          type: string
                        vendorID:
                          description: Network device vendor ID represnted as a 16
                            bit Hexmadecimal number.
                          type: string
                      type: object
                    type: array
                  userLevelNetworking:
                    description: UserLevelNetworking when enabled - sets either all
                      or specified network devices queue size to the amount of reserved
                      CPUs. Defaults to "false".
                    type: boolean
                type: object
              nodeSelector:
                additionalProperties:
                  type: string
                description: 'NodeSelector defines the Node label to use in the NodeSelectors
                  of resources like Tuned created by the operator. It most likely
                  should, but does not have to match the node label in the NodeSelector
                  of the MachineConfigPool which targets this performance profile.
                  In the case when machineConfigLabels or machineConfigPoolSelector
                  are not set, we are expecting a certain NodeSelector format <domain>/<role>:
                  "" in order to be able to calculate the default values for the former
                  mentioned fields.'
                type: object
              numa:
                description: NUMA defines options related to topology aware affinities
                properties:
                  topologyPolicy:
                    description: Name of the policy applied when TopologyManager is
                      enabled Operator defaults to "best-effort"
                    type: string
                type: object
              realTimeKernel:
                description: RealTimeKernel defines a set of real time kernel related
                  parameters. RT kernel won't be installed when not set.
                properties:
                  enabled:
                    description: Enabled defines if the real time kernel packages
                      should be installed. Defaults to "false"
                    type: boolean
                type: object
              workloadHints:
                description: WorkloadHints defines hints for different types of workloads.
                  It will allow defining exact set of tuned and kernel arguments that
                  should be applied on top of the node.
                properties:
                  highPowerConsumption:
                    description: HighPowerConsumption defines if the node should be
                      configured in high power consumption mode. The flag will affect
                      the power consumption but will improve the CPUs latency.
                    type: boolean
                  perPodPowerManagement:
                    description: PerPodPowerManagement defines if the node should
                      be configured in per pod power management. PerPodPowerManagement
                      and HighPowerConsumption hints can not be enabled together.
                    type: boolean
                  realTime:
                    description: RealTime defines if the node should be configured
                      for the real time workload.
                    type: boolean
                type: object
            required:
            - cpu
            - nodeSelector
            type: object
          status:
            description: PerformanceProfileStatus defines the observed state of PerformanceProfile.
            properties:
              conditions:
                description: Conditions represents the latest available observations
                  of current state.
                items:
                  description: Condition represents the state of the operator's reconciliation
                    functionality.
                  properties:
                    lastHeartbeatTime:
                      format: date-time
                      type: string
                    lastTransitionTime:
                      format: date-time
                      type: string
                    message:
                      type: string
                    reason:
                      type: string
                    status:
                      type: string
                    type:
                      description: ConditionType is the state of the operator's reconciliation
                        functionality.
                      type: string
                  required:
                  - status
                  - type
                  type: object
                type: array
              runtimeClass:
                description: RuntimeClass contains the name of the RuntimeClass resource
                  created by the operator.
                type: string
              tuned:
                description: Tuned points to the Tuned custom resource object that
                  contains the tuning values generated by this operator.
                type: string
            type: object
        type: object
    served: true
    storage: false
    subresources:
      status: {}
  - deprecated: true
    deprecationWarning: v1alpha1 is deprecated and should be removed in the next release,
      use v2 instead
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: PerformanceProfile is the Schema for the performanceprofiles
          API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: PerformanceProfileSpec defines the desired state of PerformanceProfile.
            properties:
              additionalKernelArgs:
                description: Additional kernel arguments.
                items:
                  type: string
                type: array
              cpu:
                description: CPU defines a set of CPU related parameters.
                properties:
                  balanceIsolated:
                    description: BalanceIsolated toggles whether or not the Isolated
                      CPU set is eligible for load balancing work loads. When this
                      option is set to "false", the Isolated CPU set will be static,
                      meaning workloads have to explicitly assign each thread to a
                      specific cpu in order to work across multiple CPUs. Setting
                      this to "true" allows workloads to be balanced across CPUs.
                      Setting this to "false" offers the most predictable performance
                      for guaranteed workloads, but it offloads the complexity of
                      cpu load balancing to the application. Defaults to "true"
                    type: boolean
                  isolated:
                    description: 'Isolated defines a set of CPUs that will be used
                      to give to application threads the most execution time possible,
                      which means removing as many extraneous tasks off a CPU as possible.
                      It is important to notice the CPU manager can choose any CPU
                      to run the workload except the reserved CPUs. In order to guarantee
                      that your workload will run on the isolated CPU:   1. The union
                      of reserved CPUs and isolated CPUs should include all online
                      CPUs   2. The isolated CPUs field should be the complementary
                      to reserved CPUs field'
                    type: string
                  reserved:
                    description: Reserved defines a set of CPUs that will not be used
                      for any container workloads initiated by kubelet.
                    type: string
                type: object
              hugepages:
                description: HugePages defines a set of huge pages related parameters.
                  It is possible to set huge pages with multiple size values at the
                  same time. For example, hugepages can be set with 1G and 2M, both
                  values will be set on the node by the performance-addon-operator.
                  It is important to notice that setting hugepages default size to
                  1G will remove all 2M related folders from the node and it will
                  be impossible to configure 2M hugepages under the node.
                properties:
                  defaultHugepagesSize:
                    description: DefaultHugePagesSize defines huge pages default size
                      under kernel boot parameters.
                    type: string
                  pages:
                    description: Pages defines huge pages that we want to allocate
                      at boot time.
                    items:
                      description: HugePage defines the number of allocated huge pages
                        of the specific size.
                      properties:
                        count:
                          description: Count defines amount of huge pages, maps to
                            the 'hugepages' kernel boot parameter.
                          format: int32
                          type: integer
                        node:
                          description: Node defines the NUMA node where hugepages
                            will be allocated, if not specified, pages will be allocated
                            equally between NUMA nodes
                          format: int32
                          type: integer
                        size:
                          description: Size defines huge page size, maps to the 'hugepagesz'
                            kernel boot parameter.
                          type: string
                      type: object
                    type: array
                type: object
              machineConfigLabel:
                additionalProperties:
                  type: string
                description: MachineConfigLabel defines the label to add to the MachineConfigs
                  the operator creates. It has to be used in the MachineConfigSelector
                  of the MachineConfigPool which targets this performance profile.
                  Defaults to "machineconfiguration.openshift.io/role=<same role as
                  in NodeSelector label key>"
                type: object
              machineConfigPoolSelector:
                additionalProperties:
                  type: string
                description: MachineConfigPoolSelector defines the MachineConfigPool
                  label to use in the MachineConfigPoolSelector of resources like
                  KubeletConfigs created by the operator. Defaults to "machineconfiguration.openshift.io/role=<same
                  role as in NodeSelector label key>"
                type: object
              nodeSelector:
                additionalProperties:
                  type: string
                description: NodeSelector defines the Node label to use in the NodeSelectors
                  of resources like Tuned created by the operator. It most likely
                  should, but does not have to match the node label in the NodeSelector
                  of the MachineConfigPool which targets this performance profile.
                type: object
              numa:
                description: NUMA defines options related to topology aware affinities
                properties:
                  topologyPolicy:
                    description: Name of the policy applied when TopologyManager is
                      enabled Operator defaults to "best-effort"
                    type: string
                type: object
              realTimeKernel:
                description: RealTimeKernel defines a set of real time kernel related
                  parameters. RT kernel won't be installed when not set.
                properties:
                  enabled:
                    description: Enabled defines if the real time kernel packages
                      should be installed. Defaults to "false"
                    type: boolean
                type: object
            type: object
          status:
            description: PerformanceProfileStatus defines the observed state of PerformanceProfile.
            properties:
              conditions:
                description: Conditions represents the latest available observations
                  of current state.
                items:
                  description: Condition represents the state of the operator's reconciliation
                    functionality.
                  properties:
                    lastHeartbeatTime:
                      format: date-time
                      type: string
                    lastTransitionTime:
                      format: date-time
                      type: string
                    message:
                      type: string
                    reason:
                      type: string
                    status:
                      type: string
                    type:
                      description: ConditionType is the state of the operator's reconciliation
                        functionality.
                      type: string
                  required:
                  - status
                  - type
                  type: object
                type: array
              runtimeClass:
                description: RuntimeClass contains the name of the RuntimeClass resource
                  created by the operator.
                type: string
              tuned:
                description: Tuned points to the Tuned custom resource object that
                  contains the tuning values generated by this operator.
                type: string
            type: object
        type: object
    served: true
    storage: false
    subresources:
      status: {}
  - name: v2
    schema:
      openAPIV3Schema:
        description: PerformanceProfile is the Schema for the performanceprofiles
          API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: PerformanceProfileSpec defines the desired state of PerformanceProfile.
            properties:
              additionalKernelArgs:
                description: Additional kernel arguments.
                items:
                  type: string
                type: array
              cpu:
                description: CPU defines a set of CPU related parameters.
                properties:
                  balanceIsolated:
                    description: BalanceIsolated toggles whether or not the Isolated
                      CPU set is eligible for load balancing work loads. When this
                      option is set to "false", the Isolated CPU set will be static,
                      meaning workloads have to explicitly assign each thread to a
                      specific cpu in order to work across multiple CPUs. Setting
                      this to "true" allows workloads to be balanced across CPUs.
                      Setting this to "false" offers the most predictable performance
                      for guaranteed workloads, but it offloads the complexity of
                      cpu load balancing to the application. Defaults to "true"
                    type: boolean
                  isolated:
                    description: 'Isolated defines a set of CPUs that will be used
                      to give to application threads the most execution time possible,
                      which means removing as many extraneous tasks off a CPU as possible.
                      It is important to notice the CPU manager can choose any CPU
                      to run the workload except the reserved CPUs. In order to guarantee
                      that your workload will run on the isolated CPU:   1. The union
                      of reserved CPUs and isolated CPUs should include all online
                      CPUs   2. The isolated CPUs field should be the complementary
                      to reserved CPUs field'
                    type: string
                  offlined:
                    description: Offline defines a set of CPUs that will be unused
                      and set offline
                    type: string
                  reserved:
                    description: Reserved defines a set of CPUs that will not be used
                      for any container workloads initiated by kubelet.
                    type: string
                  shared:
                    description: Shared defines a set of CPUs that will be shared
                      among guaranteed workloads that needs additional cpus which
                      are not exclusive, alongside the isolated, exclusive resources
                      that are being used already by those workloads.
                    type: string
                required:
                - isolated
                - reserved
                type: object
              globallyDisableIrqLoadBalancing:
                description: GloballyDisableIrqLoadBalancing toggles whether IRQ load
                  balancing will be disabled for the Isolated CPU set. When the option
                  is set to "true" it disables IRQs load balancing for the Isolated
                  CPU set. Setting the option to "false" allows the IRQs to be balanced
                  across all CPUs, however the IRQs load balancing can be disabled
                  per pod CPUs when using irq-load-balancing.crio.io/cpu-quota.crio.io
                  annotations. Defaults to "false"
                type: boolean
              hardwareTuning:
                description: HardwareTuning defines a set of CPU frequencies for isolated
                  and reserved cpus.
                properties:
                  isolatedCpuFreq:
                    description: IsolatedCpuFreq defines a minimum frequency to be
                      set across isolated cpus
                    type: integer
                  reservedCpuFreq:
                    description: ReservedCpuFreq defines a maximum frequency to be
                      set across reserved cpus
                    type: integer
                type: object
              hugepages:
                description: HugePages defines a set of huge pages related parameters.
                  It is possible to set huge pages with multiple size values at the
                  same time. For example, hugepages can be set with 1G and 2M, both
                  values will be set on the node by the Performance Profile Controller.
                  It is important to notice that setting hugepages default size to
                  1G will remove all 2M related folders from the node and it will
                  be impossible to configure 2M hugepages under the node.
                properties:
                  defaultHugepagesSize:
                    description: DefaultHugePagesSize defines huge pages default size
                      under kernel boot parameters.
                    type: string
                  pages:
                    description: Pages defines huge pages that we want to allocate
                      at boot time.
                    items:
                      description: HugePage defines the number of allocated huge pages
                        of the specific size.
                      properties:
                        count:
                          description: Count defines amount of huge pages, maps to
                            the 'hugepages' kernel boot parameter.
                          format: int32
                          type: integer
                        node:
                          description: Node defines the NUMA node where hugepages
                            will be allocated, if not specified, pages will be allocated
                            equally between NUMA nodes
                          format: int32
                          type: integer
                        size:
                          description: Size defines huge page size, maps to the 'hugepagesz'
                            kernel boot parameter.
                          type: string
                      type: object
                    type: array
                type: object
              machineConfigLabel:
                additionalProperties:
                  type: string
                description: MachineConfigLabel defines the label to add to the MachineConfigs
                  the operator creates. It has to be used in the MachineConfigSelector
                  of the MachineConfigPool which targets this performance profile.
                  Defaults to "machineconfiguration.openshift.io/role=<same role as
                  in NodeSelector label key>"
                type: object
              machineConfigPoolSelector:
                additionalProperties:
                  type: string
                description: MachineConfigPoolSelector defines the MachineConfigPool
                  label to use in the MachineConfigPoolSelector of resources like
                  KubeletConfigs created by the operator. Defaults to "machineconfiguration.openshift.io/role=<same
                  role as in NodeSelector label key>"
                type: object
              net:
                description: Net defines a set of network related features
                properties:
                  devices:
                    description: Devices contains a list of network device representations
                      that will be set with a netqueue count equal to CPU.Reserved
                      . If no devices are specified then the default is all devices.
                    items:
                      description: 'Device defines a way to represent a network device
                        in several options: device name, vendor ID, model ID, PCI
                        path and MAC address'
                      properties:
                        deviceID:
                          description: Network device ID (model) represnted as a 16
                            bit hexmadecimal number.
                          type: string
                        interfaceName:
                          description: Network device name to be matched. It uses
                            a syntax of shell-style wildcards which are either positive
                            or negative.
                          type: string
                        vendorID:
                          description: Network device vendor ID represnted as a 16
                            bit Hexmadecimal number.
                          type: string
                      type: object
                    type: array
                  userLevelNetworking:
                    description: UserLevelNetworking when enabled - sets either all
                      or specified network devices queue size to the amount of reserved
                      CPUs. Defaults to "false".
                    type: boolean
                type: object
              nodeSelector:
                additionalProperties:
                  type: string
                description: 'NodeSelector defines the Node label to use in the NodeSelectors
                  of resources like Tuned created by the operator. It most likely
                  should, but does not have to match the node label in the NodeSelector
                  of the MachineConfigPool which targets this performance profile.
                  In the case when machineConfigLabels or machineConfigPoolSelector
                  are not set, we are expecting a certain NodeSelector format <domain>/<role>:
                  "" in order to be able to calculate the default values for the former
                  mentioned fields.'
                type: object
              numa:
                description: NUMA defines options related to topology aware affinities
                properties:
                  topologyPolicy:
                    description: Name of the policy applied when TopologyManager is
                      enabled Operator defaults to "best-effort"
                    type: string
                type: object
              realTimeKernel:
                description: RealTimeKernel defines a set of real time kernel related
                  parameters. RT kernel won't be installed when not set.
                properties:
                  enabled:
                    description: Enabled defines if the real time kernel packages
                      should be installed. Defaults to "false"
                    type: boolean
                type: object
              workloadHints:
                description: WorkloadHints defines hints for different types of workloads.
                  It will allow defining exact set of tuned and kernel arguments that
                  should be applied on top of the node.
                properties:
                  highPowerConsumption:
                    description: HighPowerConsumption defines if the node should be
                      configured in high power consumption mode. The flag will affect
                      the power consumption but will improve the CPUs latency. Defaults
                      to false.
                    type: boolean
                  mixedCpus:
                    description: MixedCpus enables the mixed-cpu-node-plugin on the
                      node. Defaults to false.
                    type: boolean
                  perPodPowerManagement:
                    description: PerPodPowerManagement defines if the node should
                      be configured in per pod power management. PerPodPowerManagement
                      and HighPowerConsumption hints can not be enabled together.
                      Defaults to false.
                    type: boolean
                  realTime:
                    description: RealTime defines if the node should be configured
                      for the real time workload. Defaults to true.
                    type: boolean
                type: object
            required:
            - cpu
            - nodeSelector
            type: object
          status:
            description: PerformanceProfileStatus defines the observed state of PerformanceProfile.
            properties:
              conditions:
                description: Conditions represents the latest available observations
                  of current state.
                items:
                  description: Condition represents the state of the operator's reconciliation
                    functionality.
                  properties:
                    lastHeartbeatTime:
                      format: date-time
                      type: string
                    lastTransitionTime:
                      format: date-time
                      type: string
                    message:
                      type: string
                    reason:
                      type: string
                    status:
                      type: string
                    type:
                      description: ConditionType is the state of the operator's reconciliation
                        functionality.
                      type: string
                  required:
                  - status
                  - type
                  type: object
                type: array
              runtimeClass:
                description: RuntimeClass contains the name of the RuntimeClass resource
                  created by the operator.
                type: string
              tuned:
                description: Tuned points to the Tuned custom resource object that
                  contains the tuning values generated by this operator.
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: PerformanceProfile
    listKind: PerformanceProfileList
    plural: performanceprofiles
    singular: performanceprofile
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:40Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:40Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v2
