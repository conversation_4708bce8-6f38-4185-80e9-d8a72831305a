---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.4
  creationTimestamp: "2025-06-25T14:57:16Z"
  generation: 3
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          f:controller-gen.kubebuilder.io/version: {}
        f:ownerReferences:
          k:{"uid":"e8b7620a-d5d2-448e-b2bf-6f6768b9b1dc"}: {}
      f:spec:
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-network-operator/operconfig
    operation: Apply
    time: "2025-08-01T12:18:14Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:57:16Z"
  name: egressfirewalls.k8s.ovn.org
  ownerReferences:
  - apiVersion: operator.openshift.io/v1
    blockOwnerDeletion: true
    controller: true
    kind: Network
    name: cluster
    uid: e8b7620a-d5d2-448e-b2bf-6f6768b9b1dc
  resourceVersion: "21352805"
  uid: b917582d-c3d0-454f-a914-c629c668c543
spec:
  conversion:
    strategy: None
  group: k8s.ovn.org
  names:
    kind: EgressFirewall
    listKind: EgressFirewallList
    plural: egressfirewalls
    singular: egressfirewall
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.status
      name: EgressFirewall Status
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: |-
          EgressFirewall describes the current egress firewall for a Namespace.
          Traffic from a pod to an IP address outside the cluster will be checked against
          each EgressFirewallRule in the pod's namespace's EgressFirewall, in
          order. If no rule matches (or no EgressFirewall is present) then the traffic
          will be allowed by default.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            properties:
              name:
                pattern: ^default$
                type: string
            type: object
          spec:
            description: Specification of the desired behavior of EgressFirewall.
            properties:
              egress:
                description: a collection of egress firewall rule objects
                items:
                  description: EgressFirewallRule is a single egressfirewall rule
                    object
                  properties:
                    ports:
                      description: ports specify what ports and protocols the rule
                        applies to
                      items:
                        description: EgressFirewallPort specifies the port to allow
                          or deny traffic to
                        properties:
                          port:
                            description: port that the traffic must match
                            format: int32
                            maximum: 65535
                            minimum: 1
                            type: integer
                          protocol:
                            description: protocol (tcp, udp, sctp) that the traffic
                              must match.
                            pattern: ^TCP|UDP|SCTP$
                            type: string
                        required:
                        - port
                        - protocol
                        type: object
                      type: array
                    to:
                      description: to is the target that traffic is allowed/denied
                        to
                      maxProperties: 1
                      minProperties: 1
                      properties:
                        cidrSelector:
                          description: cidrSelector is the CIDR range to allow/deny
                            traffic to. If this is set, dnsName and nodeSelector must
                            be unset.
                          type: string
                        dnsName:
                          description: |-
                            dnsName is the domain name to allow/deny traffic to. If this is set, cidrSelector and nodeSelector must be unset.
                            For a wildcard DNS name, the '*' will match only one label. Additionally, only a single '*' can be
                            used at the beginning of the wildcard DNS name. For example, '*.example.com' will match 'sub1.example.com'
                            but won't match 'sub2.sub1.example.com'.
                          pattern: ^(\*\.)?([A-Za-z0-9-]+\.)*[A-Za-z0-9-]+\.?$
                          type: string
                        nodeSelector:
                          description: |-
                            nodeSelector will allow/deny traffic to the Kubernetes node IP of selected nodes. If this is set,
                            cidrSelector and DNSName must be unset.
                          properties:
                            matchExpressions:
                              description: matchExpressions is a list of label selector
                                requirements. The requirements are ANDed.
                              items:
                                description: |-
                                  A label selector requirement is a selector that contains values, a key, and an operator that
                                  relates the key and values.
                                properties:
                                  key:
                                    description: key is the label key that the selector
                                      applies to.
                                    type: string
                                  operator:
                                    description: |-
                                      operator represents a key's relationship to a set of values.
                                      Valid operators are In, NotIn, Exists and DoesNotExist.
                                    type: string
                                  values:
                                    description: |-
                                      values is an array of string values. If the operator is In or NotIn,
                                      the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                      the values array must be empty. This array is replaced during a strategic
                                      merge patch.
                                    items:
                                      type: string
                                    type: array
                                    x-kubernetes-list-type: atomic
                                required:
                                - key
                                - operator
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            matchLabels:
                              additionalProperties:
                                type: string
                              description: |-
                                matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                map is equivalent to an element of matchExpressions, whose key field is "key", the
                                operator is "In", and the values array contains only "value". The requirements are ANDed.
                              type: object
                          type: object
                          x-kubernetes-map-type: atomic
                      type: object
                    type:
                      description: type marks this as an "Allow" or "Deny" rule
                      pattern: ^Allow|Deny$
                      type: string
                  required:
                  - to
                  - type
                  type: object
                type: array
            required:
            - egress
            type: object
          status:
            description: Observed status of EgressFirewall
            properties:
              messages:
                items:
                  type: string
                type: array
                x-kubernetes-list-type: set
              status:
                type: string
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: EgressFirewall
    listKind: EgressFirewallList
    plural: egressfirewalls
    singular: egressfirewall
  conditions:
  - lastTransitionTime: "2025-06-25T14:57:16Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:57:16Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
