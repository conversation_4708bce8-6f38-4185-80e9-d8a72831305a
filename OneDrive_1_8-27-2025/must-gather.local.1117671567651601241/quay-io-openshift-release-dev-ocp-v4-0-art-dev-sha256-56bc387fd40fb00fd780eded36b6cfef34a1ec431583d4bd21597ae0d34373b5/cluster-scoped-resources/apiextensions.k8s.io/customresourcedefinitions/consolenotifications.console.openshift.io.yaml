---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/481
    api.openshift.io/merged-by-featuregates: "true"
    capability.openshift.io/name: Console
    description: Extension for configuring openshift web console notifications.
    displayName: ConsoleNotification
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:53:45Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:45Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:capability.openshift.io/name: {}
          f:description: {}
          f:displayName: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:42:16Z"
  name: consolenotifications.console.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "144766"
  uid: 2796ce0f-ad1b-40de-84f9-e23004ac9664
spec:
  conversion:
    strategy: None
  group: console.openshift.io
  names:
    kind: ConsoleNotification
    listKind: ConsoleNotificationList
    plural: consolenotifications
    singular: consolenotification
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.text
      name: Text
      type: string
    - jsonPath: .spec.location
      name: Location
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1
    schema:
      openAPIV3Schema:
        description: "ConsoleNotification is the extension for configuring openshift
          web console notifications. \n Compatibility level 2: Stable within a major
          release for a minimum of 9 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: ConsoleNotificationSpec is the desired console notification
              configuration.
            properties:
              backgroundColor:
                description: backgroundColor is the color of the background for the
                  notification as CSS data type color.
                type: string
              color:
                description: color is the color of the text for the notification as
                  CSS data type color.
                type: string
              link:
                description: link is an object that holds notification link details.
                properties:
                  href:
                    description: href is the absolute secure URL for the link (must
                      use https)
                    pattern: ^https://
                    type: string
                  text:
                    description: text is the display text for the link
                    type: string
                required:
                - href
                - text
                type: object
              location:
                description: 'location is the location of the notification in the
                  console. Valid values are: "BannerTop", "BannerBottom", "BannerTopBottom".'
                pattern: ^(BannerTop|BannerBottom|BannerTopBottom)$
                type: string
              text:
                description: text is the visible text of the notification.
                type: string
            required:
            - text
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ConsoleNotification
    listKind: ConsoleNotificationList
    plural: consolenotifications
    singular: consolenotification
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:45Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:45Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
