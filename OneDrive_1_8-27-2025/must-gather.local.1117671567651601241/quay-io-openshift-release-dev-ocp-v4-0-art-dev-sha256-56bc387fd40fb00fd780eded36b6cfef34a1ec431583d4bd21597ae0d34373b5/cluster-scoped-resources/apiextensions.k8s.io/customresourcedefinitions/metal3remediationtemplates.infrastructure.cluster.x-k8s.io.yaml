---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    exclude.release.openshift.io/internal-openshift-hosted: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:54:03Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:exclude.release.openshift.io/internal-openshift-hosted: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:categories: {}
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T14:54:03Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:categories: {}
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:54:03Z"
  name: metal3remediationtemplates.infrastructure.cluster.x-k8s.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "1934"
  uid: 45de3512-5c9d-4344-b275-75062a315110
spec:
  conversion:
    strategy: None
  group: infrastructure.cluster.x-k8s.io
  names:
    categories:
    - cluster-api
    kind: Metal3RemediationTemplate
    listKind: Metal3RemediationTemplateList
    plural: metal3remediationtemplates
    shortNames:
    - m3rt
    - m3remediationtemplate
    - m3remediationtemplates
    - metal3rt
    - metal3remediationtemplate
    singular: metal3remediationtemplate
  scope: Namespaced
  versions:
  - name: v1alpha5
    schema:
      openAPIV3Schema:
        description: Metal3RemediationTemplate is the Schema for the metal3remediationtemplates
          API.
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: Metal3RemediationTemplateSpec defines the desired state of
              Metal3RemediationTemplate.
            properties:
              template:
                description: Metal3RemediationTemplateResource describes the data
                  needed to create a Metal3Remediation from a template.
                properties:
                  spec:
                    description: Spec is the specification of the desired behavior
                      of the Metal3Remediation.
                    properties:
                      strategy:
                        description: Strategy field defines remediation strategy.
                        properties:
                          retryLimit:
                            description: Sets maximum number of remediation retries.
                            type: integer
                          timeout:
                            description: Sets the timeout between remediation retries.
                            type: string
                          type:
                            description: Type of remediation.
                            type: string
                        type: object
                    type: object
                required:
                - spec
                type: object
            required:
            - template
            type: object
          status:
            description: Metal3RemediationTemplateStatus defines the observed state
              of Metal3RemediationTemplate.
            properties:
              status:
                description: Metal3RemediationStatus defines the observed state of
                  Metal3Remediation
                properties:
                  lastRemediated:
                    description: LastRemediated identifies when the host was last
                      remediated
                    format: date-time
                    type: string
                  phase:
                    description: Phase represents the current phase of machine remediation.
                      E.g. Pending, Running, Done etc.
                    type: string
                  retryCount:
                    description: RetryCount can be used as a counter during the remediation.
                      Field can hold number of reboots etc.
                    type: integer
                type: object
            required:
            - status
            type: object
        type: object
    served: true
    storage: false
    subresources:
      status: {}
  - name: v1beta1
    schema:
      openAPIV3Schema:
        description: Metal3RemediationTemplate is the Schema for the metal3remediationtemplates
          API.
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: Metal3RemediationTemplateSpec defines the desired state of
              Metal3RemediationTemplate.
            properties:
              template:
                description: Metal3RemediationTemplateResource describes the data
                  needed to create a Metal3Remediation from a template.
                properties:
                  spec:
                    description: Spec is the specification of the desired behavior
                      of the Metal3Remediation.
                    properties:
                      strategy:
                        description: Strategy field defines remediation strategy.
                        properties:
                          retryLimit:
                            description: Sets maximum number of remediation retries.
                            type: integer
                          timeout:
                            description: Sets the timeout between remediation retries.
                            type: string
                          type:
                            description: Type of remediation.
                            type: string
                        type: object
                    type: object
                required:
                - spec
                type: object
            required:
            - template
            type: object
          status:
            description: Metal3RemediationTemplateStatus defines the observed state
              of Metal3RemediationTemplate.
            properties:
              status:
                description: Metal3RemediationStatus defines the observed state of
                  Metal3Remediation
                properties:
                  lastRemediated:
                    description: LastRemediated identifies when the host was last
                      remediated
                    format: date-time
                    type: string
                  phase:
                    description: Phase represents the current phase of machine remediation.
                      E.g. Pending, Running, Done etc.
                    type: string
                  retryCount:
                    description: RetryCount can be used as a counter during the remediation.
                      Field can hold number of reboots etc.
                    type: integer
                type: object
            required:
            - status
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    categories:
    - cluster-api
    kind: Metal3RemediationTemplate
    listKind: Metal3RemediationTemplateList
    plural: metal3remediationtemplates
    shortNames:
    - m3rt
    - m3remediationtemplate
    - m3remediationtemplates
    - metal3rt
    - metal3remediationtemplate
    singular: metal3remediationtemplate
  conditions:
  - lastTransitionTime: "2025-06-25T14:54:03Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:54:03Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1beta1
