---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/851
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:53:44Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:44Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:50:40Z"
  name: egressrouters.network.operator.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "155461"
  uid: a5d68dee-4b39-4002-b7ae-06425a6c3d99
spec:
  conversion:
    strategy: None
  group: network.operator.openshift.io
  names:
    kind: EgressRouter
    listKind: EgressRouterList
    plural: egressrouters
    singular: egressrouter
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.conditions[*].type
      name: Condition
      type: string
    - jsonPath: .status.conditions[*].status
      name: Status
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: "EgressRouter is a feature allowing the user to define an egress
          router that acts as a bridge between pods and external systems. The egress
          router runs a service that redirects egress traffic originating from a pod
          or a group of pods to a remote external system or multiple destinations
          as per configuration. \n It is consumed by the cluster-network-operator.
          More specifically, given an EgressRouter CR with <name>, the CNO will create
          and manage: - A service called <name> - An egress pod called <name> - A
          NAD called <name> \n Compatibility level 1: Stable within a major release
          for a minimum of 12 months or 3 minor releases (whichever is longer). \n
          EgressRouter is a single egressrouter pod configuration object."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: Specification of the desired egress router.
            oneOf:
            - properties:
                mode:
                  enum:
                  - Redirect
              required:
              - redirect
            properties:
              addresses:
                description: List of IP addresses to configure on the pod's secondary
                  interface.
                items:
                  description: EgressRouterAddress contains a pair of IP CIDR and
                    gateway to be configured on the router's interface
                  properties:
                    gateway:
                      anyOf:
                      - format: ipv4
                      - format: ipv6
                      description: IP address of the next-hop gateway, if it cannot
                        be automatically determined. Can be IPv4 or IPv6.
                      type: string
                    ip:
                      description: IP is the address to configure on the router's
                        interface. Can be IPv4 or IPv6.
                      type: string
                  required:
                  - ip
                  type: object
                type: array
              mode:
                default: Redirect
                description: Mode depicts the mode that is used for the egress router.
                  The default mode is "Redirect" and is the only supported mode currently.
                enum:
                - Redirect
                type: string
              networkInterface:
                default:
                  macvlan:
                    mode: Bridge
                description: Specification of interface to create/use. The default
                  is macvlan. Currently only macvlan is supported.
                oneOf:
                - required:
                  - macvlan
                properties:
                  macvlan:
                    default:
                      mode: Bridge
                    description: Arguments specific to the interfaceType macvlan
                    properties:
                      master:
                        description: Name of the master interface. Need not be specified
                          if it can be inferred from the IP address.
                        type: string
                      mode:
                        default: Bridge
                        description: Mode depicts the mode that is used for the macvlan
                          interface; one of Bridge|Private|VEPA|Passthru. The default
                          mode is "Bridge".
                        enum:
                        - Bridge
                        - Private
                        - VEPA
                        - Passthru
                        type: string
                    required:
                    - mode
                    type: object
                type: object
              redirect:
                description: Redirect represents the configuration parameters specific
                  to redirect mode.
                properties:
                  fallbackIP:
                    anyOf:
                    - format: ipv4
                    - format: ipv6
                    description: FallbackIP specifies the remote destination's IP
                      address. Can be IPv4 or IPv6. If no redirect rules are specified,
                      all traffic from the router are redirected to this IP. If redirect
                      rules are specified, then any connections on any other port
                      (undefined in the rules) on the router will be redirected to
                      this IP. If redirect rules are specified and no fallback IP
                      is provided, connections on other ports will simply be rejected.
                    type: string
                  redirectRules:
                    description: List of L4RedirectRules that define the DNAT redirection
                      from the pod to the destination in redirect mode.
                    items:
                      description: L4RedirectRule defines a DNAT redirection from
                        a given port to a destination IP and port.
                      properties:
                        destinationIP:
                          anyOf:
                          - format: ipv4
                          - format: ipv6
                          description: IP specifies the remote destination's IP address.
                            Can be IPv4 or IPv6.
                          type: string
                        port:
                          description: Port is the port number to which clients should
                            send traffic to be redirected.
                          format: int32
                          maximum: 65535
                          minimum: 1
                          type: integer
                        protocol:
                          description: Protocol can be TCP, SCTP or UDP.
                          enum:
                          - TCP
                          - UDP
                          - SCTP
                          type: string
                        targetPort:
                          description: TargetPort allows specifying the port number
                            on the remote destination to which the traffic gets redirected
                            to. If unspecified, the value from "Port" is used.
                          format: int32
                          maximum: 65535
                          minimum: 1
                          type: integer
                      required:
                      - destinationIP
                      - port
                      - protocol
                      type: object
                    type: array
                type: object
            required:
            - addresses
            - mode
            - networkInterface
            type: object
          status:
            description: Observed status of EgressRouter.
            properties:
              conditions:
                description: Observed status of the egress router
                items:
                  description: EgressRouterStatusCondition represents the state of
                    the egress router's managed and monitored components.
                  properties:
                    lastTransitionTime:
                      description: LastTransitionTime is the time of the last update
                        to the current status property.
                      format: date-time
                      nullable: true
                      type: string
                    message:
                      description: Message provides additional information about the
                        current condition. This is only to be consumed by humans.  It
                        may contain Line Feed characters (U+000A), which should be
                        rendered as new lines.
                      type: string
                    reason:
                      description: Reason is the CamelCase reason for the condition's
                        current status.
                      type: string
                    status:
                      description: Status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: Type specifies the aspect reported by this condition;
                        one of Available, Progressing, Degraded
                      enum:
                      - Available
                      - Progressing
                      - Degraded
                      type: string
                  required:
                  - lastTransitionTime
                  - status
                  - type
                  type: object
                type: array
            required:
            - conditions
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: EgressRouter
    listKind: EgressRouterList
    plural: egressrouters
    singular: egressrouter
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:44Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:44Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
