---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/481
    api.openshift.io/merged-by-featuregates: "true"
    capability.openshift.io/name: Console
    description: Extension for configuring openshift web console YAML samples.
    displayName: ConsoleYAMLSample
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:53:49Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:49Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:capability.openshift.io/name: {}
          f:description: {}
          f:displayName: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:42:21Z"
  name: consoleyamlsamples.console.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "144877"
  uid: f6a698a7-469d-4c81-a098-2e9a8cbcc538
spec:
  conversion:
    strategy: None
  group: console.openshift.io
  names:
    kind: ConsoleYAMLSample
    listKind: ConsoleYAMLSampleList
    plural: consoleyamlsamples
    singular: consoleyamlsample
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "ConsoleYAMLSample is an extension for customizing OpenShift
          web console YAML samples. \n Compatibility level 2: Stable within a major
          release for a minimum of 9 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: ConsoleYAMLSampleSpec is the desired YAML sample configuration.
              Samples will appear with their descriptions in a samples sidebar when
              creating a resources in the web console.
            properties:
              description:
                description: description of the YAML sample.
                pattern: ^(.|\s)*\S(.|\s)*$
                type: string
              snippet:
                description: snippet indicates that the YAML sample is not the full
                  YAML resource definition, but a fragment that can be inserted into
                  the existing YAML document at the user's cursor.
                type: boolean
              targetResource:
                description: targetResource contains apiVersion and kind of the resource
                  YAML sample is representating.
                properties:
                  apiVersion:
                    description: 'APIVersion defines the versioned schema of this
                      representation of an object. Servers should convert recognized
                      schemas to the latest internal value, and may reject unrecognized
                      values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
                    type: string
                  kind:
                    description: 'Kind is a string value representing the REST resource
                      this object represents. Servers may infer this from the endpoint
                      the client submits requests to. Cannot be updated. In CamelCase.
                      More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
                    type: string
                type: object
              title:
                description: title of the YAML sample.
                pattern: ^(.|\s)*\S(.|\s)*$
                type: string
              yaml:
                description: yaml is the YAML sample to display.
                pattern: ^(.|\s)*\S(.|\s)*$
                type: string
            required:
            - description
            - targetResource
            - title
            - yaml
            type: object
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true
status:
  acceptedNames:
    kind: ConsoleYAMLSample
    listKind: ConsoleYAMLSampleList
    plural: consoleyamlsamples
    singular: consoleyamlsample
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:49Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:49Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
