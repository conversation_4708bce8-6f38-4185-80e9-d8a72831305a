---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.18.0
    operatorframework.io/installed-alongside-b3bf724166efcfbc: openshift-gitops-operator/openshift-gitops-operator.v1.16.2
    operatorframework.io/installed-alongside-bd3517416c857c29: openshift-gitops-operator/openshift-gitops-operator.v1.17.0
  creationTimestamp: "2025-07-01T14:02:37Z"
  generation: 1
  labels:
    olm.managed: "true"
    operators.coreos.com/openshift-gitops-operator.openshift-gitops-operator: ""
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-07-01T14:02:37Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:controller-gen.kubebuilder.io/version: {}
          f:operatorframework.io/installed-alongside-b3bf724166efcfbc: {}
          f:operatorframework.io/installed-alongside-bd3517416c857c29: {}
        f:labels:
          .: {}
          f:olm.managed: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: catalog
    operation: Update
    time: "2025-08-07T15:05:18Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:labels:
          f:operators.coreos.com/openshift-gitops-operator.openshift-gitops-operator: {}
    manager: olm
    operation: Update
    time: "2025-08-07T15:05:32Z"
  name: notificationsconfigurations.argoproj.io
  resourceVersion: "25002134"
  uid: 009f8963-fa18-4581-8754-3f321759f671
spec:
  conversion:
    strategy: None
  group: argoproj.io
  names:
    kind: NotificationsConfiguration
    listKind: NotificationsConfigurationList
    plural: notificationsconfigurations
    singular: notificationsconfiguration
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: NotificationsConfiguration is the Schema for the NotificationsConfiguration
          API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: |-
              NotificationsConfigurationSpec allows users to define the triggers, templates, services, context and
              subscriptions for the notifications
            properties:
              context:
                additionalProperties:
                  type: string
                description: Context is used to define some shared context between
                  all notification templates
                type: object
              services:
                additionalProperties:
                  type: string
                description: Services are used to deliver message
                type: object
              subscriptions:
                additionalProperties:
                  type: string
                description: Subscriptions contain centrally managed global application
                  subscriptions
                type: object
              templates:
                additionalProperties:
                  type: string
                description: Templates are used to generate the notification template
                  message
                type: object
              triggers:
                additionalProperties:
                  type: string
                description: |-
                  Triggers define the condition when the notification should be sent and list of templates required to generate the message
                  Recipients can subscribe to the trigger and specify the required message template and destination notification service.
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: NotificationsConfiguration
    listKind: NotificationsConfigurationList
    plural: notificationsconfigurations
    singular: notificationsconfiguration
  conditions:
  - lastTransitionTime: "2025-07-01T14:02:37Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-07-01T14:02:37Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1alpha1
