---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/1453
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:53:54Z"
  generation: 2
  labels:
    openshift.io/operator-managed: ""
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:54Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:labels:
          .: {}
          f:openshift.io/operator-managed: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T12:25:54Z"
  name: containerruntimeconfigs.machineconfiguration.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21359998"
  uid: 71ba7a05-bba8-4fff-be5d-c7774fdeae2c
spec:
  conversion:
    strategy: None
  group: machineconfiguration.openshift.io
  names:
    kind: ContainerRuntimeConfig
    listKind: ContainerRuntimeConfigList
    plural: containerruntimeconfigs
    shortNames:
    - ctrcfg
    singular: containerruntimeconfig
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "ContainerRuntimeConfig describes a customized Container Runtime
          configuration. \n Compatibility level 1: Stable within a major release for
          a minimum of 12 months or 3 minor releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: ContainerRuntimeConfigSpec defines the desired state of ContainerRuntimeConfig
            properties:
              containerRuntimeConfig:
                description: ContainerRuntimeConfiguration defines the tuneables of
                  the container runtime
                properties:
                  defaultRuntime:
                    description: defaultRuntime is the name of the OCI runtime to
                      be used as the default.
                    type: string
                  logLevel:
                    description: logLevel specifies the verbosity of the logs based
                      on the level it is set to. Options are fatal, panic, error,
                      warn, info, and debug.
                    type: string
                  logSizeMax:
                    anyOf:
                    - type: integer
                    - type: string
                    description: logSizeMax specifies the Maximum size allowed for
                      the container log file. Negative numbers indicate that no size
                      limit is imposed. If it is positive, it must be >= 8192 to match/exceed
                      conmon's read buffer.
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                  overlaySize:
                    anyOf:
                    - type: integer
                    - type: string
                    description: 'overlaySize specifies the maximum size of a container
                      image. This flag can be used to set quota on the size of container
                      images. (default: 10GB)'
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                  pidsLimit:
                    description: pidsLimit specifies the maximum number of processes
                      allowed in a container
                    format: int64
                    type: integer
                type: object
              machineConfigPoolSelector:
                description: MachineConfigPoolSelector selects which pools the ContainerRuntimeConfig
                  shoud apply to. A nil selector will result in no pools being selected.
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: A label selector requirement is a selector that
                        contains values, a key, and an operator that relates the key
                        and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: operator represents a key's relationship to
                            a set of values. Valid operators are In, NotIn, Exists
                            and DoesNotExist.
                          type: string
                        values:
                          description: values is an array of string values. If the
                            operator is In or NotIn, the values array must be non-empty.
                            If the operator is Exists or DoesNotExist, the values
                            array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: matchLabels is a map of {key,value} pairs. A single
                      {key,value} in the matchLabels map is equivalent to an element
                      of matchExpressions, whose key field is "key", the operator
                      is "In", and the values array contains only "value". The requirements
                      are ANDed.
                    type: object
                type: object
                x-kubernetes-map-type: atomic
            required:
            - containerRuntimeConfig
            type: object
          status:
            description: ContainerRuntimeConfigStatus defines the observed state of
              a ContainerRuntimeConfig
            properties:
              conditions:
                description: conditions represents the latest available observations
                  of current state.
                items:
                  description: ContainerRuntimeConfigCondition defines the state of
                    the ContainerRuntimeConfig
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the time of the last update
                        to the current status object.
                      format: date-time
                      nullable: true
                      type: string
                    message:
                      description: message provides additional information about the
                        current condition. This is only to be consumed by humans.
                      type: string
                    reason:
                      description: reason is the reason for the condition's last transition.  Reasons
                        are PascalCase
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      type: string
                    type:
                      description: type specifies the state of the operator's reconciliation
                        functionality.
                      type: string
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              observedGeneration:
                description: observedGeneration represents the generation observed
                  by the controller.
                format: int64
                type: integer
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ContainerRuntimeConfig
    listKind: ContainerRuntimeConfigList
    plural: containerruntimeconfigs
    shortNames:
    - ctrcfg
    singular: containerruntimeconfig
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:54Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:54Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
