---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/470
    api.openshift.io/merged-by-featuregates: "true"
    capability.openshift.io/name: marketplace
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:53:35Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:35Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:capability.openshift.io/name: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:19:44Z"
  name: operatorhubs.config.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "133557"
  uid: dddec749-0fae-44f7-9adc-2088c844a1c1
spec:
  conversion:
    strategy: None
  group: config.openshift.io
  names:
    kind: OperatorHub
    listKind: OperatorHubList
    plural: operatorhubs
    singular: operatorhub
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: "OperatorHub is the Schema for the operatorhubs API. It can be
          used to change the state of the default hub sources for OperatorHub on the
          cluster from enabled to disabled and vice versa. \n Compatibility level
          1: Stable within a major release for a minimum of 12 months or 3 minor releases
          (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: OperatorHubSpec defines the desired state of OperatorHub
            properties:
              disableAllDefaultSources:
                description: disableAllDefaultSources allows you to disable all the
                  default hub sources. If this is true, a specific entry in sources
                  can be used to enable a default source. If this is false, a specific
                  entry in sources can be used to disable or enable a default source.
                type: boolean
              sources:
                description: sources is the list of default hub sources and their
                  configuration. If the list is empty, it implies that the default
                  hub sources are enabled on the cluster unless disableAllDefaultSources
                  is true. If disableAllDefaultSources is true and sources is not
                  empty, the configuration present in sources will take precedence.
                  The list of default hub sources and their current state will always
                  be reflected in the status block.
                items:
                  description: HubSource is used to specify the hub source and its
                    configuration
                  properties:
                    disabled:
                      description: disabled is used to disable a default hub source
                        on cluster
                      type: boolean
                    name:
                      description: name is the name of one of the default hub sources
                      maxLength: 253
                      minLength: 1
                      type: string
                  type: object
                type: array
            type: object
          status:
            description: OperatorHubStatus defines the observed state of OperatorHub.
              The current state of the default hub sources will always be reflected
              here.
            properties:
              sources:
                description: sources encapsulates the result of applying the configuration
                  for each hub source
                items:
                  description: HubSourceStatus is used to reflect the current state
                    of applying the configuration to a default source
                  properties:
                    disabled:
                      description: disabled is used to disable a default hub source
                        on cluster
                      type: boolean
                    message:
                      description: message provides more information regarding failures
                      type: string
                    name:
                      description: name is the name of one of the default hub sources
                      maxLength: 253
                      minLength: 1
                      type: string
                    status:
                      description: status indicates success or failure in applying
                        the configuration
                      type: string
                  type: object
                type: array
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: OperatorHub
    listKind: OperatorHubList
    plural: operatorhubs
    singular: operatorhub
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:35Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:35Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
