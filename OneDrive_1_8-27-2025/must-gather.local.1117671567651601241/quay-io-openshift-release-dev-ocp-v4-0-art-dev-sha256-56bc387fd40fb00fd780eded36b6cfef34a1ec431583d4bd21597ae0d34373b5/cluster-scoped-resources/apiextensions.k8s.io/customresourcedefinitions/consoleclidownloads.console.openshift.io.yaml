---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/481
    api.openshift.io/merged-by-featuregates: "true"
    capability.openshift.io/name: Console
    description: Extension for configuring openshift web console command line interface
      (CLI) downloads.
    displayName: ConsoleCLIDownload
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:53:40Z"
  generation: 1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:40Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:capability.openshift.io/name: {}
          f:description: {}
          f:displayName: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-06-25T18:42:12Z"
  name: consoleclidownloads.console.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "144707"
  uid: 302476d6-1a8a-424d-b8f0-021579833695
spec:
  conversion:
    strategy: None
  group: console.openshift.io
  names:
    kind: ConsoleCLIDownload
    listKind: ConsoleCLIDownloadList
    plural: consoleclidownloads
    singular: consoleclidownload
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.displayName
      name: Display name
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: "ConsoleCLIDownload is an extension for configuring openshift
          web console command line interface (CLI) downloads. \n Compatibility level
          2: Stable within a major release for a minimum of 9 months or 3 minor releases
          (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: ConsoleCLIDownloadSpec is the desired cli download configuration.
            properties:
              description:
                description: description is the description of the CLI download (can
                  include markdown).
                type: string
              displayName:
                description: displayName is the display name of the CLI download.
                type: string
              links:
                description: links is a list of objects that provide CLI download
                  link details.
                items:
                  properties:
                    href:
                      description: href is the absolute secure URL for the link (must
                        use https)
                      pattern: ^https://
                      type: string
                    text:
                      description: text is the display text for the link
                      type: string
                  required:
                  - href
                  type: object
                type: array
            required:
            - description
            - displayName
            - links
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ConsoleCLIDownload
    listKind: ConsoleCLIDownloadList
    plural: consoleclidownloads
    singular: consoleclidownload
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:40Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:40Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
