---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    capability.openshift.io/name: OperatorLifecycleManager
    controller-gen.kubebuilder.io/version: v0.15.0
    include.release.openshift.io/hypershift: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
  creationTimestamp: "2025-06-25T14:53:51Z"
  generation: 2
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:categories: {}
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:51Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:capability.openshift.io/name: {}
          f:controller-gen.kubebuilder.io/version: {}
          f:include.release.openshift.io/hypershift: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:categories: {}
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T12:09:09Z"
  name: operators.operators.coreos.com
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21340966"
  uid: 1ffdb403-2916-4abf-836a-a8e0a1fb78ec
spec:
  conversion:
    strategy: None
  group: operators.coreos.com
  names:
    categories:
    - olm
    kind: Operator
    listKind: OperatorList
    plural: operators
    singular: operator
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: Operator represents a cluster operator.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: OperatorSpec defines the desired state of Operator
            type: object
          status:
            description: OperatorStatus defines the observed state of an Operator
              and its components
            properties:
              components:
                description: Components describes resources that compose the operator.
                properties:
                  labelSelector:
                    description: LabelSelector is a label query over a set of resources
                      used to select the operator's components
                    properties:
                      matchExpressions:
                        description: matchExpressions is a list of label selector
                          requirements. The requirements are ANDed.
                        items:
                          description: |-
                            A label selector requirement is a selector that contains values, a key, and an operator that
                            relates the key and values.
                          properties:
                            key:
                              description: key is the label key that the selector
                                applies to.
                              type: string
                            operator:
                              description: |-
                                operator represents a key's relationship to a set of values.
                                Valid operators are In, NotIn, Exists and DoesNotExist.
                              type: string
                            values:
                              description: |-
                                values is an array of string values. If the operator is In or NotIn,
                                the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                the values array must be empty. This array is replaced during a strategic
                                merge patch.
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          required:
                          - key
                          - operator
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      matchLabels:
                        additionalProperties:
                          type: string
                        description: |-
                          matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                          map is equivalent to an element of matchExpressions, whose key field is "key", the
                          operator is "In", and the values array contains only "value". The requirements are ANDed.
                        type: object
                    type: object
                    x-kubernetes-map-type: atomic
                  refs:
                    description: Refs are a set of references to the operator's component
                      resources, selected with LabelSelector.
                    items:
                      description: RichReference is a reference to a resource, enriched
                        with its status conditions.
                      properties:
                        apiVersion:
                          description: API version of the referent.
                          type: string
                        conditions:
                          description: Conditions represents the latest state of the
                            component.
                          items:
                            description: Condition represent the latest available
                              observations of an component's state.
                            properties:
                              lastTransitionTime:
                                description: Last time the condition transitioned
                                  from one status to another.
                                format: date-time
                                type: string
                              lastUpdateTime:
                                description: Last time the condition was probed
                                format: date-time
                                type: string
                              message:
                                description: A human readable message indicating details
                                  about the transition.
                                type: string
                              reason:
                                description: The reason for the condition's last transition.
                                type: string
                              status:
                                description: Status of the condition, one of True,
                                  False, Unknown.
                                type: string
                              type:
                                description: Type of condition.
                                type: string
                            required:
                            - status
                            - type
                            type: object
                          type: array
                        fieldPath:
                          description: |-
                            If referring to a piece of an object instead of an entire object, this string
                            should contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].
                            For example, if the object reference is to a container within a pod, this would take on a value like:
                            "spec.containers{name}" (where "name" refers to the name of the container that triggered
                            the event) or if no container name is specified "spec.containers[2]" (container with
                            index 2 in this pod). This syntax is chosen only to have some well-defined way of
                            referencing a part of an object.
                            TODO: this design is not final and this field is subject to change in the future.
                          type: string
                        kind:
                          description: |-
                            Kind of the referent.
                            More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
                          type: string
                        name:
                          description: |-
                            Name of the referent.
                            More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                          type: string
                        namespace:
                          description: |-
                            Namespace of the referent.
                            More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/
                          type: string
                        resourceVersion:
                          description: |-
                            Specific resourceVersion to which this reference is made, if any.
                            More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
                          type: string
                        uid:
                          description: |-
                            UID of the referent.
                            More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids
                          type: string
                      type: object
                      x-kubernetes-map-type: atomic
                    type: array
                required:
                - labelSelector
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    categories:
    - olm
    kind: Operator
    listKind: OperatorList
    plural: operators
    singular: operator
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:51Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:51Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
