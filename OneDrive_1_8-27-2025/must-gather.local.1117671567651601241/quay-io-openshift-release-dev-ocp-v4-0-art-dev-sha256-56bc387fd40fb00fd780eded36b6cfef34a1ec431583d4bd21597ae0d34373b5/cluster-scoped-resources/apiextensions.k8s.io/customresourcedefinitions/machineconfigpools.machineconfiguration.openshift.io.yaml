---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/1453
    api.openshift.io/merged-by-featuregates: "true"
    include.release.openshift.io/ibm-cloud-managed: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
    release.openshift.io/feature-set: Default
  creationTimestamp: "2025-06-25T14:53:57Z"
  generation: 3
  labels:
    openshift.io/operator-managed: ""
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:53:57Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:include.release.openshift.io/ibm-cloud-managed: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
          f:release.openshift.io/feature-set: {}
        f:labels:
          .: {}
          f:openshift.io/operator-managed: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T12:25:54Z"
  name: machineconfigpools.machineconfiguration.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21360002"
  uid: adcd1622-1a74-47b2-bf35-b87b0967d97e
spec:
  conversion:
    strategy: None
  group: machineconfiguration.openshift.io
  names:
    kind: MachineConfigPool
    listKind: MachineConfigPoolList
    plural: machineconfigpools
    shortNames:
    - mcp
    singular: machineconfigpool
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.configuration.name
      name: Config
      type: string
    - description: When all the machines in the pool are updated to the correct machine
        config.
      jsonPath: .status.conditions[?(@.type=="Updated")].status
      name: Updated
      type: string
    - description: When at least one of machine is not either not updated or is in
        the process of updating to the desired machine config.
      jsonPath: .status.conditions[?(@.type=="Updating")].status
      name: Updating
      type: string
    - description: When progress is blocked on updating one or more nodes or the pool
        configuration is failing.
      jsonPath: .status.conditions[?(@.type=="Degraded")].status
      name: Degraded
      type: string
    - description: Total number of machines in the machine config pool
      jsonPath: .status.machineCount
      name: MachineCount
      type: number
    - description: Total number of ready machines targeted by the pool
      jsonPath: .status.readyMachineCount
      name: ReadyMachineCount
      type: number
    - description: Total number of machines targeted by the pool that have the CurrentMachineConfig
        as their config
      jsonPath: .status.updatedMachineCount
      name: UpdatedMachineCount
      type: number
    - description: Total number of machines marked degraded (or unreconcilable)
      jsonPath: .status.degradedMachineCount
      name: DegradedMachineCount
      type: number
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1
    schema:
      openAPIV3Schema:
        description: "MachineConfigPool describes a pool of MachineConfigs. \n Compatibility
          level 1: Stable within a major release for a minimum of 12 months or 3 minor
          releases (whichever is longer)."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: MachineConfigPoolSpec is the spec for MachineConfigPool resource.
            properties:
              configuration:
                description: The targeted MachineConfig object for the machine config
                  pool.
                properties:
                  apiVersion:
                    description: API version of the referent.
                    type: string
                  fieldPath:
                    description: 'If referring to a piece of an object instead of
                      an entire object, this string should contain a valid JSON/Go
                      field access statement, such as desiredState.manifest.containers[2].
                      For example, if the object reference is to a container within
                      a pod, this would take on a value like: "spec.containers{name}"
                      (where "name" refers to the name of the container that triggered
                      the event) or if no container name is specified "spec.containers[2]"
                      (container with index 2 in this pod). This syntax is chosen
                      only to have some well-defined way of referencing a part of
                      an object. TODO: this design is not final and this field is
                      subject to change in the future.'
                    type: string
                  kind:
                    description: 'Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
                    type: string
                  name:
                    description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names'
                    type: string
                  namespace:
                    description: 'Namespace of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/'
                    type: string
                  resourceVersion:
                    description: 'Specific resourceVersion to which this reference
                      is made, if any. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency'
                    type: string
                  source:
                    description: source is the list of MachineConfig objects that
                      were used to generate the single MachineConfig object specified
                      in `content`.
                    items:
                      description: "ObjectReference contains enough information to
                        let you inspect or modify the referred object. --- New uses
                        of this type are discouraged because of difficulty describing
                        its usage when embedded in APIs. 1. Ignored fields.  It includes
                        many fields which are not generally honored.  For instance,
                        ResourceVersion and FieldPath are both very rarely valid in
                        actual usage. 2. Invalid usage help.  It is impossible to
                        add specific help for individual usage.  In most embedded
                        usages, there are particular restrictions like, \"must refer
                        only to types A and B\" or \"UID not honored\" or \"name must
                        be restricted\". Those cannot be well described when embedded.
                        3. Inconsistent validation.  Because the usages are different,
                        the validation rules are different by usage, which makes it
                        hard for users to predict what will happen. 4. The fields
                        are both imprecise and overly precise.  Kind is not a precise
                        mapping to a URL. This can produce ambiguity during interpretation
                        and require a REST mapping.  In most cases, the dependency
                        is on the group,resource tuple and the version of the actual
                        struct is irrelevant. 5. We cannot easily change it.  Because
                        this type is embedded in many locations, updates to this type
                        will affect numerous schemas.  Don't make new APIs embed an
                        underspecified API type they do not control. \n Instead of
                        using this type, create a locally provided and used type that
                        is well-focused on your reference. For example, ServiceReferences
                        for admission registration: https://github.com/kubernetes/api/blob/release-1.17/admissionregistration/v1/types.go#L533
                        ."
                      properties:
                        apiVersion:
                          description: API version of the referent.
                          type: string
                        fieldPath:
                          description: 'If referring to a piece of an object instead
                            of an entire object, this string should contain a valid
                            JSON/Go field access statement, such as desiredState.manifest.containers[2].
                            For example, if the object reference is to a container
                            within a pod, this would take on a value like: "spec.containers{name}"
                            (where "name" refers to the name of the container that
                            triggered the event) or if no container name is specified
                            "spec.containers[2]" (container with index 2 in this pod).
                            This syntax is chosen only to have some well-defined way
                            of referencing a part of an object. TODO: this design
                            is not final and this field is subject to change in the
                            future.'
                          type: string
                        kind:
                          description: 'Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
                          type: string
                        name:
                          description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names'
                          type: string
                        namespace:
                          description: 'Namespace of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/'
                          type: string
                        resourceVersion:
                          description: 'Specific resourceVersion to which this reference
                            is made, if any. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency'
                          type: string
                        uid:
                          description: 'UID of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids'
                          type: string
                      type: object
                      x-kubernetes-map-type: atomic
                    type: array
                    x-kubernetes-list-type: atomic
                  uid:
                    description: 'UID of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids'
                    type: string
                type: object
                x-kubernetes-map-type: atomic
              machineConfigSelector:
                description: machineConfigSelector specifies a label selector for
                  MachineConfigs. Refer https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
                  on how label and selectors work.
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: A label selector requirement is a selector that
                        contains values, a key, and an operator that relates the key
                        and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: operator represents a key's relationship to
                            a set of values. Valid operators are In, NotIn, Exists
                            and DoesNotExist.
                          type: string
                        values:
                          description: values is an array of string values. If the
                            operator is In or NotIn, the values array must be non-empty.
                            If the operator is Exists or DoesNotExist, the values
                            array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: matchLabels is a map of {key,value} pairs. A single
                      {key,value} in the matchLabels map is equivalent to an element
                      of matchExpressions, whose key field is "key", the operator
                      is "In", and the values array contains only "value". The requirements
                      are ANDed.
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              maxUnavailable:
                anyOf:
                - type: integer
                - type: string
                description: "maxUnavailable defines either an integer number or percentage
                  of nodes in the pool that can go Unavailable during an update. This
                  includes nodes Unavailable for any reason, including user initiated
                  cordons, failing nodes, etc. The default value is 1. \n A value
                  larger than 1 will mean multiple nodes going unavailable during
                  the update, which may affect your workload stress on the remaining
                  nodes. You cannot set this value to 0 to stop updates (it will default
                  back to 1); to stop updates, use the 'paused' property instead.
                  Drain will respect Pod Disruption Budgets (PDBs) such as etcd quorum
                  guards, even if maxUnavailable is greater than one."
                x-kubernetes-int-or-string: true
              nodeSelector:
                description: nodeSelector specifies a label selector for Machines
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: A label selector requirement is a selector that
                        contains values, a key, and an operator that relates the key
                        and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: operator represents a key's relationship to
                            a set of values. Valid operators are In, NotIn, Exists
                            and DoesNotExist.
                          type: string
                        values:
                          description: values is an array of string values. If the
                            operator is In or NotIn, the values array must be non-empty.
                            If the operator is Exists or DoesNotExist, the values
                            array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: matchLabels is a map of {key,value} pairs. A single
                      {key,value} in the matchLabels map is equivalent to an element
                      of matchExpressions, whose key field is "key", the operator
                      is "In", and the values array contains only "value". The requirements
                      are ANDed.
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              paused:
                description: paused specifies whether or not changes to this machine
                  config pool should be stopped. This includes generating new desiredMachineConfig
                  and update of machines.
                type: boolean
            type: object
          status:
            description: MachineConfigPoolStatus is the status for MachineConfigPool
              resource.
            properties:
              certExpirys:
                description: certExpirys keeps track of important certificate expiration
                  data
                items:
                  description: ceryExpiry contains the bundle name and the expiry
                    date
                  properties:
                    bundle:
                      description: bundle is the name of the bundle in which the subject
                        certificate resides
                      type: string
                    expiry:
                      description: expiry is the date after which the certificate
                        will no longer be valid
                      format: date-time
                      type: string
                    subject:
                      description: subject is the subject of the certificate
                      type: string
                  required:
                  - bundle
                  - subject
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              conditions:
                description: conditions represents the latest available observations
                  of current state.
                items:
                  description: MachineConfigPoolCondition contains condition information
                    for an MachineConfigPool.
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the timestamp corresponding
                        to the last status change of this condition.
                      format: date-time
                      nullable: true
                      type: string
                    message:
                      description: message is a human readable description of the
                        details of the last transition, complementing reason.
                      type: string
                    reason:
                      description: reason is a brief machine readable explanation
                        for the condition's last transition.
                      type: string
                    status:
                      description: status of the condition, one of ('True', 'False',
                        'Unknown').
                      type: string
                    type:
                      description: type of the condition, currently ('Done', 'Updating',
                        'Failed').
                      type: string
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              configuration:
                description: configuration represents the current MachineConfig object
                  for the machine config pool.
                properties:
                  apiVersion:
                    description: API version of the referent.
                    type: string
                  fieldPath:
                    description: 'If referring to a piece of an object instead of
                      an entire object, this string should contain a valid JSON/Go
                      field access statement, such as desiredState.manifest.containers[2].
                      For example, if the object reference is to a container within
                      a pod, this would take on a value like: "spec.containers{name}"
                      (where "name" refers to the name of the container that triggered
                      the event) or if no container name is specified "spec.containers[2]"
                      (container with index 2 in this pod). This syntax is chosen
                      only to have some well-defined way of referencing a part of
                      an object. TODO: this design is not final and this field is
                      subject to change in the future.'
                    type: string
                  kind:
                    description: 'Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
                    type: string
                  name:
                    description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names'
                    type: string
                  namespace:
                    description: 'Namespace of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/'
                    type: string
                  resourceVersion:
                    description: 'Specific resourceVersion to which this reference
                      is made, if any. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency'
                    type: string
                  source:
                    description: source is the list of MachineConfig objects that
                      were used to generate the single MachineConfig object specified
                      in `content`.
                    items:
                      description: "ObjectReference contains enough information to
                        let you inspect or modify the referred object. --- New uses
                        of this type are discouraged because of difficulty describing
                        its usage when embedded in APIs. 1. Ignored fields.  It includes
                        many fields which are not generally honored.  For instance,
                        ResourceVersion and FieldPath are both very rarely valid in
                        actual usage. 2. Invalid usage help.  It is impossible to
                        add specific help for individual usage.  In most embedded
                        usages, there are particular restrictions like, \"must refer
                        only to types A and B\" or \"UID not honored\" or \"name must
                        be restricted\". Those cannot be well described when embedded.
                        3. Inconsistent validation.  Because the usages are different,
                        the validation rules are different by usage, which makes it
                        hard for users to predict what will happen. 4. The fields
                        are both imprecise and overly precise.  Kind is not a precise
                        mapping to a URL. This can produce ambiguity during interpretation
                        and require a REST mapping.  In most cases, the dependency
                        is on the group,resource tuple and the version of the actual
                        struct is irrelevant. 5. We cannot easily change it.  Because
                        this type is embedded in many locations, updates to this type
                        will affect numerous schemas.  Don't make new APIs embed an
                        underspecified API type they do not control. \n Instead of
                        using this type, create a locally provided and used type that
                        is well-focused on your reference. For example, ServiceReferences
                        for admission registration: https://github.com/kubernetes/api/blob/release-1.17/admissionregistration/v1/types.go#L533
                        ."
                      properties:
                        apiVersion:
                          description: API version of the referent.
                          type: string
                        fieldPath:
                          description: 'If referring to a piece of an object instead
                            of an entire object, this string should contain a valid
                            JSON/Go field access statement, such as desiredState.manifest.containers[2].
                            For example, if the object reference is to a container
                            within a pod, this would take on a value like: "spec.containers{name}"
                            (where "name" refers to the name of the container that
                            triggered the event) or if no container name is specified
                            "spec.containers[2]" (container with index 2 in this pod).
                            This syntax is chosen only to have some well-defined way
                            of referencing a part of an object. TODO: this design
                            is not final and this field is subject to change in the
                            future.'
                          type: string
                        kind:
                          description: 'Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
                          type: string
                        name:
                          description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names'
                          type: string
                        namespace:
                          description: 'Namespace of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/'
                          type: string
                        resourceVersion:
                          description: 'Specific resourceVersion to which this reference
                            is made, if any. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency'
                          type: string
                        uid:
                          description: 'UID of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids'
                          type: string
                      type: object
                      x-kubernetes-map-type: atomic
                    type: array
                    x-kubernetes-list-type: atomic
                  uid:
                    description: 'UID of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids'
                    type: string
                type: object
                x-kubernetes-map-type: atomic
              degradedMachineCount:
                description: degradedMachineCount represents the total number of machines
                  marked degraded (or unreconcilable). A node is marked degraded if
                  applying a configuration failed..
                format: int32
                type: integer
              machineCount:
                description: machineCount represents the total number of machines
                  in the machine config pool.
                format: int32
                type: integer
              observedGeneration:
                description: observedGeneration represents the generation observed
                  by the controller.
                format: int64
                type: integer
              readyMachineCount:
                description: readyMachineCount represents the total number of ready
                  machines targeted by the pool.
                format: int32
                type: integer
              unavailableMachineCount:
                description: unavailableMachineCount represents the total number of
                  unavailable (non-ready) machines targeted by the pool. A node is
                  marked unavailable if it is in updating state or NodeReady condition
                  is false.
                format: int32
                type: integer
              updatedMachineCount:
                description: updatedMachineCount represents the total number of machines
                  targeted by the pool that have the CurrentMachineConfig as their
                  config.
                format: int32
                type: integer
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: MachineConfigPool
    listKind: MachineConfigPoolList
    plural: machineconfigpools
    shortNames:
    - mcp
    singular: machineconfigpool
  conditions:
  - lastTransitionTime: "2025-06-25T14:53:57Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:53:57Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
