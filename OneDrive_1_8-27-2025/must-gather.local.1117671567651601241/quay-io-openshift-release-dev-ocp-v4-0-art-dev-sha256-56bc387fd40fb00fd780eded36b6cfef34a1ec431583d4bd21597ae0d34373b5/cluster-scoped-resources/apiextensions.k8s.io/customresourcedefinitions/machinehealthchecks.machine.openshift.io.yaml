---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    api-approved.openshift.io: https://github.com/openshift/api/pull/1032
    api.openshift.io/merged-by-featuregates: "true"
    capability.openshift.io/name: MachineAPI
    exclude.release.openshift.io/internal-openshift-hosted: "true"
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: "2025-06-25T14:54:02Z"
  generation: 2
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-06-25T14:54:02Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:api-approved.openshift.io: {}
          f:api.openshift.io/merged-by-featuregates: {}
          f:capability.openshift.io/name: {}
          f:exclude.release.openshift.io/internal-openshift-hosted: {}
          f:include.release.openshift.io/self-managed-high-availability: {}
          f:include.release.openshift.io/single-node-developer: {}
        f:ownerReferences:
          .: {}
          k:{"uid":"626c388f-d114-47b4-91f7-94de65fec21f"}: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:shortNames: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: cluster-version-operator
    operation: Update
    time: "2025-08-01T12:05:34Z"
  name: machinehealthchecks.machine.openshift.io
  ownerReferences:
  - apiVersion: config.openshift.io/v1
    controller: true
    kind: ClusterVersion
    name: version
    uid: 626c388f-d114-47b4-91f7-94de65fec21f
  resourceVersion: "21338543"
  uid: 9700d161-67ff-4e48-864b-5ea25adf3e12
spec:
  conversion:
    strategy: None
  group: machine.openshift.io
  names:
    kind: MachineHealthCheck
    listKind: MachineHealthCheckList
    plural: machinehealthchecks
    shortNames:
    - mhc
    - mhcs
    singular: machinehealthcheck
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Maximum number of unhealthy machines allowed
      jsonPath: .spec.maxUnhealthy
      name: MaxUnhealthy
      type: string
    - description: Number of machines currently monitored
      jsonPath: .status.expectedMachines
      name: ExpectedMachines
      type: integer
    - description: Current observed healthy machines
      jsonPath: .status.currentHealthy
      name: CurrentHealthy
      type: integer
    name: v1beta1
    schema:
      openAPIV3Schema:
        description: 'MachineHealthCheck is the Schema for the machinehealthchecks
          API Compatibility level 2: Stable within a major release for a minimum of
          9 months or 3 minor releases (whichever is longer).'
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: Specification of machine health check policy
            properties:
              maxUnhealthy:
                anyOf:
                - type: integer
                - type: string
                default: 100%
                description: Any farther remediation is only allowed if at most "MaxUnhealthy"
                  machines selected by "selector" are not healthy. Expects either
                  a postive integer value or a percentage value. Percentage values
                  must be positive whole numbers and are capped at 100%. Both 0 and
                  0% are valid and will block all remediation.
                pattern: ^((100|[0-9]{1,2})%|[0-9]+)$
                x-kubernetes-int-or-string: true
              nodeStartupTimeout:
                default: 10m
                description: Machines older than this duration without a node will
                  be considered to have failed and will be remediated. To prevent
                  Machines without Nodes from being removed, disable startup checks
                  by setting this value explicitly to "0". Expects an unsigned duration
                  string of decimal numbers each with optional fraction and a unit
                  suffix, eg "300ms", "1.5h" or "2h45m". Valid time units are "ns",
                  "us" (or "µs"), "ms", "s", "m", "h".
                pattern: ^0|([0-9]+(\.[0-9]+)?(ns|us|µs|ms|s|m|h))+$
                type: string
              remediationTemplate:
                description: "RemediationTemplate is a reference to a remediation
                  template provided by an infrastructure provider. \n This field is
                  completely optional, when filled, the MachineHealthCheck controller
                  creates a new object from the template referenced and hands off
                  remediation of the machine to a controller that lives outside of
                  Machine API Operator."
                properties:
                  apiVersion:
                    description: API version of the referent.
                    type: string
                  fieldPath:
                    description: 'If referring to a piece of an object instead of
                      an entire object, this string should contain a valid JSON/Go
                      field access statement, such as desiredState.manifest.containers[2].
                      For example, if the object reference is to a container within
                      a pod, this would take on a value like: "spec.containers{name}"
                      (where "name" refers to the name of the container that triggered
                      the event) or if no container name is specified "spec.containers[2]"
                      (container with index 2 in this pod). This syntax is chosen
                      only to have some well-defined way of referencing a part of
                      an object. TODO: this design is not final and this field is
                      subject to change in the future.'
                    type: string
                  kind:
                    description: 'Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
                    type: string
                  name:
                    description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names'
                    type: string
                  namespace:
                    description: 'Namespace of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/'
                    type: string
                  resourceVersion:
                    description: 'Specific resourceVersion to which this reference
                      is made, if any. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency'
                    type: string
                  uid:
                    description: 'UID of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids'
                    type: string
                type: object
                x-kubernetes-map-type: atomic
              selector:
                description: 'Label selector to match machines whose health will be
                  exercised. Note: An empty selector will match all machines.'
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: A label selector requirement is a selector that
                        contains values, a key, and an operator that relates the key
                        and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: operator represents a key's relationship to
                            a set of values. Valid operators are In, NotIn, Exists
                            and DoesNotExist.
                          type: string
                        values:
                          description: values is an array of string values. If the
                            operator is In or NotIn, the values array must be non-empty.
                            If the operator is Exists or DoesNotExist, the values
                            array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: matchLabels is a map of {key,value} pairs. A single
                      {key,value} in the matchLabels map is equivalent to an element
                      of matchExpressions, whose key field is "key", the operator
                      is "In", and the values array contains only "value". The requirements
                      are ANDed.
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              unhealthyConditions:
                description: UnhealthyConditions contains a list of the conditions
                  that determine whether a node is considered unhealthy.  The conditions
                  are combined in a logical OR, i.e. if any of the conditions is met,
                  the node is unhealthy.
                items:
                  description: UnhealthyCondition represents a Node condition type
                    and value with a timeout specified as a duration.  When the named
                    condition has been in the given status for at least the timeout
                    value, a node is considered unhealthy.
                  properties:
                    status:
                      minLength: 1
                      type: string
                    timeout:
                      description: Expects an unsigned duration string of decimal
                        numbers each with optional fraction and a unit suffix, eg
                        "300ms", "1.5h" or "2h45m". Valid time units are "ns", "us"
                        (or "µs"), "ms", "s", "m", "h".
                      pattern: ^([0-9]+(\.[0-9]+)?(ns|us|µs|ms|s|m|h))+$
                      type: string
                    type:
                      minLength: 1
                      type: string
                  type: object
                minItems: 1
                type: array
            type: object
          status:
            description: Most recently observed status of MachineHealthCheck resource
            properties:
              conditions:
                description: Conditions defines the current state of the MachineHealthCheck
                items:
                  description: Condition defines an observation of a Machine API resource
                    operational state.
                  properties:
                    lastTransitionTime:
                      description: Last time the condition transitioned from one status
                        to another. This should be when the underlying condition changed.
                        If that is not known, then using the time when the API field
                        changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: A human readable message indicating details about
                        the transition. This field may be empty.
                      type: string
                    reason:
                      description: The reason for the condition's last transition
                        in CamelCase. The specific API may choose whether or not this
                        field is considered a guaranteed API. This field may not be
                        empty.
                      type: string
                    severity:
                      description: Severity provides an explicit classification of
                        Reason code, so the users or machines can immediately understand
                        the current situation and act accordingly. The Severity field
                        MUST be set only when Status=False.
                      type: string
                    status:
                      description: Status of the condition, one of True, False, Unknown.
                      type: string
                    type:
                      description: Type of condition in CamelCase or in foo.example.com/CamelCase.
                        Many .condition.type values are consistent across resources
                        like Available, but because arbitrary conditions can be useful
                        (see .node.status.conditions), the ability to deconflict is
                        important.
                      type: string
                  required:
                  - type
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              currentHealthy:
                description: total number of machines counted by this machine health
                  check
                minimum: 0
                type: integer
              expectedMachines:
                description: total number of machines counted by this machine health
                  check
                minimum: 0
                type: integer
              remediationsAllowed:
                description: RemediationsAllowed is the number of further remediations
                  allowed by this machine health check before maxUnhealthy short circuiting
                  will be applied
                format: int32
                minimum: 0
                type: integer
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: MachineHealthCheck
    listKind: MachineHealthCheckList
    plural: machinehealthchecks
    shortNames:
    - mhc
    - mhcs
    singular: machinehealthcheck
  conditions:
  - lastTransitionTime: "2025-06-25T14:54:02Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-06-25T14:54:02Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1beta1
