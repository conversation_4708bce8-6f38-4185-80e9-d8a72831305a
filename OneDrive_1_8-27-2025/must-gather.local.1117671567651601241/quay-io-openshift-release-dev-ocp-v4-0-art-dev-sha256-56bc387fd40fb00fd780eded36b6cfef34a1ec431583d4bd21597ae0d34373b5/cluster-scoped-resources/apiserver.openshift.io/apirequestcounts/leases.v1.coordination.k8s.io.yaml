---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T14:59:44Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:59:44Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:41:30Z"
  name: leases.v1.coordination.k8s.io
  resourceVersion: "33963787"
  uid: ce0ee711-9cee-4aa3-bd73-100487cb98e6
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 426
          verb: get
        requestCount: 426
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 179
          verb: get
        - requestCount: 80
          verb: update
        requestCount: 259
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 203
          verb: update
        - requestCount: 4
          verb: watch
        requestCount: 207
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 204
          verb: update
        requestCount: 204
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 204
          verb: update
        requestCount: 204
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 204
          verb: update
        requestCount: 204
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 204
          verb: update
        requestCount: 204
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 204
          verb: update
        requestCount: 204
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 204
          verb: update
        requestCount: 204
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 204
          verb: update
        requestCount: 204
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      nodeName: 10.9.212.10
      requestCount: 2893
    - byUser:
      - byVerb:
        - requestCount: 238
          verb: update
        - requestCount: 5
          verb: watch
        requestCount: 243
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 238
          verb: update
        requestCount: 238
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 75
          verb: get
        requestCount: 75
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 59
          verb: get
        requestCount: 59
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 58
          verb: get
        requestCount: 58
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 58
          verb: get
        requestCount: 58
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 58
          verb: get
        requestCount: 58
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: **********
      requestCount: 977
    - byUser:
      - byVerb:
        - requestCount: 473
          verb: get
        - requestCount: 750
          verb: update
        - requestCount: 6
          verb: watch
        requestCount: 1229
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1122
          verb: update
        requestCount: 1122
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 221
          verb: update
        - requestCount: 6
          verb: watch
        requestCount: 227
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 221
          verb: update
        requestCount: 221
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 221
          verb: update
        requestCount: 221
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 86
          verb: get
        - requestCount: 86
          verb: update
        requestCount: 172
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 57
          verb: get
        - requestCount: 86
          verb: update
        requestCount: 143
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 54
          verb: get
        - requestCount: 87
          verb: update
        requestCount: 141
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 113
          verb: update
        requestCount: 113
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 113
          verb: update
        requestCount: 113
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      nodeName: 10.9.212.9
      requestCount: 3915
    requestCount: 7785
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 755
          verb: get
        requestCount: 755
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 309
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 448
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 352
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 360
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      nodeName: 10.9.212.10
      requestCount: 4032
    - byUser:
      - byVerb:
        - requestCount: 350
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 358
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 110
          verb: get
        requestCount: 110
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 83
          verb: get
        requestCount: 83
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 81
          verb: get
        requestCount: 81
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: **********
      requestCount: 1148
    - byUser:
      - byVerb:
        - requestCount: 721
          verb: get
        - requestCount: 1146
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 1874
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1716
          verb: update
        requestCount: 1716
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 337
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 345
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 338
          verb: update
        requestCount: 338
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 337
          verb: update
        requestCount: 337
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 337
          verb: update
        requestCount: 337
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 132
          verb: get
        - requestCount: 132
          verb: update
        requestCount: 264
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 86
          verb: get
        - requestCount: 133
          verb: update
        requestCount: 219
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 81
          verb: get
        - requestCount: 132
          verb: update
        requestCount: 213
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 180
          verb: update
        requestCount: 180
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      nodeName: 10.9.212.9
      requestCount: 5823
    requestCount: 11003
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 743
          verb: get
        requestCount: 743
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 313
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 451
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      nodeName: 10.9.212.10
      requestCount: 4022
    - byUser:
      - byVerb:
        - requestCount: 352
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 112
          verb: get
        requestCount: 112
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 90
          verb: get
        requestCount: 90
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 89
          verb: get
        requestCount: 89
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 83
          verb: get
        requestCount: 83
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      nodeName: **********
      requestCount: 1169
    - byUser:
      - byVerb:
        - requestCount: 744
          verb: get
        - requestCount: 1197
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 1949
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1793
          verb: update
        requestCount: 1793
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 10
          verb: watch
        requestCount: 363
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 139
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 278
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 86
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 225
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 222
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 180
          verb: update
        requestCount: 180
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      nodeName: 10.9.212.9
      requestCount: 6069
    requestCount: 11260
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 759
          verb: get
        requestCount: 759
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 306
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 444
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 352
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      nodeName: 10.9.212.10
      requestCount: 4032
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 116
          verb: get
        requestCount: 116
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 90
          verb: get
        requestCount: 90
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 89
          verb: get
        requestCount: 89
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      nodeName: **********
      requestCount: 1180
    - byUser:
      - byVerb:
        - requestCount: 758
          verb: get
        - requestCount: 1198
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 1964
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1794
          verb: update
        requestCount: 1794
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 362
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 138
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 276
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 88
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 226
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 85
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 224
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 180
          verb: update
        requestCount: 180
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      nodeName: 10.9.212.9
      requestCount: 6084
    requestCount: 11296
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 753
          verb: get
        requestCount: 753
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 314
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 453
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 362
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      nodeName: 10.9.212.10
      requestCount: 4036
    - byUser:
      - byVerb:
        - requestCount: 352
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 360
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 116
          verb: get
        requestCount: 116
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 90
          verb: get
        requestCount: 90
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 84
          verb: get
        requestCount: 84
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 81
          verb: get
        requestCount: 81
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      nodeName: **********
      requestCount: 1161
    - byUser:
      - byVerb:
        - requestCount: 748
          verb: get
        - requestCount: 1197
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 1953
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1793
          verb: update
        requestCount: 1793
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 352
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 360
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 139
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 278
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 87
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 225
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 86
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 224
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 180
          verb: update
        requestCount: 180
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      nodeName: 10.9.212.9
      requestCount: 6071
    requestCount: 11268
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 756
          verb: get
        requestCount: 756
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 311
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 449
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 360
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      nodeName: 10.9.212.10
      requestCount: 4034
    - byUser:
      - byVerb:
        - requestCount: 350
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 357
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 107
          verb: get
        requestCount: 107
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 91
          verb: get
        requestCount: 91
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 90
          verb: get
        requestCount: 90
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 84
          verb: get
        requestCount: 84
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: **********
      requestCount: 1158
    - byUser:
      - byVerb:
        - requestCount: 747
          verb: get
        - requestCount: 1197
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 1953
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1793
          verb: update
        requestCount: 1793
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 138
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 276
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 90
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 229
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 86
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 225
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 180
          verb: update
        requestCount: 180
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      nodeName: 10.9.212.9
      requestCount: 6076
    requestCount: 11268
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 746
          verb: get
        requestCount: 746
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 316
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 455
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 352
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 359
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      nodeName: 10.9.212.10
      requestCount: 4029
    - byUser:
      - byVerb:
        - requestCount: 349
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 356
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 108
          verb: get
        requestCount: 108
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 91
          verb: get
        requestCount: 91
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 84
          verb: get
        requestCount: 84
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 83
          verb: get
        requestCount: 83
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 83
          verb: get
        requestCount: 83
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: **********
      requestCount: 1151
    - byUser:
      - byVerb:
        - requestCount: 747
          verb: get
        - requestCount: 1197
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 1952
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1793
          verb: update
        requestCount: 1793
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 138
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 276
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 88
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 226
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 86
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 224
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 180
          verb: update
        requestCount: 180
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      nodeName: 10.9.212.9
      requestCount: 6069
    requestCount: 11249
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 755
          verb: get
        requestCount: 755
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 313
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 451
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 360
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      nodeName: 10.9.212.10
      requestCount: 4036
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 362
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 112
          verb: get
        requestCount: 112
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 91
          verb: get
        requestCount: 91
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 89
          verb: get
        requestCount: 89
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 89
          verb: get
        requestCount: 89
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 84
          verb: get
        requestCount: 84
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 83
          verb: get
        requestCount: 83
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      nodeName: **********
      requestCount: 1169
    - byUser:
      - byVerb:
        - requestCount: 748
          verb: get
        - requestCount: 1197
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 1953
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1792
          verb: update
        requestCount: 1792
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 10
          verb: watch
        requestCount: 363
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 139
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 278
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 87
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 225
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 83
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 222
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 180
          verb: update
        requestCount: 180
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 6071
    requestCount: 11276
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 743
          verb: get
        requestCount: 743
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 307
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 445
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 352
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 359
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      nodeName: 10.9.212.10
      requestCount: 4015
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 360
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 114
          verb: get
        requestCount: 114
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 90
          verb: get
        requestCount: 90
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 89
          verb: get
        requestCount: 89
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 89
          verb: get
        requestCount: 89
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 84
          verb: get
        requestCount: 84
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 84
          verb: get
        requestCount: 84
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 84
          verb: get
        requestCount: 84
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      nodeName: **********
      requestCount: 1167
    - byUser:
      - byVerb:
        - requestCount: 753
          verb: get
        - requestCount: 1197
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 1958
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1794
          verb: update
        requestCount: 1794
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 352
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 359
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 138
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 276
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 87
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 225
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 86
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 225
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 180
          verb: update
        requestCount: 180
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 6076
    requestCount: 11258
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 754
          verb: get
        requestCount: 754
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 317
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 456
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 362
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      nodeName: 10.9.212.10
      requestCount: 4040
    - byUser:
      - byVerb:
        - requestCount: 352
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 359
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 115
          verb: get
        requestCount: 115
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 90
          verb: get
        requestCount: 90
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: **********
      requestCount: 1167
    - byUser:
      - byVerb:
        - requestCount: 754
          verb: get
        - requestCount: 1197
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 1959
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1792
          verb: update
        requestCount: 1792
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 362
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 139
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 278
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 89
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 227
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 87
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 226
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 180
          verb: update
        requestCount: 180
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 6081
    requestCount: 11288
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 742
          verb: get
        requestCount: 742
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 309
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 447
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: 10.9.212.10
      requestCount: 4019
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 109
          verb: get
        requestCount: 109
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 90
          verb: get
        requestCount: 90
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 90
          verb: get
        requestCount: 90
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 90
          verb: get
        requestCount: 90
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 89
          verb: get
        requestCount: 89
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 84
          verb: get
        requestCount: 84
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 82
          verb: get
        requestCount: 82
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      nodeName: **********
      requestCount: 1170
    - byUser:
      - byVerb:
        - requestCount: 758
          verb: get
        - requestCount: 1198
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 1965
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1793
          verb: update
        requestCount: 1793
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 138
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 276
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 88
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 227
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 88
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 226
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 180
          verb: update
        requestCount: 180
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      nodeName: 10.9.212.9
      requestCount: 6087
    requestCount: 11276
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 750
          verb: get
        requestCount: 750
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 309
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 448
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 6
          verb: watch
        requestCount: 359
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      nodeName: 10.9.212.10
      requestCount: 4025
    - byUser:
      - byVerb:
        - requestCount: 350
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 359
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 111
          verb: get
        requestCount: 111
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 84
          verb: get
        requestCount: 84
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      nodeName: **********
      requestCount: 1154
    - byUser:
      - byVerb:
        - requestCount: 743
          verb: get
        - requestCount: 1197
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 1948
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1793
          verb: update
        requestCount: 1793
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 352
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 359
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 138
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 276
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 88
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 226
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 87
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 225
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 180
          verb: update
        requestCount: 180
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 6063
    requestCount: 11242
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 760
          verb: get
        requestCount: 760
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 314
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 452
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 352
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 360
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      nodeName: 10.9.212.10
      requestCount: 4041
    - byUser:
      - byVerb:
        - requestCount: 352
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 359
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 114
          verb: get
        requestCount: 114
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 91
          verb: get
        requestCount: 91
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      nodeName: **********
      requestCount: 1168
    - byUser:
      - byVerb:
        - requestCount: 758
          verb: get
        - requestCount: 1197
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 1962
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1793
          verb: update
        requestCount: 1793
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 139
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 278
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 87
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 226
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 85
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 224
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 180
          verb: update
        requestCount: 180
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 6083
    requestCount: 11292
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 749
          verb: get
        requestCount: 749
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 311
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 449
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      nodeName: 10.9.212.10
      requestCount: 4030
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 110
          verb: get
        requestCount: 110
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 90
          verb: get
        requestCount: 90
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      nodeName: **********
      requestCount: 1168
    - byUser:
      - byVerb:
        - requestCount: 751
          verb: get
        - requestCount: 1197
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 1956
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1793
          verb: update
        requestCount: 1793
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 138
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 276
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 90
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 228
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 85
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 223
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 179
          verb: update
        requestCount: 179
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 6072
    requestCount: 11270
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 750
          verb: get
        requestCount: 750
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 451
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      nodeName: 10.9.212.10
      requestCount: 4030
    - byUser:
      - byVerb:
        - requestCount: 350
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 357
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 253
          verb: update
        requestCount: 253
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 113
          verb: get
        requestCount: 113
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 91
          verb: get
        requestCount: 91
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 84
          verb: get
        requestCount: 84
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      nodeName: **********
      requestCount: 1327
    - byUser:
      - byVerb:
        - requestCount: 757
          verb: get
        - requestCount: 1197
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 1962
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1794
          verb: update
        requestCount: 1794
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 352
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 360
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 139
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 278
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 87
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 226
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 222
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 180
          verb: update
        requestCount: 180
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 164
          verb: get
        requestCount: 164
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.9
      requestCount: 5892
    requestCount: 11249
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 426
          verb: get
        requestCount: 426
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 179
          verb: get
        - requestCount: 80
          verb: update
        requestCount: 259
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 203
          verb: update
        - requestCount: 4
          verb: watch
        requestCount: 207
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 204
          verb: update
        requestCount: 204
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 204
          verb: update
        requestCount: 204
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 204
          verb: update
        requestCount: 204
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 204
          verb: update
        requestCount: 204
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 204
          verb: update
        requestCount: 204
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 204
          verb: update
        requestCount: 204
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 204
          verb: update
        requestCount: 204
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      nodeName: 10.9.212.10
      requestCount: 2893
    - byUser:
      - byVerb:
        - requestCount: 238
          verb: update
        - requestCount: 5
          verb: watch
        requestCount: 243
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 238
          verb: update
        requestCount: 238
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 75
          verb: get
        requestCount: 75
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 59
          verb: get
        requestCount: 59
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 58
          verb: get
        requestCount: 58
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 58
          verb: get
        requestCount: 58
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 58
          verb: get
        requestCount: 58
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: **********
      requestCount: 977
    - byUser:
      - byVerb:
        - requestCount: 473
          verb: get
        - requestCount: 750
          verb: update
        - requestCount: 6
          verb: watch
        requestCount: 1229
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1122
          verb: update
        requestCount: 1122
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 221
          verb: update
        - requestCount: 6
          verb: watch
        requestCount: 227
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 221
          verb: update
        requestCount: 221
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 221
          verb: update
        requestCount: 221
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 86
          verb: get
        - requestCount: 86
          verb: update
        requestCount: 172
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 57
          verb: get
        - requestCount: 86
          verb: update
        requestCount: 143
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 54
          verb: get
        - requestCount: 87
          verb: update
        requestCount: 141
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 113
          verb: update
        requestCount: 113
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 113
          verb: update
        requestCount: 113
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      nodeName: 10.9.212.9
      requestCount: 3915
    requestCount: 7785
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 753
          verb: get
        requestCount: 753
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 316
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 455
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 352
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 360
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: 10.9.212.10
      requestCount: 4038
    - byUser:
      - byVerb:
        - requestCount: 349
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 357
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 112
          verb: get
        requestCount: 112
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 90
          verb: get
        requestCount: 90
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 84
          verb: get
        requestCount: 84
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 84
          verb: get
        requestCount: 84
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      nodeName: **********
      requestCount: 1073
    - byUser:
      - byVerb:
        - requestCount: 761
          verb: get
        - requestCount: 1197
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 1967
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1793
          verb: update
        requestCount: 1793
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 352
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 360
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 138
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 276
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 88
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 227
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 85
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 223
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 180
          verb: update
        requestCount: 180
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      nodeName: 10.9.212.9
      requestCount: 6083
    requestCount: 11194
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 749
          verb: get
        requestCount: 749
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 450
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      nodeName: 10.9.212.10
      requestCount: 4029
    - byUser:
      - byVerb:
        - requestCount: 349
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 356
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 113
          verb: get
        requestCount: 113
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 92
          verb: get
        requestCount: 92
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 84
          verb: get
        requestCount: 84
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      nodeName: **********
      requestCount: 1076
    - byUser:
      - byVerb:
        - requestCount: 751
          verb: get
        - requestCount: 1197
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 1955
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1793
          verb: update
        requestCount: 1793
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 139
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 278
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 88
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 226
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 87
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 225
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 180
          verb: update
        requestCount: 180
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 6077
    requestCount: 11182
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 746
          verb: get
        requestCount: 746
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 310
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 448
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 362
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      nodeName: 10.9.212.10
      requestCount: 4026
    - byUser:
      - byVerb:
        - requestCount: 354
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 362
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 113
          verb: get
        requestCount: 113
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      nodeName: **********
      requestCount: 1081
    - byUser:
      - byVerb:
        - requestCount: 750
          verb: get
        - requestCount: 1197
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 1956
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1793
          verb: update
        requestCount: 1793
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 352
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 138
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 276
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 89
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 227
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 88
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 227
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 180
          verb: update
        requestCount: 180
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      nodeName: 10.9.212.9
      requestCount: 6078
    requestCount: 11185
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 748
          verb: get
        requestCount: 748
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 314
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 453
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 352
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 360
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      nodeName: 10.9.212.10
      requestCount: 4030
    - byUser:
      - byVerb:
        - requestCount: 349
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 356
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 113
          verb: get
        requestCount: 113
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 91
          verb: get
        requestCount: 91
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 84
          verb: get
        requestCount: 84
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 83
          verb: get
        requestCount: 83
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 83
          verb: get
        requestCount: 83
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      nodeName: **********
      requestCount: 1070
    - byUser:
      - byVerb:
        - requestCount: 759
          verb: get
        - requestCount: 1198
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 1964
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1793
          verb: update
        requestCount: 1793
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 360
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 138
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 276
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 89
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 227
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 87
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 226
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 180
          verb: update
        requestCount: 180
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      nodeName: 10.9.212.9
      requestCount: 6083
    requestCount: 11183
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 745
          verb: get
        requestCount: 745
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 310
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 448
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 360
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      nodeName: 10.9.212.10
      requestCount: 4020
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 362
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 116
          verb: get
        requestCount: 116
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      nodeName: **********
      requestCount: 1085
    - byUser:
      - byVerb:
        - requestCount: 743
          verb: get
        - requestCount: 1197
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 1949
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1794
          verb: update
        requestCount: 1794
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 362
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 139
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 278
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 83
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 222
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 83
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 221
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 180
          verb: update
        requestCount: 180
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      nodeName: 10.9.212.9
      requestCount: 6064
    requestCount: 11169
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 742
          verb: get
        requestCount: 742
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 313
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 451
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 352
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 360
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      nodeName: 10.9.212.10
      requestCount: 4024
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 113
          verb: get
        requestCount: 113
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 90
          verb: get
        requestCount: 90
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 83
          verb: get
        requestCount: 83
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      nodeName: **********
      requestCount: 1079
    - byUser:
      - byVerb:
        - requestCount: 749
          verb: get
        - requestCount: 1197
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 1953
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1793
          verb: update
        requestCount: 1793
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 352
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 360
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 138
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 276
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 91
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 230
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 88
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 226
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 180
          verb: update
        requestCount: 180
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 6077
    requestCount: 11180
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 748
          verb: get
        requestCount: 748
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 315
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 454
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      nodeName: 10.9.212.10
      requestCount: 4031
    - byUser:
      - byVerb:
        - requestCount: 352
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 111
          verb: get
        requestCount: 111
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 90
          verb: get
        requestCount: 90
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 89
          verb: get
        requestCount: 89
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 88
          verb: get
        requestCount: 88
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 78
          verb: get
        requestCount: 78
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: **********
      requestCount: 1160
    - byUser:
      - byVerb:
        - requestCount: 751
          verb: get
        - requestCount: 1197
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 1957
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1793
          verb: update
        requestCount: 1793
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 360
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 139
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 278
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 91
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 229
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 87
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 225
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 180
          verb: update
        requestCount: 180
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      nodeName: 10.9.212.9
      requestCount: 6080
    requestCount: 11271
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 747
          verb: get
        requestCount: 747
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 315
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 453
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      nodeName: 10.9.212.10
      requestCount: 4030
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 110
          verb: get
        requestCount: 110
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 89
          verb: get
        requestCount: 89
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 87
          verb: get
        requestCount: 87
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 86
          verb: get
        requestCount: 86
        userAgent: azure-cloud-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-controller-manager
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 85
          verb: get
        requestCount: 85
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      nodeName: **********
      requestCount: 1162
    - byUser:
      - byVerb:
        - requestCount: 745
          verb: get
        - requestCount: 1198
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 1951
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1793
          verb: update
        requestCount: 1793
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 352
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 361
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 353
          verb: update
        requestCount: 353
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 352
          verb: update
        requestCount: 352
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 138
          verb: get
        - requestCount: 138
          verb: update
        requestCount: 276
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 89
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 228
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 88
          verb: get
        - requestCount: 139
          verb: update
        requestCount: 227
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 180
          verb: update
        requestCount: 180
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 6074
    requestCount: 11266
  requestCount: 254910
