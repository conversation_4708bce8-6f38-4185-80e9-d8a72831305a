---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T14:58:56Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:58:56Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:40:42Z"
  name: subjectaccessreviews.v1.authorization.k8s.io
  resourceVersion: "********"
  uid: aa31ba53-ae8e-40d4-8cb6-fbe505b3f9be
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 5214
          verb: create
        requestCount: 5214
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 1827
          verb: create
        requestCount: 1827
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 207
          verb: create
        requestCount: 207
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 200
          verb: create
        requestCount: 200
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 158
          verb: create
        requestCount: 158
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 146
          verb: create
        requestCount: 146
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 85
          verb: create
        requestCount: 85
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 40
          verb: create
        requestCount: 40
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 37
          verb: create
        requestCount: 37
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 36
          verb: create
        requestCount: 36
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      nodeName: 10.9.212.10
      requestCount: 8008
    - byUser:
      - byVerb:
        - requestCount: 86
          verb: create
        requestCount: 86
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 43
          verb: create
        requestCount: 43
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 30
          verb: create
        requestCount: 30
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 13
          verb: create
        requestCount: 13
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 4
          verb: create
        requestCount: 4
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 3
          verb: create
        requestCount: 3
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 3
          verb: create
        requestCount: 3
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      nodeName: 10.9.212.8
      requestCount: 192
    - byUser:
      - byVerb:
        - requestCount: 2852
          verb: create
        requestCount: 2852
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 233
          verb: create
        requestCount: 233
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 218
          verb: create
        requestCount: 218
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 150
          verb: create
        requestCount: 150
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 40
          verb: create
        requestCount: 40
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 40
          verb: create
        requestCount: 40
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 36
          verb: create
        requestCount: 36
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 15
          verb: create
        requestCount: 15
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 6
          verb: create
        requestCount: 6
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      nodeName: 10.9.212.9
      requestCount: 3599
    requestCount: 11799
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8818
          verb: create
        requestCount: 8818
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3193
          verb: create
        requestCount: 3193
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 335
          verb: create
        requestCount: 335
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 297
          verb: create
        requestCount: 297
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 150
          verb: create
        requestCount: 150
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 111
          verb: create
        requestCount: 111
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 66
          verb: create
        requestCount: 66
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 61
          verb: create
        requestCount: 61
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      nodeName: 10.9.212.10
      requestCount: 13643
    - byUser:
      - byVerb:
        - requestCount: 102
          verb: create
        requestCount: 102
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 28
          verb: create
        requestCount: 28
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 19
          verb: create
        requestCount: 19
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 16
          verb: create
        requestCount: 16
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 14
          verb: create
        requestCount: 14
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 10
          verb: create
        requestCount: 10
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: create
        requestCount: 6
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 4
          verb: create
        requestCount: 4
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 3
          verb: create
        requestCount: 3
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.8
      requestCount: 210
    - byUser:
      - byVerb:
        - requestCount: 4537
          verb: create
        requestCount: 4537
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 372
          verb: create
        requestCount: 372
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 366
          verb: create
        requestCount: 366
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 64
          verb: create
        requestCount: 64
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 62
          verb: create
        requestCount: 62
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 59
          verb: create
        requestCount: 59
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 55
          verb: create
        requestCount: 55
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 27
          verb: create
        requestCount: 27
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 23
          verb: create
        requestCount: 23
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 5805
    requestCount: 19658
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8895
          verb: create
        requestCount: 8895
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3194
          verb: create
        requestCount: 3194
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 353
          verb: create
        requestCount: 353
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 291
          verb: create
        requestCount: 291
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 149
          verb: create
        requestCount: 149
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 106
          verb: create
        requestCount: 106
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 70
          verb: create
        requestCount: 70
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 66
          verb: create
        requestCount: 66
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 13736
    - byUser:
      - byVerb:
        - requestCount: 76
          verb: create
        requestCount: 76
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 16
          verb: create
        requestCount: 16
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 13
          verb: create
        requestCount: 13
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 13
          verb: create
        requestCount: 13
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 12
          verb: create
        requestCount: 12
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 6
          verb: create
        requestCount: 6
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 4
          verb: create
        requestCount: 4
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.8
      requestCount: 150
    - byUser:
      - byVerb:
        - requestCount: 4491
          verb: create
        requestCount: 4491
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 372
          verb: create
        requestCount: 372
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 349
          verb: create
        requestCount: 349
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 84
          verb: create
        requestCount: 84
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 60
          verb: create
        requestCount: 60
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 59
          verb: create
        requestCount: 59
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 59
          verb: create
        requestCount: 59
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 39
          verb: create
        requestCount: 39
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 37
          verb: create
        requestCount: 37
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 5790
    requestCount: 19676
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9100
          verb: create
        requestCount: 9100
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3211
          verb: create
        requestCount: 3211
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 347
          verb: create
        requestCount: 347
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 291
          verb: create
        requestCount: 291
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 147
          verb: create
        requestCount: 147
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 105
          verb: create
        requestCount: 105
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 69
          verb: create
        requestCount: 69
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 66
          verb: create
        requestCount: 66
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 13948
    - byUser:
      - byVerb:
        - requestCount: 72
          verb: create
        requestCount: 72
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 17
          verb: create
        requestCount: 17
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 16
          verb: create
        requestCount: 16
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 12
          verb: create
        requestCount: 12
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 11
          verb: create
        requestCount: 11
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 10
          verb: create
        requestCount: 10
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 3
          verb: create
        requestCount: 3
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.8
      requestCount: 165
    - byUser:
      - byVerb:
        - requestCount: 4630
          verb: create
        requestCount: 4630
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 372
          verb: create
        requestCount: 372
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 346
          verb: create
        requestCount: 346
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 87
          verb: create
        requestCount: 87
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 61
          verb: create
        requestCount: 61
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 61
          verb: create
        requestCount: 61
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 60
          verb: create
        requestCount: 60
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 23
          verb: create
        requestCount: 23
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      nodeName: 10.9.212.9
      requestCount: 5888
    requestCount: 20001
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8870
          verb: create
        requestCount: 8870
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3180
          verb: create
        requestCount: 3180
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 342
          verb: create
        requestCount: 342
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 287
          verb: create
        requestCount: 287
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 150
          verb: create
        requestCount: 150
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 114
          verb: create
        requestCount: 114
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 70
          verb: create
        requestCount: 70
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 69
          verb: create
        requestCount: 69
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 13694
    - byUser:
      - byVerb:
        - requestCount: 81
          verb: create
        requestCount: 81
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 32
          verb: create
        requestCount: 32
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 19
          verb: create
        requestCount: 19
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 15
          verb: create
        requestCount: 15
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 11
          verb: create
        requestCount: 11
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 10
          verb: create
        requestCount: 10
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 5
          verb: create
        requestCount: 5
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 4
          verb: create
        requestCount: 4
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 3
          verb: create
        requestCount: 3
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      nodeName: 10.9.212.8
      requestCount: 188
    - byUser:
      - byVerb:
        - requestCount: 4654
          verb: create
        requestCount: 4654
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 372
          verb: create
        requestCount: 372
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 356
          verb: create
        requestCount: 356
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 77
          verb: create
        requestCount: 77
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 61
          verb: create
        requestCount: 61
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 60
          verb: create
        requestCount: 60
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 59
          verb: create
        requestCount: 59
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 16
          verb: create
        requestCount: 16
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 13
          verb: create
        requestCount: 13
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      nodeName: 10.9.212.9
      requestCount: 5908
    requestCount: 19790
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8938
          verb: create
        requestCount: 8938
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3216
          verb: create
        requestCount: 3216
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 341
          verb: create
        requestCount: 341
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 293
          verb: create
        requestCount: 293
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 147
          verb: create
        requestCount: 147
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 90
          verb: create
        requestCount: 90
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 70
          verb: create
        requestCount: 70
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 69
          verb: create
        requestCount: 69
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 13776
    - byUser:
      - byVerb:
        - requestCount: 87
          verb: create
        requestCount: 87
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 25
          verb: create
        requestCount: 25
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 18
          verb: create
        requestCount: 18
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 17
          verb: create
        requestCount: 17
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 10
          verb: create
        requestCount: 10
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 10
          verb: create
        requestCount: 10
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 5
          verb: create
        requestCount: 5
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 3
          verb: create
        requestCount: 3
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.8
      requestCount: 191
    - byUser:
      - byVerb:
        - requestCount: 4549
          verb: create
        requestCount: 4549
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 372
          verb: create
        requestCount: 372
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 355
          verb: create
        requestCount: 355
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 99
          verb: create
        requestCount: 99
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 62
          verb: create
        requestCount: 62
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 60
          verb: create
        requestCount: 60
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 59
          verb: create
        requestCount: 59
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 33
          verb: create
        requestCount: 33
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 27
          verb: create
        requestCount: 27
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 5856
    requestCount: 19823
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8843
          verb: create
        requestCount: 8843
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3145
          verb: create
        requestCount: 3145
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 329
          verb: create
        requestCount: 329
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 285
          verb: create
        requestCount: 285
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 148
          verb: create
        requestCount: 148
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 76
          verb: create
        requestCount: 76
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 68
          verb: create
        requestCount: 68
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 66
          verb: create
        requestCount: 66
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 13572
    - byUser:
      - byVerb:
        - requestCount: 75
          verb: create
        requestCount: 75
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 20
          verb: create
        requestCount: 20
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 11
          verb: create
        requestCount: 11
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 6
          verb: create
        requestCount: 6
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 4
          verb: create
        requestCount: 4
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 4
          verb: create
        requestCount: 4
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 1
          verb: create
        requestCount: 1
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.8
      requestCount: 133
    - byUser:
      - byVerb:
        - requestCount: 4565
          verb: create
        requestCount: 4565
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 372
          verb: create
        requestCount: 372
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 369
          verb: create
        requestCount: 369
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 106
          verb: create
        requestCount: 106
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 62
          verb: create
        requestCount: 62
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 60
          verb: create
        requestCount: 60
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 58
          verb: create
        requestCount: 58
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 41
          verb: create
        requestCount: 41
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 18
          verb: create
        requestCount: 18
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      nodeName: 10.9.212.9
      requestCount: 5891
    requestCount: 19596
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8895
          verb: create
        requestCount: 8895
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3152
          verb: create
        requestCount: 3152
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 356
          verb: create
        requestCount: 356
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 284
          verb: create
        requestCount: 284
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 150
          verb: create
        requestCount: 150
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 92
          verb: create
        requestCount: 92
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 71
          verb: create
        requestCount: 71
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 66
          verb: create
        requestCount: 66
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 13678
    - byUser:
      - byVerb:
        - requestCount: 92
          verb: create
        requestCount: 92
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 22
          verb: create
        requestCount: 22
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 18
          verb: create
        requestCount: 18
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 9
          verb: create
        requestCount: 9
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 5
          verb: create
        requestCount: 5
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 4
          verb: create
        requestCount: 4
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 4
          verb: create
        requestCount: 4
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      nodeName: 10.9.212.8
      requestCount: 166
    - byUser:
      - byVerb:
        - requestCount: 4572
          verb: create
        requestCount: 4572
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 372
          verb: create
        requestCount: 372
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 344
          verb: create
        requestCount: 344
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 80
          verb: create
        requestCount: 80
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 63
          verb: create
        requestCount: 63
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 60
          verb: create
        requestCount: 60
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 59
          verb: create
        requestCount: 59
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 36
          verb: create
        requestCount: 36
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 16
          verb: create
        requestCount: 16
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      nodeName: 10.9.212.9
      requestCount: 5842
    requestCount: 19686
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8836
          verb: create
        requestCount: 8836
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3198
          verb: create
        requestCount: 3198
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 362
          verb: create
        requestCount: 362
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 291
          verb: create
        requestCount: 291
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 148
          verb: create
        requestCount: 148
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 107
          verb: create
        requestCount: 107
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 70
          verb: create
        requestCount: 70
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 62
          verb: create
        requestCount: 62
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      nodeName: 10.9.212.10
      requestCount: 13686
    - byUser:
      - byVerb:
        - requestCount: 84
          verb: create
        requestCount: 84
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 18
          verb: create
        requestCount: 18
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 16
          verb: create
        requestCount: 16
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 14
          verb: create
        requestCount: 14
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 12
          verb: create
        requestCount: 12
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 10
          verb: create
        requestCount: 10
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 10
          verb: create
        requestCount: 10
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 4
          verb: create
        requestCount: 4
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 4
          verb: create
        requestCount: 4
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 3
          verb: create
        requestCount: 3
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.8
      requestCount: 175
    - byUser:
      - byVerb:
        - requestCount: 4610
          verb: create
        requestCount: 4610
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 372
          verb: create
        requestCount: 372
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 344
          verb: create
        requestCount: 344
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 74
          verb: create
        requestCount: 74
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 60
          verb: create
        requestCount: 60
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 59
          verb: create
        requestCount: 59
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 59
          verb: create
        requestCount: 59
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 16
          verb: create
        requestCount: 16
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 16
          verb: create
        requestCount: 16
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 5850
    requestCount: 19711
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8948
          verb: create
        requestCount: 8948
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3222
          verb: create
        requestCount: 3222
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 286
          verb: create
        requestCount: 286
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 150
          verb: create
        requestCount: 150
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 63
          verb: create
        requestCount: 63
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 61
          verb: create
        requestCount: 61
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      nodeName: 10.9.212.10
      requestCount: 13823
    - byUser:
      - byVerb:
        - requestCount: 73
          verb: create
        requestCount: 73
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 12
          verb: create
        requestCount: 12
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 7
          verb: create
        requestCount: 7
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 5
          verb: create
        requestCount: 5
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 4
          verb: create
        requestCount: 4
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      nodeName: 10.9.212.8
      requestCount: 111
    - byUser:
      - byVerb:
        - requestCount: 4537
          verb: create
        requestCount: 4537
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 372
          verb: create
        requestCount: 372
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 341
          verb: create
        requestCount: 341
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 73
          verb: create
        requestCount: 73
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 64
          verb: create
        requestCount: 64
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 62
          verb: create
        requestCount: 62
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 62
          verb: create
        requestCount: 62
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 16
          verb: create
        requestCount: 16
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 16
          verb: create
        requestCount: 16
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      nodeName: 10.9.212.9
      requestCount: 5783
    requestCount: 19717
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9006
          verb: create
        requestCount: 9006
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3220
          verb: create
        requestCount: 3220
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 294
          verb: create
        requestCount: 294
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 145
          verb: create
        requestCount: 145
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 114
          verb: create
        requestCount: 114
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 69
          verb: create
        requestCount: 69
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 66
          verb: create
        requestCount: 66
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 13886
    - byUser:
      - byVerb:
        - requestCount: 104
          verb: create
        requestCount: 104
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 27
          verb: create
        requestCount: 27
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 20
          verb: create
        requestCount: 20
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 18
          verb: create
        requestCount: 18
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 15
          verb: create
        requestCount: 15
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 10
          verb: create
        requestCount: 10
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 6
          verb: create
        requestCount: 6
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 4
          verb: create
        requestCount: 4
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1
          verb: create
        requestCount: 1
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.8
      requestCount: 205
    - byUser:
      - byVerb:
        - requestCount: 4531
          verb: create
        requestCount: 4531
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 372
          verb: create
        requestCount: 372
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 345
          verb: create
        requestCount: 345
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 61
          verb: create
        requestCount: 61
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 59
          verb: create
        requestCount: 59
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 59
          verb: create
        requestCount: 59
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 46
          verb: create
        requestCount: 46
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 33
          verb: create
        requestCount: 33
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 21
          verb: create
        requestCount: 21
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      nodeName: 10.9.212.9
      requestCount: 5767
    requestCount: 19858
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8960
          verb: create
        requestCount: 8960
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3168
          verb: create
        requestCount: 3168
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 352
          verb: create
        requestCount: 352
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 282
          verb: create
        requestCount: 282
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 152
          verb: create
        requestCount: 152
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 101
          verb: create
        requestCount: 101
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 71
          verb: create
        requestCount: 71
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 66
          verb: create
        requestCount: 66
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 13764
    - byUser:
      - byVerb:
        - requestCount: 79
          verb: create
        requestCount: 79
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 22
          verb: create
        requestCount: 22
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 19
          verb: create
        requestCount: 19
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 14
          verb: create
        requestCount: 14
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 10
          verb: create
        requestCount: 10
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: create
        requestCount: 6
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 6
          verb: create
        requestCount: 6
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 6
          verb: create
        requestCount: 6
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 6
          verb: create
        requestCount: 6
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 3
          verb: create
        requestCount: 3
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.8
      requestCount: 171
    - byUser:
      - byVerb:
        - requestCount: 4523
          verb: create
        requestCount: 4523
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 372
          verb: create
        requestCount: 372
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 347
          verb: create
        requestCount: 347
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 87
          verb: create
        requestCount: 87
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 60
          verb: create
        requestCount: 60
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 60
          verb: create
        requestCount: 60
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 58
          verb: create
        requestCount: 58
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 37
          verb: create
        requestCount: 37
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 10
          verb: create
        requestCount: 10
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 5794
    requestCount: 19729
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9004
          verb: create
        requestCount: 9004
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3254
          verb: create
        requestCount: 3254
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 338
          verb: create
        requestCount: 338
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 293
          verb: create
        requestCount: 293
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 147
          verb: create
        requestCount: 147
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 69
          verb: create
        requestCount: 69
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 66
          verb: create
        requestCount: 66
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 13904
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: create
        requestCount: 69
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 23
          verb: create
        requestCount: 23
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 22
          verb: create
        requestCount: 22
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 21
          verb: create
        requestCount: 21
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 12
          verb: create
        requestCount: 12
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 12
          verb: create
        requestCount: 12
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 4
          verb: create
        requestCount: 4
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1
          verb: create
        requestCount: 1
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.8
      requestCount: 174
    - byUser:
      - byVerb:
        - requestCount: 4628
          verb: create
        requestCount: 4628
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 372
          verb: create
        requestCount: 372
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 356
          verb: create
        requestCount: 356
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 78
          verb: create
        requestCount: 78
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 61
          verb: create
        requestCount: 61
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 60
          verb: create
        requestCount: 60
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 58
          verb: create
        requestCount: 58
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 29
          verb: create
        requestCount: 29
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 12
          verb: create
        requestCount: 12
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      nodeName: 10.9.212.9
      requestCount: 5894
    requestCount: 19972
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8950
          verb: create
        requestCount: 8950
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3187
          verb: create
        requestCount: 3187
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 345
          verb: create
        requestCount: 345
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 287
          verb: create
        requestCount: 287
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 148
          verb: create
        requestCount: 148
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 111
          verb: create
        requestCount: 111
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 70
          verb: create
        requestCount: 70
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 68
          verb: create
        requestCount: 68
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      nodeName: 10.9.212.10
      requestCount: 13778
    - byUser:
      - byVerb:
        - requestCount: 90
          verb: create
        requestCount: 90
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 25
          verb: create
        requestCount: 25
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 25
          verb: create
        requestCount: 25
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 16
          verb: create
        requestCount: 16
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 10
          verb: create
        requestCount: 10
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 9
          verb: create
        requestCount: 9
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 9
          verb: create
        requestCount: 9
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 4
          verb: create
        requestCount: 4
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 4
          verb: create
        requestCount: 4
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 1
          verb: create
        requestCount: 1
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.8
      requestCount: 193
    - byUser:
      - byVerb:
        - requestCount: 4533
          verb: create
        requestCount: 4533
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 372
          verb: create
        requestCount: 372
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 357
          verb: create
        requestCount: 357
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 75
          verb: create
        requestCount: 75
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 64
          verb: create
        requestCount: 64
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 61
          verb: create
        requestCount: 61
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 58
          verb: create
        requestCount: 58
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 17
          verb: create
        requestCount: 17
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 14
          verb: create
        requestCount: 14
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      nodeName: 10.9.212.9
      requestCount: 5791
    requestCount: 19762
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8990
          verb: create
        requestCount: 8990
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3234
          verb: create
        requestCount: 3234
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 343
          verb: create
        requestCount: 343
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 288
          verb: create
        requestCount: 288
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 148
          verb: create
        requestCount: 148
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 122
          verb: create
        requestCount: 122
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 69
          verb: create
        requestCount: 69
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 68
          verb: create
        requestCount: 68
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 13874
    - byUser:
      - byVerb:
        - requestCount: 65
          verb: create
        requestCount: 65
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 42
          verb: create
        requestCount: 42
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 27
          verb: create
        requestCount: 27
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 19
          verb: create
        requestCount: 19
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 17
          verb: create
        requestCount: 17
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 16
          verb: create
        requestCount: 16
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 16
          verb: create
        requestCount: 16
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: create
        requestCount: 6
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 4
          verb: create
        requestCount: 4
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.8
      requestCount: 214
    - byUser:
      - byVerb:
        - requestCount: 4390
          verb: create
        requestCount: 4390
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 356
          verb: create
        requestCount: 356
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 346
          verb: create
        requestCount: 346
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 230
          verb: create
        requestCount: 230
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 84
          verb: create
        requestCount: 84
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 58
          verb: create
        requestCount: 58
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 58
          verb: create
        requestCount: 58
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 28
          verb: create
        requestCount: 28
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 18
          verb: create
        requestCount: 18
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 16
          verb: create
        requestCount: 16
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.9
      requestCount: 5584
    requestCount: 19672
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 5214
          verb: create
        requestCount: 5214
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 1827
          verb: create
        requestCount: 1827
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 207
          verb: create
        requestCount: 207
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 200
          verb: create
        requestCount: 200
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 158
          verb: create
        requestCount: 158
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 146
          verb: create
        requestCount: 146
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 85
          verb: create
        requestCount: 85
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 40
          verb: create
        requestCount: 40
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 37
          verb: create
        requestCount: 37
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 36
          verb: create
        requestCount: 36
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      nodeName: 10.9.212.10
      requestCount: 8008
    - byUser:
      - byVerb:
        - requestCount: 86
          verb: create
        requestCount: 86
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 43
          verb: create
        requestCount: 43
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 30
          verb: create
        requestCount: 30
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 13
          verb: create
        requestCount: 13
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 4
          verb: create
        requestCount: 4
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 3
          verb: create
        requestCount: 3
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 3
          verb: create
        requestCount: 3
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      nodeName: 10.9.212.8
      requestCount: 192
    - byUser:
      - byVerb:
        - requestCount: 2852
          verb: create
        requestCount: 2852
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 233
          verb: create
        requestCount: 233
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 218
          verb: create
        requestCount: 218
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 150
          verb: create
        requestCount: 150
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 40
          verb: create
        requestCount: 40
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 40
          verb: create
        requestCount: 40
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 36
          verb: create
        requestCount: 36
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 15
          verb: create
        requestCount: 15
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 6
          verb: create
        requestCount: 6
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      nodeName: 10.9.212.9
      requestCount: 3599
    requestCount: 11799
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8756
          verb: create
        requestCount: 8756
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3211
          verb: create
        requestCount: 3211
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 346
          verb: create
        requestCount: 346
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 291
          verb: create
        requestCount: 291
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 150
          verb: create
        requestCount: 150
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 142
          verb: create
        requestCount: 142
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 70
          verb: create
        requestCount: 70
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 66
          verb: create
        requestCount: 66
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 13644
    - byUser:
      - byVerb:
        - requestCount: 62
          verb: create
        requestCount: 62
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 45
          verb: create
        requestCount: 45
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 23
          verb: create
        requestCount: 23
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 23
          verb: create
        requestCount: 23
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 11
          verb: create
        requestCount: 11
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 7
          verb: create
        requestCount: 7
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 6
          verb: create
        requestCount: 6
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.8
      requestCount: 195
    - byUser:
      - byVerb:
        - requestCount: 4552
          verb: create
        requestCount: 4552
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 372
          verb: create
        requestCount: 372
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 359
          verb: create
        requestCount: 359
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 64
          verb: create
        requestCount: 64
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 63
          verb: create
        requestCount: 63
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 62
          verb: create
        requestCount: 62
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 60
          verb: create
        requestCount: 60
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 12
          verb: create
        requestCount: 12
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 10
          verb: create
        requestCount: 10
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      nodeName: 10.9.212.9
      requestCount: 5794
    requestCount: 19633
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8976
          verb: create
        requestCount: 8976
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3181
          verb: create
        requestCount: 3181
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 362
          verb: create
        requestCount: 362
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 286
          verb: create
        requestCount: 286
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 150
          verb: create
        requestCount: 150
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 98
          verb: create
        requestCount: 98
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 72
          verb: create
        requestCount: 72
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 69
          verb: create
        requestCount: 69
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 13806
    - byUser:
      - byVerb:
        - requestCount: 100
          verb: create
        requestCount: 100
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 34
          verb: create
        requestCount: 34
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 18
          verb: create
        requestCount: 18
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 17
          verb: create
        requestCount: 17
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 10
          verb: create
        requestCount: 10
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 6
          verb: create
        requestCount: 6
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 5
          verb: create
        requestCount: 5
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 5
          verb: create
        requestCount: 5
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 5
          verb: create
        requestCount: 5
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.8
      requestCount: 202
    - byUser:
      - byVerb:
        - requestCount: 4610
          verb: create
        requestCount: 4610
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 373
          verb: create
        requestCount: 373
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 346
          verb: create
        requestCount: 346
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 75
          verb: create
        requestCount: 75
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 60
          verb: create
        requestCount: 60
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 58
          verb: create
        requestCount: 58
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 58
          verb: create
        requestCount: 58
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 22
          verb: create
        requestCount: 22
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 15
          verb: create
        requestCount: 15
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      nodeName: 10.9.212.9
      requestCount: 5857
    requestCount: 19865
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8899
          verb: create
        requestCount: 8899
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3170
          verb: create
        requestCount: 3170
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 348
          verb: create
        requestCount: 348
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 287
          verb: create
        requestCount: 287
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 144
          verb: create
        requestCount: 144
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 83
          verb: create
        requestCount: 83
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 70
          verb: create
        requestCount: 70
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 68
          verb: create
        requestCount: 68
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 13681
    - byUser:
      - byVerb:
        - requestCount: 83
          verb: create
        requestCount: 83
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 15
          verb: create
        requestCount: 15
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 7
          verb: create
        requestCount: 7
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 5
          verb: create
        requestCount: 5
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 3
          verb: create
        requestCount: 3
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 3
          verb: create
        requestCount: 3
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      nodeName: 10.9.212.8
      requestCount: 136
    - byUser:
      - byVerb:
        - requestCount: 4606
          verb: create
        requestCount: 4606
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 372
          verb: create
        requestCount: 372
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 351
          verb: create
        requestCount: 351
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 103
          verb: create
        requestCount: 103
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 62
          verb: create
        requestCount: 62
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 62
          verb: create
        requestCount: 62
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 59
          verb: create
        requestCount: 59
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 18
          verb: create
        requestCount: 18
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 18
          verb: create
        requestCount: 18
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 5891
    requestCount: 19708
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8780
          verb: create
        requestCount: 8780
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3174
          verb: create
        requestCount: 3174
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 338
          verb: create
        requestCount: 338
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 294
          verb: create
        requestCount: 294
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 153
          verb: create
        requestCount: 153
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 115
          verb: create
        requestCount: 115
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 69
          verb: create
        requestCount: 69
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 67
          verb: create
        requestCount: 67
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 13602
    - byUser:
      - byVerb:
        - requestCount: 57
          verb: create
        requestCount: 57
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 17
          verb: create
        requestCount: 17
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 15
          verb: create
        requestCount: 15
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 10
          verb: create
        requestCount: 10
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      nodeName: 10.9.212.8
      requestCount: 115
    - byUser:
      - byVerb:
        - requestCount: 4682
          verb: create
        requestCount: 4682
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 372
          verb: create
        requestCount: 372
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 359
          verb: create
        requestCount: 359
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 102
          verb: create
        requestCount: 102
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 60
          verb: create
        requestCount: 60
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 59
          verb: create
        requestCount: 59
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 59
          verb: create
        requestCount: 59
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 24
          verb: create
        requestCount: 24
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 13
          verb: create
        requestCount: 13
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      nodeName: 10.9.212.9
      requestCount: 5970
    requestCount: 19687
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8792
          verb: create
        requestCount: 8792
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3240
          verb: create
        requestCount: 3240
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 351
          verb: create
        requestCount: 351
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 285
          verb: create
        requestCount: 285
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 146
          verb: create
        requestCount: 146
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 110
          verb: create
        requestCount: 110
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 69
          verb: create
        requestCount: 69
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 66
          verb: create
        requestCount: 66
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 13671
    - byUser:
      - byVerb:
        - requestCount: 78
          verb: create
        requestCount: 78
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 27
          verb: create
        requestCount: 27
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 24
          verb: create
        requestCount: 24
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 14
          verb: create
        requestCount: 14
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 14
          verb: create
        requestCount: 14
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 12
          verb: create
        requestCount: 12
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 12
          verb: create
        requestCount: 12
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 6
          verb: create
        requestCount: 6
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 4
          verb: create
        requestCount: 4
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.8
      requestCount: 199
    - byUser:
      - byVerb:
        - requestCount: 4605
          verb: create
        requestCount: 4605
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 372
          verb: create
        requestCount: 372
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 342
          verb: create
        requestCount: 342
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 76
          verb: create
        requestCount: 76
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 63
          verb: create
        requestCount: 63
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 62
          verb: create
        requestCount: 62
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 61
          verb: create
        requestCount: 61
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 16
          verb: create
        requestCount: 16
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 14
          verb: create
        requestCount: 14
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 5851
    requestCount: 19721
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8893
          verb: create
        requestCount: 8893
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3209
          verb: create
        requestCount: 3209
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 348
          verb: create
        requestCount: 348
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 283
          verb: create
        requestCount: 283
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 151
          verb: create
        requestCount: 151
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 100
          verb: create
        requestCount: 100
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 70
          verb: create
        requestCount: 70
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 58
          verb: create
        requestCount: 58
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      nodeName: 10.9.212.10
      requestCount: 13724
    - byUser:
      - byVerb:
        - requestCount: 48
          verb: create
        requestCount: 48
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 20
          verb: create
        requestCount: 20
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 19
          verb: create
        requestCount: 19
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 16
          verb: create
        requestCount: 16
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 12
          verb: create
        requestCount: 12
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 12
          verb: create
        requestCount: 12
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 12
          verb: create
        requestCount: 12
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 6
          verb: create
        requestCount: 6
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.8
      requestCount: 155
    - byUser:
      - byVerb:
        - requestCount: 4550
          verb: create
        requestCount: 4550
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 372
          verb: create
        requestCount: 372
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 347
          verb: create
        requestCount: 347
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 116
          verb: create
        requestCount: 116
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 62
          verb: create
        requestCount: 62
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 61
          verb: create
        requestCount: 61
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 58
          verb: create
        requestCount: 58
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 30
          verb: create
        requestCount: 30
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 18
          verb: create
        requestCount: 18
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 5854
    requestCount: 19733
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8775
          verb: create
        requestCount: 8775
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3194
          verb: create
        requestCount: 3194
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 359
          verb: create
        requestCount: 359
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 287
          verb: create
        requestCount: 287
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 147
          verb: create
        requestCount: 147
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 107
          verb: create
        requestCount: 107
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 69
          verb: create
        requestCount: 69
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 62
          verb: create
        requestCount: 62
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      nodeName: 10.9.212.10
      requestCount: 13612
    - byUser:
      - byVerb:
        - requestCount: 78
          verb: create
        requestCount: 78
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 30
          verb: create
        requestCount: 30
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 20
          verb: create
        requestCount: 20
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 13
          verb: create
        requestCount: 13
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 12
          verb: create
        requestCount: 12
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 12
          verb: create
        requestCount: 12
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 10
          verb: create
        requestCount: 10
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 6
          verb: create
        requestCount: 6
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 4
          verb: create
        requestCount: 4
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.8
      requestCount: 187
    - byUser:
      - byVerb:
        - requestCount: 4591
          verb: create
        requestCount: 4591
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 372
          verb: create
        requestCount: 372
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 350
          verb: create
        requestCount: 350
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 80
          verb: create
        requestCount: 80
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 61
          verb: create
        requestCount: 61
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 61
          verb: create
        requestCount: 61
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 59
          verb: create
        requestCount: 59
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 19
          verb: create
        requestCount: 19
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 14
          verb: create
        requestCount: 14
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 5847
    requestCount: 19646
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8925
          verb: create
        requestCount: 8925
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 3215
          verb: create
        requestCount: 3215
        userAgent: oauth-apiserver/v0.0.0
        username: system:serviceaccount:openshift-oauth-apiserver:oauth-apiserver-sa
      - byVerb:
        - requestCount: 360
          verb: create
        requestCount: 360
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 346
          verb: create
        requestCount: 346
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 297
          verb: create
        requestCount: 297
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 147
          verb: create
        requestCount: 147
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 117
          verb: create
        requestCount: 117
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 66
          verb: create
        requestCount: 66
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 61
          verb: create
        requestCount: 61
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: 10.9.212.10
      requestCount: 13786
    - byUser:
      - byVerb:
        - requestCount: 76
          verb: create
        requestCount: 76
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 18
          verb: create
        requestCount: 18
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 16
          verb: create
        requestCount: 16
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 14
          verb: create
        requestCount: 14
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 13
          verb: create
        requestCount: 13
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 10
          verb: create
        requestCount: 10
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 8
          verb: create
        requestCount: 8
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 4
          verb: create
        requestCount: 4
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 2
          verb: create
        requestCount: 2
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      nodeName: 10.9.212.8
      requestCount: 163
    - byUser:
      - byVerb:
        - requestCount: 4634
          verb: create
        requestCount: 4634
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 372
          verb: create
        requestCount: 372
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      - byVerb:
        - requestCount: 351
          verb: create
        requestCount: 351
        userAgent: metrics-server/v4.17.0
        username: system:serviceaccount:openshift-monitoring:metrics-server
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 78
          verb: create
        requestCount: 78
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 62
          verb: create
        requestCount: 62
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 60
          verb: create
        requestCount: 60
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 58
          verb: create
        requestCount: 58
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 21
          verb: create
        requestCount: 21
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 10
          verb: create
        requestCount: 10
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      nodeName: 10.9.212.9
      requestCount: 5886
    requestCount: 19835
  requestCount: 446278
