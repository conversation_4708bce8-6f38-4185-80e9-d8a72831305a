---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T15:00:22Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T15:00:22Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:41:06Z"
  name: oauths.v1.config.openshift.io
  resourceVersion: "********"
  uid: 886faa5e-ea9f-422b-9ff6-bc04ed0332bb
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 11
          verb: get
        requestCount: 11
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.9.212.10
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: oc/4.17.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      nodeName: 10.9.212.8
      requestCount: 2
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 23
    requestCount: 41
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 29
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      nodeName: 10.9.212.9
      requestCount: 38
    requestCount: 67
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.9.212.10
      requestCount: 28
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 37
    requestCount: 65
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 29
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 35
    requestCount: 64
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.9.212.10
      requestCount: 28
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 33
    requestCount: 61
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 29
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 37
    requestCount: 66
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.9.212.10
      requestCount: 28
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 35
    requestCount: 63
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 28
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 39
    requestCount: 67
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.9.212.10
      requestCount: 28
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      nodeName: 10.9.212.9
      requestCount: 36
    requestCount: 64
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 28
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 36
    requestCount: 64
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.9.212.10
      requestCount: 27
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      nodeName: 10.9.212.9
      requestCount: 38
    requestCount: 65
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 29
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      nodeName: 10.9.212.9
      requestCount: 36
    requestCount: 65
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.9.212.10
      requestCount: 27
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 35
    requestCount: 62
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 28
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 34
    requestCount: 62
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.9.212.10
      requestCount: 27
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 37
    requestCount: 64
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 11
          verb: get
        requestCount: 11
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.9.212.10
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: oc/4.17.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      nodeName: 10.9.212.8
      requestCount: 2
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 23
    requestCount: 41
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 28
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 35
    requestCount: 63
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.9.212.10
      requestCount: 28
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 37
    requestCount: 65
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 28
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      nodeName: 10.9.212.9
      requestCount: 36
    requestCount: 64
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.9.212.10
      requestCount: 27
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 36
    requestCount: 63
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 27
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      nodeName: 10.9.212.9
      requestCount: 39
    requestCount: 66
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.9.212.10
      requestCount: 28
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 35
    requestCount: 63
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 30
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 37
    requestCount: 67
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: 10.9.212.10
      requestCount: 25
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 35
    requestCount: 60
  requestCount: 1451
