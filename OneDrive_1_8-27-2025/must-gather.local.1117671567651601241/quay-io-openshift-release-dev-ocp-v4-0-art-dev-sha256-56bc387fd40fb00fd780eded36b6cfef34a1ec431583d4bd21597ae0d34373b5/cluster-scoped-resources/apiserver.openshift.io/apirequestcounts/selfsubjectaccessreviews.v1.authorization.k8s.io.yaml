---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T15:12:01Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T15:12:01Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:38:16Z"
  name: selfsubjectaccessreviews.v1.authorization.k8s.io
  resourceVersion: "********"
  uid: b0ebf9e8-dee5-4655-bea6-4a0e2ff46835
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 280
          verb: create
        requestCount: 280
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 280
    requestCount: 280
  last24h:
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 459
          verb: create
        requestCount: 459
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 459
    requestCount: 459
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 280
          verb: create
        requestCount: 280
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 280
    requestCount: 280
  - requestCount: 0
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: create
        requestCount: 3
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 483
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  requestCount: 10822
