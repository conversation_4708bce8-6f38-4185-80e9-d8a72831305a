---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T15:28:22Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T15:28:22Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:40:25Z"
  name: upgradeconfigs.v1alpha1.upgrade.managed.openshift.io
  resourceVersion: "********"
  uid: 9ec67bfb-5c80-491f-94f9-5fbad114d15a
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 3
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 320
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 320
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 17
    requestCount: 337
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 458
          verb: get
        - requestCount: 4
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 470
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 470
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 29
    requestCount: 499
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 470
          verb: get
        - requestCount: 4
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 482
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 482
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 28
    requestCount: 510
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 463
          verb: get
        - requestCount: 4
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 474
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 474
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 28
    requestCount: 502
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 462
          verb: get
        - requestCount: 4
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 474
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 474
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 27
    requestCount: 501
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 467
          verb: get
        - requestCount: 4
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 480
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 480
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 29
    requestCount: 509
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 459
          verb: get
        - requestCount: 3
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 470
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 470
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 25
    requestCount: 495
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 472
          verb: get
        - requestCount: 4
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 484
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 484
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 28
    requestCount: 512
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 463
          verb: get
        - requestCount: 4
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 475
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 475
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 28
    requestCount: 503
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 457
          verb: get
        - requestCount: 4
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 468
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 468
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 28
    requestCount: 496
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 470
          verb: get
        - requestCount: 4
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 482
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 482
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 29
    requestCount: 511
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 462
          verb: get
        - requestCount: 4
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 474
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 474
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 27
    requestCount: 501
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 463
          verb: get
        - requestCount: 4
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 475
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 475
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 30
    requestCount: 505
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 469
          verb: get
        - requestCount: 4
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 481
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 481
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 27
    requestCount: 508
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 459
          verb: get
        - requestCount: 4
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 471
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 471
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 29
    requestCount: 500
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 3
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 320
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 320
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 17
    requestCount: 337
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 459
          verb: get
        - requestCount: 4
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 470
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 470
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 30
    requestCount: 500
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 470
          verb: get
        - requestCount: 4
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 482
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 482
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 30
    requestCount: 512
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 463
          verb: get
        - requestCount: 4
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 475
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 475
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 33
    requestCount: 508
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 470
          verb: get
        - requestCount: 4
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 483
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 483
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 29
    requestCount: 512
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 458
          verb: get
        - requestCount: 4
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 469
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 469
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 27
    requestCount: 496
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 460
          verb: get
        - requestCount: 4
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 472
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 472
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 28
    requestCount: 500
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 472
          verb: get
        - requestCount: 4
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 484
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 484
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 27
    requestCount: 511
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 461
          verb: get
        - requestCount: 4
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 472
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      nodeName: 10.9.212.10
      requestCount: 472
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 28
    requestCount: 500
  requestCount: 11428
