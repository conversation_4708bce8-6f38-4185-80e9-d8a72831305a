---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T14:58:59Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:58:59Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:41:04Z"
  name: projecthelmchartrepositories.v1beta1.helm.openshift.io
  resourceVersion: "33963586"
  uid: 0174279a-5f15-4573-aca7-e487c1a8de52
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 18
    requestCount: 24
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 5
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 31
    requestCount: 39
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 30
    requestCount: 36
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 4
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 1
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 30
    requestCount: 35
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 2
    - byUser:
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 2
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 32
    requestCount: 36
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 29
    requestCount: 35
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 2
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 27
    requestCount: 32
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 1
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 4
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 30
    requestCount: 35
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 4
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 1
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 30
    requestCount: 35
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 2
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 31
    requestCount: 36
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 31
    requestCount: 37
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 6
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 1
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 30
    requestCount: 37
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 1
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 4
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 28
    requestCount: 33
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 4
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 31
    requestCount: 35
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 5
    - byUser:
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 2
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 33
    requestCount: 40
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 18
    requestCount: 24
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 10
          verb: list
        requestCount: 10
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 10
    - byUser:
      - byVerb:
        - requestCount: 93
          verb: list
        requestCount: 93
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 93
    - byUser:
      - byVerb:
        - requestCount: 85
          verb: list
        requestCount: 85
        userAgent: ""
        username: M0681
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 114
    requestCount: 217
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        requestCount: 6
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 6
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: list
        requestCount: 33
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 33
    - byUser:
      - byVerb:
        - requestCount: 31
          verb: list
        requestCount: 31
        userAgent: ""
        username: M0681
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 60
    requestCount: 99
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 5
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: list
        requestCount: 8
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: ""
        username: M0681
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 39
    requestCount: 52
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 28
          verb: list
        requestCount: 28
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 28
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 32
    requestCount: 77
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: list
        requestCount: 7
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 28
    requestCount: 38
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 2
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 32
    requestCount: 37
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 4
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 30
    requestCount: 37
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 2
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: list
        requestCount: 3
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 3
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: list
        requestCount: 4
        userAgent: ""
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 32
    requestCount: 37
  requestCount: 1119
