---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-26T14:35:07Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-26T14:35:07Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:41:08Z"
  name: lokistacks.v1.loki.grafana.com
  resourceVersion: "********"
  uid: ec7992f4-e2e5-4fe3-95f3-1b4422770e67
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  requestCount: 12
