---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-26T14:38:30Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-26T14:38:30Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:05:22Z"
  name: netnamespaces.v1.network.openshift.io
  resourceVersion: "********"
  uid: a8cfad7c-055d-45b0-9703-cde4eba6de78
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 1
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  requestCount: 11
