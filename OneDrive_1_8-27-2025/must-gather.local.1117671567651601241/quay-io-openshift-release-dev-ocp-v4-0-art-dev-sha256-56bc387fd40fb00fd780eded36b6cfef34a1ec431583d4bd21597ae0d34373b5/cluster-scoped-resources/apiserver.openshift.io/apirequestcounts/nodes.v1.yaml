---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T14:59:51Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:59:51Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:39:45Z"
  name: nodes.v1
  resourceVersion: "33962990"
  uid: ad13c728-8290-49c3-9c94-b7a31b9f6926
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 204
          verb: get
        - requestCount: 7
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 216
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 204
          verb: get
        - requestCount: 7
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 216
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 203
          verb: get
        - requestCount: 7
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 215
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 204
          verb: get
        - requestCount: 7
          verb: patch
        - requestCount: 4
          verb: watch
        requestCount: 215
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 204
          verb: get
        - requestCount: 6
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 215
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 204
          verb: get
        - requestCount: 7
          verb: patch
        - requestCount: 4
          verb: watch
        requestCount: 215
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 203
          verb: get
        - requestCount: 7
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 215
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 203
          verb: get
        - requestCount: 7
          verb: patch
        - requestCount: 3
          verb: watch
        requestCount: 213
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 204
          verb: get
        - requestCount: 6
          verb: patch
        - requestCount: 3
          verb: watch
        requestCount: 213
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 43
          verb: watch
        requestCount: 43
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.10
      requestCount: 2019
    - byUser:
      - byVerb:
        - requestCount: 206
          verb: get
        - requestCount: 7
          verb: patch
        - requestCount: 4
          verb: watch
        requestCount: 217
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 27
          verb: list
        requestCount: 27
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 5
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 2
          verb: get
        requestCount: 2
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 278
    - byUser:
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 484
          verb: watch
        requestCount: 486
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 221
          verb: get
        - requestCount: 7
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 233
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 221
          verb: get
        - requestCount: 7
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 233
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 45
          verb: list
        requestCount: 45
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 18
          verb: list
        requestCount: 18
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 12
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 18
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.9
      requestCount: 1101
    requestCount: 3398
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 81
          verb: watch
        requestCount: 81
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.10
      requestCount: 3430
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: list
        requestCount: 60
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: get
        requestCount: 7
        userAgent: Mozilla/5.0
        username: M0681
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 99
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 765
          verb: watch
        requestCount: 769
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 370
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 60
          verb: list
        requestCount: 60
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 29
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 28
          verb: list
        requestCount: 28
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      nodeName: 10.9.212.9
      requestCount: 2068
    requestCount: 5597
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 10
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 70
          verb: watch
        requestCount: 70
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.10
      requestCount: 3425
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: list
        requestCount: 70
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 10
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: Mozilla/5.0
        username: M0681
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 2
          verb: watch
        requestCount: 3
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      nodeName: 10.9.212.8
      requestCount: 117
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 766
          verb: watch
        requestCount: 769
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 54
          verb: list
        requestCount: 54
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 35
          verb: watch
        requestCount: 35
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 27
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 13
          verb: watch
        requestCount: 13
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.9
      requestCount: 2057
    requestCount: 5599
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 66
          verb: watch
        requestCount: 66
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.10
      requestCount: 3420
    - byUser:
      - byVerb:
        - requestCount: 57
          verb: list
        requestCount: 57
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 10
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 110
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 768
          verb: watch
        requestCount: 771
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 10
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 59
          verb: list
        requestCount: 59
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 28
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 2070
    requestCount: 5600
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 81
          verb: watch
        requestCount: 81
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.10
      requestCount: 3432
    - byUser:
      - byVerb:
        - requestCount: 67
          verb: list
        requestCount: 67
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 2
          verb: watch
        requestCount: 4
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 123
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 768
          verb: watch
        requestCount: 771
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 61
          verb: list
        requestCount: 61
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 29
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 2065
    requestCount: 5620
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 370
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 82
          verb: watch
        requestCount: 82
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.10
      requestCount: 3433
    - byUser:
      - byVerb:
        - requestCount: 67
          verb: list
        requestCount: 67
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 10
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 4
          verb: get
        requestCount: 4
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 124
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 767
          verb: watch
        requestCount: 768
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 54
          verb: list
        requestCount: 54
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 28
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.9
      requestCount: 2062
    requestCount: 5619
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 68
          verb: watch
        requestCount: 68
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.10
      requestCount: 3418
    - byUser:
      - byVerb:
        - requestCount: 67
          verb: list
        requestCount: 67
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 10
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 10
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 8
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 4
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 130
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 771
          verb: watch
        requestCount: 774
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 61
          verb: list
        requestCount: 61
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 28
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 2068
    requestCount: 5616
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 10
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 77
          verb: watch
        requestCount: 77
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.10
      requestCount: 3432
    - byUser:
      - byVerb:
        - requestCount: 61
          verb: list
        requestCount: 61
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 5
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 127
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 769
          verb: watch
        requestCount: 772
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 62
          verb: list
        requestCount: 62
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 28
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.9
      requestCount: 2069
    requestCount: 5628
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 51
          verb: list
        - requestCount: 16
          verb: watch
        requestCount: 67
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 3424
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: list
        requestCount: 60
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 120
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 767
          verb: watch
        requestCount: 770
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 74
          verb: list
        requestCount: 74
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 29
          verb: list
        requestCount: 29
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 27
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.9
      requestCount: 2078
    requestCount: 5622
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 370
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 54
          verb: list
        - requestCount: 15
          verb: watch
        requestCount: 69
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 3416
    - byUser:
      - byVerb:
        - requestCount: 57
          verb: list
        requestCount: 57
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 10
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 120
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 768
          verb: watch
        requestCount: 771
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 59
          verb: list
        requestCount: 59
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 28
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.9
      requestCount: 2069
    requestCount: 5605
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 51
          verb: list
        - requestCount: 17
          verb: watch
        requestCount: 68
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 3422
    - byUser:
      - byVerb:
        - requestCount: 63
          verb: list
        requestCount: 63
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 5
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 123
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 768
          verb: watch
        requestCount: 771
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 370
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 63
          verb: list
        requestCount: 63
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 28
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 2063
    requestCount: 5608
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 74
          verb: list
        requestCount: 74
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 3429
    - byUser:
      - byVerb:
        - requestCount: 52
          verb: list
        requestCount: 52
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 10
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 111
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 770
          verb: watch
        requestCount: 771
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 55
          verb: list
        requestCount: 55
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 28
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      nodeName: 10.9.212.9
      requestCount: 2058
    requestCount: 5598
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 54
          verb: list
        - requestCount: 15
          verb: watch
        requestCount: 69
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 3425
    - byUser:
      - byVerb:
        - requestCount: 63
          verb: list
        requestCount: 63
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 10
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 4
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 128
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 767
          verb: watch
        requestCount: 770
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 61
          verb: list
        requestCount: 61
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 28
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      nodeName: 10.9.212.9
      requestCount: 2068
    requestCount: 5621
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 74
          verb: watch
        requestCount: 74
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.10
      requestCount: 3429
    - byUser:
      - byVerb:
        - requestCount: 62
          verb: list
        requestCount: 62
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 10
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 2
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 121
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 768
          verb: watch
        requestCount: 771
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 64
          verb: list
        requestCount: 64
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 27
          verb: list
        requestCount: 27
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 27
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 2074
    requestCount: 5624
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 72
          verb: watch
        requestCount: 72
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.10
      requestCount: 3424
    - byUser:
      - byVerb:
        - requestCount: 253
          verb: get
        - requestCount: 8
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 268
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 59
          verb: list
        requestCount: 59
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 2
          verb: get
        requestCount: 2
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 389
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 768
          verb: watch
        requestCount: 771
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 100
          verb: get
        - requestCount: 3
          verb: patch
        - requestCount: 2
          verb: watch
        requestCount: 105
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 59
          verb: list
        requestCount: 59
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 27
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.9
      requestCount: 1793
    requestCount: 5606
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 204
          verb: get
        - requestCount: 7
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 216
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 204
          verb: get
        - requestCount: 7
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 216
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 203
          verb: get
        - requestCount: 7
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 215
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 204
          verb: get
        - requestCount: 7
          verb: patch
        - requestCount: 4
          verb: watch
        requestCount: 215
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 204
          verb: get
        - requestCount: 6
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 215
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 204
          verb: get
        - requestCount: 7
          verb: patch
        - requestCount: 4
          verb: watch
        requestCount: 215
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 203
          verb: get
        - requestCount: 7
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 215
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 203
          verb: get
        - requestCount: 7
          verb: patch
        - requestCount: 3
          verb: watch
        requestCount: 213
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 204
          verb: get
        - requestCount: 6
          verb: patch
        - requestCount: 3
          verb: watch
        requestCount: 213
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 43
          verb: watch
        requestCount: 43
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.10
      requestCount: 2019
    - byUser:
      - byVerb:
        - requestCount: 206
          verb: get
        - requestCount: 7
          verb: patch
        - requestCount: 4
          verb: watch
        requestCount: 217
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 27
          verb: list
        requestCount: 27
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 5
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: ovnkube-identity/v0.30.2
        username: system:serviceaccount:openshift-network-node-identity:network-node-identity
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 2
          verb: get
        requestCount: 2
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 278
    - byUser:
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 484
          verb: watch
        requestCount: 486
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 221
          verb: get
        - requestCount: 7
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 233
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 221
          verb: get
        - requestCount: 7
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 233
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 45
          verb: list
        requestCount: 45
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 18
          verb: list
        requestCount: 18
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 12
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 18
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.9
      requestCount: 1101
    requestCount: 3398
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 82
          verb: watch
        requestCount: 82
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.10
      requestCount: 3431
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: list
        requestCount: 69
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: get
        requestCount: 32
        userAgent: Mozilla/5.0
        username: M0681
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      nodeName: 10.9.212.8
      requestCount: 118
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 767
          verb: watch
        requestCount: 770
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 61
          verb: list
        requestCount: 61
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 34
          verb: get
        requestCount: 34
        userAgent: Mozilla/5.0
        username: M0681
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 32
          verb: list
        requestCount: 32
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 29
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.9
      requestCount: 2093
    requestCount: 5642
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 10
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 83
          verb: watch
        requestCount: 83
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.10
      requestCount: 3435
    - byUser:
      - byVerb:
        - requestCount: 65
          verb: list
        requestCount: 65
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 16
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 17
        userAgent: Mozilla/5.0
        username: M0681
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 98
    - byUser:
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 766
          verb: watch
        requestCount: 768
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 64
          verb: list
        requestCount: 64
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 30
          verb: list
        requestCount: 30
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 27
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 19
          verb: get
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: watch
        requestCount: 21
        userAgent: Mozilla/5.0
        username: M0681
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 2078
    requestCount: 5611
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 10
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 84
          verb: watch
        requestCount: 84
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.10
      requestCount: 3443
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: list
        requestCount: 64
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 87
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 769
          verb: watch
        requestCount: 773
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 54
          verb: list
        requestCount: 54
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 30
          verb: list
        requestCount: 30
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 29
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      nodeName: 10.9.212.9
      requestCount: 2070
    requestCount: 5600
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 78
          verb: watch
        requestCount: 78
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.10
      requestCount: 3431
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: list
        requestCount: 70
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 10
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: get
        requestCount: 7
        userAgent: Mozilla/5.0
        username: M0681
      - byVerb:
        - requestCount: 3
          verb: watch
        requestCount: 3
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      nodeName: 10.9.212.8
      requestCount: 98
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 765
          verb: watch
        requestCount: 768
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 46
          verb: list
        requestCount: 46
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 29
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.9
      requestCount: 2052
    requestCount: 5581
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 81
          verb: watch
        requestCount: 81
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.10
      requestCount: 3430
    - byUser:
      - byVerb:
        - requestCount: 57
          verb: list
        requestCount: 57
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 10
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 86
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 769
          verb: watch
        requestCount: 772
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 62
          verb: list
        requestCount: 62
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 28
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 27
          verb: list
        requestCount: 27
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.9
      requestCount: 2069
    requestCount: 5585
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 85
          verb: watch
        requestCount: 85
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.10
      requestCount: 3439
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: list
        requestCount: 60
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 10
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 4
          verb: get
        requestCount: 4
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 91
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 767
          verb: watch
        requestCount: 770
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 54
          verb: list
        requestCount: 54
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 28
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.9
      requestCount: 2062
    requestCount: 5592
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 75
          verb: watch
        requestCount: 75
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.10
      requestCount: 3427
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: list
        requestCount: 64
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 3
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 101
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 767
          verb: watch
        requestCount: 770
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 56
          verb: list
        requestCount: 56
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 28
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 2062
    requestCount: 5590
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 11
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 82
          verb: watch
        requestCount: 82
        userAgent: azure-cloud-node-manager/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager:cloud-node-manager
      nodeName: 10.9.212.10
      requestCount: 3433
    - byUser:
      - byVerb:
        - requestCount: 57
          verb: list
        requestCount: 57
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 92
    - byUser:
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 767
          verb: watch
        requestCount: 769
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 353
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 352
          verb: get
        - requestCount: 12
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 58
          verb: list
        requestCount: 58
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 20
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 28
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 2064
    requestCount: 5589
  requestCount: 126751
