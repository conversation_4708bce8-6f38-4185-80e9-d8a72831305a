---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T14:58:01Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:58:01Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:40:35Z"
  name: jobs.v1.batch
  resourceVersion: "********"
  uid: 992226a5-8892-45a6-9097-35d12d11349c
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 767
          verb: list
        requestCount: 767
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 780
    - byUser:
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 826
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 471
          verb: watch
        requestCount: 474
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 427
          verb: list
        requestCount: 427
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: update
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: create
        - requestCount: 3
          verb: delete
        requestCount: 6
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      nodeName: 10.9.212.9
      requestCount: 948
    requestCount: 2554
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: oc/4.17.0
        username: system:serviceaccount:openshift-image-registry:pruner
      nodeName: 10.9.212.10
      requestCount: 1323
    - byUser:
      - byVerb:
        - requestCount: 944
          verb: list
        requestCount: 944
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 944
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 5
          verb: get
        - requestCount: 750
          verb: watch
        requestCount: 755
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 19
          verb: update
        requestCount: 19
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: create
        - requestCount: 5
          verb: delete
        requestCount: 10
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 2133
    requestCount: 4400
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 944
          verb: list
        requestCount: 944
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 969
    - byUser:
      - byVerb:
        - requestCount: 885
          verb: list
        requestCount: 885
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 885
    - byUser:
      - byVerb:
        - requestCount: 1711
          verb: list
        requestCount: 1711
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 750
          verb: watch
        requestCount: 754
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 12
          verb: update
        requestCount: 12
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 4
          verb: delete
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 2538
    requestCount: 4392
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 885
          verb: list
        requestCount: 885
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 907
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1298
    - byUser:
      - byVerb:
        - requestCount: 1357
          verb: list
        requestCount: 1357
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 754
          verb: watch
        requestCount: 758
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 14
          verb: update
        requestCount: 14
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 4
          verb: delete
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 2188
    requestCount: 4393
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 1146
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1298
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 753
          verb: watch
        requestCount: 757
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 12
          verb: update
        requestCount: 12
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 4
          verb: delete
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      nodeName: 10.9.212.9
      requestCount: 1952
    requestCount: 4396
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1416
          verb: list
        requestCount: 1416
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 1439
    - byUser:
      - byVerb:
        - requestCount: 1239
          verb: list
        requestCount: 1239
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1239
    - byUser:
      - byVerb:
        - requestCount: 885
          verb: list
        requestCount: 885
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 754
          verb: watch
        requestCount: 758
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: update
        requestCount: 16
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 4
          verb: delete
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      nodeName: 10.9.212.9
      requestCount: 1718
    requestCount: 4396
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 1144
    - byUser:
      - byVerb:
        - requestCount: 1180
          verb: list
        requestCount: 1180
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1180
    - byUser:
      - byVerb:
        - requestCount: 1239
          verb: list
        requestCount: 1239
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 753
          verb: watch
        requestCount: 757
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 14
          verb: update
        requestCount: 14
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 4
          verb: delete
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      nodeName: 10.9.212.9
      requestCount: 2071
    requestCount: 4395
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1003
          verb: list
        requestCount: 1003
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 1027
    - byUser:
      - byVerb:
        - requestCount: 1300
          verb: list
        requestCount: 1300
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1300
    - byUser:
      - byVerb:
        - requestCount: 1180
          verb: list
        requestCount: 1180
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 749
          verb: watch
        requestCount: 753
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 12
          verb: update
        requestCount: 12
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 4
          verb: delete
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 2004
    requestCount: 4331
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 1086
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1298
    - byUser:
      - byVerb:
        - requestCount: 1180
          verb: list
        requestCount: 1180
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 5
          verb: get
        - requestCount: 754
          verb: watch
        requestCount: 759
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 21
          verb: update
        requestCount: 21
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: create
        - requestCount: 5
          verb: delete
        requestCount: 10
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      nodeName: 10.9.212.9
      requestCount: 2023
    requestCount: 4407
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1357
          verb: list
        requestCount: 1357
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 1383
    - byUser:
      - byVerb:
        - requestCount: 885
          verb: list
        requestCount: 885
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 885
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 752
          verb: watch
        requestCount: 756
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 12
          verb: update
        requestCount: 12
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 4
          verb: delete
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 2125
    requestCount: 4393
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 1144
    - byUser:
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1062
    - byUser:
      - byVerb:
        - requestCount: 1226
          verb: list
        requestCount: 1226
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 753
          verb: watch
        requestCount: 757
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 12
          verb: update
        requestCount: 12
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 4
          verb: delete
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 2055
    requestCount: 4261
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1165
          verb: list
        requestCount: 1165
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 1190
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1298
    - byUser:
      - byVerb:
        - requestCount: 1075
          verb: list
        requestCount: 1075
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 752
          verb: watch
        requestCount: 756
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: update
        requestCount: 16
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 4
          verb: delete
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1907
    requestCount: 4395
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1195
          verb: list
        requestCount: 1195
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 1219
    - byUser:
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1062
    - byUser:
      - byVerb:
        - requestCount: 1285
          verb: list
        requestCount: 1285
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 752
          verb: watch
        requestCount: 756
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 12
          verb: update
        requestCount: 12
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 4
          verb: delete
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      nodeName: 10.9.212.9
      requestCount: 2113
    requestCount: 4394
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 1322
    - byUser:
      - byVerb:
        - requestCount: 1165
          verb: list
        requestCount: 1165
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1165
    - byUser:
      - byVerb:
        - requestCount: 1075
          verb: list
        requestCount: 1075
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 752
          verb: watch
        requestCount: 756
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 14
          verb: update
        requestCount: 14
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 4
          verb: delete
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      nodeName: 10.9.212.9
      requestCount: 1904
    requestCount: 4391
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1003
          verb: list
        requestCount: 1003
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 1027
    - byUser:
      - byVerb:
        - requestCount: 1490
          verb: list
        requestCount: 1490
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1490
    - byUser:
      - byVerb:
        - requestCount: 1048
          verb: list
        requestCount: 1048
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 754
          verb: watch
        requestCount: 758
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 12
          verb: update
        requestCount: 12
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 4
          verb: delete
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      nodeName: 10.9.212.9
      requestCount: 1877
    requestCount: 4394
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 767
          verb: list
        requestCount: 767
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 780
    - byUser:
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 826
    - byUser:
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 471
          verb: watch
        requestCount: 474
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 427
          verb: list
        requestCount: 427
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: update
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: create
        - requestCount: 3
          verb: delete
        requestCount: 6
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      nodeName: 10.9.212.9
      requestCount: 948
    requestCount: 2554
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 1320
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1121
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 750
          verb: watch
        requestCount: 754
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 14
          verb: update
        requestCount: 14
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 4
          verb: delete
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1949
    requestCount: 4390
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1003
          verb: list
        requestCount: 1003
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 1028
    - byUser:
      - byVerb:
        - requestCount: 1239
          verb: list
        requestCount: 1239
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1239
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 753
          verb: watch
        requestCount: 757
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 12
          verb: update
        requestCount: 12
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 4
          verb: delete
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      nodeName: 10.9.212.9
      requestCount: 2125
    requestCount: 4392
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1239
          verb: list
        requestCount: 1239
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 1263
    - byUser:
      - byVerb:
        - requestCount: 944
          verb: list
        requestCount: 944
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 944
    - byUser:
      - byVerb:
        - requestCount: 1357
          verb: list
        requestCount: 1357
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 753
          verb: watch
        requestCount: 757
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 14
          verb: update
        requestCount: 14
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 4
          verb: delete
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 2189
    requestCount: 4396
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1475
          verb: list
        requestCount: 1475
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 1500
    - byUser:
      - byVerb:
        - requestCount: 1003
          verb: list
        requestCount: 1003
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1003
    - byUser:
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 753
          verb: watch
        requestCount: 757
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 12
          verb: update
        requestCount: 12
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 4
          verb: delete
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1894
    requestCount: 4397
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 1321
    - byUser:
      - byVerb:
        - requestCount: 885
          verb: list
        requestCount: 885
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 885
    - byUser:
      - byVerb:
        - requestCount: 1357
          verb: list
        requestCount: 1357
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 751
          verb: watch
        requestCount: 755
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 14
          verb: update
        requestCount: 14
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 4
          verb: delete
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      nodeName: 10.9.212.9
      requestCount: 2187
    requestCount: 4393
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1357
          verb: list
        requestCount: 1357
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 1381
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1121
    - byUser:
      - byVerb:
        - requestCount: 1003
          verb: list
        requestCount: 1003
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 753
          verb: watch
        requestCount: 757
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 12
          verb: update
        requestCount: 12
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 4
          verb: delete
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1830
    requestCount: 4332
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1357
          verb: list
        requestCount: 1357
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 1381
    - byUser:
      - byVerb:
        - requestCount: 1124
          verb: list
        requestCount: 1124
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1124
    - byUser:
      - byVerb:
        - requestCount: 1003
          verb: list
        requestCount: 1003
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 750
          verb: watch
        requestCount: 754
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: update
        requestCount: 16
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 4
          verb: delete
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1834
    requestCount: 4339
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 1145
    - byUser:
      - byVerb:
        - requestCount: 1357
          verb: list
        requestCount: 1357
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1357
    - byUser:
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 752
          verb: watch
        requestCount: 756
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 12
          verb: update
        requestCount: 12
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:job-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 4
          verb: delete
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:cronjob-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1889
    requestCount: 4391
  requestCount: 98922
