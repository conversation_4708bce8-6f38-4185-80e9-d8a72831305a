---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T15:03:27Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T15:03:27Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:38:28Z"
  name: oauthclients.v1.oauth.openshift.io
  resourceVersion: "********"
  uid: 83b1153c-5a92-4ac5-9a5d-668abaf0892a
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 37
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 42
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 11
          verb: get
        requestCount: 11
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 53
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 12
    requestCount: 65
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 72
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 92
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 19
    requestCount: 111
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 72
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 91
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 111
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 72
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 92
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 112
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 63
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 71
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 91
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 111
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 71
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 90
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 110
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 72
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 92
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 112
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 72
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 91
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 111
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 63
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 71
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 91
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 111
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 63
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 72
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 92
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 21
    requestCount: 113
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 72
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 91
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 111
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 72
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 92
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 19
    requestCount: 111
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 72
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 91
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 111
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 63
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 71
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 91
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 111
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 73
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 93
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 113
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 37
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 42
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 11
          verb: get
        requestCount: 11
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 53
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 12
    requestCount: 65
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 72
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 91
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 21
    requestCount: 112
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 72
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 92
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 5
          verb: get
        requestCount: 5
        userAgent: oauth-server/v0.0.0
        username: system:serviceaccount:openshift-authentication:oauth-openshift
      nodeName: 10.9.212.9
      requestCount: 26
    requestCount: 118
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 63
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 71
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 90
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 110
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 71
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 91
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 21
    requestCount: 112
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 72
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 92
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 112
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 72
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 91
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 111
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 63
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 71
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 91
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 19
    requestCount: 110
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 71
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      nodeName: 10.9.212.10
      requestCount: 90
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 110
  requestCount: 2519
