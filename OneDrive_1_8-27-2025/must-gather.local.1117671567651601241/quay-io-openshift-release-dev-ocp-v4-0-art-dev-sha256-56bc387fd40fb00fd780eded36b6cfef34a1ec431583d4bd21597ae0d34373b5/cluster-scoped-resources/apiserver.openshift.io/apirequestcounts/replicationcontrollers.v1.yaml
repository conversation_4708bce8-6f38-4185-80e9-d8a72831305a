---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T14:59:18Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:59:18Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:37:58Z"
  name: replicationcontrollers.v1
  resourceVersion: "********"
  uid: 58db86fe-e797-41d6-8c31-529f59e34d43
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.10
      requestCount: 18
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 6
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      nodeName: 10.9.212.9
      requestCount: 38
    requestCount: 62
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: oc/4.17.0
        username: system:serviceaccount:openshift-image-registry:pruner
      nodeName: 10.9.212.10
      requestCount: 32
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 60
    requestCount: 100
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 34
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 60
    requestCount: 103
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 30
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 61
    requestCount: 98
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 33
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 58
    requestCount: 99
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 32
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 59
    requestCount: 100
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 33
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 60
    requestCount: 101
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 32
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      nodeName: 10.9.212.9
      requestCount: 63
    requestCount: 103
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.10
      requestCount: 31
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 59
    requestCount: 98
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 32
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 13
          verb: watch
        requestCount: 13
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 56
    requestCount: 96
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.10
      requestCount: 32
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      nodeName: 10.9.212.9
      requestCount: 60
    requestCount: 100
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 33
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 60
    requestCount: 101
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.10
      requestCount: 28
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 59
    requestCount: 95
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.10
      requestCount: 36
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 63
    requestCount: 106
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 31
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      nodeName: 10.9.212.9
      requestCount: 58
    requestCount: 97
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.10
      requestCount: 18
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 6
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      nodeName: 10.9.212.9
      requestCount: 38
    requestCount: 62
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 40
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 62
    requestCount: 102
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 41
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 60
    requestCount: 101
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 40
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 63
    requestCount: 103
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 39
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 60
    requestCount: 99
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 38
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      nodeName: 10.9.212.9
      requestCount: 60
    requestCount: 98
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 39
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 59
    requestCount: 98
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.10
      requestCount: 35
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 60
    requestCount: 104
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.10
      requestCount: 32
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 59
    requestCount: 100
  requestCount: 2264
