---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T15:10:55Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T15:10:55Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:40:39Z"
  name: routes.v1.route.openshift.io
  resourceVersion: "********"
  uid: be1bed62-f1b7-41e9-8501-dcb49be3c355
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 118
          verb: delete
        - requestCount: 118
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 241
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 42
          verb: get
        - requestCount: 32
          verb: watch
        requestCount: 74
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      nodeName: 10.9.212.10
      requestCount: 345
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 21
          verb: list
        requestCount: 22
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: oc/4.17.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      nodeName: 10.9.212.8
      requestCount: 23
    - byUser:
      - byVerb:
        - requestCount: 12
          verb: delete
        - requestCount: 66
          verb: get
        - requestCount: 24
          verb: update
        requestCount: 102
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 97
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 102
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 4
          verb: watch
        requestCount: 6
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      nodeName: 10.9.212.9
      requestCount: 234
    requestCount: 602
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 180
          verb: delete
        - requestCount: 180
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 368
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 50
          verb: watch
        requestCount: 110
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      nodeName: 10.9.212.10
      requestCount: 519
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 28
          verb: delete
        - requestCount: 154
          verb: get
        - requestCount: 56
          verb: update
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 150
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 157
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 10
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 439
    requestCount: 958
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 180
          verb: delete
        - requestCount: 180
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 368
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 44
          verb: watch
        requestCount: 104
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 512
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: delete
        - requestCount: 165
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 255
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 151
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 159
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      nodeName: 10.9.212.9
      requestCount: 461
    requestCount: 973
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 180
          verb: delete
        - requestCount: 180
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 367
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 48
          verb: watch
        requestCount: 108
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      nodeName: 10.9.212.10
      requestCount: 513
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 26
          verb: delete
        - requestCount: 143
          verb: get
        - requestCount: 52
          verb: update
        requestCount: 221
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 150
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 158
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 10
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 424
    requestCount: 937
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 179
          verb: delete
        - requestCount: 179
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 366
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 52
          verb: watch
        requestCount: 112
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 519
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 28
          verb: delete
        - requestCount: 154
          verb: get
        - requestCount: 56
          verb: update
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 151
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 159
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 440
    requestCount: 959
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 180
          verb: delete
        - requestCount: 180
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 368
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 61
          verb: get
        - requestCount: 47
          verb: watch
        requestCount: 108
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 516
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 28
          verb: delete
        - requestCount: 154
          verb: get
        - requestCount: 56
          verb: update
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 150
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 158
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 12
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      nodeName: 10.9.212.9
      requestCount: 446
    requestCount: 962
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 179
          verb: delete
        - requestCount: 179
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 367
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 62
          verb: get
        - requestCount: 49
          verb: watch
        requestCount: 111
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      nodeName: 10.9.212.10
      requestCount: 518
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: delete
        - requestCount: 165
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 255
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 150
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 157
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 5
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 13
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 462
    requestCount: 980
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 179
          verb: delete
        - requestCount: 179
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 365
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 47
          verb: watch
        requestCount: 107
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      nodeName: 10.9.212.10
      requestCount: 511
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 24
          verb: delete
        - requestCount: 132
          verb: get
        - requestCount: 48
          verb: update
        requestCount: 204
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 151
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 159
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 10
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 407
    requestCount: 918
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 180
          verb: delete
        - requestCount: 180
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 368
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 50
          verb: watch
        requestCount: 110
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 27
          verb: watch
        requestCount: 27
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 521
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: delete
        - requestCount: 165
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 255
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 150
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 160
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 11
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      nodeName: 10.9.212.9
      requestCount: 462
    requestCount: 983
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 180
          verb: delete
        - requestCount: 180
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 367
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 47
          verb: watch
        requestCount: 107
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      nodeName: 10.9.212.10
      requestCount: 512
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 26
          verb: delete
        - requestCount: 143
          verb: get
        - requestCount: 52
          verb: update
        requestCount: 221
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 150
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 158
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 424
    requestCount: 936
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 180
          verb: delete
        - requestCount: 180
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 368
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 50
          verb: watch
        requestCount: 110
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 27
          verb: watch
        requestCount: 27
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      nodeName: 10.9.212.10
      requestCount: 520
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 28
          verb: delete
        - requestCount: 154
          verb: get
        - requestCount: 56
          verb: update
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 151
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 159
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 445
    requestCount: 965
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 179
          verb: delete
        - requestCount: 179
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 366
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 49
          verb: watch
        requestCount: 109
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 512
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 28
          verb: delete
        - requestCount: 154
          verb: get
        - requestCount: 56
          verb: update
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 149
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 156
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 437
    requestCount: 949
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 180
          verb: delete
        - requestCount: 180
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 367
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 46
          verb: watch
        requestCount: 106
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 511
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 26
          verb: delete
        - requestCount: 143
          verb: get
        - requestCount: 52
          verb: update
        requestCount: 221
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 150
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 158
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      nodeName: 10.9.212.9
      requestCount: 426
    requestCount: 937
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 179
          verb: delete
        - requestCount: 179
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 366
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 47
          verb: watch
        requestCount: 107
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 514
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: delete
        - requestCount: 165
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 255
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 149
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 158
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 10
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      nodeName: 10.9.212.9
      requestCount: 460
    requestCount: 974
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 179
          verb: delete
        - requestCount: 179
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 365
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 48
          verb: watch
        requestCount: 108
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      nodeName: 10.9.212.10
      requestCount: 512
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 28
          verb: delete
        - requestCount: 154
          verb: get
        - requestCount: 56
          verb: update
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 149
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 156
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 12
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 442
    requestCount: 954
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 118
          verb: delete
        - requestCount: 118
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 241
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 42
          verb: get
        - requestCount: 32
          verb: watch
        requestCount: 74
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      nodeName: 10.9.212.10
      requestCount: 345
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 21
          verb: list
        requestCount: 22
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: oc/4.17.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      nodeName: 10.9.212.8
      requestCount: 23
    - byUser:
      - byVerb:
        - requestCount: 12
          verb: delete
        - requestCount: 66
          verb: get
        - requestCount: 24
          verb: update
        requestCount: 102
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 97
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 102
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 4
          verb: watch
        requestCount: 6
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      nodeName: 10.9.212.9
      requestCount: 234
    requestCount: 602
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 180
          verb: delete
        - requestCount: 180
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 369
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 51
          verb: watch
        requestCount: 111
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 521
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 28
          verb: delete
        - requestCount: 154
          verb: get
        - requestCount: 56
          verb: update
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 149
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 157
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 12
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 445
    requestCount: 966
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 179
          verb: delete
        - requestCount: 179
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 365
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 46
          verb: watch
        requestCount: 106
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 513
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 28
          verb: delete
        - requestCount: 154
          verb: get
        - requestCount: 56
          verb: update
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 151
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 160
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 446
    requestCount: 959
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 180
          verb: delete
        - requestCount: 180
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 368
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 61
          verb: get
        - requestCount: 50
          verb: watch
        requestCount: 111
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      nodeName: 10.9.212.10
      requestCount: 518
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 28
          verb: delete
        - requestCount: 154
          verb: get
        - requestCount: 56
          verb: update
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 149
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 158
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 11
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 446
    requestCount: 964
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 180
          verb: delete
        - requestCount: 180
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 368
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 61
          verb: get
        - requestCount: 47
          verb: watch
        requestCount: 108
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      nodeName: 10.9.212.10
      requestCount: 517
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 26
          verb: delete
        - requestCount: 143
          verb: get
        - requestCount: 52
          verb: update
        requestCount: 221
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 150
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 157
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 12
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 425
    requestCount: 942
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 180
          verb: delete
        - requestCount: 180
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 368
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 61
          verb: get
        - requestCount: 49
          verb: watch
        requestCount: 110
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 517
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: delete
        - requestCount: 165
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 255
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 138
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 145
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 11
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 448
    requestCount: 965
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 180
          verb: delete
        - requestCount: 180
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 368
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 48
          verb: watch
        requestCount: 108
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 516
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 28
          verb: delete
        - requestCount: 154
          verb: get
        - requestCount: 56
          verb: update
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 149
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 158
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 5
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 13
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 443
    requestCount: 959
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 180
          verb: delete
        - requestCount: 180
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 368
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 48
          verb: watch
        requestCount: 108
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 518
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 26
          verb: delete
        - requestCount: 143
          verb: get
        - requestCount: 52
          verb: update
        requestCount: 221
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 151
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 159
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 425
    requestCount: 943
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 180
          verb: delete
        - requestCount: 180
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 368
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 50
          verb: watch
        requestCount: 110
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 26
          verb: watch
        requestCount: 26
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-state-metrics/UNKNOWN
        username: system:serviceaccount:openshift-monitoring:openshift-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 521
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 28
          verb: delete
        - requestCount: 154
          verb: get
        - requestCount: 56
          verb: update
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 137
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 146
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 428
    requestCount: 949
  requestCount: 21634
