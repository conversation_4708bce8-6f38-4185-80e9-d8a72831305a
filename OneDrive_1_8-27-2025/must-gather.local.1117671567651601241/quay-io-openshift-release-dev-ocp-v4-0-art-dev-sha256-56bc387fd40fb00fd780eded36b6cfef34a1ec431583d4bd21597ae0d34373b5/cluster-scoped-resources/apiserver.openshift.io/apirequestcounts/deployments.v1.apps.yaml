---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T14:59:50Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:59:50Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:39:09Z"
  name: deployments.v1.apps
  resourceVersion: "33962587"
  uid: ad97a6e1-0837-490a-9263-20697c524e4d
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        - requestCount: 720
          verb: list
        requestCount: 734
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 244
          verb: get
        - requestCount: 244
          verb: update
        - requestCount: 4
          verb: watch
        requestCount: 492
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 386
          verb: get
        requestCount: 386
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 351
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 361
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 252
          verb: get
        - requestCount: 4
          verb: watch
        requestCount: 256
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 126
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 131
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 107
          verb: get
        - requestCount: 4
          verb: watch
        requestCount: 111
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 10
          verb: get
        - requestCount: 50
          verb: patch
        - requestCount: 4
          verb: watch
        requestCount: 64
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 56
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 64
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 41
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 46
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 2660
    - byUser:
      - byVerb:
        - requestCount: 10
          verb: get
        - requestCount: 780
          verb: list
        requestCount: 790
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 790
    - byUser:
      - byVerb:
        - requestCount: 11
          verb: get
        - requestCount: 540
          verb: list
        requestCount: 551
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 278
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 288
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 151
          verb: get
        - requestCount: 4
          verb: watch
        requestCount: 155
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 151
          verb: get
        requestCount: 151
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 6
          verb: delete
        - requestCount: 96
          verb: get
        - requestCount: 48
          verb: update
        requestCount: 150
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 12
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 29
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 4
          verb: watch
        requestCount: 22
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 12
          verb: update
        - requestCount: 4
          verb: watch
        requestCount: 16
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 1402
    requestCount: 4852
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 19
          verb: get
        - requestCount: 1440
          verb: list
        requestCount: 1459
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 431
          verb: get
        - requestCount: 431
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 870
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 680
          verb: get
        requestCount: 680
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 616
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 632
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 428
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 436
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 214
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 223
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 181
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 189
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 90
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 117
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 102
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 79
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 4787
    - byUser:
      - byVerb:
        - requestCount: 19
          verb: get
        - requestCount: 1200
          verb: list
        requestCount: 1219
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1219
    - byUser:
      - byVerb:
        - requestCount: 21
          verb: get
        - requestCount: 960
          verb: list
        requestCount: 981
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 440
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 456
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 14
          verb: delete
        - requestCount: 224
          verb: get
        - requestCount: 112
          verb: update
        requestCount: 350
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 234
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 239
          verb: get
        requestCount: 239
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 47
          verb: watch
        requestCount: 47
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 45
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 28
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 35
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 7
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 16
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 2421
    requestCount: 8427
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 21
          verb: get
        - requestCount: 1260
          verb: list
        requestCount: 1281
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 427
          verb: get
        - requestCount: 427
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 862
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 656
          verb: get
        requestCount: 656
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 614
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 629
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 432
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 441
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 216
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 224
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 183
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 192
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 85
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 110
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 82
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 98
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 71
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 80
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 4573
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 1200
          verb: list
        requestCount: 1218
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1218
    - byUser:
      - byVerb:
        - requestCount: 21
          verb: get
        - requestCount: 1140
          verb: list
        requestCount: 1161
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 440
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 457
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 15
          verb: delete
        - requestCount: 240
          verb: get
        - requestCount: 120
          verb: update
        requestCount: 375
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 236
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 243
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 240
          verb: get
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 48
          verb: watch
        requestCount: 48
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 43
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 35
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 13
          verb: update
        - requestCount: 6
          verb: watch
        requestCount: 19
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 2631
    requestCount: 8422
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 1080
          verb: list
        requestCount: 1098
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 419
          verb: get
        - requestCount: 419
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 845
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 670
          verb: get
        requestCount: 670
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 618
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 632
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 434
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 442
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 217
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 226
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 183
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 192
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 90
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 116
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 102
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 79
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 4402
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        - requestCount: 1320
          verb: list
        requestCount: 1334
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1334
    - byUser:
      - byVerb:
        - requestCount: 25
          verb: get
        - requestCount: 1140
          verb: list
        requestCount: 1165
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 418
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 433
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 13
          verb: delete
        - requestCount: 208
          verb: get
        - requestCount: 104
          verb: update
        requestCount: 325
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 230
          verb: get
        requestCount: 230
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 221
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 227
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 45
          verb: watch
        requestCount: 45
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: get
        - requestCount: 16
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 39
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 34
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 19
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 27
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 2537
    requestCount: 8273
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        - requestCount: 1140
          verb: list
        requestCount: 1160
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 419
          verb: get
        - requestCount: 419
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 846
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 680
          verb: get
        requestCount: 680
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 616
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 632
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 436
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 444
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 218
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 226
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 185
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 193
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 90
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 116
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 82
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 98
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 4475
    - byUser:
      - byVerb:
        - requestCount: 22
          verb: get
        - requestCount: 1020
          verb: list
        requestCount: 1042
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1042
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 1380
          verb: list
        requestCount: 1398
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 424
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 441
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 14
          verb: delete
        - requestCount: 224
          verb: get
        - requestCount: 112
          verb: update
        requestCount: 350
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 229
          verb: get
        requestCount: 229
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 220
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 226
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 44
          verb: watch
        requestCount: 44
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: get
        - requestCount: 16
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 39
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 34
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 12
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 21
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 2794
    requestCount: 8311
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        - requestCount: 1140
          verb: list
        requestCount: 1160
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 437
          verb: get
        - requestCount: 437
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 882
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 646
          verb: get
        requestCount: 646
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 610
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 625
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 428
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 436
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 214
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 223
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 181
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 189
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 85
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 110
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 99
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 4450
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        - requestCount: 900
          verb: list
        requestCount: 920
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 920
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        - requestCount: 1500
          verb: list
        requestCount: 1520
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 439
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 455
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 14
          verb: delete
        - requestCount: 224
          verb: get
        - requestCount: 112
          verb: update
        requestCount: 350
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 235
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 244
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 240
          verb: get
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 46
          verb: watch
        requestCount: 46
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 45
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 34
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 2972
    requestCount: 8342
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 436
          verb: get
        - requestCount: 436
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 880
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 680
          verb: get
        requestCount: 680
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 19
          verb: get
        - requestCount: 660
          verb: list
        requestCount: 679
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 610
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 626
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 430
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 439
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 215
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 222
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 182
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 189
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 90
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 115
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 82
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 97
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 4007
    - byUser:
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 1320
          verb: list
        requestCount: 1344
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1344
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 1620
          verb: list
        requestCount: 1637
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 441
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 458
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 15
          verb: delete
        - requestCount: 240
          verb: get
        - requestCount: 120
          verb: update
        requestCount: 375
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 235
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 244
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 240
          verb: get
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 46
          verb: watch
        requestCount: 46
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 44
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 31
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 39
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 34
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 3129
    requestCount: 8480
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 11
          verb: get
        - requestCount: 1320
          verb: list
        requestCount: 1331
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 423
          verb: get
        - requestCount: 423
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 853
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 650
          verb: get
        requestCount: 650
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 618
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 634
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 434
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 442
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 217
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 226
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 183
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 192
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 86
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 112
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 100
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 71
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 79
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 4619
    - byUser:
      - byVerb:
        - requestCount: 25
          verb: get
        - requestCount: 900
          verb: list
        requestCount: 925
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 925
    - byUser:
      - byVerb:
        - requestCount: 23
          verb: get
        - requestCount: 1380
          verb: list
        requestCount: 1403
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 445
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 459
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 12
          verb: delete
        - requestCount: 192
          verb: get
        - requestCount: 96
          verb: update
        requestCount: 300
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 238
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 245
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 240
          verb: get
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 50
          verb: watch
        requestCount: 50
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 45
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 34
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: update
        - requestCount: 6
          verb: watch
        requestCount: 24
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 2812
    requestCount: 8356
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 449
          verb: get
        - requestCount: 449
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 907
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 676
          verb: get
        requestCount: 676
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 616
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 633
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 21
          verb: get
        - requestCount: 480
          verb: list
        requestCount: 501
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 438
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 447
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 219
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 227
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 186
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 194
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 89
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 115
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 100
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 81
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 3881
    - byUser:
      - byVerb:
        - requestCount: 23
          verb: get
        - requestCount: 1380
          verb: list
        requestCount: 1403
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1403
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: get
        - requestCount: 1740
          verb: list
        requestCount: 1756
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 448
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 466
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 15
          verb: delete
        - requestCount: 240
          verb: get
        - requestCount: 120
          verb: update
        requestCount: 375
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 241
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 248
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 239
          verb: get
        requestCount: 239
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 47
          verb: watch
        requestCount: 47
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 44
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 29
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 37
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 12
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 19
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 3243
    requestCount: 8527
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 22
          verb: get
        - requestCount: 900
          verb: list
        requestCount: 922
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 437
          verb: get
        - requestCount: 437
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 882
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 677
          verb: get
        requestCount: 677
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 608
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 622
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 426
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 433
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 213
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 221
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 180
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 187
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 90
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 116
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 100
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 4240
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        - requestCount: 1380
          verb: list
        requestCount: 1400
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1400
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: get
        - requestCount: 1320
          verb: list
        requestCount: 1335
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 420
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 436
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 13
          verb: delete
        - requestCount: 208
          verb: get
        - requestCount: 104
          verb: update
        requestCount: 325
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 223
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 230
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 230
          verb: get
        requestCount: 230
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 46
          verb: watch
        requestCount: 46
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: get
        - requestCount: 16
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 39
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 34
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 6
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 13
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 2698
    requestCount: 8338
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 1380
          verb: list
        requestCount: 1398
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 442
          verb: get
        - requestCount: 442
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 892
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 649
          verb: get
        requestCount: 649
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 618
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 634
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 432
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 438
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 216
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 225
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 183
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 192
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 85
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 110
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 82
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 98
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 79
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 4715
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 1020
          verb: list
        requestCount: 1037
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1037
    - byUser:
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 1140
          verb: list
        requestCount: 1164
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 443
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 460
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 14
          verb: delete
        - requestCount: 224
          verb: get
        - requestCount: 112
          verb: update
        requestCount: 350
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 237
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 246
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 240
          verb: get
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 47
          verb: watch
        requestCount: 47
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 44
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 33
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 13
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 21
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 2617
    requestCount: 8369
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 23
          verb: get
        - requestCount: 1440
          verb: list
        requestCount: 1463
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 424
          verb: get
        - requestCount: 424
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 857
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 680
          verb: get
        requestCount: 680
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 616
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 631
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 434
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 442
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 218
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 225
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 182
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 191
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 90
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 115
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 99
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 81
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 4784
    - byUser:
      - byVerb:
        - requestCount: 21
          verb: get
        - requestCount: 1080
          verb: list
        requestCount: 1101
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1101
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: get
        - requestCount: 1080
          verb: list
        requestCount: 1096
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 440
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 455
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 14
          verb: delete
        - requestCount: 224
          verb: get
        - requestCount: 112
          verb: update
        requestCount: 350
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 239
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 247
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 239
          verb: get
        requestCount: 239
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 49
          verb: watch
        requestCount: 49
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 45
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 35
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 7
          verb: update
        - requestCount: 6
          verb: watch
        requestCount: 13
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 2541
    requestCount: 8426
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 27
          verb: get
        - requestCount: 840
          verb: list
        requestCount: 867
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 428
          verb: get
        - requestCount: 428
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 864
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 646
          verb: get
        requestCount: 646
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 622
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 639
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 438
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 445
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 218
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 226
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 184
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 191
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 90
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 116
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 99
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 81
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 4174
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 1080
          verb: list
        requestCount: 1097
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1097
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: get
        - requestCount: 1680
          verb: list
        requestCount: 1696
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 446
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 462
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 13
          verb: delete
        - requestCount: 208
          verb: get
        - requestCount: 104
          verb: update
        requestCount: 325
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 239
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 247
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 240
          verb: get
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 49
          verb: watch
        requestCount: 49
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 44
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 34
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 14
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 21
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 3130
    requestCount: 8401
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 1380
          verb: list
        requestCount: 1392
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 420
          verb: get
        - requestCount: 420
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 848
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 680
          verb: get
        requestCount: 680
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 612
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 627
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 432
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 441
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 216
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 223
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 183
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 190
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 85
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 110
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 78
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 96
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 81
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 4688
    - byUser:
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 1080
          verb: list
        requestCount: 1104
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1104
    - byUser:
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 1140
          verb: list
        requestCount: 1164
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 441
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 458
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 15
          verb: delete
        - requestCount: 240
          verb: get
        - requestCount: 120
          verb: update
        requestCount: 375
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 237
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 245
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 240
          verb: get
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 45
          verb: watch
        requestCount: 45
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 44
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 27
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 36
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: update
        - requestCount: 6
          verb: watch
        requestCount: 24
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 2643
    requestCount: 8435
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 21
          verb: get
        - requestCount: 1200
          verb: list
        requestCount: 1221
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 432
          verb: get
        - requestCount: 432
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 872
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 668
          verb: get
        requestCount: 668
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 614
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 630
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 430
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 437
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 215
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 224
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 182
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 191
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 90
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 117
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 82
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 98
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 4538
    - byUser:
      - byVerb:
        - requestCount: 22
          verb: get
        - requestCount: 1020
          verb: list
        requestCount: 1042
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1042
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 1380
          verb: list
        requestCount: 1397
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 441
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 457
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 14
          verb: delete
        - requestCount: 224
          verb: get
        - requestCount: 112
          verb: update
        requestCount: 350
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 237
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 245
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 239
          verb: get
        requestCount: 239
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 50
          verb: watch
        requestCount: 50
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 44
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 34
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 25
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 2853
    requestCount: 8433
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        - requestCount: 720
          verb: list
        requestCount: 734
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 244
          verb: get
        - requestCount: 244
          verb: update
        - requestCount: 4
          verb: watch
        requestCount: 492
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 386
          verb: get
        requestCount: 386
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 351
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 361
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 252
          verb: get
        - requestCount: 4
          verb: watch
        requestCount: 256
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 126
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 131
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 107
          verb: get
        - requestCount: 4
          verb: watch
        requestCount: 111
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 10
          verb: get
        - requestCount: 50
          verb: patch
        - requestCount: 4
          verb: watch
        requestCount: 64
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 56
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 64
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 41
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 46
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 2660
    - byUser:
      - byVerb:
        - requestCount: 10
          verb: get
        - requestCount: 780
          verb: list
        requestCount: 790
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 790
    - byUser:
      - byVerb:
        - requestCount: 11
          verb: get
        - requestCount: 540
          verb: list
        requestCount: 551
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 278
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 288
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 151
          verb: get
        - requestCount: 4
          verb: watch
        requestCount: 155
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 151
          verb: get
        requestCount: 151
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 6
          verb: delete
        - requestCount: 96
          verb: get
        - requestCount: 48
          verb: update
        requestCount: 150
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 12
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 29
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 4
          verb: watch
        requestCount: 22
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 12
          verb: update
        - requestCount: 4
          verb: watch
        requestCount: 16
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 1402
    requestCount: 4852
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 22
          verb: get
        - requestCount: 1200
          verb: list
        requestCount: 1222
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 436
          verb: get
        - requestCount: 436
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 880
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 660
          verb: get
        requestCount: 660
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 610
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 627
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 429
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 436
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 215
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 223
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 182
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 191
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 90
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 116
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 100
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 79
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 4534
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        - requestCount: 1200
          verb: list
        requestCount: 1220
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1220
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 1200
          verb: list
        requestCount: 1218
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 441
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 458
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 14
          verb: delete
        - requestCount: 224
          verb: get
        - requestCount: 112
          verb: update
        requestCount: 350
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 237
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 246
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 240
          verb: get
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 45
          verb: watch
        requestCount: 45
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 44
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 32
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 40
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 19
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 27
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 2680
    requestCount: 8434
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 1200
          verb: list
        requestCount: 1217
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 442
          verb: get
        - requestCount: 442
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 892
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 680
          verb: get
        requestCount: 680
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 614
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 628
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 430
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 437
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 215
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 223
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 179
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 186
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 85
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 110
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 100
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 81
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 4554
    - byUser:
      - byVerb:
        - requestCount: 22
          verb: get
        - requestCount: 1260
          verb: list
        requestCount: 1282
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1282
    - byUser:
      - byVerb:
        - requestCount: 21
          verb: get
        - requestCount: 1140
          verb: list
        requestCount: 1161
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 437
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 454
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 14
          verb: delete
        - requestCount: 224
          verb: get
        - requestCount: 112
          verb: update
        requestCount: 350
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 237
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 245
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 239
          verb: get
        requestCount: 239
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 49
          verb: watch
        requestCount: 49
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 44
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 30
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 39
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 12
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 20
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 2611
    requestCount: 8447
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 1440
          verb: list
        requestCount: 1458
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 429
          verb: get
        - requestCount: 429
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 867
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 646
          verb: get
        requestCount: 646
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 620
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 636
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 435
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 444
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 218
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 226
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 185
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 193
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 90
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 116
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 100
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 4766
    - byUser:
      - byVerb:
        - requestCount: 19
          verb: get
        - requestCount: 1140
          verb: list
        requestCount: 1159
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1159
    - byUser:
      - byVerb:
        - requestCount: 23
          verb: get
        - requestCount: 1020
          verb: list
        requestCount: 1043
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 445
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 462
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 14
          verb: delete
        - requestCount: 224
          verb: get
        - requestCount: 112
          verb: update
        requestCount: 350
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 239
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 247
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 240
          verb: get
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 49
          verb: watch
        requestCount: 49
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 44
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 30
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 38
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 19
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 27
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 2512
    requestCount: 8437
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 10
          verb: get
        - requestCount: 1200
          verb: list
        requestCount: 1210
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 425
          verb: get
        - requestCount: 425
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 858
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 680
          verb: get
        requestCount: 680
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 616
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 631
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 434
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 442
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 217
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 225
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 182
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 191
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 89
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 115
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 100
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 4532
    - byUser:
      - byVerb:
        - requestCount: 27
          verb: get
        - requestCount: 1260
          verb: list
        requestCount: 1287
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1287
    - byUser:
      - byVerb:
        - requestCount: 23
          verb: get
        - requestCount: 1140
          verb: list
        requestCount: 1163
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 443
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 457
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 13
          verb: delete
        - requestCount: 208
          verb: get
        - requestCount: 104
          verb: update
        requestCount: 325
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 238
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 246
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 240
          verb: get
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 46
          verb: watch
        requestCount: 46
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 45
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 35
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 25
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 2594
    requestCount: 8413
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 436
          verb: get
        - requestCount: 436
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 880
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 23
          verb: get
        - requestCount: 720
          verb: list
        requestCount: 743
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 659
          verb: get
        requestCount: 659
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 610
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 628
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 427
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 433
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 214
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 223
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 181
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 189
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 86
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 110
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 100
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 79
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 4044
    - byUser:
      - byVerb:
        - requestCount: 23
          verb: get
        - requestCount: 1620
          verb: list
        requestCount: 1643
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1643
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        - requestCount: 1260
          verb: list
        requestCount: 1274
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 439
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 455
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 15
          verb: delete
        - requestCount: 240
          verb: get
        - requestCount: 120
          verb: update
        requestCount: 375
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 235
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 244
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 240
          verb: get
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 48
          verb: watch
        requestCount: 48
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 43
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 27
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 35
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 25
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 2749
    requestCount: 8436
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        - requestCount: 1260
          verb: list
        requestCount: 1280
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 414
          verb: get
        - requestCount: 414
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 836
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 667
          verb: get
        requestCount: 667
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 614
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 631
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 432
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 441
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 216
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 223
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 181
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 189
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 90
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 115
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 82
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 98
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 71
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 79
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 4559
    - byUser:
      - byVerb:
        - requestCount: 22
          verb: get
        - requestCount: 1200
          verb: list
        requestCount: 1222
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1222
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 1140
          verb: list
        requestCount: 1158
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 435
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 451
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 14
          verb: delete
        - requestCount: 224
          verb: get
        - requestCount: 112
          verb: update
        requestCount: 350
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 239
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 248
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 239
          verb: get
        requestCount: 239
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 51
          verb: watch
        requestCount: 51
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 44
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 30
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 38
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 34
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 2625
    requestCount: 8406
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: get
        - requestCount: 1440
          verb: list
        requestCount: 1455
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 431
          verb: get
        - requestCount: 431
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 870
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 680
          verb: get
        requestCount: 680
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 614
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 630
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 436
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 444
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 218
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 226
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 182
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 191
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 85
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 111
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 84
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 100
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 80
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 4787
    - byUser:
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 1080
          verb: list
        requestCount: 1104
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1104
    - byUser:
      - byVerb:
        - requestCount: 21
          verb: get
        - requestCount: 1020
          verb: list
        requestCount: 1041
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 445
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 462
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 13
          verb: delete
        - requestCount: 208
          verb: get
        - requestCount: 104
          verb: update
        requestCount: 325
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 240
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 247
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 240
          verb: get
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 46
          verb: watch
        requestCount: 46
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 44
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 34
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 2459
    requestCount: 8350
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 22
          verb: get
        - requestCount: 1080
          verb: list
        requestCount: 1102
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 406
          verb: get
        - requestCount: 406
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 820
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 646
          verb: get
        requestCount: 646
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 618
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 634
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 434
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 442
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 217
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 225
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 182
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 189
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 90
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 116
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 82
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 99
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 81
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      nodeName: 10.9.212.10
      requestCount: 4354
    - byUser:
      - byVerb:
        - requestCount: 19
          verb: get
        - requestCount: 1200
          verb: list
        requestCount: 1219
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1219
    - byUser:
      - byVerb:
        - requestCount: 19
          verb: get
        - requestCount: 1320
          verb: list
        requestCount: 1339
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 442
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 457
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 14
          verb: delete
        - requestCount: 224
          verb: get
        - requestCount: 112
          verb: update
        requestCount: 350
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 237
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 246
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 240
          verb: get
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      - byVerb:
        - requestCount: 49
          verb: watch
        requestCount: 49
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 44
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 35
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 13
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 21
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 2793
    requestCount: 8366
  requestCount: 189681
