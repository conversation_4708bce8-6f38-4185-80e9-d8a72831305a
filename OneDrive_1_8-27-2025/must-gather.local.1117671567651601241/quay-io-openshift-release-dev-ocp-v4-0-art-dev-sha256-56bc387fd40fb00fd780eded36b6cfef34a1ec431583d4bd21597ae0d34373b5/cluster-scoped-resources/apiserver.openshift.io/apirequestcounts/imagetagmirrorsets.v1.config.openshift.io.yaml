---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T14:58:31Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:58:31Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:38:02Z"
  name: imagetagmirrorsets.v1.config.openshift.io
  resourceVersion: "********"
  uid: 2743ff81-c703-4c59-a937-aee838e12355
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 11
          verb: watch
        requestCount: 11
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 15
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.9
      requestCount: 25
    requestCount: 40
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 24
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      nodeName: 10.9.212.9
      requestCount: 36
    requestCount: 60
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 24
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.9
      requestCount: 36
    requestCount: 60
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 24
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.9
      requestCount: 36
    requestCount: 60
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 23
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      nodeName: 10.9.212.9
      requestCount: 35
    requestCount: 58
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 24
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      nodeName: 10.9.212.9
      requestCount: 36
    requestCount: 60
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 25
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.9
      requestCount: 34
    requestCount: 59
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 23
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.9
      requestCount: 37
    requestCount: 60
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 25
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 38
    requestCount: 63
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 24
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      nodeName: 10.9.212.9
      requestCount: 32
    requestCount: 56
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 26
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      nodeName: 10.9.212.9
      requestCount: 37
    requestCount: 63
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 24
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.9
      requestCount: 37
    requestCount: 61
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 24
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      nodeName: 10.9.212.9
      requestCount: 38
    requestCount: 62
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 24
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 36
    requestCount: 60
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 23
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.9
      requestCount: 35
    requestCount: 58
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 11
          verb: watch
        requestCount: 11
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 15
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.9
      requestCount: 25
    requestCount: 40
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 24
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 37
    requestCount: 61
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 25
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.9
      requestCount: 36
    requestCount: 61
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 24
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.9
      requestCount: 33
    requestCount: 57
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 23
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.9
      requestCount: 37
    requestCount: 60
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 25
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 34
    requestCount: 59
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 23
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      nodeName: 10.9.212.9
      requestCount: 38
    requestCount: 61
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 24
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.9
      requestCount: 36
    requestCount: 60
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 23
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      nodeName: 10.9.212.9
      requestCount: 35
    requestCount: 58
  requestCount: 1357
