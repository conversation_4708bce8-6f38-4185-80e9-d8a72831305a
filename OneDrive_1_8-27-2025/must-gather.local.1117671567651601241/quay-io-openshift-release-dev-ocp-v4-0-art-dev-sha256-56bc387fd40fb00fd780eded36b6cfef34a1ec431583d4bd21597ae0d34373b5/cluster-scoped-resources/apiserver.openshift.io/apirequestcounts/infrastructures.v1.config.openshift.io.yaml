---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T14:58:01Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:58:01Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:42:03Z"
  name: infrastructures.v1.config.openshift.io
  resourceVersion: "********"
  uid: f6b31a43-0aea-487f-9845-18a5e449b218
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 38
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 22
          verb: get
        requestCount: 22
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 7
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cloud-credential-operator/v0.0.0
        username: system:serviceaccount:openshift-cloud-credential-operator:cloud-credential-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: ***********
      requestCount: 110
    - byUser:
      - byVerb:
        - requestCount: 11
          verb: watch
        requestCount: 11
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: oc/4.17.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      nodeName: **********
      requestCount: 13
    - byUser:
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: 10.9.212.9
      requestCount: 61
    requestCount: 184
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 54
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 70
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: get
        requestCount: 40
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: config-sync-controllers/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager-operator:cluster-cloud-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: psm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: ***********
      requestCount: 185
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 14
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 15
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      nodeName: 10.9.212.9
      requestCount: 99
    requestCount: 300
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 51
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 67
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 38
          verb: get
        requestCount: 38
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cloud-credential-operator/v0.0.0
        username: system:serviceaccount:openshift-cloud-credential-operator:cloud-credential-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: config-sync-controllers/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager-operator:cluster-cloud-controller-manager
      nodeName: ***********
      requestCount: 179
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 16
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 99
    requestCount: 295
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 54
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 71
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: get
        requestCount: 40
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-config-operator/v0.0.0
        username: system:serviceaccount:openshift-config-operator:openshift-config-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: config-sync-controllers/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager-operator:cluster-cloud-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      nodeName: ***********
      requestCount: 184
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 13
          verb: get
        requestCount: 13
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      nodeName: 10.9.212.9
      requestCount: 97
    requestCount: 297
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 55
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 72
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: get
        requestCount: 40
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 11
          verb: get
        requestCount: 11
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-config-operator/v0.0.0
        username: system:serviceaccount:openshift-config-operator:openshift-config-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cloud-credential-operator/v0.0.0
        username: system:serviceaccount:openshift-cloud-credential-operator:cloud-credential-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      nodeName: ***********
      requestCount: 191
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 15
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 98
    requestCount: 305
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 51
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 67
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 38
          verb: get
        requestCount: 38
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 10
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: config-sync-controllers/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager-operator:cluster-cloud-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-config-operator/v0.0.0
        username: system:serviceaccount:openshift-config-operator:openshift-config-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager-operator:cluster-cloud-controller-manager
      nodeName: ***********
      requestCount: 181
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 96
    requestCount: 293
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 54
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 69
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: get
        requestCount: 40
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-config-operator/v0.0.0
        username: system:serviceaccount:openshift-config-operator:openshift-config-operator
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 8
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager-operator:cluster-cloud-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: psm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: config-sync-controllers/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager-operator:cluster-cloud-controller-manager
      nodeName: ***********
      requestCount: 178
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 16
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 103
    requestCount: 297
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 54
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 71
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 38
          verb: get
        requestCount: 38
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 10
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      nodeName: ***********
      requestCount: 182
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      nodeName: 10.9.212.9
      requestCount: 94
    requestCount: 291
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 51
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 67
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: get
        requestCount: 40
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cloud-credential-operator/v0.0.0
        username: system:serviceaccount:openshift-cloud-credential-operator:cloud-credential-operator
      - byVerb:
        - requestCount: 8
          verb: get
        requestCount: 8
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      nodeName: ***********
      requestCount: 176
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 14
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 15
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 16
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: 10.9.212.9
      requestCount: 101
    requestCount: 291
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 54
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 70
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: get
        requestCount: 40
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 13
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: config-sync-controllers/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager-operator:cluster-cloud-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager-operator:cluster-cloud-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: psm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: ***********
      requestCount: 190
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 13
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 14
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 98
    requestCount: 305
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 51
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 67
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 38
          verb: get
        requestCount: 38
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager-operator:cluster-cloud-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cloud-credential-operator/v0.0.0
        username: system:serviceaccount:openshift-cloud-credential-operator:cloud-credential-operator
      nodeName: ***********
      requestCount: 176
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      nodeName: 10.9.212.9
      requestCount: 95
    requestCount: 288
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 54
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 69
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: get
        requestCount: 40
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: cluster-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager-operator:cluster-cloud-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: config-sync-controllers/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager-operator:cluster-cloud-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: psm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cloud-credential-operator/v0.0.0
        username: system:serviceaccount:openshift-cloud-credential-operator:cloud-credential-operator
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      nodeName: ***********
      requestCount: 184
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 15
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      nodeName: 10.9.212.9
      requestCount: 101
    requestCount: 301
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 54
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 72
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 38
          verb: get
        requestCount: 38
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-config-operator/v0.0.0
        username: system:serviceaccount:openshift-config-operator:openshift-config-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      nodeName: ***********
      requestCount: 182
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 13
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 14
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 95
    requestCount: 292
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 51
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 68
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: get
        requestCount: 40
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: psm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager-operator:cluster-cloud-controller-manager
      nodeName: ***********
      requestCount: 182
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 15
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 16
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      nodeName: 10.9.212.9
      requestCount: 98
    requestCount: 295
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 55
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 71
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: get
        requestCount: 40
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cloud-credential-operator/v0.0.0
        username: system:serviceaccount:openshift-cloud-credential-operator:cloud-credential-operator
      nodeName: ***********
      requestCount: 181
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      nodeName: 10.9.212.9
      requestCount: 100
    requestCount: 296
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 38
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 22
          verb: get
        requestCount: 22
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 7
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cloud-credential-operator/v0.0.0
        username: system:serviceaccount:openshift-cloud-credential-operator:cloud-credential-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: ***********
      requestCount: 110
    - byUser:
      - byVerb:
        - requestCount: 11
          verb: watch
        requestCount: 11
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: oc/4.17.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      nodeName: **********
      requestCount: 13
    - byUser:
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: 10.9.212.9
      requestCount: 61
    requestCount: 184
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 54
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 70
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 38
          verb: get
        requestCount: 38
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-config-operator/v0.0.0
        username: system:serviceaccount:openshift-config-operator:openshift-config-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cloud-credential-operator/v0.0.0
        username: system:serviceaccount:openshift-cloud-credential-operator:cloud-credential-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: config-sync-controllers/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager-operator:cluster-cloud-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: ***********
      requestCount: 182
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 15
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: 10.9.212.9
      requestCount: 99
    requestCount: 297
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 52
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 67
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: get
        requestCount: 40
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: config-sync-controllers/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager-operator:cluster-cloud-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cloud-credential-operator/v0.0.0
        username: system:serviceaccount:openshift-cloud-credential-operator:cloud-credential-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager-operator:cluster-cloud-controller-manager
      nodeName: ***********
      requestCount: 178
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 14
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 14
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 15
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 100
    requestCount: 292
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 54
          verb: get
        - requestCount: 19
          verb: watch
        requestCount: 73
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 38
          verb: get
        requestCount: 38
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: psm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-config-operator/v0.0.0
        username: system:serviceaccount:openshift-config-operator:openshift-config-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      nodeName: ***********
      requestCount: 182
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 99
    requestCount: 298
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 54
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 70
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: get
        requestCount: 40
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 12
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: config-sync-controllers/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager-operator:cluster-cloud-controller-manager
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      nodeName: ***********
      requestCount: 188
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 13
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 14
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: 10.9.212.9
      requestCount: 98
    requestCount: 302
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 51
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 68
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 39
          verb: get
        requestCount: 39
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 10
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cloud-credential-operator/v0.0.0
        username: system:serviceaccount:openshift-cloud-credential-operator:cloud-credential-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: psm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      nodeName: ***********
      requestCount: 183
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 18
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 15
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 16
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      nodeName: 10.9.212.9
      requestCount: 102
    requestCount: 303
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 54
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 69
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 39
          verb: get
        requestCount: 39
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: config-sync-controllers/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager-operator:cluster-cloud-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: psm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 8
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      nodeName: ***********
      requestCount: 186
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 14
          verb: get
        requestCount: 14
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 101
    requestCount: 302
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 51
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 67
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 40
          verb: get
        requestCount: 40
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-config-operator/v0.0.0
        username: system:serviceaccount:openshift-config-operator:openshift-config-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cloud-credential-operator/v0.0.0
        username: system:serviceaccount:openshift-cloud-credential-operator:cloud-credential-operator
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 7
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      nodeName: ***********
      requestCount: 182
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 13
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 14
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 96
    requestCount: 295
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 54
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 72
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 38
          verb: get
        requestCount: 38
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 10
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 9
          verb: get
        requestCount: 9
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: config-sync-controllers/v0.0.0
        username: system:serviceaccount:openshift-cloud-controller-manager-operator:cluster-cloud-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      nodeName: ***********
      requestCount: 187
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 14
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 15
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 98
    requestCount: 300
  requestCount: 6719
