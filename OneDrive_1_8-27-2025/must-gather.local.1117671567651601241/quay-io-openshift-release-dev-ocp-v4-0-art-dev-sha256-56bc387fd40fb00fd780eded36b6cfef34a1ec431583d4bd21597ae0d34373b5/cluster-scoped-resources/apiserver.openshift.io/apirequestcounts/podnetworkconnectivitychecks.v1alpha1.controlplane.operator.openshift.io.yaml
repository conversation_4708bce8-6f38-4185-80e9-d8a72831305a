---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T14:58:21Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:58:21Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:41:05Z"
  name: podnetworkconnectivitychecks.v1alpha1.controlplane.operator.openshift.io
  resourceVersion: "********"
  uid: c9fe3204-4195-459d-a4d2-71535545bfc8
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 192
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 197
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 125
          verb: list
        requestCount: 125
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: oc/4.17.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      nodeName: 10.9.212.10
      requestCount: 339
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 5
    - byUser:
      - byVerb:
        - requestCount: 129
          verb: update
        - requestCount: 5
          verb: watch
        requestCount: 134
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 23
          verb: list
        requestCount: 23
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.9
      requestCount: 183
    requestCount: 527
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 288
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 295
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 187
          verb: list
        requestCount: 187
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 506
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 201
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 209
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 290
    requestCount: 803
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 288
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 297
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 189
          verb: list
        requestCount: 189
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.10
      requestCount: 511
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 218
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 226
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 35
          verb: list
        requestCount: 35
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.9
      requestCount: 305
    requestCount: 825
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 288
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 295
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 190
          verb: list
        requestCount: 190
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 512
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 203
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 211
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 35
          verb: list
        requestCount: 35
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 288
    requestCount: 809
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 336
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 344
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 191
          verb: list
        requestCount: 191
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.10
      requestCount: 559
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 215
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 223
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.9
      requestCount: 302
    requestCount: 868
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 288
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 295
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 187
          verb: list
        requestCount: 187
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 508
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 215
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 223
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.9
      requestCount: 302
    requestCount: 817
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 288
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 295
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 188
          verb: list
        requestCount: 188
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.10
      requestCount: 508
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 202
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 210
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.9
      requestCount: 295
    requestCount: 811
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 288
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 297
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 190
          verb: list
        requestCount: 190
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 513
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 216
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 225
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 35
          verb: list
        requestCount: 35
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 302
    requestCount: 823
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 288
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 296
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 192
          verb: list
        requestCount: 192
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.10
      requestCount: 513
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 201
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 209
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 289
    requestCount: 810
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 288
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 296
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 186
          verb: list
        requestCount: 186
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 506
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 218
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 227
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 307
    requestCount: 821
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 288
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 295
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 188
          verb: list
        requestCount: 188
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.10
      requestCount: 507
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 203
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 211
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.9
      requestCount: 292
    requestCount: 807
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 288
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 296
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 191
          verb: list
        requestCount: 191
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 512
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 215
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 223
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 37
          verb: list
        requestCount: 37
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 304
    requestCount: 823
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 288
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 297
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 192
          verb: list
        requestCount: 192
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.10
      requestCount: 514
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 215
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 222
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.9
      requestCount: 302
    requestCount: 824
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 288
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 296
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 189
          verb: list
        requestCount: 189
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 509
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 202
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 210
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.9
      requestCount: 288
    requestCount: 805
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 288
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 295
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 188
          verb: list
        requestCount: 188
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.10
      requestCount: 507
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 216
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 224
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 306
    requestCount: 821
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 192
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 197
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 125
          verb: list
        requestCount: 125
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: oc/4.17.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      nodeName: 10.9.212.10
      requestCount: 339
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 5
    - byUser:
      - byVerb:
        - requestCount: 129
          verb: update
        - requestCount: 5
          verb: watch
        requestCount: 134
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 23
          verb: list
        requestCount: 23
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.9
      requestCount: 183
    requestCount: 527
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 288
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 297
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 187
          verb: list
        requestCount: 187
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 509
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 209
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 216
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 297
    requestCount: 813
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 288
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 297
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 188
          verb: list
        requestCount: 188
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.10
      requestCount: 509
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 201
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 209
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 291
    requestCount: 808
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 288
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 296
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 190
          verb: list
        requestCount: 190
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 510
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 218
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 227
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 311
    requestCount: 829
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 288
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 296
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 190
          verb: list
        requestCount: 190
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.10
      requestCount: 508
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 203
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 210
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 35
          verb: list
        requestCount: 35
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 287
    requestCount: 802
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 288
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 295
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 187
          verb: list
        requestCount: 187
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 508
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 215
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 223
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 303
    requestCount: 818
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 288
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 296
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 191
          verb: list
        requestCount: 191
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.10
      requestCount: 512
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 215
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 223
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.9
      requestCount: 303
    requestCount: 822
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 288
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 297
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 192
          verb: list
        requestCount: 192
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 514
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 202
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 211
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 37
          verb: list
        requestCount: 37
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 290
    requestCount: 811
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 288
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 296
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 190
          verb: list
        requestCount: 190
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.10
      requestCount: 509
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 216
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 223
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 303
    requestCount: 820
  requestCount: 18517
