---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T14:57:51Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:57:51Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:41:07Z"
  name: services.v1
  resourceVersion: "********"
  uid: bb0669e4-b0c3-43ba-9f26-5957bd6f5056
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 523
          verb: get
        requestCount: 523
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 503
          verb: watch
        requestCount: 503
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 216
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 226
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 51
          verb: delete
        - requestCount: 102
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 159
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 82
          verb: patch
        - requestCount: 21
          verb: watch
        requestCount: 103
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 67
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 73
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 48
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 53
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 53
          verb: watch
        requestCount: 53
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 48
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 53
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 40
          verb: get
        - requestCount: 11
          verb: watch
        requestCount: 51
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 1837
    - byUser:
      - byVerb:
        - requestCount: 24
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 29
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.8
      requestCount: 52
    - byUser:
      - byVerb:
        - requestCount: 245
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 260
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 109
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 118
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 30
          verb: delete
        - requestCount: 72
          verb: get
        requestCount: 102
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 68
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 83
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 62
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 67
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 46
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 52
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 24
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 29
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 27
          verb: watch
        requestCount: 27
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 765
    requestCount: 2654
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 820
          verb: get
        requestCount: 820
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 757
          verb: watch
        requestCount: 757
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 318
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 335
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 243
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 126
          verb: patch
        - requestCount: 33
          verb: watch
        requestCount: 159
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 100
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 108
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 81
          verb: watch
        requestCount: 81
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 79
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 77
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 2739
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 47
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      nodeName: 10.9.212.8
      requestCount: 71
    - byUser:
      - byVerb:
        - requestCount: 390
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 414
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 70
          verb: delete
        - requestCount: 168
          verb: get
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 171
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 186
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 106
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 130
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 101
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 109
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 83
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 46
          verb: watch
        requestCount: 46
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus2-kx2t4/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      nodeName: 10.9.212.9
      requestCount: 1278
    requestCount: 4088
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 790
          verb: get
        requestCount: 790
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 746
          verb: watch
        requestCount: 746
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 327
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 119
          verb: patch
        - requestCount: 32
          verb: watch
        requestCount: 151
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 95
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 102
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 81
          verb: watch
        requestCount: 81
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 81
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 79
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 77
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 2676
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 2
          verb: watch
        requestCount: 2
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: 10.9.212.8
      requestCount: 76
    - byUser:
      - byVerb:
        - requestCount: 388
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 411
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 75
          verb: delete
        - requestCount: 180
          verb: get
        requestCount: 255
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 186
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 102
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 126
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 97
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 105
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 75
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 85
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 52
          verb: watch
        requestCount: 52
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 1297
    requestCount: 4049
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 809
          verb: get
        requestCount: 809
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 749
          verb: watch
        requestCount: 749
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 330
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 126
          verb: patch
        - requestCount: 33
          verb: watch
        requestCount: 159
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 97
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 105
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 82
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 80
          verb: watch
        requestCount: 80
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 76
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 2712
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 81
    - byUser:
      - byVerb:
        - requestCount: 387
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 410
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 65
          verb: delete
        - requestCount: 156
          verb: get
        requestCount: 221
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 186
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 104
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 129
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 99
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 108
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 83
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 47
          verb: watch
        requestCount: 47
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 1257
    requestCount: 4050
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 820
          verb: get
        requestCount: 820
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 746
          verb: watch
        requestCount: 746
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 328
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 126
          verb: patch
        - requestCount: 33
          verb: watch
        requestCount: 159
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 106
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 81
          verb: watch
        requestCount: 81
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 79
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 79
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 75
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 2715
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 47
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      nodeName: 10.9.212.8
      requestCount: 81
    - byUser:
      - byVerb:
        - requestCount: 387
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 411
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 70
          verb: delete
        - requestCount: 168
          verb: get
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 185
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 103
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 125
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 106
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 80
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 49
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 48
          verb: watch
        requestCount: 48
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      nodeName: 10.9.212.9
      requestCount: 1267
    requestCount: 4063
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 779
          verb: get
        requestCount: 779
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 756
          verb: watch
        requestCount: 756
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 327
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 119
          verb: patch
        - requestCount: 34
          verb: watch
        requestCount: 153
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 94
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 102
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 79
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 77
          verb: watch
        requestCount: 77
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 76
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 2671
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: 10.9.212.8
      requestCount: 80
    - byUser:
      - byVerb:
        - requestCount: 390
          verb: get
        - requestCount: 27
          verb: watch
        requestCount: 417
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 70
          verb: delete
        - requestCount: 168
          verb: get
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 187
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 102
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 127
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 107
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 82
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 48
          verb: watch
        requestCount: 48
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 1280
    requestCount: 4031
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 820
          verb: get
        requestCount: 820
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 767
          verb: watch
        requestCount: 767
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 328
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 243
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 126
          verb: patch
        - requestCount: 32
          verb: watch
        requestCount: 158
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 97
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 106
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 77
          verb: watch
        requestCount: 77
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 75
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 2734
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 49
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 81
    - byUser:
      - byVerb:
        - requestCount: 390
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 412
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 75
          verb: delete
        - requestCount: 180
          verb: get
        requestCount: 255
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 185
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 102
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 126
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 106
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 82
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 50
          verb: watch
        requestCount: 50
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 1290
    requestCount: 4105
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 783
          verb: get
        requestCount: 783
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 751
          verb: watch
        requestCount: 751
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 328
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 121
          verb: patch
        - requestCount: 31
          verb: watch
        requestCount: 152
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 106
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 79
          verb: watch
        requestCount: 79
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 76
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 2677
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 49
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      nodeName: 10.9.212.8
      requestCount: 80
    - byUser:
      - byVerb:
        - requestCount: 388
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 412
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 60
          verb: delete
        - requestCount: 144
          verb: get
        requestCount: 204
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 185
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 104
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 128
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 99
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 107
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 82
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 47
          verb: watch
        requestCount: 47
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.9
      requestCount: 1237
    requestCount: 3994
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 816
          verb: get
        requestCount: 816
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 757
          verb: watch
        requestCount: 757
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 318
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 334
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 124
          verb: patch
        - requestCount: 33
          verb: watch
        requestCount: 157
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 97
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 105
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 79
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 78
          verb: watch
        requestCount: 78
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 75
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 2723
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 47
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 80
    - byUser:
      - byVerb:
        - requestCount: 386
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 411
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 75
          verb: delete
        - requestCount: 180
          verb: get
        requestCount: 255
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 172
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 187
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 105
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 129
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 100
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 110
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 81
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 46
          verb: watch
        requestCount: 46
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 1295
    requestCount: 4098
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 817
          verb: get
        requestCount: 817
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 766
          verb: watch
        requestCount: 766
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 328
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 126
          verb: patch
        - requestCount: 32
          verb: watch
        requestCount: 158
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 106
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 81
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 78
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 64
          verb: watch
        requestCount: 64
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      nodeName: 10.9.212.10
      requestCount: 2720
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: 10.9.212.8
      requestCount: 80
    - byUser:
      - byVerb:
        - requestCount: 388
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 413
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 65
          verb: delete
        - requestCount: 156
          verb: get
        requestCount: 221
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 184
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 103
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 128
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 105
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 81
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 48
          verb: watch
        requestCount: 48
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 47
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 1256
    requestCount: 4056
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 782
          verb: get
        requestCount: 782
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 751
          verb: watch
        requestCount: 751
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 329
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 241
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 119
          verb: patch
        - requestCount: 33
          verb: watch
        requestCount: 152
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 97
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 106
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 84
          verb: watch
        requestCount: 84
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 77
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 2682
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 49
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: 10.9.212.8
      requestCount: 81
    - byUser:
      - byVerb:
        - requestCount: 388
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 412
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 70
          verb: delete
        - requestCount: 168
          verb: get
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 186
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 104
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 127
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 100
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 109
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 80
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 51
          verb: watch
        requestCount: 51
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 49
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 1280
    requestCount: 4043
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 820
          verb: get
        requestCount: 820
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 752
          verb: watch
        requestCount: 752
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 326
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 126
          verb: patch
        - requestCount: 31
          verb: watch
        requestCount: 157
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 97
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 105
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 83
          verb: watch
        requestCount: 83
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 82
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 82
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 61
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 77
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 2726
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 47
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 79
    - byUser:
      - byVerb:
        - requestCount: 385
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 408
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 70
          verb: delete
        - requestCount: 168
          verb: get
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 174
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 190
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 105
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 131
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 99
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 106
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 81
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 46
          verb: watch
        requestCount: 46
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 1276
    requestCount: 4081
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 779
          verb: get
        requestCount: 779
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 749
          verb: watch
        requestCount: 749
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 309
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 324
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 126
          verb: patch
        - requestCount: 32
          verb: watch
        requestCount: 158
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 105
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 82
          verb: watch
        requestCount: 82
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 79
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 77
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 2675
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 49
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      nodeName: 10.9.212.8
      requestCount: 82
    - byUser:
      - byVerb:
        - requestCount: 387
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 411
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 65
          verb: delete
        - requestCount: 156
          verb: get
        requestCount: 221
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 187
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 102
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 128
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 106
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 83
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 51
          verb: watch
        requestCount: 51
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 1264
    requestCount: 4021
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 820
          verb: get
        requestCount: 820
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 760
          verb: watch
        requestCount: 760
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 327
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 119
          verb: patch
        - requestCount: 34
          verb: watch
        requestCount: 153
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 97
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 105
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 80
          verb: watch
        requestCount: 80
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 71
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 79
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 76
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 2722
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 49
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      nodeName: 10.9.212.8
      requestCount: 83
    - byUser:
      - byVerb:
        - requestCount: 389
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 412
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 75
          verb: delete
        - requestCount: 180
          verb: get
        requestCount: 255
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 183
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 104
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 129
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 106
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 75
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 83
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 49
          verb: watch
        requestCount: 49
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1288
    requestCount: 4093
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 807
          verb: get
        requestCount: 807
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 756
          verb: watch
        requestCount: 756
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 327
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 126
          verb: patch
        - requestCount: 31
          verb: watch
        requestCount: 157
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 97
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 105
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 81
          verb: watch
        requestCount: 81
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 76
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 2711
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 47
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.8
      requestCount: 85
    - byUser:
      - byVerb:
        - requestCount: 387
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 411
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 70
          verb: delete
        - requestCount: 168
          verb: get
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 186
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 104
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 126
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 107
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 81
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 50
          verb: watch
        requestCount: 50
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 10
          verb: watch
        requestCount: 50
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 1277
    requestCount: 4073
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 523
          verb: get
        requestCount: 523
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 503
          verb: watch
        requestCount: 503
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 216
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 226
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 51
          verb: delete
        - requestCount: 102
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 159
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 82
          verb: patch
        - requestCount: 21
          verb: watch
        requestCount: 103
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 67
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 73
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 48
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 53
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 53
          verb: watch
        requestCount: 53
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 48
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 53
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 40
          verb: get
        - requestCount: 11
          verb: watch
        requestCount: 51
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 1837
    - byUser:
      - byVerb:
        - requestCount: 24
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 29
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.8
      requestCount: 52
    - byUser:
      - byVerb:
        - requestCount: 245
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 260
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 109
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 118
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 30
          verb: delete
        - requestCount: 72
          verb: get
        requestCount: 102
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 68
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 83
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 62
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 67
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 46
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 52
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 24
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 29
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 27
          verb: watch
        requestCount: 27
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 765
    requestCount: 2654
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 794
          verb: get
        requestCount: 794
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 752
          verb: watch
        requestCount: 752
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 327
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 343
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 241
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 126
          verb: patch
        - requestCount: 33
          verb: watch
        requestCount: 159
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 102
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 110
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 77
          verb: watch
        requestCount: 77
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 75
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 2711
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 49
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      nodeName: 10.9.212.8
      requestCount: 57
    - byUser:
      - byVerb:
        - requestCount: 388
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 412
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 70
          verb: delete
        - requestCount: 168
          verb: get
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 174
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 189
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 104
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 128
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 103
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 111
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 82
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 47
          verb: watch
        requestCount: 47
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 1283
    requestCount: 4051
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 820
          verb: get
        requestCount: 820
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 755
          verb: watch
        requestCount: 755
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 324
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 341
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 119
          verb: patch
        - requestCount: 33
          verb: watch
        requestCount: 152
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 99
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 108
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 81
          verb: watch
        requestCount: 81
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 79
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 76
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 2734
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      nodeName: 10.9.212.8
      requestCount: 55
    - byUser:
      - byVerb:
        - requestCount: 387
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 413
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 70
          verb: delete
        - requestCount: 168
          verb: get
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 174
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 191
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 103
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 128
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 104
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 112
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 82
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 47
          verb: watch
        requestCount: 47
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 1287
    requestCount: 4076
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 779
          verb: get
        requestCount: 779
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 745
          verb: watch
        requestCount: 745
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 324
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 339
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 126
          verb: patch
        - requestCount: 30
          verb: watch
        requestCount: 156
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 100
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 108
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 82
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 79
          verb: watch
        requestCount: 79
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 75
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 2685
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      nodeName: 10.9.212.8
      requestCount: 56
    - byUser:
      - byVerb:
        - requestCount: 387
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 411
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 70
          verb: delete
        - requestCount: 168
          verb: get
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 174
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 190
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 102
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 127
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 102
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 109
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 49
          verb: watch
        requestCount: 49
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 47
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1277
    requestCount: 4018
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 820
          verb: get
        requestCount: 820
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 751
          verb: watch
        requestCount: 751
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 309
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 325
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 125
          verb: patch
        - requestCount: 31
          verb: watch
        requestCount: 156
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 96
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 104
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 81
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 81
          verb: watch
        requestCount: 81
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 77
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 2717
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 47
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 3
          verb: watch
        requestCount: 3
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 59
    - byUser:
      - byVerb:
        - requestCount: 389
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 411
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 65
          verb: delete
        - requestCount: 156
          verb: get
        requestCount: 221
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 185
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 97
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 119
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 106
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 80
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 10
          verb: watch
        requestCount: 50
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 49
          verb: watch
        requestCount: 49
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 1245
    requestCount: 4021
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 797
          verb: get
        requestCount: 797
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 757
          verb: watch
        requestCount: 757
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 329
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 120
          verb: patch
        - requestCount: 33
          verb: watch
        requestCount: 153
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 96
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 104
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 82
          verb: watch
        requestCount: 82
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 71
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 79
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 79
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 76
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 2698
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 49
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      nodeName: 10.9.212.8
      requestCount: 64
    - byUser:
      - byVerb:
        - requestCount: 388
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 412
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 75
          verb: delete
        - requestCount: 180
          verb: get
        requestCount: 255
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 186
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 103
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 127
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 99
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 106
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 81
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 47
          verb: watch
        requestCount: 47
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 1290
    requestCount: 4052
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 802
          verb: get
        requestCount: 802
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 756
          verb: watch
        requestCount: 756
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 327
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 126
          verb: patch
        - requestCount: 31
          verb: watch
        requestCount: 157
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 106
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 82
          verb: watch
        requestCount: 82
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 81
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 78
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 2711
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      nodeName: 10.9.212.8
      requestCount: 62
    - byUser:
      - byVerb:
        - requestCount: 390
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 414
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 70
          verb: delete
        - requestCount: 168
          verb: get
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 187
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 100
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 124
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 106
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 79
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 48
          verb: watch
        requestCount: 48
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1269
    requestCount: 4042
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 820
          verb: get
        requestCount: 820
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 764
          verb: watch
        requestCount: 764
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 329
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 243
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 119
          verb: patch
        - requestCount: 33
          verb: watch
        requestCount: 152
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 97
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 104
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 82
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 81
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 80
          verb: watch
        requestCount: 80
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 61
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 76
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 2731
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 10
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      nodeName: 10.9.212.8
      requestCount: 74
    - byUser:
      - byVerb:
        - requestCount: 370
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 392
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 65
          verb: delete
        - requestCount: 156
          verb: get
        requestCount: 221
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 162
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 179
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 96
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 118
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 93
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 100
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 78
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 38
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 45
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 44
          verb: watch
        requestCount: 44
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 1200
    requestCount: 4005
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 779
          verb: get
        requestCount: 779
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 756
          verb: watch
        requestCount: 756
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 328
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 78
          verb: delete
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 242
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 126
          verb: patch
        - requestCount: 33
          verb: watch
        requestCount: 159
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 96
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 104
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 81
          verb: watch
        requestCount: 81
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 75
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      nodeName: 10.9.212.10
      requestCount: 2684
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 49
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      nodeName: 10.9.212.8
      requestCount: 73
    - byUser:
      - byVerb:
        - requestCount: 389
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 414
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 70
          verb: delete
        - requestCount: 168
          verb: get
        requestCount: 238
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 185
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 100
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 122
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 106
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 81
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 49
          verb: watch
        requestCount: 49
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 40
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 48
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1267
    requestCount: 4024
  requestCount: 91788
