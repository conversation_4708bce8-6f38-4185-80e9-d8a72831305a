---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T15:00:51Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T15:00:51Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:41:33Z"
  name: machineconfigpools.v1.machineconfiguration.openshift.io
  resourceVersion: "********"
  uid: 0c848e9a-e8e5-4fec-814c-a7aec08fc196
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 488
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 493
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 16
          verb: list
        requestCount: 16
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 7
          verb: get
        - requestCount: 4
          verb: watch
        requestCount: 11
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 553
    - byUser:
      - byVerb:
        - requestCount: 22
          verb: list
        requestCount: 22
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 22
    - byUser:
      - byVerb:
        - requestCount: 34
          verb: list
        requestCount: 34
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: update
        - requestCount: 5
          verb: watch
        requestCount: 11
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 58
    requestCount: 633
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 862
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 869
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 44
          verb: list
        requestCount: 44
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 20
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 988
    - byUser:
      - byVerb:
        - requestCount: 43
          verb: list
        requestCount: 43
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 43
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: list
        requestCount: 33
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 16
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 69
    requestCount: 1100
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 854
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 862
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 30
          verb: list
        requestCount: 30
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 26
          verb: watch
        requestCount: 26
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 20
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      nodeName: 10.9.212.10
      requestCount: 971
    - byUser:
      - byVerb:
        - requestCount: 31
          verb: list
        requestCount: 31
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 31
    - byUser:
      - byVerb:
        - requestCount: 59
          verb: list
        requestCount: 59
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 16
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 96
    requestCount: 1098
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 838
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 846
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 34
          verb: list
        requestCount: 34
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 20
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 956
    - byUser:
      - byVerb:
        - requestCount: 44
          verb: list
        requestCount: 44
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 44
    - byUser:
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 13
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 72
    requestCount: 1072
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 840
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 848
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 19
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      nodeName: 10.9.212.10
      requestCount: 960
    - byUser:
      - byVerb:
        - requestCount: 42
          verb: list
        requestCount: 42
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 42
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        requestCount: 40
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 17
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 77
    requestCount: 1079
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 874
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 881
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 52
          verb: list
        requestCount: 52
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 20
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1010
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: list
        requestCount: 30
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 30
    - byUser:
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 17
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 75
    requestCount: 1115
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 870
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 879
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 40
          verb: list
        requestCount: 40
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 20
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      nodeName: 10.9.212.10
      requestCount: 994
    - byUser:
      - byVerb:
        - requestCount: 46
          verb: list
        requestCount: 46
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 46
    - byUser:
      - byVerb:
        - requestCount: 34
          verb: list
        requestCount: 34
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 16
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 70
    requestCount: 1110
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 846
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 856
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 42
          verb: list
        requestCount: 42
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 20
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 974
    - byUser:
      - byVerb:
        - requestCount: 42
          verb: list
        requestCount: 42
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 42
    - byUser:
      - byVerb:
        - requestCount: 34
          verb: list
        requestCount: 34
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 17
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 72
    requestCount: 1088
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 900
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 907
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 46
          verb: list
        requestCount: 46
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 20
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 1029
    - byUser:
      - byVerb:
        - requestCount: 27
          verb: list
        requestCount: 27
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 27
    - byUser:
      - byVerb:
        - requestCount: 45
          verb: list
        requestCount: 45
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 17
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 83
    requestCount: 1139
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 872
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 881
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 22
          verb: list
        requestCount: 22
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 19
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 979
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: list
        requestCount: 60
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 60
    - byUser:
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 16
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 74
    requestCount: 1113
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 886
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 894
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 40
          verb: list
        requestCount: 40
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 21
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 1011
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 36
    - byUser:
      - byVerb:
        - requestCount: 42
          verb: list
        requestCount: 42
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 15
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 77
    requestCount: 1124
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 846
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 854
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 21
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 971
    - byUser:
      - byVerb:
        - requestCount: 48
          verb: list
        requestCount: 48
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 48
    - byUser:
      - byVerb:
        - requestCount: 34
          verb: list
        requestCount: 34
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 17
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 72
    requestCount: 1091
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 856
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 863
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 34
          verb: list
        requestCount: 34
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 20
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 974
    - byUser:
      - byVerb:
        - requestCount: 44
          verb: list
        requestCount: 44
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 44
    - byUser:
      - byVerb:
        - requestCount: 42
          verb: list
        requestCount: 42
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 15
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 76
    requestCount: 1094
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 842
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 851
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 34
          verb: list
        requestCount: 34
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 20
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 963
    - byUser:
      - byVerb:
        - requestCount: 54
          verb: list
        requestCount: 54
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 54
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: list
        requestCount: 32
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 17
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 69
    requestCount: 1086
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 862
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 870
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 50
          verb: list
        requestCount: 50
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 20
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      nodeName: 10.9.212.10
      requestCount: 995
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 36
    - byUser:
      - byVerb:
        - requestCount: 34
          verb: list
        requestCount: 34
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 16
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 70
    requestCount: 1101
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 488
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 493
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 16
          verb: list
        requestCount: 16
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 7
          verb: get
        - requestCount: 4
          verb: watch
        requestCount: 11
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 553
    - byUser:
      - byVerb:
        - requestCount: 22
          verb: list
        requestCount: 22
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 22
    - byUser:
      - byVerb:
        - requestCount: 34
          verb: list
        requestCount: 34
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: update
        - requestCount: 5
          verb: watch
        requestCount: 11
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 58
    requestCount: 633
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 872
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 879
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 40
          verb: list
        requestCount: 40
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 20
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 994
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: list
        requestCount: 32
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 32
    - byUser:
      - byVerb:
        - requestCount: 46
          verb: list
        requestCount: 46
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 16
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 81
    requestCount: 1107
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 884
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 892
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 28
          verb: list
        requestCount: 28
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 19
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      nodeName: 10.9.212.10
      requestCount: 995
    - byUser:
      - byVerb:
        - requestCount: 54
          verb: list
        requestCount: 54
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 54
    - byUser:
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 15
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 73
    requestCount: 1122
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 858
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 867
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 39
          verb: list
        requestCount: 39
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 20
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 980
    - byUser:
      - byVerb:
        - requestCount: 37
          verb: list
        requestCount: 37
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 37
    - byUser:
      - byVerb:
        - requestCount: 44
          verb: list
        requestCount: 44
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 16
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 80
    requestCount: 1097
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 850
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 859
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 26
          verb: watch
        requestCount: 26
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 18
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 972
    - byUser:
      - byVerb:
        - requestCount: 46
          verb: list
        requestCount: 46
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 46
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 16
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 71
    requestCount: 1089
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 872
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 879
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 32
          verb: list
        requestCount: 32
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 20
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 987
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 36
    - byUser:
      - byVerb:
        - requestCount: 52
          verb: list
        requestCount: 52
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 15
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 88
    requestCount: 1111
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 828
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 835
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 40
          verb: list
        requestCount: 40
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 11
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 19
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 949
    - byUser:
      - byVerb:
        - requestCount: 42
          verb: list
        requestCount: 42
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 42
    - byUser:
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 16
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 74
    requestCount: 1065
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 862
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 869
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 37
          verb: list
        requestCount: 37
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 19
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 984
    - byUser:
      - byVerb:
        - requestCount: 48
          verb: list
        requestCount: 48
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 48
    - byUser:
      - byVerb:
        - requestCount: 35
          verb: list
        requestCount: 35
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 16
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 71
    requestCount: 1103
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 812
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 821
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 40
          verb: list
        requestCount: 40
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 21
        userAgent: managed-upgrade-operator/v0.0.0
        username: system:serviceaccount:openshift-managed-upgrade-operator:managed-upgrade-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      nodeName: 10.9.212.10
      requestCount: 937
    - byUser:
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 38
    - byUser:
      - byVerb:
        - requestCount: 42
          verb: list
        requestCount: 42
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 15
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 76
    requestCount: 1051
  requestCount: 24788
