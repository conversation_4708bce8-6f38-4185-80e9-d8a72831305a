---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T15:10:04Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T15:10:04Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:40:16Z"
  name: selfsubjectreviews.v1.authentication.k8s.io
  resourceVersion: "********"
  uid: 4ef5d706-f61d-4376-9000-1a58af0d6f15
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 301
          verb: create
        requestCount: 301
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 301
    requestCount: 301
  last24h:
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 459
          verb: create
        requestCount: 459
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 459
    requestCount: 459
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 301
          verb: create
        requestCount: 301
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 301
    requestCount: 301
  - requestCount: 0
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: create
        requestCount: 1
        userAgent: oc/4.15.0
        username: V8131
      nodeName: 10.9.212.10
      requestCount: 1
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 481
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 480
          verb: create
        requestCount: 480
        userAgent: dockerregistry/v0.0.0
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      nodeName: 10.9.212.9
      requestCount: 480
    requestCount: 480
  requestCount: 10841
