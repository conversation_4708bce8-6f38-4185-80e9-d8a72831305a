---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T14:58:14Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:58:14Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:41:46Z"
  name: machineconfigs.v1.machineconfiguration.openshift.io
  resourceVersion: "********"
  uid: f06a3864-d499-461f-a157-534d1ccf3d28
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 72
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 78
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 42
          verb: watch
        requestCount: 42
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 11
          verb: watch
        requestCount: 11
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 158
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      nodeName: 10.9.212.8
      requestCount: 1
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: get
        - requestCount: 56
          verb: update
        - requestCount: 5
          verb: watch
        requestCount: 125
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 159
    requestCount: 318
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 108
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 116
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 64
          verb: watch
        requestCount: 64
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 2
          verb: create
        - requestCount: 8
          verb: watch
        requestCount: 10
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 239
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 80
          verb: get
        - requestCount: 68
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 155
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 208
    requestCount: 447
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 102
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 110
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 64
          verb: watch
        requestCount: 64
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      nodeName: 10.9.212.10
      requestCount: 232
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 62
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 143
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 196
    requestCount: 428
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 108
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 115
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 64
          verb: watch
        requestCount: 64
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 236
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 80
          verb: get
        - requestCount: 68
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 157
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 211
    requestCount: 447
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 108
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 116
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 66
          verb: watch
        requestCount: 66
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      nodeName: 10.9.212.10
      requestCount: 237
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 80
          verb: get
        - requestCount: 68
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 156
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 208
    requestCount: 445
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 102
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 110
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 26
          verb: watch
        requestCount: 26
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 229
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 62
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 144
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 198
    requestCount: 427
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 108
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 116
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 2
          verb: create
        - requestCount: 9
          verb: watch
        requestCount: 11
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      nodeName: 10.9.212.10
      requestCount: 239
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 64
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 145
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 195
    requestCount: 434
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 108
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 116
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 64
          verb: watch
        requestCount: 64
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 26
          verb: watch
        requestCount: 26
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 237
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 80
          verb: get
        - requestCount: 68
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 157
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 209
    requestCount: 446
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 102
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 111
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 64
          verb: watch
        requestCount: 64
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 2
          verb: create
        - requestCount: 7
          verb: watch
        requestCount: 9
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      nodeName: 10.9.212.10
      requestCount: 232
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 80
          verb: get
        - requestCount: 68
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 156
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 208
    requestCount: 440
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 108
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 115
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 63
          verb: watch
        requestCount: 63
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 236
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 62
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 143
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 193
    requestCount: 429
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 102
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 110
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 63
          verb: watch
        requestCount: 63
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      nodeName: 10.9.212.10
      requestCount: 226
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 80
          verb: get
        - requestCount: 68
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 157
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 210
    requestCount: 436
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 108
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 116
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 2
          verb: create
        - requestCount: 7
          verb: watch
        requestCount: 9
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 239
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 80
          verb: get
        - requestCount: 68
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 156
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 207
    requestCount: 446
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 108
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 117
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 63
          verb: watch
        requestCount: 63
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      nodeName: 10.9.212.10
      requestCount: 234
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 62
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 144
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 196
    requestCount: 430
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 102
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 110
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 230
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 80
          verb: get
        - requestCount: 68
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 156
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 208
    requestCount: 438
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 108
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 116
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 64
          verb: watch
        requestCount: 64
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      nodeName: 10.9.212.10
      requestCount: 237
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 80
          verb: get
        - requestCount: 68
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 155
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 207
    requestCount: 444
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 72
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 78
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 42
          verb: watch
        requestCount: 42
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 11
          verb: watch
        requestCount: 11
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 158
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      nodeName: 10.9.212.8
      requestCount: 1
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: get
        - requestCount: 56
          verb: update
        - requestCount: 5
          verb: watch
        requestCount: 125
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 159
    requestCount: 318
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 108
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 117
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 66
          verb: watch
        requestCount: 66
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 239
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 80
          verb: get
        - requestCount: 68
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 156
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 209
    requestCount: 448
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 102
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 109
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      nodeName: 10.9.212.10
      requestCount: 228
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 80
          verb: get
        - requestCount: 68
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 157
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 209
    requestCount: 437
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 108
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 116
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 67
          verb: watch
        requestCount: 67
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 238
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 80
          verb: get
        - requestCount: 68
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 156
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 207
    requestCount: 445
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 108
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 116
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      nodeName: 10.9.212.10
      requestCount: 232
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 62
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 145
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 196
    requestCount: 428
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 102
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 111
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 4
          verb: create
        - requestCount: 8
          verb: watch
        requestCount: 12
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 238
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 80
          verb: get
        - requestCount: 68
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 156
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 204
    requestCount: 442
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 108
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 115
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 61
          verb: watch
        requestCount: 61
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      nodeName: 10.9.212.10
      requestCount: 234
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 80
          verb: get
        - requestCount: 68
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 156
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 209
    requestCount: 443
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 102
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 111
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 230
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 62
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 144
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 196
    requestCount: 426
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 108
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 115
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 63
          verb: watch
        requestCount: 63
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: machine-config-server/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-server
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      nodeName: 10.9.212.10
      requestCount: 232
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 48
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 117
        userAgent: machine-config-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-controller
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: machine-config-daemon/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 167
    requestCount: 399
  requestCount: 9923
