---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T14:59:03Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:59:03Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:41:51Z"
  name: serviceaccounts.v1
  resourceVersion: "********"
  uid: 9ae22274-4c16-448e-a617-6d58fbc83743
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 1681
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 1687
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 233
          verb: create
        - requestCount: 486
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 724
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 523
          verb: get
        requestCount: 523
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 67
          verb: delete
        - requestCount: 298
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 381
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 338
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 348
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 160
          verb: get
        - requestCount: 11
          verb: watch
        requestCount: 171
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 161
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 116
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 126
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 118
          verb: patch
        requestCount: 118
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 96
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 101
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 4482
    - byUser:
      - byVerb:
        - requestCount: 22
          verb: create
        requestCount: 22
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 27
    - byUser:
      - byVerb:
        - requestCount: 245
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 254
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 204
          verb: get
        - requestCount: 4
          verb: watch
        requestCount: 208
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 173
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 178
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 167
          verb: get
        - requestCount: 4
          verb: watch
        requestCount: 171
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 109
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 119
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 24
          verb: delete
        - requestCount: 66
          verb: get
        requestCount: 90
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 43
          verb: create
        requestCount: 43
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 37
          verb: create
        requestCount: 37
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 6
          verb: create
        - requestCount: 6
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 18
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: 10.9.212.9
      requestCount: 1142
    requestCount: 5651
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2589
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 2597
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 340
          verb: create
        - requestCount: 724
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 1072
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 820
          verb: get
        requestCount: 820
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 100
          verb: delete
        - requestCount: 445
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 568
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 500
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 517
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 235
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 251
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 212
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 236
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 171
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 188
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 151
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6580
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 390
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 404
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 322
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 331
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 275
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 284
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 262
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 269
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 154
          verb: get
        requestCount: 210
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 171
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 185
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 99
          verb: create
        requestCount: 99
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 83
          verb: create
        requestCount: 83
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 37
          verb: create
        requestCount: 37
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 9
          verb: create
        - requestCount: 9
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 27
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1929
    requestCount: 8517
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2562
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 2570
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 316
          verb: create
        - requestCount: 664
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 987
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 792
          verb: get
        requestCount: 792
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 95
          verb: delete
        - requestCount: 429
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 548
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 498
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 514
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 234
          verb: get
        - requestCount: 19
          verb: watch
        requestCount: 253
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 233
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 184
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 170
          verb: patch
        requestCount: 170
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 153
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6404
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 388
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 404
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 320
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 327
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 266
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 274
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 257
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 266
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 60
          verb: delete
        - requestCount: 165
          verb: get
        requestCount: 225
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 187
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 82
          verb: create
        requestCount: 82
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 71
          verb: create
        requestCount: 71
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 25
          verb: create
        requestCount: 25
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 16
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 23
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: 10.9.212.9
      requestCount: 1884
    requestCount: 8297
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2514
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 2522
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 336
          verb: create
        - requestCount: 720
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 1064
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 807
          verb: get
        requestCount: 807
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 97
          verb: delete
        - requestCount: 435
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 555
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 456
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 473
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 232
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 248
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 230
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 186
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 153
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6418
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 387
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 403
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 320
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 328
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 270
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 278
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 260
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 268
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 52
          verb: delete
        - requestCount: 143
          verb: get
        requestCount: 195
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 186
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 74
          verb: create
        requestCount: 74
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 67
          verb: create
        requestCount: 67
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 27
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: create
        requestCount: 26
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.9
      requestCount: 1852
    requestCount: 8279
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2515
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 2525
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 319
          verb: create
        - requestCount: 670
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 996
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 820
          verb: get
        requestCount: 820
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 98
          verb: delete
        - requestCount: 438
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 560
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 498
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 514
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 233
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 248
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 231
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 187
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 152
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6413
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 386
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 402
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 320
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 328
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 268
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 276
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 259
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 267
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 154
          verb: get
        requestCount: 210
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 185
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 85
          verb: create
        requestCount: 85
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 80
          verb: create
        requestCount: 80
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 30
          verb: create
        requestCount: 30
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: 10.9.212.9
      requestCount: 1889
    requestCount: 8310
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2622
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 2631
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 334
          verb: create
        - requestCount: 712
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 1054
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 779
          verb: get
        requestCount: 779
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 94
          verb: delete
        - requestCount: 426
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 544
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 462
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 479
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 233
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 249
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 231
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 184
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 170
          verb: patch
        requestCount: 170
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 152
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6473
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 391
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 407
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 319
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 327
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 268
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 276
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 256
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 265
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 154
          verb: get
        requestCount: 210
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 187
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 93
          verb: create
        requestCount: 93
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 89
          verb: create
        requestCount: 89
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 35
          verb: create
        requestCount: 35
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 8
          verb: create
        - requestCount: 8
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 25
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1914
    requestCount: 8395
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2614
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 2623
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 330
          verb: create
        - requestCount: 708
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 1045
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 820
          verb: get
        requestCount: 820
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 97
          verb: delete
        - requestCount: 435
          verb: get
        - requestCount: 27
          verb: watch
        requestCount: 559
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 486
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 503
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 233
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 249
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 230
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 185
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 153
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6547
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 390
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 405
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 320
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 328
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 269
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 276
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 258
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 265
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 60
          verb: delete
        - requestCount: 165
          verb: get
        requestCount: 225
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 184
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 92
          verb: create
        requestCount: 92
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 72
          verb: create
        requestCount: 72
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 32
          verb: create
        requestCount: 32
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 9
          verb: create
        - requestCount: 9
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1905
    requestCount: 8461
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2539
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 2547
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 250
          verb: create
        - requestCount: 536
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 794
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 784
          verb: get
        requestCount: 784
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 98
          verb: delete
        - requestCount: 438
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 561
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 468
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 483
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 235
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 252
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 232
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 185
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 175
          verb: patch
        requestCount: 175
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 151
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6164
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 387
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 403
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 319
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 327
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 270
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 278
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 260
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 268
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 185
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 48
          verb: delete
        - requestCount: 132
          verb: get
        requestCount: 180
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 74
          verb: create
        requestCount: 74
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 69
          verb: create
        requestCount: 69
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: create
        requestCount: 26
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.9
      requestCount: 1836
    requestCount: 8008
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2695
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 2704
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 311
          verb: create
        - requestCount: 670
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 990
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 815
          verb: get
        requestCount: 815
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 97
          verb: delete
        - requestCount: 435
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 557
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 492
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 508
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 243
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 258
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 212
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 237
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 172
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 189
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 175
          verb: patch
        requestCount: 175
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 152
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6585
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 387
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 403
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 320
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 329
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 275
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 283
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 261
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 270
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 60
          verb: delete
        - requestCount: 165
          verb: get
        requestCount: 225
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 172
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 189
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 79
          verb: create
        requestCount: 79
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 77
          verb: create
        requestCount: 77
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 31
          verb: create
        requestCount: 31
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 11
          verb: create
        - requestCount: 11
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 30
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1916
    requestCount: 8508
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2620
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 2627
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 326
          verb: create
        - requestCount: 684
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 1017
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 818
          verb: get
        requestCount: 818
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 98
          verb: delete
        - requestCount: 438
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 560
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 474
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 491
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 236
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 252
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 233
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 185
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 152
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6515
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 388
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 402
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 320
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 327
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 272
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 280
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 258
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 265
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 52
          verb: delete
        - requestCount: 143
          verb: get
        requestCount: 195
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 186
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 93
          verb: create
        requestCount: 93
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 87
          verb: create
        requestCount: 87
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 35
          verb: create
        requestCount: 35
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 11
          verb: create
        - requestCount: 11
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 30
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1900
    requestCount: 8423
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2654
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 2662
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 301
          verb: create
        - requestCount: 638
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 948
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 781
          verb: get
        requestCount: 781
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 97
          verb: delete
        - requestCount: 435
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 557
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 486
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 502
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 235
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 251
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 231
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 186
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 170
          verb: patch
        requestCount: 170
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 152
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6440
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 388
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 402
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 319
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 326
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 275
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 283
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 260
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 268
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 154
          verb: get
        requestCount: 210
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 185
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 95
          verb: create
        requestCount: 95
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 78
          verb: create
        requestCount: 78
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 33
          verb: create
        requestCount: 33
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 25
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: 10.9.212.9
      requestCount: 1905
    requestCount: 8354
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2542
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 2550
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 305
          verb: create
        - requestCount: 642
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 955
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 820
          verb: get
        requestCount: 820
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 97
          verb: delete
        - requestCount: 438
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 560
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 488
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 505
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 233
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 251
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 233
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 173
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 190
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 146
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 154
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6398
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 385
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 402
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 322
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 331
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 275
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 283
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 263
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 270
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 154
          verb: get
        requestCount: 210
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 174
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 190
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 72
          verb: create
        requestCount: 72
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 69
          verb: create
        requestCount: 69
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 26
          verb: create
        requestCount: 26
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 7
          verb: create
        - requestCount: 7
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 23
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1876
    requestCount: 8281
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2566
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 2575
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 332
          verb: create
        - requestCount: 712
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 1052
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 779
          verb: get
        requestCount: 779
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 98
          verb: delete
        - requestCount: 438
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 560
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 476
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 490
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 235
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 250
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 206
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 229
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 185
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 152
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6452
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 387
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 402
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 320
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 329
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 274
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 283
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 258
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 266
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 52
          verb: delete
        - requestCount: 143
          verb: get
        requestCount: 195
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 185
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 76
          verb: create
        requestCount: 76
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 72
          verb: create
        requestCount: 72
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 28
          verb: create
        requestCount: 28
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 10
          verb: create
        - requestCount: 10
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 27
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1863
    requestCount: 8324
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2523
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 2531
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 322
          verb: create
        - requestCount: 676
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 1006
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 820
          verb: get
        requestCount: 820
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 97
          verb: delete
        - requestCount: 435
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 554
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 492
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 508
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 234
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 252
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 233
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 187
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 170
          verb: patch
        requestCount: 170
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 152
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6413
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 389
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 406
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 321
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 329
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 275
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 282
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 260
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 268
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 60
          verb: delete
        - requestCount: 165
          verb: get
        requestCount: 225
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 185
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 90
          verb: create
        requestCount: 90
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 86
          verb: create
        requestCount: 86
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 11
          verb: create
        - requestCount: 11
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 29
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 29
          verb: create
        requestCount: 29
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.9
      requestCount: 1929
    requestCount: 8349
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2592
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 2601
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 319
          verb: create
        - requestCount: 686
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 1013
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 807
          verb: get
        requestCount: 807
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 97
          verb: delete
        - requestCount: 435
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 556
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 498
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 514
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 233
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 249
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 230
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 185
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 152
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6487
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: create
        requestCount: 20
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 28
    - byUser:
      - byVerb:
        - requestCount: 387
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 404
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 318
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 327
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 273
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 281
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 260
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 268
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 154
          verb: get
        requestCount: 210
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 186
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 100
          verb: create
        requestCount: 100
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 83
          verb: create
        requestCount: 83
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 7
          verb: create
        - requestCount: 7
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 21
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1906
    requestCount: 8421
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1681
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 1687
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 233
          verb: create
        - requestCount: 486
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 724
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 523
          verb: get
        requestCount: 523
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 67
          verb: delete
        - requestCount: 298
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 381
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 338
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 348
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 160
          verb: get
        - requestCount: 11
          verb: watch
        requestCount: 171
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 161
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 116
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 126
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 118
          verb: patch
        requestCount: 118
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 96
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 101
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 4482
    - byUser:
      - byVerb:
        - requestCount: 22
          verb: create
        requestCount: 22
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 27
    - byUser:
      - byVerb:
        - requestCount: 245
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 254
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 204
          verb: get
        - requestCount: 4
          verb: watch
        requestCount: 208
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 173
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 178
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 167
          verb: get
        - requestCount: 4
          verb: watch
        requestCount: 171
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 109
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 119
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 24
          verb: delete
        - requestCount: 66
          verb: get
        requestCount: 90
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 43
          verb: create
        requestCount: 43
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 37
          verb: create
        requestCount: 37
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 6
          verb: create
        - requestCount: 6
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 18
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: 10.9.212.9
      requestCount: 1142
    requestCount: 5651
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2615
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 2622
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 306
          verb: create
        - requestCount: 648
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 962
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 795
          verb: get
        requestCount: 795
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 102
          verb: delete
        - requestCount: 450
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 578
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 514
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 530
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 252
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 270
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 218
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 240
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 175
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 191
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 153
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6521
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 388
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 404
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 319
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 327
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 280
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 287
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 260
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 269
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 154
          verb: get
        requestCount: 210
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 174
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 190
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 94
          verb: create
        requestCount: 94
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 75
          verb: create
        requestCount: 75
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 15
          verb: create
        - requestCount: 15
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 38
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 24
          verb: create
        requestCount: 24
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.9
      requestCount: 1918
    requestCount: 8446
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2653
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 2661
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 314
          verb: create
        - requestCount: 676
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 998
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 820
          verb: get
        requestCount: 820
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 99
          verb: delete
        - requestCount: 441
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 564
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 480
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 497
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 244
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 260
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 216
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 242
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 173
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 188
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 170
          verb: patch
        requestCount: 170
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 151
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6551
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 387
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 403
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 319
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 327
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 280
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 289
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 257
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 264
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 154
          verb: get
        requestCount: 210
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 174
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 191
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 74
          verb: create
        requestCount: 74
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 64
          verb: create
        requestCount: 64
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 25
          verb: create
        requestCount: 25
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.9
      requestCount: 1873
    requestCount: 8433
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2574
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 2582
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 323
          verb: create
        - requestCount: 682
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 1013
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 779
          verb: get
        requestCount: 779
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 100
          verb: delete
        - requestCount: 444
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 569
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 492
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 509
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 234
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 250
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 216
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 239
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 186
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 153
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6460
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 387
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 402
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 317
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 326
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 276
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 285
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 258
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 266
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 154
          verb: get
        requestCount: 210
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 174
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 189
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 76
          verb: create
        requestCount: 76
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 75
          verb: create
        requestCount: 75
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 26
          verb: create
        requestCount: 26
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: 10.9.212.9
      requestCount: 1881
    requestCount: 8349
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2550
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 2557
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 270
          verb: create
        - requestCount: 580
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 857
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 820
          verb: get
        requestCount: 820
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 96
          verb: delete
        - requestCount: 432
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 550
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 472
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 488
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 237
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 255
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 206
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 229
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 188
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 179
          verb: patch
        requestCount: 179
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 152
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6275
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 354
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 369
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 299
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 307
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 249
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 256
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 235
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 242
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 52
          verb: delete
        - requestCount: 143
          verb: get
        requestCount: 195
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 153
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 169
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 84
          verb: create
        requestCount: 84
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 79
          verb: create
        requestCount: 79
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 28
          verb: create
        requestCount: 28
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 15
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 21
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: 10.9.212.9
      requestCount: 1750
    requestCount: 8033
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2616
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 2624
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 283
          verb: create
        - requestCount: 602
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 893
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 797
          verb: get
        requestCount: 797
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 96
          verb: delete
        - requestCount: 433
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 555
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 494
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 509
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 235
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 251
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 233
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 184
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 171
          verb: patch
        requestCount: 171
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 151
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6368
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 388
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 403
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 320
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 329
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 270
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 278
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 259
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 267
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 60
          verb: delete
        - requestCount: 165
          verb: get
        requestCount: 225
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 185
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 98
          verb: create
        requestCount: 98
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 81
          verb: create
        requestCount: 81
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 35
          verb: create
        requestCount: 35
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 10
          verb: create
        - requestCount: 10
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 30
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1931
    requestCount: 8306
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2484
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 2492
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 286
          verb: create
        - requestCount: 604
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 898
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 802
          verb: get
        requestCount: 802
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 98
          verb: delete
        - requestCount: 438
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 560
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 472
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 488
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 232
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 247
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 233
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 186
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 153
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6239
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 390
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 406
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 317
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 324
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 268
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 277
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 256
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 264
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 154
          verb: get
        requestCount: 210
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 185
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 75
          verb: create
        requestCount: 75
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 68
          verb: create
        requestCount: 68
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 27
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 25
          verb: create
        requestCount: 25
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.9
      requestCount: 1861
    requestCount: 8108
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2583
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 2591
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 312
          verb: create
        - requestCount: 672
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 992
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 820
          verb: get
        requestCount: 820
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 97
          verb: delete
        - requestCount: 438
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 558
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 476
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 491
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 232
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 249
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 230
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 174
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 190
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 170
          verb: patch
        requestCount: 170
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 146
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 154
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6445
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 390
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 407
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 323
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 332
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 270
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 278
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 259
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 267
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 52
          verb: delete
        - requestCount: 143
          verb: get
        requestCount: 195
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 174
          verb: get
        - requestCount: 19
          verb: watch
        requestCount: 193
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 76
          verb: create
        requestCount: 76
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 70
          verb: create
        requestCount: 70
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 27
          verb: create
        requestCount: 27
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: 10.9.212.9
      requestCount: 1871
    requestCount: 8324
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2436
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 2445
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 311
          verb: create
        - requestCount: 650
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 969
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 779
          verb: get
        requestCount: 779
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 96
          verb: delete
        - requestCount: 432
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 552
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 492
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 509
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 237
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 252
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 234
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 185
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 180
          verb: patch
        requestCount: 180
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 152
        userAgent: csi-snapshot-controller-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller-operator
      nodeName: 10.9.212.10
      requestCount: 6257
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 389
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 405
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 318
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 327
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 268
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 277
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 255
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 263
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 154
          verb: get
        requestCount: 210
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 185
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 91
          verb: create
        requestCount: 91
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 81
          verb: create
        requestCount: 81
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 29
          verb: create
        requestCount: 29
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 8
          verb: create
        - requestCount: 8
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 24
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1892
    requestCount: 8158
  requestCount: 188735
