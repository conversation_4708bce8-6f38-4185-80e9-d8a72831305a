---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T15:01:10Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T15:01:10Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:40:39Z"
  name: machines.v1beta1.machine.openshift.io
  resourceVersion: "********"
  uid: dbe4ae2a-505c-4a6b-af88-2438a47d4997
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 6342
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 6348
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 6381
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: list
        requestCount: 18
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      nodeName: 10.9.212.8
      requestCount: 19
    - byUser:
      - byVerb:
        - requestCount: 144
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 149
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 21
          verb: create
        - requestCount: 4
          verb: watch
        requestCount: 25
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      nodeName: 10.9.212.9
      requestCount: 212
    requestCount: 6612
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9585
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9593
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 23
          verb: list
        requestCount: 23
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 9648
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 20
    - byUser:
      - byVerb:
        - requestCount: 216
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 223
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 27
          verb: create
        - requestCount: 7
          verb: watch
        requestCount: 34
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 15
          verb: list
        requestCount: 15
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      nodeName: 10.9.212.9
      requestCount: 316
    requestCount: 9984
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9585
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9593
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 21
          verb: list
        requestCount: 21
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.10
      requestCount: 9647
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 216
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 225
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 30
          verb: create
        - requestCount: 8
          verb: watch
        requestCount: 38
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 22
          verb: list
        requestCount: 22
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 329
    requestCount: 9993
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9602
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9610
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 22
          verb: list
        requestCount: 22
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 9666
    - byUser:
      - byVerb:
        - requestCount: 23
          verb: list
        requestCount: 23
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 23
    - byUser:
      - byVerb:
        - requestCount: 216
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 224
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 33
          verb: create
        - requestCount: 9
          verb: watch
        requestCount: 42
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 15
          verb: list
        requestCount: 15
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 329
    requestCount: 10018
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9600
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9608
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 15
          verb: list
        requestCount: 15
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      nodeName: 10.9.212.10
      requestCount: 9653
    - byUser:
      - byVerb:
        - requestCount: 24
          verb: list
        requestCount: 24
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 24
    - byUser:
      - byVerb:
        - requestCount: 216
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 224
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 21
          verb: create
        - requestCount: 7
          verb: watch
        requestCount: 28
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 21
          verb: list
        requestCount: 21
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      nodeName: 10.9.212.9
      requestCount: 315
    requestCount: 9992
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9598
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 9605
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 21
          verb: list
        requestCount: 21
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 9660
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 20
    - byUser:
      - byVerb:
        - requestCount: 216
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 225
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 30
          verb: create
        - requestCount: 8
          verb: watch
        requestCount: 38
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 19
          verb: list
        requestCount: 19
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 327
    requestCount: 10007
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9585
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9593
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.10
      requestCount: 9640
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 20
    - byUser:
      - byVerb:
        - requestCount: 252
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 261
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 30
          verb: create
        - requestCount: 8
          verb: watch
        requestCount: 38
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 23
          verb: list
        requestCount: 23
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 365
    requestCount: 10025
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9585
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9593
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 22
          verb: list
        requestCount: 22
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 9648
    - byUser:
      - byVerb:
        - requestCount: 22
          verb: list
        requestCount: 22
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 22
    - byUser:
      - byVerb:
        - requestCount: 216
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 224
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 30
          verb: create
        - requestCount: 8
          verb: watch
        requestCount: 38
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 16
          verb: list
        requestCount: 16
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 321
    requestCount: 9991
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9599
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9607
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 22
          verb: list
        requestCount: 22
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.10
      requestCount: 9664
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 20
    - byUser:
      - byVerb:
        - requestCount: 216
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 224
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 30
          verb: create
        - requestCount: 8
          verb: watch
        requestCount: 38
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 18
          verb: list
        requestCount: 18
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 323
    requestCount: 10007
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9605
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9613
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 22
          verb: list
        requestCount: 22
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 9666
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 20
    - byUser:
      - byVerb:
        - requestCount: 216
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 224
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 33
          verb: create
        - requestCount: 8
          verb: watch
        requestCount: 41
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 18
          verb: list
        requestCount: 18
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 329
    requestCount: 10015
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9593
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9601
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      nodeName: 10.9.212.10
      requestCount: 9652
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: list
        requestCount: 18
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 18
    - byUser:
      - byVerb:
        - requestCount: 216
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 224
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 30
          verb: create
        - requestCount: 8
          verb: watch
        requestCount: 38
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 24
          verb: list
        requestCount: 24
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: machine-approver/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      nodeName: 10.9.212.9
      requestCount: 332
    requestCount: 10002
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9585
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 9592
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 16
          verb: list
        requestCount: 16
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 9642
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 20
    - byUser:
      - byVerb:
        - requestCount: 216
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 223
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 27
          verb: create
        - requestCount: 8
          verb: watch
        requestCount: 35
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 24
          verb: list
        requestCount: 24
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 324
    requestCount: 9986
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9585
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 9594
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      nodeName: 10.9.212.10
      requestCount: 9643
    - byUser:
      - byVerb:
        - requestCount: 19
          verb: list
        requestCount: 19
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 19
    - byUser:
      - byVerb:
        - requestCount: 252
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 261
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 30
          verb: create
        - requestCount: 8
          verb: watch
        requestCount: 38
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 24
          verb: list
        requestCount: 24
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 368
    requestCount: 10030
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9585
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9593
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 23
          verb: list
        requestCount: 23
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 9649
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 20
    - byUser:
      - byVerb:
        - requestCount: 216
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 224
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 30
          verb: create
        - requestCount: 8
          verb: watch
        requestCount: 38
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 322
    requestCount: 9991
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9603
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 9612
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      nodeName: 10.9.212.10
      requestCount: 9664
    - byUser:
      - byVerb:
        - requestCount: 22
          verb: list
        requestCount: 22
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 22
    - byUser:
      - byVerb:
        - requestCount: 216
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 223
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 30
          verb: create
        - requestCount: 9
          verb: watch
        requestCount: 39
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 18
          verb: list
        requestCount: 18
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 326
    requestCount: 10012
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 6342
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 6348
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 6381
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: list
        requestCount: 18
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      nodeName: 10.9.212.8
      requestCount: 19
    - byUser:
      - byVerb:
        - requestCount: 144
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 149
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 21
          verb: create
        - requestCount: 4
          verb: watch
        requestCount: 25
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 11
          verb: list
        requestCount: 11
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      nodeName: 10.9.212.9
      requestCount: 212
    requestCount: 6612
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9600
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9608
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 22
          verb: list
        requestCount: 22
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 9665
    - byUser:
      - byVerb:
        - requestCount: 21
          verb: list
        requestCount: 21
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 21
    - byUser:
      - byVerb:
        - requestCount: 252
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 259
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 30
          verb: create
        - requestCount: 7
          verb: watch
        requestCount: 37
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 360
    requestCount: 10046
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9591
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 9598
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      nodeName: 10.9.212.10
      requestCount: 9648
    - byUser:
      - byVerb:
        - requestCount: 21
          verb: list
        requestCount: 21
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 22
    - byUser:
      - byVerb:
        - requestCount: 216
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 223
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 30
          verb: create
        - requestCount: 9
          verb: watch
        requestCount: 39
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 19
          verb: list
        requestCount: 19
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.9
      requestCount: 328
    requestCount: 9998
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9585
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 9594
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      - byVerb:
        - requestCount: 1
          verb: watch
        requestCount: 1
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.10
      requestCount: 9648
    - byUser:
      - byVerb:
        - requestCount: 24
          verb: list
        requestCount: 24
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 25
    - byUser:
      - byVerb:
        - requestCount: 216
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 225
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 30
          verb: create
        - requestCount: 8
          verb: watch
        requestCount: 38
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 16
          verb: list
        requestCount: 16
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 324
    requestCount: 9997
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9585
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 9592
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      nodeName: 10.9.212.10
      requestCount: 9645
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 216
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 223
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 30
          verb: create
        - requestCount: 8
          verb: watch
        requestCount: 38
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 23
          verb: list
        requestCount: 23
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 328
    requestCount: 9990
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9595
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 9602
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 21
          verb: list
        requestCount: 21
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 9655
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: list
        requestCount: 15
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 216
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 224
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 30
          verb: create
        - requestCount: 8
          verb: watch
        requestCount: 38
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 24
          verb: list
        requestCount: 24
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: machine-approver/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      nodeName: 10.9.212.9
      requestCount: 332
    requestCount: 10002
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9594
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9602
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 23
          verb: list
        requestCount: 23
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      nodeName: 10.9.212.10
      requestCount: 9659
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: list
        requestCount: 17
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 216
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 224
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 30
          verb: create
        - requestCount: 8
          verb: watch
        requestCount: 38
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      nodeName: 10.9.212.9
      requestCount: 327
    requestCount: 10003
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9594
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 9603
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 18
          verb: list
        requestCount: 18
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 9656
    - byUser:
      - byVerb:
        - requestCount: 22
          verb: list
        requestCount: 22
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 22
    - byUser:
      - byVerb:
        - requestCount: 216
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 224
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 27
          verb: create
        - requestCount: 7
          verb: watch
        requestCount: 34
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 320
    requestCount: 9998
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9599
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9607
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 20
          verb: list
        requestCount: 20
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: machine-healthcheck/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: nodelink-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: aro/v0.0.0
        username: system:serviceaccount:openshift-azure-operator:aro-operator-master
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: machine-api-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      nodeName: 10.9.212.10
      requestCount: 9661
    - byUser:
      - byVerb:
        - requestCount: 21
          verb: list
        requestCount: 21
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 21
    - byUser:
      - byVerb:
        - requestCount: 252
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 259
        userAgent: machine-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 30
          verb: create
        - requestCount: 9
          verb: watch
        requestCount: 39
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-machine-api:control-plane-machine-set-operator
      - byVerb:
        - requestCount: 19
          verb: list
        requestCount: 19
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: machineset-controller/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 360
    requestCount: 10042
  requestCount: 226741
