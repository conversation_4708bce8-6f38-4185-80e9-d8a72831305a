---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T15:09:08Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T15:09:08Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:40:20Z"
  name: volumesnapshotclasses.v1.snapshot.storage.k8s.io
  resourceVersion: "********"
  uid: ********-61da-4ffe-960b-4f7bb6e49b30
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 35
          verb: get
        requestCount: 35
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      nodeName: 10.9.212.10
      requestCount: 48
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 38
          verb: get
        - requestCount: 38
          verb: update
        requestCount: 76
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 99
    requestCount: 147
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      nodeName: 10.9.212.10
      requestCount: 83
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 156
    requestCount: 239
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 84
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 155
    requestCount: 239
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      nodeName: 10.9.212.10
      requestCount: 85
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 155
    requestCount: 240
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      nodeName: 10.9.212.10
      requestCount: 85
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 156
    requestCount: 241
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      nodeName: 10.9.212.10
      requestCount: 83
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 156
    requestCount: 239
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 85
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 155
    requestCount: 240
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      nodeName: 10.9.212.10
      requestCount: 84
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      nodeName: 10.9.212.9
      requestCount: 157
    requestCount: 241
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      nodeName: 10.9.212.10
      requestCount: 84
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 156
    requestCount: 240
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      nodeName: 10.9.212.10
      requestCount: 83
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 154
    requestCount: 237
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      nodeName: 10.9.212.10
      requestCount: 83
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 157
    requestCount: 240
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 61
          verb: get
        requestCount: 61
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 86
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 62
          verb: get
        - requestCount: 62
          verb: update
        requestCount: 124
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      nodeName: 10.9.212.9
      requestCount: 160
    requestCount: 246
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      nodeName: 10.9.212.10
      requestCount: 84
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 155
    requestCount: 239
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      nodeName: 10.9.212.10
      requestCount: 84
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      nodeName: 10.9.212.9
      requestCount: 158
    requestCount: 242
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      nodeName: 10.9.212.10
      requestCount: 83
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      nodeName: 10.9.212.9
      requestCount: 156
    requestCount: 239
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 35
          verb: get
        requestCount: 35
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      nodeName: 10.9.212.10
      requestCount: 48
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 38
          verb: get
        - requestCount: 38
          verb: update
        requestCount: 76
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 99
    requestCount: 147
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      nodeName: 10.9.212.10
      requestCount: 86
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 156
    requestCount: 242
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 84
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      nodeName: 10.9.212.9
      requestCount: 155
    requestCount: 239
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      nodeName: 10.9.212.10
      requestCount: 83
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 157
    requestCount: 240
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      nodeName: 10.9.212.10
      requestCount: 85
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 155
    requestCount: 240
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 84
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 156
    requestCount: 240
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 85
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 156
    requestCount: 241
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 62
          verb: get
        requestCount: 62
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 84
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 61
          verb: get
        - requestCount: 61
          verb: update
        requestCount: 122
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 159
    requestCount: 243
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: snapshot-controller/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      nodeName: 10.9.212.10
      requestCount: 86
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 60
          verb: update
        requestCount: 120
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-snapshotter/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: snapshot-validation-webhook/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:csi-snapshot-webhook
      nodeName: 10.9.212.9
      requestCount: 156
    requestCount: 242
  requestCount: 5436
