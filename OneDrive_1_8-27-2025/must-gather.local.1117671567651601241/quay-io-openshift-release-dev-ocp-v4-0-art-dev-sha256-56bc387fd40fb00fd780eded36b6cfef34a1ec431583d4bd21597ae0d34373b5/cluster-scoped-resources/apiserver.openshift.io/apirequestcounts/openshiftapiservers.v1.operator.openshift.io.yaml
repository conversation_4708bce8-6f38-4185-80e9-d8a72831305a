---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T14:59:33Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:59:33Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:41:35Z"
  name: openshiftapiservers.v1.operator.openshift.io
  resourceVersion: "********"
  uid: 3b80bec8-52a2-42aa-922d-d742821f843c
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 258
          verb: get
        - requestCount: 11
          verb: watch
        requestCount: 269
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 13
          verb: get
        requestCount: 13
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 288
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 14
    requestCount: 302
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 386
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 402
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 432
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 452
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 389
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 406
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 434
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 19
    requestCount: 453
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 390
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 406
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 434
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 454
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 394
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 411
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 438
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 458
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 386
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 403
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 431
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 451
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 388
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 405
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 433
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 453
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 390
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 407
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 434
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 21
    requestCount: 455
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 395
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 410
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 437
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 457
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 383
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 398
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 428
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 448
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 390
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 406
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 434
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 19
    requestCount: 453
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 389
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 404
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 433
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 19
    requestCount: 452
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 392
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 409
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 435
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 455
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 389
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 405
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 435
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 455
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 388
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 404
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 432
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 19
    requestCount: 451
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 258
          verb: get
        - requestCount: 11
          verb: watch
        requestCount: 269
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 13
          verb: get
        requestCount: 13
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 288
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 14
    requestCount: 302
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 388
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 403
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 431
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 451
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 381
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 397
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 425
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 19
    requestCount: 444
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 394
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 411
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 438
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 458
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 387
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 402
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 429
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 21
    requestCount: 450
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 386
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 402
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 432
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 21
    requestCount: 453
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 386
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 402
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 428
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 19
    requestCount: 447
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 388
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 405
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 1
          verb: get
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 433
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 453
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 388
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 405
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 432
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 452
  requestCount: 10257
