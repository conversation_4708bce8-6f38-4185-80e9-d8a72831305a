---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-07-01T15:33:35Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-07-01T15:33:35Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:38:09Z"
  name: pods.v1beta1.metrics.k8s.io
  resourceVersion: "********"
  uid: f16513b1-2e11-42d0-b1a8-dca71359b017
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 151
          verb: list
        requestCount: 151
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 151
    requestCount: 151
  last24h:
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 239
          verb: list
        requestCount: 239
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 239
    requestCount: 239
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 240
          verb: list
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 240
    requestCount: 240
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 240
          verb: list
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 240
    requestCount: 240
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 239
          verb: list
        requestCount: 239
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 239
    requestCount: 239
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 240
          verb: list
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 240
    requestCount: 240
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 240
          verb: list
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 240
    requestCount: 240
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 240
          verb: list
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 240
    requestCount: 240
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 239
          verb: list
        requestCount: 239
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 239
    requestCount: 239
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 240
          verb: list
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 240
    requestCount: 240
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 240
          verb: list
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 240
    requestCount: 240
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 239
          verb: list
        requestCount: 239
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 239
    requestCount: 239
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 240
          verb: list
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 240
    requestCount: 240
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 240
          verb: list
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 240
    requestCount: 240
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 239
          verb: list
        requestCount: 239
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 239
    requestCount: 239
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 151
          verb: list
        requestCount: 151
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 151
    requestCount: 151
  - requestCount: 0
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 240
          verb: list
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 240
    requestCount: 240
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 229
          verb: list
        requestCount: 229
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 229
    requestCount: 229
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 240
          verb: list
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 240
    requestCount: 240
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 240
          verb: list
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 240
    requestCount: 240
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 240
          verb: list
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 240
    requestCount: 240
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 239
          verb: list
        requestCount: 239
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 239
    requestCount: 239
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 240
          verb: list
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 240
    requestCount: 240
  - byNode:
    - nodeName: 10.9.212.10
      requestCount: 0
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 240
          verb: list
        requestCount: 240
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:horizontal-pod-autoscaler
      nodeName: 10.9.212.9
      requestCount: 240
    requestCount: 240
  requestCount: 5414
