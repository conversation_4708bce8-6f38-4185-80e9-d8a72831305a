---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T14:58:20Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:58:20Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:41:13Z"
  name: profiles.v1.tuned.openshift.io
  resourceVersion: "********"
  uid: ********-6e94-474a-9c25-b86e9c9adcbe
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 41
          verb: watch
        requestCount: 41
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 57
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 40
    requestCount: 97
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 62
          verb: watch
        requestCount: 62
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 86
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 59
    requestCount: 145
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 63
          verb: watch
        requestCount: 63
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 86
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 60
    requestCount: 146
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 63
          verb: watch
        requestCount: 63
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 88
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 59
    requestCount: 147
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 89
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 61
    requestCount: 150
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 89
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 63
    requestCount: 152
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 68
          verb: watch
        requestCount: 68
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 90
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 60
    requestCount: 150
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 66
          verb: watch
        requestCount: 66
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 90
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 60
    requestCount: 150
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 67
          verb: watch
        requestCount: 67
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 89
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 62
    requestCount: 151
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 26
          verb: watch
        requestCount: 26
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 86
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 58
    requestCount: 144
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 88
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 57
    requestCount: 145
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 88
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 56
    requestCount: 144
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 66
          verb: watch
        requestCount: 66
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 26
          verb: watch
        requestCount: 26
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 92
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 61
    requestCount: 153
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 62
          verb: watch
        requestCount: 62
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 86
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 62
    requestCount: 148
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 66
          verb: watch
        requestCount: 66
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 91
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 59
    requestCount: 150
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 41
          verb: watch
        requestCount: 41
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 57
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 40
    requestCount: 97
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 66
          verb: watch
        requestCount: 66
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 90
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 59
    requestCount: 149
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 63
          verb: watch
        requestCount: 63
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 87
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 66
    requestCount: 153
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 59
          verb: watch
        requestCount: 59
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 83
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 60
    requestCount: 143
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 88
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 35
          verb: watch
        requestCount: 35
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 63
    requestCount: 151
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: watch
        requestCount: 64
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 27
          verb: watch
        requestCount: 27
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 91
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 63
    requestCount: 154
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 88
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 60
    requestCount: 148
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 64
          verb: watch
        requestCount: 64
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 88
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 61
    requestCount: 149
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 65
          verb: watch
        requestCount: 65
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:cluster-node-tuning-operator
      nodeName: 10.9.212.10
      requestCount: 87
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: cluster-node-tuning-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-node-tuning-operator:tuned
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 60
    requestCount: 147
  requestCount: 3366
