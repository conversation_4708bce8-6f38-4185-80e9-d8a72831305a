---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T15:09:36Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T15:09:36Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:41:35Z"
  name: tokenreviews.v1.authentication.k8s.io
  resourceVersion: "********"
  uid: 0d1000f0-b2fa-43c0-8681-b57c4ab76a32
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 418
          verb: create
        requestCount: 418
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 301
          verb: create
        requestCount: 301
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 253
          verb: create
        requestCount: 253
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 140
          verb: create
        requestCount: 140
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 140
          verb: create
        requestCount: 140
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 129
          verb: create
        requestCount: 129
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 104
          verb: create
        requestCount: 104
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 98
          verb: create
        requestCount: 98
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 91
          verb: create
        requestCount: 91
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 78
          verb: create
        requestCount: 78
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      nodeName: 10.9.212.10
      requestCount: 1864
    - byUser:
      - byVerb:
        - requestCount: 292
          verb: create
        requestCount: 292
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 199
          verb: create
        requestCount: 199
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 172
          verb: create
        requestCount: 172
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 143
          verb: create
        requestCount: 143
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 132
          verb: create
        requestCount: 132
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 124
          verb: create
        requestCount: 124
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 80
          verb: create
        requestCount: 80
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 69
          verb: create
        requestCount: 69
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 33
          verb: create
        requestCount: 33
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 30
          verb: create
        requestCount: 30
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.8
      requestCount: 1287
    - byUser:
      - byVerb:
        - requestCount: 232
          verb: create
        requestCount: 232
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 189
          verb: create
        requestCount: 189
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 137
          verb: create
        requestCount: 137
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 109
          verb: create
        requestCount: 109
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 78
          verb: create
        requestCount: 78
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 76
          verb: create
        requestCount: 76
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 75
          verb: create
        requestCount: 75
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 71
          verb: create
        requestCount: 71
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 68
          verb: create
        requestCount: 68
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 30
          verb: create
        requestCount: 30
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      nodeName: 10.9.212.9
      requestCount: 1082
    requestCount: 4233
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 520
          verb: create
        requestCount: 520
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 398
          verb: create
        requestCount: 398
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 247
          verb: create
        requestCount: 247
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 246
          verb: create
        requestCount: 246
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 244
          verb: create
        requestCount: 244
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 238
          verb: create
        requestCount: 238
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 232
          verb: create
        requestCount: 232
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 190
          verb: create
        requestCount: 190
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 3275
    - byUser:
      - byVerb:
        - requestCount: 396
          verb: create
        requestCount: 396
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 209
          verb: create
        requestCount: 209
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 195
          verb: create
        requestCount: 195
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 178
          verb: create
        requestCount: 178
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 158
          verb: create
        requestCount: 158
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 142
          verb: create
        requestCount: 142
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 139
          verb: create
        requestCount: 139
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 57
          verb: create
        requestCount: 57
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 36
          verb: create
        requestCount: 36
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 36
          verb: create
        requestCount: 36
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cloud-credential-operator:cloud-credential-operator
      nodeName: 10.9.212.8
      requestCount: 1546
    - byUser:
      - byVerb:
        - requestCount: 382
          verb: create
        requestCount: 382
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 262
          verb: create
        requestCount: 262
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 202
          verb: create
        requestCount: 202
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 158
          verb: create
        requestCount: 158
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 150
          verb: create
        requestCount: 150
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 133
          verb: create
        requestCount: 133
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 120
          verb: create
        requestCount: 120
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 94
          verb: create
        requestCount: 94
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 65
          verb: create
        requestCount: 65
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      nodeName: 10.9.212.9
      requestCount: 1687
    requestCount: 6508
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 516
          verb: create
        requestCount: 516
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 513
          verb: create
        requestCount: 513
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 292
          verb: create
        requestCount: 292
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 242
          verb: create
        requestCount: 242
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 238
          verb: create
        requestCount: 238
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 235
          verb: create
        requestCount: 235
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 231
          verb: create
        requestCount: 231
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 204
          verb: create
        requestCount: 204
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 189
          verb: create
        requestCount: 189
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 3380
    - byUser:
      - byVerb:
        - requestCount: 344
          verb: create
        requestCount: 344
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 220
          verb: create
        requestCount: 220
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 182
          verb: create
        requestCount: 182
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 172
          verb: create
        requestCount: 172
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 154
          verb: create
        requestCount: 154
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 137
          verb: create
        requestCount: 137
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 133
          verb: create
        requestCount: 133
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 28
          verb: create
        requestCount: 28
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 25
          verb: create
        requestCount: 25
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 24
          verb: create
        requestCount: 24
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cloud-credential-operator:cloud-credential-operator
      nodeName: 10.9.212.8
      requestCount: 1419
    - byUser:
      - byVerb:
        - requestCount: 319
          verb: create
        requestCount: 319
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 213
          verb: create
        requestCount: 213
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 184
          verb: create
        requestCount: 184
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 181
          verb: create
        requestCount: 181
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 175
          verb: create
        requestCount: 175
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 154
          verb: create
        requestCount: 154
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 147
          verb: create
        requestCount: 147
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 84
          verb: create
        requestCount: 84
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 1699
    requestCount: 6498
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 508
          verb: create
        requestCount: 508
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 439
          verb: create
        requestCount: 439
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 266
          verb: create
        requestCount: 266
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 243
          verb: create
        requestCount: 243
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 231
          verb: create
        requestCount: 231
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 208
          verb: create
        requestCount: 208
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 198
          verb: create
        requestCount: 198
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 198
          verb: create
        requestCount: 198
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 192
          verb: create
        requestCount: 192
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 3203
    - byUser:
      - byVerb:
        - requestCount: 370
          verb: create
        requestCount: 370
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 208
          verb: create
        requestCount: 208
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 204
          verb: create
        requestCount: 204
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 190
          verb: create
        requestCount: 190
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 171
          verb: create
        requestCount: 171
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 150
          verb: create
        requestCount: 150
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 146
          verb: create
        requestCount: 146
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 40
          verb: create
        requestCount: 40
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 30
          verb: create
        requestCount: 30
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      - byVerb:
        - requestCount: 26
          verb: create
        requestCount: 26
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      nodeName: 10.9.212.8
      requestCount: 1535
    - byUser:
      - byVerb:
        - requestCount: 391
          verb: create
        requestCount: 391
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 211
          verb: create
        requestCount: 211
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 202
          verb: create
        requestCount: 202
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 198
          verb: create
        requestCount: 198
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 184
          verb: create
        requestCount: 184
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 157
          verb: create
        requestCount: 157
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 156
          verb: create
        requestCount: 156
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 50
          verb: create
        requestCount: 50
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 1791
    requestCount: 6529
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 508
          verb: create
        requestCount: 508
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 458
          verb: create
        requestCount: 458
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 264
          verb: create
        requestCount: 264
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 253
          verb: create
        requestCount: 253
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 212
          verb: create
        requestCount: 212
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 210
          verb: create
        requestCount: 210
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 197
          verb: create
        requestCount: 197
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 186
          verb: create
        requestCount: 186
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      nodeName: 10.9.212.10
      requestCount: 3248
    - byUser:
      - byVerb:
        - requestCount: 394
          verb: create
        requestCount: 394
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 253
          verb: create
        requestCount: 253
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 192
          verb: create
        requestCount: 192
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 177
          verb: create
        requestCount: 177
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 172
          verb: create
        requestCount: 172
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 155
          verb: create
        requestCount: 155
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 155
          verb: create
        requestCount: 155
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 68
          verb: create
        requestCount: 68
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 43
          verb: create
        requestCount: 43
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 29
          verb: create
        requestCount: 29
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      nodeName: 10.9.212.8
      requestCount: 1638
    - byUser:
      - byVerb:
        - requestCount: 348
          verb: create
        requestCount: 348
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 234
          verb: create
        requestCount: 234
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 196
          verb: create
        requestCount: 196
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 181
          verb: create
        requestCount: 181
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 170
          verb: create
        requestCount: 170
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 135
          verb: create
        requestCount: 135
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 131
          verb: create
        requestCount: 131
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 120
          verb: create
        requestCount: 120
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 120
          verb: create
        requestCount: 120
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 50
          verb: create
        requestCount: 50
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      nodeName: 10.9.212.9
      requestCount: 1685
    requestCount: 6571
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 508
          verb: create
        requestCount: 508
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 430
          verb: create
        requestCount: 430
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 278
          verb: create
        requestCount: 278
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 243
          verb: create
        requestCount: 243
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 201
          verb: create
        requestCount: 201
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 197
          verb: create
        requestCount: 197
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 188
          verb: create
        requestCount: 188
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 170
          verb: create
        requestCount: 170
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 158
          verb: create
        requestCount: 158
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      nodeName: 10.9.212.10
      requestCount: 3093
    - byUser:
      - byVerb:
        - requestCount: 400
          verb: create
        requestCount: 400
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 191
          verb: create
        requestCount: 191
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 186
          verb: create
        requestCount: 186
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 181
          verb: create
        requestCount: 181
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 173
          verb: create
        requestCount: 173
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 158
          verb: create
        requestCount: 158
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 53
          verb: create
        requestCount: 53
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 40
          verb: create
        requestCount: 40
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 40
          verb: create
        requestCount: 40
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      nodeName: 10.9.212.8
      requestCount: 1662
    - byUser:
      - byVerb:
        - requestCount: 346
          verb: create
        requestCount: 346
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 235
          verb: create
        requestCount: 235
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 209
          verb: create
        requestCount: 209
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 206
          verb: create
        requestCount: 206
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 177
          verb: create
        requestCount: 177
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 147
          verb: create
        requestCount: 147
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 135
          verb: create
        requestCount: 135
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 122
          verb: create
        requestCount: 122
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 122
          verb: create
        requestCount: 122
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 56
          verb: create
        requestCount: 56
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 1755
    requestCount: 6510
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 508
          verb: create
        requestCount: 508
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 475
          verb: create
        requestCount: 475
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 259
          verb: create
        requestCount: 259
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 253
          verb: create
        requestCount: 253
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 242
          verb: create
        requestCount: 242
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 188
          verb: create
        requestCount: 188
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 188
          verb: create
        requestCount: 188
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 183
          verb: create
        requestCount: 183
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 162
          verb: create
        requestCount: 162
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 3178
    - byUser:
      - byVerb:
        - requestCount: 397
          verb: create
        requestCount: 397
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 308
          verb: create
        requestCount: 308
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 233
          verb: create
        requestCount: 233
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 173
          verb: create
        requestCount: 173
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 166
          verb: create
        requestCount: 166
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 150
          verb: create
        requestCount: 150
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 127
          verb: create
        requestCount: 127
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 76
          verb: create
        requestCount: 76
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:multus-ac
      - byVerb:
        - requestCount: 41
          verb: create
        requestCount: 41
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 22
          verb: create
        requestCount: 22
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      nodeName: 10.9.212.8
      requestCount: 1693
    - byUser:
      - byVerb:
        - requestCount: 308
          verb: create
        requestCount: 308
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 234
          verb: create
        requestCount: 234
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 180
          verb: create
        requestCount: 180
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 178
          verb: create
        requestCount: 178
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 167
          verb: create
        requestCount: 167
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 164
          verb: create
        requestCount: 164
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 120
          verb: create
        requestCount: 120
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 120
          verb: create
        requestCount: 120
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 117
          verb: create
        requestCount: 117
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 80
          verb: create
        requestCount: 80
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 1668
    requestCount: 6539
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 559
          verb: create
        requestCount: 559
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 508
          verb: create
        requestCount: 508
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 256
          verb: create
        requestCount: 256
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 233
          verb: create
        requestCount: 233
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 219
          verb: create
        requestCount: 219
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 211
          verb: create
        requestCount: 211
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 206
          verb: create
        requestCount: 206
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 174
          verb: create
        requestCount: 174
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      nodeName: 10.9.212.10
      requestCount: 3326
    - byUser:
      - byVerb:
        - requestCount: 327
          verb: create
        requestCount: 327
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 270
          verb: create
        requestCount: 270
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 209
          verb: create
        requestCount: 209
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 178
          verb: create
        requestCount: 178
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 165
          verb: create
        requestCount: 165
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 132
          verb: create
        requestCount: 132
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 132
          verb: create
        requestCount: 132
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 53
          verb: create
        requestCount: 53
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 48
          verb: create
        requestCount: 48
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:multus-ac
      - byVerb:
        - requestCount: 41
          verb: create
        requestCount: 41
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.8
      requestCount: 1555
    - byUser:
      - byVerb:
        - requestCount: 309
          verb: create
        requestCount: 309
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 200
          verb: create
        requestCount: 200
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 183
          verb: create
        requestCount: 183
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 179
          verb: create
        requestCount: 179
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 157
          verb: create
        requestCount: 157
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 133
          verb: create
        requestCount: 133
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 120
          verb: create
        requestCount: 120
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 120
          verb: create
        requestCount: 120
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 82
          verb: create
        requestCount: 82
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 1723
    requestCount: 6604
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 508
          verb: create
        requestCount: 508
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 465
          verb: create
        requestCount: 465
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 278
          verb: create
        requestCount: 278
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 242
          verb: create
        requestCount: 242
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 229
          verb: create
        requestCount: 229
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 228
          verb: create
        requestCount: 228
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 197
          verb: create
        requestCount: 197
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 195
          verb: create
        requestCount: 195
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 169
          verb: create
        requestCount: 169
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 3231
    - byUser:
      - byVerb:
        - requestCount: 340
          verb: create
        requestCount: 340
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 215
          verb: create
        requestCount: 215
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 194
          verb: create
        requestCount: 194
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 179
          verb: create
        requestCount: 179
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 172
          verb: create
        requestCount: 172
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 154
          verb: create
        requestCount: 154
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 131
          verb: create
        requestCount: 131
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 39
          verb: create
        requestCount: 39
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 35
          verb: create
        requestCount: 35
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      - byVerb:
        - requestCount: 33
          verb: create
        requestCount: 33
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      nodeName: 10.9.212.8
      requestCount: 1492
    - byUser:
      - byVerb:
        - requestCount: 371
          verb: create
        requestCount: 371
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 229
          verb: create
        requestCount: 229
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 221
          verb: create
        requestCount: 221
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 190
          verb: create
        requestCount: 190
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 165
          verb: create
        requestCount: 165
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 139
          verb: create
        requestCount: 139
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 131
          verb: create
        requestCount: 131
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 120
          verb: create
        requestCount: 120
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 120
          verb: create
        requestCount: 120
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 49
          verb: create
        requestCount: 49
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.9
      requestCount: 1735
    requestCount: 6458
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 512
          verb: create
        requestCount: 512
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 427
          verb: create
        requestCount: 427
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 362
          verb: create
        requestCount: 362
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 302
          verb: create
        requestCount: 302
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 279
          verb: create
        requestCount: 279
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 278
          verb: create
        requestCount: 278
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 241
          verb: create
        requestCount: 241
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 214
          verb: create
        requestCount: 214
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 186
          verb: create
        requestCount: 186
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 3521
    - byUser:
      - byVerb:
        - requestCount: 452
          verb: create
        requestCount: 452
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 154
          verb: create
        requestCount: 154
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 147
          verb: create
        requestCount: 147
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 145
          verb: create
        requestCount: 145
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 124
          verb: create
        requestCount: 124
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 108
          verb: create
        requestCount: 108
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 103
          verb: create
        requestCount: 103
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 38
          verb: create
        requestCount: 38
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 28
          verb: create
        requestCount: 28
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 18
          verb: create
        requestCount: 18
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns-operator:dns-operator
      nodeName: 10.9.212.8
      requestCount: 1317
    - byUser:
      - byVerb:
        - requestCount: 313
          verb: create
        requestCount: 313
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 231
          verb: create
        requestCount: 231
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 199
          verb: create
        requestCount: 199
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 190
          verb: create
        requestCount: 190
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 158
          verb: create
        requestCount: 158
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 143
          verb: create
        requestCount: 143
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 116
          verb: create
        requestCount: 116
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 116
          verb: create
        requestCount: 116
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 113
          verb: create
        requestCount: 113
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 50
          verb: create
        requestCount: 50
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.9
      requestCount: 1629
    requestCount: 6467
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 512
          verb: create
        requestCount: 512
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 475
          verb: create
        requestCount: 475
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 291
          verb: create
        requestCount: 291
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 262
          verb: create
        requestCount: 262
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 258
          verb: create
        requestCount: 258
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 225
          verb: create
        requestCount: 225
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 223
          verb: create
        requestCount: 223
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 144
          verb: create
        requestCount: 144
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 3350
    - byUser:
      - byVerb:
        - requestCount: 363
          verb: create
        requestCount: 363
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 232
          verb: create
        requestCount: 232
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 181
          verb: create
        requestCount: 181
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 172
          verb: create
        requestCount: 172
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 157
          verb: create
        requestCount: 157
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 151
          verb: create
        requestCount: 151
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 118
          verb: create
        requestCount: 118
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 50
          verb: create
        requestCount: 50
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 44
          verb: create
        requestCount: 44
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      - byVerb:
        - requestCount: 28
          verb: create
        requestCount: 28
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:multus-ac
      nodeName: 10.9.212.8
      requestCount: 1496
    - byUser:
      - byVerb:
        - requestCount: 333
          verb: create
        requestCount: 333
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 212
          verb: create
        requestCount: 212
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 187
          verb: create
        requestCount: 187
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 186
          verb: create
        requestCount: 186
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 170
          verb: create
        requestCount: 170
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 143
          verb: create
        requestCount: 143
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 120
          verb: create
        requestCount: 120
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 93
          verb: create
        requestCount: 93
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 74
          verb: create
        requestCount: 74
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 1639
    requestCount: 6485
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 529
          verb: create
        requestCount: 529
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 512
          verb: create
        requestCount: 512
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 273
          verb: create
        requestCount: 273
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 267
          verb: create
        requestCount: 267
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 254
          verb: create
        requestCount: 254
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 242
          verb: create
        requestCount: 242
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 236
          verb: create
        requestCount: 236
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 231
          verb: create
        requestCount: 231
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 195
          verb: create
        requestCount: 195
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 3459
    - byUser:
      - byVerb:
        - requestCount: 318
          verb: create
        requestCount: 318
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 285
          verb: create
        requestCount: 285
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 218
          verb: create
        requestCount: 218
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 175
          verb: create
        requestCount: 175
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 170
          verb: create
        requestCount: 170
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 170
          verb: create
        requestCount: 170
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 109
          verb: create
        requestCount: 109
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 48
          verb: create
        requestCount: 48
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      - byVerb:
        - requestCount: 41
          verb: create
        requestCount: 41
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 34
          verb: create
        requestCount: 34
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      nodeName: 10.9.212.8
      requestCount: 1568
    - byUser:
      - byVerb:
        - requestCount: 354
          verb: create
        requestCount: 354
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 199
          verb: create
        requestCount: 199
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 194
          verb: create
        requestCount: 194
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 138
          verb: create
        requestCount: 138
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 132
          verb: create
        requestCount: 132
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 130
          verb: create
        requestCount: 130
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 113
          verb: create
        requestCount: 113
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 72
          verb: create
        requestCount: 72
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      nodeName: 10.9.212.9
      requestCount: 1574
    requestCount: 6601
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 504
          verb: create
        requestCount: 504
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 463
          verb: create
        requestCount: 463
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 306
          verb: create
        requestCount: 306
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 296
          verb: create
        requestCount: 296
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 259
          verb: create
        requestCount: 259
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 242
          verb: create
        requestCount: 242
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 219
          verb: create
        requestCount: 219
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 206
          verb: create
        requestCount: 206
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 185
          verb: create
        requestCount: 185
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 3400
    - byUser:
      - byVerb:
        - requestCount: 424
          verb: create
        requestCount: 424
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 215
          verb: create
        requestCount: 215
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 205
          verb: create
        requestCount: 205
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 166
          verb: create
        requestCount: 166
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 156
          verb: create
        requestCount: 156
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 137
          verb: create
        requestCount: 137
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 124
          verb: create
        requestCount: 124
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 53
          verb: create
        requestCount: 53
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 48
          verb: create
        requestCount: 48
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 35
          verb: create
        requestCount: 35
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      nodeName: 10.9.212.8
      requestCount: 1563
    - byUser:
      - byVerb:
        - requestCount: 273
          verb: create
        requestCount: 273
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 164
          verb: create
        requestCount: 164
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 163
          verb: create
        requestCount: 163
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 163
          verb: create
        requestCount: 163
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 153
          verb: create
        requestCount: 153
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 141
          verb: create
        requestCount: 141
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 116
          verb: create
        requestCount: 116
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 116
          verb: create
        requestCount: 116
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 114
          verb: create
        requestCount: 114
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: create
        requestCount: 60
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      nodeName: 10.9.212.9
      requestCount: 1463
    requestCount: 6426
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 519
          verb: create
        requestCount: 519
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 508
          verb: create
        requestCount: 508
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 263
          verb: create
        requestCount: 263
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 244
          verb: create
        requestCount: 244
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 242
          verb: create
        requestCount: 242
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 242
          verb: create
        requestCount: 242
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 212
          verb: create
        requestCount: 212
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 183
          verb: create
        requestCount: 183
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 173
          verb: create
        requestCount: 173
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 3306
    - byUser:
      - byVerb:
        - requestCount: 351
          verb: create
        requestCount: 351
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 186
          verb: create
        requestCount: 186
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 186
          verb: create
        requestCount: 186
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 140
          verb: create
        requestCount: 140
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 139
          verb: create
        requestCount: 139
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 132
          verb: create
        requestCount: 132
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 131
          verb: create
        requestCount: 131
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 60
          verb: create
        requestCount: 60
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 52
          verb: create
        requestCount: 52
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 28
          verb: create
        requestCount: 28
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      nodeName: 10.9.212.8
      requestCount: 1405
    - byUser:
      - byVerb:
        - requestCount: 315
          verb: create
        requestCount: 315
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 294
          verb: create
        requestCount: 294
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 214
          verb: create
        requestCount: 214
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 201
          verb: create
        requestCount: 201
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 192
          verb: create
        requestCount: 192
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 170
          verb: create
        requestCount: 170
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 168
          verb: create
        requestCount: 168
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 51
          verb: create
        requestCount: 51
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      nodeName: 10.9.212.9
      requestCount: 1847
    requestCount: 6558
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 524
          verb: create
        requestCount: 524
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 281
          verb: create
        requestCount: 281
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 259
          verb: create
        requestCount: 259
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 193
          verb: create
        requestCount: 193
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 185
          verb: create
        requestCount: 185
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 176
          verb: create
        requestCount: 176
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 173
          verb: create
        requestCount: 173
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: cluster-config-operator/v0.0.0
        username: system:serviceaccount:openshift-config-operator:openshift-config-operator
      nodeName: 10.9.212.10
      requestCount: 2872
    - byUser:
      - byVerb:
        - requestCount: 325
          verb: create
        requestCount: 325
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 251
          verb: create
        requestCount: 251
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 202
          verb: create
        requestCount: 202
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 161
          verb: create
        requestCount: 161
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 144
          verb: create
        requestCount: 144
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 132
          verb: create
        requestCount: 132
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 61
          verb: create
        requestCount: 61
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 43
          verb: create
        requestCount: 43
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 37
          verb: create
        requestCount: 37
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.8
      requestCount: 1608
    - byUser:
      - byVerb:
        - requestCount: 581
          verb: create
        requestCount: 581
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 364
          verb: create
        requestCount: 364
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 233
          verb: create
        requestCount: 233
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 202
          verb: create
        requestCount: 202
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 184
          verb: create
        requestCount: 184
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 130
          verb: create
        requestCount: 130
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 120
          verb: create
        requestCount: 120
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 72
          verb: create
        requestCount: 72
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 65
          verb: create
        requestCount: 65
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 2072
    requestCount: 6552
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 418
          verb: create
        requestCount: 418
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 301
          verb: create
        requestCount: 301
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 253
          verb: create
        requestCount: 253
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 140
          verb: create
        requestCount: 140
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 140
          verb: create
        requestCount: 140
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 129
          verb: create
        requestCount: 129
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 104
          verb: create
        requestCount: 104
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 98
          verb: create
        requestCount: 98
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 91
          verb: create
        requestCount: 91
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 78
          verb: create
        requestCount: 78
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      nodeName: 10.9.212.10
      requestCount: 1864
    - byUser:
      - byVerb:
        - requestCount: 292
          verb: create
        requestCount: 292
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 199
          verb: create
        requestCount: 199
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 172
          verb: create
        requestCount: 172
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 143
          verb: create
        requestCount: 143
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 132
          verb: create
        requestCount: 132
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 124
          verb: create
        requestCount: 124
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 80
          verb: create
        requestCount: 80
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 69
          verb: create
        requestCount: 69
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 33
          verb: create
        requestCount: 33
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 30
          verb: create
        requestCount: 30
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.8
      requestCount: 1287
    - byUser:
      - byVerb:
        - requestCount: 232
          verb: create
        requestCount: 232
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 189
          verb: create
        requestCount: 189
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 137
          verb: create
        requestCount: 137
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 109
          verb: create
        requestCount: 109
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 78
          verb: create
        requestCount: 78
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 76
          verb: create
        requestCount: 76
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 75
          verb: create
        requestCount: 75
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 71
          verb: create
        requestCount: 71
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 68
          verb: create
        requestCount: 68
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 30
          verb: create
        requestCount: 30
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      nodeName: 10.9.212.9
      requestCount: 1082
    requestCount: 4233
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1012
          verb: create
        requestCount: 1012
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 374
          verb: create
        requestCount: 374
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 312
          verb: create
        requestCount: 312
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 236
          verb: create
        requestCount: 236
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 200
          verb: create
        requestCount: 200
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 180
          verb: create
        requestCount: 180
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 174
          verb: create
        requestCount: 174
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 162
          verb: create
        requestCount: 162
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      nodeName: 10.9.212.10
      requestCount: 3610
    - byUser:
      - byVerb:
        - requestCount: 424
          verb: create
        requestCount: 424
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 250
          verb: create
        requestCount: 250
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 234
          verb: create
        requestCount: 234
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 174
          verb: create
        requestCount: 174
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 160
          verb: create
        requestCount: 160
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 151
          verb: create
        requestCount: 151
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 148
          verb: create
        requestCount: 148
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 83
          verb: create
        requestCount: 83
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 66
          verb: create
        requestCount: 66
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:multus-ac
      - byVerb:
        - requestCount: 48
          verb: create
        requestCount: 48
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.8
      requestCount: 1738
    - byUser:
      - byVerb:
        - requestCount: 398
          verb: create
        requestCount: 398
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 226
          verb: create
        requestCount: 226
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 203
          verb: create
        requestCount: 203
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 188
          verb: create
        requestCount: 188
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 178
          verb: create
        requestCount: 178
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 158
          verb: create
        requestCount: 158
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 136
          verb: create
        requestCount: 136
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 120
          verb: create
        requestCount: 120
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 51
          verb: create
        requestCount: 51
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.9
      requestCount: 1779
    requestCount: 7127
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 692
          verb: create
        requestCount: 692
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 486
          verb: create
        requestCount: 486
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 242
          verb: create
        requestCount: 242
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 222
          verb: create
        requestCount: 222
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 187
          verb: create
        requestCount: 187
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 186
          verb: create
        requestCount: 186
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 164
          verb: create
        requestCount: 164
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 159
          verb: create
        requestCount: 159
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 150
          verb: create
        requestCount: 150
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 3208
    - byUser:
      - byVerb:
        - requestCount: 349
          verb: create
        requestCount: 349
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 222
          verb: create
        requestCount: 222
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 222
          verb: create
        requestCount: 222
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 199
          verb: create
        requestCount: 199
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 196
          verb: create
        requestCount: 196
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 184
          verb: create
        requestCount: 184
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 119
          verb: create
        requestCount: 119
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 71
          verb: create
        requestCount: 71
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 38
          verb: create
        requestCount: 38
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 30
          verb: create
        requestCount: 30
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:multus-ac
      nodeName: 10.9.212.8
      requestCount: 1630
    - byUser:
      - byVerb:
        - requestCount: 365
          verb: create
        requestCount: 365
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 329
          verb: create
        requestCount: 329
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 295
          verb: create
        requestCount: 295
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 214
          verb: create
        requestCount: 214
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 156
          verb: create
        requestCount: 156
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 140
          verb: create
        requestCount: 140
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 120
          verb: create
        requestCount: 120
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 120
          verb: create
        requestCount: 120
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 50
          verb: create
        requestCount: 50
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      nodeName: 10.9.212.9
      requestCount: 1910
    requestCount: 6748
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 552
          verb: create
        requestCount: 552
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 444
          verb: create
        requestCount: 444
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 265
          verb: create
        requestCount: 265
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 248
          verb: create
        requestCount: 248
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 241
          verb: create
        requestCount: 241
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 206
          verb: create
        requestCount: 206
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 188
          verb: create
        requestCount: 188
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 182
          verb: create
        requestCount: 182
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 168
          verb: create
        requestCount: 168
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 3214
    - byUser:
      - byVerb:
        - requestCount: 388
          verb: create
        requestCount: 388
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 212
          verb: create
        requestCount: 212
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 190
          verb: create
        requestCount: 190
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 176
          verb: create
        requestCount: 176
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 174
          verb: create
        requestCount: 174
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 152
          verb: create
        requestCount: 152
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 150
          verb: create
        requestCount: 150
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 34
          verb: create
        requestCount: 34
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:multus-ac
      - byVerb:
        - requestCount: 28
          verb: create
        requestCount: 28
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 14
          verb: create
        requestCount: 14
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-operator
      nodeName: 10.9.212.8
      requestCount: 1518
    - byUser:
      - byVerb:
        - requestCount: 320
          verb: create
        requestCount: 320
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 214
          verb: create
        requestCount: 214
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 209
          verb: create
        requestCount: 209
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 194
          verb: create
        requestCount: 194
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 162
          verb: create
        requestCount: 162
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 146
          verb: create
        requestCount: 146
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 138
          verb: create
        requestCount: 138
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 122
          verb: create
        requestCount: 122
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 122
          verb: create
        requestCount: 122
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 50
          verb: create
        requestCount: 50
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.9
      requestCount: 1677
    requestCount: 6409
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 640
          verb: create
        requestCount: 640
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 350
          verb: create
        requestCount: 350
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 246
          verb: create
        requestCount: 246
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 243
          verb: create
        requestCount: 243
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 222
          verb: create
        requestCount: 222
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 204
          verb: create
        requestCount: 204
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 176
          verb: create
        requestCount: 176
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 174
          verb: create
        requestCount: 174
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 144
          verb: create
        requestCount: 144
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 3119
    - byUser:
      - byVerb:
        - requestCount: 433
          verb: create
        requestCount: 433
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 224
          verb: create
        requestCount: 224
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 211
          verb: create
        requestCount: 211
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 206
          verb: create
        requestCount: 206
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 184
          verb: create
        requestCount: 184
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 163
          verb: create
        requestCount: 163
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 131
          verb: create
        requestCount: 131
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 33
          verb: create
        requestCount: 33
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 32
          verb: create
        requestCount: 32
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 30
          verb: create
        requestCount: 30
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:thanos-querier
      nodeName: 10.9.212.8
      requestCount: 1647
    - byUser:
      - byVerb:
        - requestCount: 410
          verb: create
        requestCount: 410
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 262
          verb: create
        requestCount: 262
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 218
          verb: create
        requestCount: 218
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 214
          verb: create
        requestCount: 214
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 200
          verb: create
        requestCount: 200
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 140
          verb: create
        requestCount: 140
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 138
          verb: create
        requestCount: 138
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 120
          verb: create
        requestCount: 120
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 120
          verb: create
        requestCount: 120
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 54
          verb: create
        requestCount: 54
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      nodeName: 10.9.212.9
      requestCount: 1876
    requestCount: 6642
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 524
          verb: create
        requestCount: 524
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 379
          verb: create
        requestCount: 379
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 239
          verb: create
        requestCount: 239
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 235
          verb: create
        requestCount: 235
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 229
          verb: create
        requestCount: 229
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 224
          verb: create
        requestCount: 224
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 189
          verb: create
        requestCount: 189
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 145
          verb: create
        requestCount: 145
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 3124
    - byUser:
      - byVerb:
        - requestCount: 392
          verb: create
        requestCount: 392
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 231
          verb: create
        requestCount: 231
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 193
          verb: create
        requestCount: 193
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 180
          verb: create
        requestCount: 180
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 176
          verb: create
        requestCount: 176
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 156
          verb: create
        requestCount: 156
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 148
          verb: create
        requestCount: 148
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 54
          verb: create
        requestCount: 54
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 51
          verb: create
        requestCount: 51
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 29
          verb: create
        requestCount: 29
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-machine-approver:machine-approver-sa
      nodeName: 10.9.212.8
      requestCount: 1610
    - byUser:
      - byVerb:
        - requestCount: 387
          verb: create
        requestCount: 387
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 206
          verb: create
        requestCount: 206
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 177
          verb: create
        requestCount: 177
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 173
          verb: create
        requestCount: 173
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 163
          verb: create
        requestCount: 163
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 161
          verb: create
        requestCount: 161
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 136
          verb: create
        requestCount: 136
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 50
          verb: create
        requestCount: 50
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.9
      requestCount: 1695
    requestCount: 6429
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 531
          verb: create
        requestCount: 531
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 516
          verb: create
        requestCount: 516
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 268
          verb: create
        requestCount: 268
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 252
          verb: create
        requestCount: 252
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 242
          verb: create
        requestCount: 242
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 242
          verb: create
        requestCount: 242
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 238
          verb: create
        requestCount: 238
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 232
          verb: create
        requestCount: 232
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 190
          verb: create
        requestCount: 190
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 3431
    - byUser:
      - byVerb:
        - requestCount: 402
          verb: create
        requestCount: 402
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 271
          verb: create
        requestCount: 271
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 216
          verb: create
        requestCount: 216
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 170
          verb: create
        requestCount: 170
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 164
          verb: create
        requestCount: 164
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 153
          verb: create
        requestCount: 153
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 58
          verb: create
        requestCount: 58
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:multus-ac
      - byVerb:
        - requestCount: 45
          verb: create
        requestCount: 45
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 36
          verb: create
        requestCount: 36
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.8
      requestCount: 1636
    - byUser:
      - byVerb:
        - requestCount: 285
          verb: create
        requestCount: 285
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 274
          verb: create
        requestCount: 274
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 257
          verb: create
        requestCount: 257
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 174
          verb: create
        requestCount: 174
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 155
          verb: create
        requestCount: 155
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 114
          verb: create
        requestCount: 114
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 69
          verb: create
        requestCount: 69
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 50
          verb: create
        requestCount: 50
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.9
      requestCount: 1620
    requestCount: 6687
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 520
          verb: create
        requestCount: 520
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 447
          verb: create
        requestCount: 447
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 272
          verb: create
        requestCount: 272
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 250
          verb: create
        requestCount: 250
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 244
          verb: create
        requestCount: 244
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 235
          verb: create
        requestCount: 235
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 204
          verb: create
        requestCount: 204
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 204
          verb: create
        requestCount: 204
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 203
          verb: create
        requestCount: 203
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      nodeName: 10.9.212.10
      requestCount: 3299
    - byUser:
      - byVerb:
        - requestCount: 422
          verb: create
        requestCount: 422
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 232
          verb: create
        requestCount: 232
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 206
          verb: create
        requestCount: 206
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 173
          verb: create
        requestCount: 173
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 169
          verb: create
        requestCount: 169
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 140
          verb: create
        requestCount: 140
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 113
          verb: create
        requestCount: 113
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 60
          verb: create
        requestCount: 60
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      - byVerb:
        - requestCount: 38
          verb: create
        requestCount: 38
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:multus-ac
      - byVerb:
        - requestCount: 36
          verb: create
        requestCount: 36
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns-operator:dns-operator
      nodeName: 10.9.212.8
      requestCount: 1589
    - byUser:
      - byVerb:
        - requestCount: 307
          verb: create
        requestCount: 307
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 217
          verb: create
        requestCount: 217
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 180
          verb: create
        requestCount: 180
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 180
          verb: create
        requestCount: 180
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 178
          verb: create
        requestCount: 178
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 146
          verb: create
        requestCount: 146
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 127
          verb: create
        requestCount: 127
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 50
          verb: create
        requestCount: 50
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      nodeName: 10.9.212.9
      requestCount: 1627
    requestCount: 6515
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 720
          verb: create
        requestCount: 720
        userAgent: openshift-router/v0.0.0
        username: system:serviceaccount:openshift-ingress:router
      - byVerb:
        - requestCount: 516
          verb: create
        requestCount: 516
        userAgent: bridge/v0.0.0
        username: system:serviceaccount:openshift-console:console
      - byVerb:
        - requestCount: 435
          verb: create
        requestCount: 435
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 255
          verb: create
        requestCount: 255
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 254
          verb: create
        requestCount: 254
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 241
          verb: create
        requestCount: 241
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 240
          verb: create
        requestCount: 240
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 209
          verb: create
        requestCount: 209
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 189
          verb: create
        requestCount: 189
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 173
          verb: create
        requestCount: 173
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 3232
    - byUser:
      - byVerb:
        - requestCount: 368
          verb: create
        requestCount: 368
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 208
          verb: create
        requestCount: 208
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 182
          verb: create
        requestCount: 182
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 177
          verb: create
        requestCount: 177
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 165
          verb: create
        requestCount: 165
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 164
          verb: create
        requestCount: 164
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 144
          verb: create
        requestCount: 144
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 40
          verb: create
        requestCount: 40
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:machine-api-controllers
      - byVerb:
        - requestCount: 34
          verb: create
        requestCount: 34
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler-operator
      - byVerb:
        - requestCount: 28
          verb: create
        requestCount: 28
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-control-plane
      nodeName: 10.9.212.8
      requestCount: 1510
    - byUser:
      - byVerb:
        - requestCount: 373
          verb: create
        requestCount: 373
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-ovn-kubernetes:ovn-kubernetes-node
      - byVerb:
        - requestCount: 254
          verb: create
        requestCount: 254
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 188
          verb: create
        requestCount: 188
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 171
          verb: create
        requestCount: 171
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-node-sa
      - byVerb:
        - requestCount: 169
          verb: create
        requestCount: 169
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-daemon
      - byVerb:
        - requestCount: 137
          verb: create
        requestCount: 137
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 134
          verb: create
        requestCount: 134
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 121
          verb: create
        requestCount: 121
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      - byVerb:
        - requestCount: 120
          verb: create
        requestCount: 120
        userAgent: cluster-network-check-endpoints/v0.0.0
        username: system:serviceaccount:openshift-network-diagnostics:network-diagnostics
      - byVerb:
        - requestCount: 59
          verb: create
        requestCount: 59
        userAgent: kube-rbac-proxy/v0.0.0
        username: system:serviceaccount:openshift-monitoring:alertmanager-main
      nodeName: 10.9.212.9
      requestCount: 1726
    requestCount: 6468
  requestCount: 148564
