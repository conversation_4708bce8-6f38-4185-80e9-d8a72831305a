---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T15:00:01Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T15:00:01Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:41:40Z"
  name: secrets.v1
  resourceVersion: "********"
  uid: 208fa923-dac4-450b-8183-f21556700797
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 1177
          verb: patch
        requestCount: 1177
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 732
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 746
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 316
          verb: watch
        requestCount: 316
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 290
          verb: watch
        requestCount: 290
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 205
          verb: get
        - requestCount: 29
          verb: watch
        requestCount: 234
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 218
          verb: watch
        requestCount: 218
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 185
          verb: watch
        requestCount: 191
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 189
          verb: watch
        requestCount: 189
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 186
          verb: watch
        requestCount: 186
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 172
          verb: watch
        requestCount: 172
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 3845
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 308
          verb: watch
        requestCount: 309
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 21
          verb: list
        requestCount: 21
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: **********
      requestCount: 371
    - byUser:
      - byVerb:
        - requestCount: 990
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 26
          verb: watch
        requestCount: 1025
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 804
          verb: get
        - requestCount: 42
          verb: watch
        requestCount: 846
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 572
          verb: watch
        requestCount: 572
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 565
          verb: watch
        requestCount: 565
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 90
          verb: delete
        - requestCount: 246
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 15
          verb: watch
        requestCount: 369
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 116
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 141
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 36
          verb: watch
        requestCount: 105
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 36
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 41
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 16
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 40
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 37
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: **********
      requestCount: 3765
    requestCount: 7981
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2342
          verb: patch
        requestCount: 2342
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1294
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 1318
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 564
          verb: watch
        requestCount: 564
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 494
          verb: watch
        requestCount: 494
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 365
          verb: get
        - requestCount: 49
          verb: watch
        requestCount: 414
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 368
          verb: watch
        requestCount: 368
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: list
        - requestCount: 326
          verb: watch
        requestCount: 334
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 325
          verb: watch
        requestCount: 325
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 303
          verb: watch
        requestCount: 304
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 294
          verb: watch
        requestCount: 294
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6757
    - byUser:
      - byVerb:
        - requestCount: 21
          verb: get
        requestCount: 21
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 2
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: **********
      requestCount: 59
    - byUser:
      - byVerb:
        - requestCount: 1620
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 42
          verb: watch
        requestCount: 1671
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1258
          verb: get
        - requestCount: 64
          verb: watch
        requestCount: 1322
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 920
          verb: watch
        requestCount: 920
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 893
          verb: watch
        requestCount: 893
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 210
          verb: delete
        - requestCount: 528
          verb: get
        - requestCount: 42
          verb: list
        - requestCount: 24
          verb: watch
        requestCount: 804
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 459
          verb: watch
        requestCount: 459
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 180
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 220
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 109
          verb: get
        - requestCount: 56
          verb: watch
        requestCount: 165
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 41
          verb: watch
        requestCount: 67
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 56
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 63
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      nodeName: **********
      requestCount: 6584
    requestCount: 13400
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2342
          verb: patch
        requestCount: 2342
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1281
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 1304
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 550
          verb: watch
        requestCount: 550
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 499
          verb: watch
        requestCount: 499
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 47
          verb: watch
        requestCount: 407
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 381
          verb: watch
        requestCount: 381
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 328
          verb: watch
        requestCount: 328
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 8
          verb: list
        - requestCount: 318
          verb: watch
        requestCount: 326
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 325
          verb: watch
        requestCount: 325
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 281
          verb: watch
        requestCount: 281
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6743
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 58
    - byUser:
      - byVerb:
        - requestCount: 1590
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 40
          verb: watch
        requestCount: 1639
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1268
          verb: get
        - requestCount: 64
          verb: watch
        requestCount: 1332
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 908
          verb: watch
        requestCount: 908
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 905
          verb: watch
        requestCount: 905
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 225
          verb: delete
        - requestCount: 588
          verb: get
        - requestCount: 45
          verb: list
        - requestCount: 26
          verb: watch
        requestCount: 884
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 471
          verb: watch
        requestCount: 471
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 172
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 212
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 107
          verb: get
        - requestCount: 56
          verb: watch
        requestCount: 163
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 52
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 60
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 39
          verb: watch
        requestCount: 57
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: **********
      requestCount: 6631
    requestCount: 13432
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2342
          verb: patch
        requestCount: 2342
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1257
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 1280
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 552
          verb: watch
        requestCount: 552
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 530
          verb: watch
        requestCount: 530
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 49
          verb: watch
        requestCount: 409
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 371
          verb: watch
        requestCount: 371
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: list
        - requestCount: 322
          verb: watch
        requestCount: 330
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 324
          verb: watch
        requestCount: 324
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 321
          verb: watch
        requestCount: 321
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 275
          verb: watch
        requestCount: 275
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6734
    - byUser:
      - byVerb:
        - requestCount: 22
          verb: get
        requestCount: 22
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: **********
      requestCount: 62
    - byUser:
      - byVerb:
        - requestCount: 1575
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 39
          verb: watch
        requestCount: 1623
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1283
          verb: get
        - requestCount: 63
          verb: watch
        requestCount: 1346
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 917
          verb: watch
        requestCount: 917
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 881
          verb: watch
        requestCount: 881
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 195
          verb: delete
        - requestCount: 530
          verb: get
        - requestCount: 39
          verb: list
        - requestCount: 21
          verb: watch
        requestCount: 785
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 466
          verb: watch
        requestCount: 466
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 178
          verb: get
        - requestCount: 41
          verb: watch
        requestCount: 219
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 108
          verb: get
        - requestCount: 55
          verb: watch
        requestCount: 163
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 52
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 60
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 58
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: **********
      requestCount: 6518
    requestCount: 13314
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2342
          verb: patch
        requestCount: 2342
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1259
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 1282
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 556
          verb: watch
        requestCount: 556
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 504
          verb: watch
        requestCount: 504
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 47
          verb: watch
        requestCount: 407
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 374
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: list
        - requestCount: 324
          verb: watch
        requestCount: 332
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 330
          verb: watch
        requestCount: 330
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 313
          verb: watch
        requestCount: 313
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 293
          verb: watch
        requestCount: 293
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6733
    - byUser:
      - byVerb:
        - requestCount: 24
          verb: get
        requestCount: 24
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      nodeName: **********
      requestCount: 65
    - byUser:
      - byVerb:
        - requestCount: 1620
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 41
          verb: watch
        requestCount: 1670
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1272
          verb: get
        - requestCount: 63
          verb: watch
        requestCount: 1335
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 909
          verb: watch
        requestCount: 909
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 899
          verb: watch
        requestCount: 899
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 210
          verb: delete
        - requestCount: 544
          verb: get
        - requestCount: 42
          verb: list
        - requestCount: 23
          verb: watch
        requestCount: 819
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 465
          verb: watch
        requestCount: 465
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 176
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 216
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 108
          verb: get
        - requestCount: 60
          verb: watch
        requestCount: 168
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 43
          verb: watch
        requestCount: 67
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 42
          verb: watch
        requestCount: 60
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: **********
      requestCount: 6608
    requestCount: 13406
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2342
          verb: patch
        requestCount: 2342
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1311
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 1335
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 561
          verb: watch
        requestCount: 561
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 501
          verb: watch
        requestCount: 501
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 49
          verb: watch
        requestCount: 409
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 384
          verb: watch
        requestCount: 384
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: list
        - requestCount: 326
          verb: watch
        requestCount: 334
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 334
          verb: watch
        requestCount: 334
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 320
          verb: watch
        requestCount: 320
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 296
          verb: watch
        requestCount: 296
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6816
    - byUser:
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 1
          verb: update
        requestCount: 2
        userAgent: collect-profiles/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:collect-profiles
      nodeName: **********
      requestCount: 58
    - byUser:
      - byVerb:
        - requestCount: 1620
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 41
          verb: watch
        requestCount: 1670
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1260
          verb: get
        - requestCount: 62
          verb: watch
        requestCount: 1322
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 924
          verb: watch
        requestCount: 924
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 888
          verb: watch
        requestCount: 888
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 210
          verb: delete
        - requestCount: 532
          verb: get
        - requestCount: 42
          verb: list
        - requestCount: 24
          verb: watch
        requestCount: 808
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 464
          verb: watch
        requestCount: 464
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 178
          verb: get
        - requestCount: 41
          verb: watch
        requestCount: 219
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 106
          verb: get
        - requestCount: 55
          verb: watch
        requestCount: 161
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 52
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 60
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 58
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: **********
      requestCount: 6574
    requestCount: 13448
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2342
          verb: patch
        requestCount: 2342
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1306
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 1331
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 540
          verb: watch
        requestCount: 540
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 499
          verb: watch
        requestCount: 499
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 47
          verb: watch
        requestCount: 407
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 363
          verb: watch
        requestCount: 363
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: list
        - requestCount: 323
          verb: watch
        requestCount: 331
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 329
          verb: watch
        requestCount: 329
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 319
          verb: watch
        requestCount: 319
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 303
          verb: watch
        requestCount: 303
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6764
    - byUser:
      - byVerb:
        - requestCount: 23
          verb: get
        requestCount: 23
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: **********
      requestCount: 62
    - byUser:
      - byVerb:
        - requestCount: 1590
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 42
          verb: watch
        requestCount: 1641
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1260
          verb: get
        - requestCount: 67
          verb: watch
        requestCount: 1327
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 907
          verb: watch
        requestCount: 907
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 895
          verb: watch
        requestCount: 895
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 225
          verb: delete
        - requestCount: 562
          verb: get
        - requestCount: 45
          verb: list
        - requestCount: 24
          verb: watch
        requestCount: 856
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 471
          verb: watch
        requestCount: 471
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 176
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 216
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 108
          verb: get
        - requestCount: 58
          verb: watch
        requestCount: 166
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 52
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 59
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 58
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: **********
      requestCount: 6596
    requestCount: 13422
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2342
          verb: patch
        requestCount: 2342
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1269
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 1293
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 551
          verb: watch
        requestCount: 551
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 492
          verb: watch
        requestCount: 492
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 52
          verb: watch
        requestCount: 412
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 372
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 335
          verb: watch
        requestCount: 335
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 8
          verb: list
        - requestCount: 322
          verb: watch
        requestCount: 330
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 329
          verb: watch
        requestCount: 329
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 285
          verb: watch
        requestCount: 285
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6741
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      nodeName: **********
      requestCount: 63
    - byUser:
      - byVerb:
        - requestCount: 1575
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 38
          verb: watch
        requestCount: 1622
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1267
          verb: get
        - requestCount: 65
          verb: watch
        requestCount: 1332
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 917
          verb: watch
        requestCount: 917
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 898
          verb: watch
        requestCount: 898
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 180
          verb: delete
        - requestCount: 478
          verb: get
        - requestCount: 36
          verb: list
        - requestCount: 24
          verb: watch
        requestCount: 718
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 472
          verb: watch
        requestCount: 472
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 174
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 214
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 108
          verb: get
        - requestCount: 54
          verb: watch
        requestCount: 162
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 42
          verb: watch
        requestCount: 66
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 52
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 60
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      nodeName: **********
      requestCount: 6461
    requestCount: 13265
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2342
          verb: patch
        requestCount: 2342
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1349
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 1373
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 541
          verb: watch
        requestCount: 541
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 499
          verb: watch
        requestCount: 499
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 48
          verb: watch
        requestCount: 408
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 373
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 10
          verb: list
        - requestCount: 321
          verb: watch
        requestCount: 331
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 328
          verb: watch
        requestCount: 328
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 326
          verb: watch
        requestCount: 326
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 299
          verb: watch
        requestCount: 299
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6820
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      nodeName: **********
      requestCount: 60
    - byUser:
      - byVerb:
        - requestCount: 1620
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 40
          verb: watch
        requestCount: 1669
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1273
          verb: get
        - requestCount: 63
          verb: watch
        requestCount: 1336
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 908
          verb: watch
        requestCount: 908
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 896
          verb: watch
        requestCount: 896
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 225
          verb: delete
        - requestCount: 578
          verb: get
        - requestCount: 45
          verb: list
        - requestCount: 24
          verb: watch
        requestCount: 872
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 464
          verb: watch
        requestCount: 464
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 176
          verb: get
        - requestCount: 41
          verb: watch
        requestCount: 217
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 108
          verb: get
        - requestCount: 56
          verb: watch
        requestCount: 164
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 58
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 65
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 38
          verb: watch
        requestCount: 56
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: **********
      requestCount: 6647
    requestCount: 13527
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2342
          verb: patch
        requestCount: 2342
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1309
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 1332
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 560
          verb: watch
        requestCount: 560
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 499
          verb: watch
        requestCount: 499
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 47
          verb: watch
        requestCount: 407
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 389
          verb: watch
        requestCount: 389
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 332
          verb: watch
        requestCount: 332
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 8
          verb: list
        - requestCount: 319
          verb: watch
        requestCount: 327
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 321
          verb: watch
        requestCount: 321
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 298
          verb: watch
        requestCount: 298
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6807
    - byUser:
      - byVerb:
        - requestCount: 22
          verb: get
        requestCount: 22
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      nodeName: **********
      requestCount: 63
    - byUser:
      - byVerb:
        - requestCount: 1620
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 43
          verb: watch
        requestCount: 1672
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1263
          verb: get
        - requestCount: 65
          verb: watch
        requestCount: 1328
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 909
          verb: watch
        requestCount: 909
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 902
          verb: watch
        requestCount: 902
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 195
          verb: delete
        - requestCount: 502
          verb: get
        - requestCount: 39
          verb: list
        - requestCount: 22
          verb: watch
        requestCount: 758
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 464
          verb: watch
        requestCount: 464
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 176
          verb: get
        - requestCount: 42
          verb: watch
        requestCount: 218
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 107
          verb: get
        - requestCount: 55
          verb: watch
        requestCount: 162
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 39
          verb: watch
        requestCount: 63
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 52
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 59
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      nodeName: **********
      requestCount: 6535
    requestCount: 13405
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2342
          verb: patch
        requestCount: 2342
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1328
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 1353
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 546
          verb: watch
        requestCount: 546
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 496
          verb: watch
        requestCount: 496
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 48
          verb: watch
        requestCount: 408
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 376
          verb: watch
        requestCount: 376
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: list
        - requestCount: 319
          verb: watch
        requestCount: 327
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 321
          verb: watch
        requestCount: 321
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 321
          verb: watch
        requestCount: 321
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 272
          verb: watch
        requestCount: 272
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6762
    - byUser:
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      nodeName: **********
      requestCount: 54
    - byUser:
      - byVerb:
        - requestCount: 1605
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 41
          verb: watch
        requestCount: 1655
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1282
          verb: get
        - requestCount: 59
          verb: watch
        requestCount: 1341
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 907
          verb: watch
        requestCount: 907
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 900
          verb: watch
        requestCount: 900
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 210
          verb: delete
        - requestCount: 574
          verb: get
        - requestCount: 42
          verb: list
        - requestCount: 26
          verb: watch
        requestCount: 852
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 468
          verb: watch
        requestCount: 468
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 178
          verb: get
        - requestCount: 39
          verb: watch
        requestCount: 217
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 108
          verb: get
        - requestCount: 55
          verb: watch
        requestCount: 163
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 64
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 52
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 60
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      nodeName: **********
      requestCount: 6627
    requestCount: 13443
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2342
          verb: patch
        requestCount: 2342
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1270
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 1294
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 562
          verb: watch
        requestCount: 562
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 494
          verb: watch
        requestCount: 494
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 370
          verb: get
        - requestCount: 48
          verb: watch
        requestCount: 418
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 374
          verb: watch
        requestCount: 374
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: list
        - requestCount: 321
          verb: watch
        requestCount: 329
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 327
          verb: watch
        requestCount: 327
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 316
          verb: watch
        requestCount: 316
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 300
          verb: watch
        requestCount: 300
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6756
    - byUser:
      - byVerb:
        - requestCount: 28
          verb: get
        requestCount: 28
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      nodeName: **********
      requestCount: 71
    - byUser:
      - byVerb:
        - requestCount: 1605
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 38
          verb: watch
        requestCount: 1652
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1272
          verb: get
        - requestCount: 66
          verb: watch
        requestCount: 1338
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 909
          verb: watch
        requestCount: 909
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 898
          verb: watch
        requestCount: 898
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 210
          verb: delete
        - requestCount: 558
          verb: get
        - requestCount: 42
          verb: list
        - requestCount: 24
          verb: watch
        requestCount: 834
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 467
          verb: watch
        requestCount: 467
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 182
          verb: get
        - requestCount: 41
          verb: watch
        requestCount: 223
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 110
          verb: get
        - requestCount: 57
          verb: watch
        requestCount: 167
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 52
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 60
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 42
          verb: watch
        requestCount: 60
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: **********
      requestCount: 6608
    requestCount: 13435
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2342
          verb: patch
        requestCount: 2342
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1283
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 1307
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 545
          verb: watch
        requestCount: 545
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 501
          verb: watch
        requestCount: 501
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 47
          verb: watch
        requestCount: 407
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 380
          verb: watch
        requestCount: 380
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: list
        - requestCount: 325
          verb: watch
        requestCount: 333
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 325
          verb: watch
        requestCount: 325
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 320
          verb: watch
        requestCount: 320
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 300
          verb: watch
        requestCount: 300
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6760
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: get
        requestCount: 17
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      nodeName: **********
      requestCount: 57
    - byUser:
      - byVerb:
        - requestCount: 1605
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 42
          verb: watch
        requestCount: 1656
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1269
          verb: get
        - requestCount: 61
          verb: watch
        requestCount: 1330
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 931
          verb: watch
        requestCount: 932
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 914
          verb: watch
        requestCount: 914
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 195
          verb: delete
        - requestCount: 502
          verb: get
        - requestCount: 39
          verb: list
        - requestCount: 23
          verb: watch
        requestCount: 759
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 468
          verb: watch
        requestCount: 468
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 176
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 216
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 108
          verb: get
        - requestCount: 54
          verb: watch
        requestCount: 162
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 64
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 58
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: **********
      requestCount: 6559
    requestCount: 13376
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2342
          verb: patch
        requestCount: 2342
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1263
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 1288
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 560
          verb: watch
        requestCount: 560
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 504
          verb: watch
        requestCount: 504
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 47
          verb: watch
        requestCount: 407
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 372
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 331
          verb: watch
        requestCount: 331
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 324
          verb: watch
        requestCount: 324
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 8
          verb: list
        - requestCount: 313
          verb: watch
        requestCount: 321
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 275
          verb: watch
        requestCount: 275
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6724
    - byUser:
      - byVerb:
        - requestCount: 24
          verb: get
        requestCount: 24
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      nodeName: **********
      requestCount: 63
    - byUser:
      - byVerb:
        - requestCount: 1620
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 36
          verb: watch
        requestCount: 1665
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1265
          verb: get
        - requestCount: 63
          verb: watch
        requestCount: 1328
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 915
          verb: watch
        requestCount: 915
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 903
          verb: watch
        requestCount: 903
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 225
          verb: delete
        - requestCount: 572
          verb: get
        - requestCount: 45
          verb: list
        - requestCount: 23
          verb: watch
        requestCount: 865
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 464
          verb: watch
        requestCount: 464
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 174
          verb: get
        - requestCount: 39
          verb: watch
        requestCount: 213
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 108
          verb: get
        - requestCount: 58
          verb: watch
        requestCount: 166
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 39
          verb: watch
        requestCount: 63
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 50
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 57
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      nodeName: **********
      requestCount: 6639
    requestCount: 13426
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2342
          verb: patch
        requestCount: 2342
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1294
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 1319
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 552
          verb: watch
        requestCount: 552
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 491
          verb: watch
        requestCount: 491
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 46
          verb: watch
        requestCount: 406
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 384
          verb: watch
        requestCount: 384
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: list
        - requestCount: 331
          verb: watch
        requestCount: 339
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 323
          verb: watch
        requestCount: 323
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 316
          verb: watch
        requestCount: 316
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 295
          verb: watch
        requestCount: 295
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6767
    - byUser:
      - byVerb:
        - requestCount: 358
          verb: watch
        requestCount: 358
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: **********
      requestCount: 416
    - byUser:
      - byVerb:
        - requestCount: 1605
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 41
          verb: watch
        requestCount: 1655
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1274
          verb: get
        - requestCount: 65
          verb: watch
        requestCount: 1339
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 924
          verb: watch
        requestCount: 924
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 908
          verb: watch
        requestCount: 908
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 210
          verb: delete
        - requestCount: 564
          verb: get
        - requestCount: 42
          verb: list
        - requestCount: 27
          verb: watch
        requestCount: 843
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 176
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 216
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 108
          verb: get
        - requestCount: 55
          verb: watch
        requestCount: 163
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 127
          verb: watch
        requestCount: 127
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 64
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 52
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 59
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      nodeName: **********
      requestCount: 6298
    requestCount: 13481
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1177
          verb: patch
        requestCount: 1177
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 732
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 746
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 316
          verb: watch
        requestCount: 316
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 290
          verb: watch
        requestCount: 290
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 205
          verb: get
        - requestCount: 29
          verb: watch
        requestCount: 234
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 218
          verb: watch
        requestCount: 218
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 185
          verb: watch
        requestCount: 191
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 189
          verb: watch
        requestCount: 189
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 186
          verb: watch
        requestCount: 186
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 172
          verb: watch
        requestCount: 172
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 3845
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 308
          verb: watch
        requestCount: 309
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 21
          verb: list
        requestCount: 21
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 15
          verb: get
        requestCount: 15
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      nodeName: **********
      requestCount: 371
    - byUser:
      - byVerb:
        - requestCount: 990
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 26
          verb: watch
        requestCount: 1025
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 804
          verb: get
        - requestCount: 42
          verb: watch
        requestCount: 846
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 572
          verb: watch
        requestCount: 572
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 565
          verb: watch
        requestCount: 565
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 90
          verb: delete
        - requestCount: 246
          verb: get
        - requestCount: 18
          verb: list
        - requestCount: 15
          verb: watch
        requestCount: 369
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 116
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 141
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 36
          verb: watch
        requestCount: 105
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 36
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 41
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 16
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 40
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 37
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: **********
      requestCount: 3765
    requestCount: 7981
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2345
          verb: patch
        requestCount: 2345
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1308
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 1331
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 558
          verb: watch
        requestCount: 558
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 489
          verb: watch
        requestCount: 489
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 51
          verb: watch
        requestCount: 411
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 385
          verb: watch
        requestCount: 385
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: list
        - requestCount: 330
          verb: watch
        requestCount: 338
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 327
          verb: watch
        requestCount: 327
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 322
          verb: watch
        requestCount: 322
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 297
          verb: watch
        requestCount: 297
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6803
    - byUser:
      - byVerb:
        - requestCount: 21
          verb: get
        requestCount: 21
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      nodeName: **********
      requestCount: 59
    - byUser:
      - byVerb:
        - requestCount: 1620
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 41
          verb: watch
        requestCount: 1670
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1267
          verb: get
        - requestCount: 65
          verb: watch
        requestCount: 1332
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 909
          verb: watch
        requestCount: 909
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 888
          verb: watch
        requestCount: 888
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 210
          verb: delete
        - requestCount: 540
          verb: get
        - requestCount: 42
          verb: list
        - requestCount: 23
          verb: watch
        requestCount: 815
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 481
          verb: watch
        requestCount: 482
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 174
          verb: get
        - requestCount: 39
          verb: watch
        requestCount: 213
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 108
          verb: get
        - requestCount: 58
          verb: watch
        requestCount: 166
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 64
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 73
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 42
          verb: watch
        requestCount: 66
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      nodeName: **********
      requestCount: 6614
    requestCount: 13476
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2366
          verb: patch
        requestCount: 2366
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1326
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 1351
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 552
          verb: watch
        requestCount: 552
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 505
          verb: watch
        requestCount: 506
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 46
          verb: watch
        requestCount: 406
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 372
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 330
          verb: watch
        requestCount: 330
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 8
          verb: list
        - requestCount: 319
          verb: watch
        requestCount: 327
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 316
          verb: watch
        requestCount: 316
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 299
          verb: watch
        requestCount: 299
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6825
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      nodeName: **********
      requestCount: 58
    - byUser:
      - byVerb:
        - requestCount: 1590
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 38
          verb: watch
        requestCount: 1637
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1270
          verb: get
        - requestCount: 64
          verb: watch
        requestCount: 1334
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 918
          verb: watch
        requestCount: 918
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 908
          verb: watch
        requestCount: 908
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 210
          verb: delete
        - requestCount: 564
          verb: get
        - requestCount: 42
          verb: list
        - requestCount: 25
          verb: watch
        requestCount: 841
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 466
          verb: watch
        requestCount: 466
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 178
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 218
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 106
          verb: get
        - requestCount: 56
          verb: watch
        requestCount: 162
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 68
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 20
          verb: get
        - requestCount: 35
          verb: watch
        requestCount: 55
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      nodeName: **********
      requestCount: 6607
    requestCount: 13490
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2357
          verb: patch
        requestCount: 2357
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1287
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 1310
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 552
          verb: watch
        requestCount: 552
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 494
          verb: watch
        requestCount: 495
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 47
          verb: watch
        requestCount: 407
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 373
          verb: watch
        requestCount: 373
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 330
          verb: watch
        requestCount: 330
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 8
          verb: list
        - requestCount: 319
          verb: watch
        requestCount: 327
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 319
          verb: watch
        requestCount: 319
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 300
          verb: watch
        requestCount: 300
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6770
    - byUser:
      - byVerb:
        - requestCount: 22
          verb: get
        requestCount: 22
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      nodeName: **********
      requestCount: 62
    - byUser:
      - byVerb:
        - requestCount: 1590
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 42
          verb: watch
        requestCount: 1641
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1270
          verb: get
        - requestCount: 65
          verb: watch
        requestCount: 1335
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 916
          verb: watch
        requestCount: 916
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 908
          verb: watch
        requestCount: 908
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 210
          verb: delete
        - requestCount: 536
          verb: get
        - requestCount: 42
          verb: list
        - requestCount: 24
          verb: watch
        requestCount: 812
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 468
          verb: watch
        requestCount: 468
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 180
          verb: get
        - requestCount: 38
          verb: watch
        requestCount: 218
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 108
          verb: get
        - requestCount: 55
          verb: watch
        requestCount: 163
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 60
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 69
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 64
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      nodeName: **********
      requestCount: 6594
    requestCount: 13426
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2339
          verb: patch
        requestCount: 2339
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1275
          verb: get
        - requestCount: 22
          verb: watch
        requestCount: 1297
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 542
          verb: watch
        requestCount: 542
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 503
          verb: watch
        requestCount: 503
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 48
          verb: watch
        requestCount: 408
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 379
          verb: watch
        requestCount: 379
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 335
          verb: watch
        requestCount: 335
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 8
          verb: list
        - requestCount: 321
          verb: watch
        requestCount: 329
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 322
          verb: watch
        requestCount: 322
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 296
          verb: watch
        requestCount: 296
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6750
    - byUser:
      - byVerb:
        - requestCount: 21
          verb: get
        requestCount: 21
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      nodeName: **********
      requestCount: 60
    - byUser:
      - byVerb:
        - requestCount: 1620
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 37
          verb: watch
        requestCount: 1666
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1274
          verb: get
        - requestCount: 64
          verb: watch
        requestCount: 1338
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 909
          verb: watch
        requestCount: 909
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 892
          verb: watch
        requestCount: 892
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 195
          verb: delete
        - requestCount: 520
          verb: get
        - requestCount: 39
          verb: list
        - requestCount: 27
          verb: watch
        requestCount: 781
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 462
          verb: watch
        requestCount: 462
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 178
          verb: get
        - requestCount: 42
          verb: watch
        requestCount: 220
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 106
          verb: get
        - requestCount: 57
          verb: watch
        requestCount: 163
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 64
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 52
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 60
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      nodeName: **********
      requestCount: 6555
    requestCount: 13365
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2342
          verb: patch
        requestCount: 2342
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1308
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 1334
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 560
          verb: watch
        requestCount: 560
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 492
          verb: watch
        requestCount: 492
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 365
          verb: get
        - requestCount: 51
          verb: watch
        requestCount: 416
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 372
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: list
        - requestCount: 321
          verb: watch
        requestCount: 329
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 326
          verb: watch
        requestCount: 326
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 324
          verb: watch
        requestCount: 324
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 305
          verb: watch
        requestCount: 305
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6800
    - byUser:
      - byVerb:
        - requestCount: 17
          verb: get
        requestCount: 17
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      nodeName: **********
      requestCount: 56
    - byUser:
      - byVerb:
        - requestCount: 1620
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 40
          verb: watch
        requestCount: 1669
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1276
          verb: get
        - requestCount: 65
          verb: watch
        requestCount: 1341
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 909
          verb: watch
        requestCount: 909
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 225
          verb: delete
        - requestCount: 592
          verb: get
        - requestCount: 45
          verb: list
        - requestCount: 25
          verb: watch
        requestCount: 887
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 882
          verb: watch
        requestCount: 882
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 467
          verb: watch
        requestCount: 467
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 180
          verb: get
        - requestCount: 41
          verb: watch
        requestCount: 221
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 108
          verb: get
        - requestCount: 56
          verb: watch
        requestCount: 164
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 54
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 62
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 58
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: **********
      requestCount: 6660
    requestCount: 13516
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2342
          verb: patch
        requestCount: 2342
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1242
          verb: get
        - requestCount: 21
          verb: watch
        requestCount: 1263
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 541
          verb: watch
        requestCount: 541
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 492
          verb: watch
        requestCount: 492
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 48
          verb: watch
        requestCount: 408
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 377
          verb: watch
        requestCount: 377
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 330
          verb: watch
        requestCount: 330
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 8
          verb: list
        - requestCount: 316
          verb: watch
        requestCount: 324
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 315
          verb: watch
        requestCount: 315
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 293
          verb: watch
        requestCount: 293
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6685
    - byUser:
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      nodeName: **********
      requestCount: 62
    - byUser:
      - byVerb:
        - requestCount: 1590
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 39
          verb: watch
        requestCount: 1638
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1255
          verb: get
        - requestCount: 67
          verb: watch
        requestCount: 1322
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 914
          verb: watch
        requestCount: 914
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 908
          verb: watch
        requestCount: 908
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 210
          verb: delete
        - requestCount: 528
          verb: get
        - requestCount: 42
          verb: list
        - requestCount: 23
          verb: watch
        requestCount: 803
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 470
          verb: watch
        requestCount: 470
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 178
          verb: get
        - requestCount: 43
          verb: watch
        requestCount: 221
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 108
          verb: get
        - requestCount: 59
          verb: watch
        requestCount: 167
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 64
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 41
          verb: watch
        requestCount: 59
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: **********
      requestCount: 6566
    requestCount: 13313
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2342
          verb: patch
        requestCount: 2342
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1292
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 1318
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 543
          verb: watch
        requestCount: 543
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 491
          verb: watch
        requestCount: 491
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 370
          verb: get
        - requestCount: 47
          verb: watch
        requestCount: 417
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 375
          verb: watch
        requestCount: 375
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: list
        - requestCount: 326
          verb: watch
        requestCount: 334
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 327
          verb: watch
        requestCount: 327
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 325
          verb: watch
        requestCount: 325
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 300
          verb: watch
        requestCount: 300
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6772
    - byUser:
      - byVerb:
        - requestCount: 25
          verb: get
        requestCount: 25
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      nodeName: **********
      requestCount: 66
    - byUser:
      - byVerb:
        - requestCount: 1560
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 37
          verb: watch
        requestCount: 1606
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1255
          verb: get
        - requestCount: 61
          verb: watch
        requestCount: 1316
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 905
          verb: watch
        requestCount: 905
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 885
          verb: watch
        requestCount: 885
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 195
          verb: delete
        - requestCount: 478
          verb: get
        - requestCount: 39
          verb: list
        - requestCount: 25
          verb: watch
        requestCount: 737
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 455
          verb: watch
        requestCount: 455
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 184
          verb: get
        - requestCount: 39
          verb: watch
        requestCount: 223
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 110
          verb: get
        - requestCount: 54
          verb: watch
        requestCount: 164
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 42
          verb: watch
        requestCount: 66
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 37
          verb: watch
        requestCount: 55
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      nodeName: **********
      requestCount: 6412
    requestCount: 13250
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2342
          verb: patch
        requestCount: 2342
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-infra:serviceaccount-pull-secrets-controller
      - byVerb:
        - requestCount: 1218
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 1243
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 545
          verb: watch
        requestCount: 545
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 491
          verb: watch
        requestCount: 491
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 360
          verb: get
        - requestCount: 49
          verb: watch
        requestCount: 409
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 377
          verb: watch
        requestCount: 377
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 363
          verb: watch
        requestCount: 364
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 8
          verb: list
        - requestCount: 317
          verb: watch
        requestCount: 325
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 319
          verb: watch
        requestCount: 319
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 296
          verb: watch
        requestCount: 296
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: ***********
      requestCount: 6711
    - byUser:
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler:localhost-recovery-client
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:check-endpoints
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver:localhost-recovery-client
      nodeName: **********
      requestCount: 59
    - byUser:
      - byVerb:
        - requestCount: 1605
          verb: get
        - requestCount: 9
          verb: update
        - requestCount: 40
          verb: watch
        requestCount: 1654
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1262
          verb: get
        - requestCount: 67
          verb: watch
        requestCount: 1329
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 921
          verb: watch
        requestCount: 921
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 906
          verb: watch
        requestCount: 906
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 210
          verb: delete
        - requestCount: 530
          verb: get
        - requestCount: 42
          verb: list
        - requestCount: 24
          verb: watch
        requestCount: 806
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 462
          verb: watch
        requestCount: 462
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 176
          verb: get
        - requestCount: 38
          verb: watch
        requestCount: 214
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 107
          verb: get
        - requestCount: 57
          verb: watch
        requestCount: 164
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 42
          verb: watch
        requestCount: 66
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager:localhost-recovery-client
      - byVerb:
        - requestCount: 52
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 60
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      nodeName: **********
      requestCount: 6582
    requestCount: 13352
  requestCount: 302949
