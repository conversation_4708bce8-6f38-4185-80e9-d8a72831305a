---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T15:00:14Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T15:00:14Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:40:44Z"
  name: ingresses.v1.networking.k8s.io
  resourceVersion: "********"
  uid: 408682d2-898e-4ca7-af2d-aa711c5477d8
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 10
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 43
    requestCount: 53
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 14
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 67
    requestCount: 81
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 16
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 66
    requestCount: 82
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 16
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 66
    requestCount: 82
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 15
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 69
    requestCount: 84
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 16
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 28
          verb: watch
        requestCount: 28
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 63
    requestCount: 79
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 15
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 69
    requestCount: 84
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 17
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 67
    requestCount: 84
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 16
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 71
    requestCount: 87
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 15
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 65
    requestCount: 80
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 14
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 69
    requestCount: 83
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 17
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 67
    requestCount: 84
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 14
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 69
    requestCount: 83
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 17
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 66
    requestCount: 83
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 17
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 68
    requestCount: 85
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 10
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 43
    requestCount: 53
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 16
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 71
    requestCount: 87
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 17
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 71
    requestCount: 88
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 17
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 71
    requestCount: 88
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 15
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 66
    requestCount: 81
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 16
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 67
    requestCount: 83
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 15
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 68
    requestCount: 83
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 16
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 67
    requestCount: 83
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: route-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-route-controller-manager:route-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 15
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 69
    requestCount: 84
  requestCount: 1891
