---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T14:58:22Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:58:22Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:40:09Z"
  name: storageclasses.v1.storage.k8s.io
  resourceVersion: "********"
  uid: 84f3ec69-2bd8-407c-a317-08c10568bf16
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 42
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 47
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      nodeName: 10.9.212.10
      requestCount: 71
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 45
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 51
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 11
          verb: watch
        requestCount: 11
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 115
    requestCount: 195
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 77
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 118
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 180
    requestCount: 313
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 77
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      nodeName: 10.9.212.10
      requestCount: 121
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 177
    requestCount: 315
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 77
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 117
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 76
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 174
    requestCount: 308
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 116
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 79
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 181
    requestCount: 313
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 121
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 77
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 175
    requestCount: 313
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 76
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.10
      requestCount: 114
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 77
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 180
    requestCount: 310
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 120
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 77
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 175
    requestCount: 311
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 77
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.10
      requestCount: 117
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 76
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.9
      requestCount: 178
    requestCount: 310
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 120
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 176
    requestCount: 312
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 76
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.10
      requestCount: 116
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 13
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 179
    requestCount: 308
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 118
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 71
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 79
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 181
    requestCount: 315
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.10
      requestCount: 119
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 77
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 29
          verb: watch
        requestCount: 29
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 172
    requestCount: 308
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 77
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 116
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.9
      requestCount: 181
    requestCount: 313
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 78
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 120
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.9
      requestCount: 181
    requestCount: 318
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 42
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 47
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      nodeName: 10.9.212.10
      requestCount: 71
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 45
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 51
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 21
          verb: watch
        requestCount: 21
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 11
          verb: watch
        requestCount: 11
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 115
    requestCount: 195
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 77
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 126
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 79
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.9
      requestCount: 179
    requestCount: 313
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 79
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 128
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 76
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 171
    requestCount: 308
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 76
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 126
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 77
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 177
    requestCount: 311
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 77
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.10
      requestCount: 124
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 76
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 174
    requestCount: 305
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 129
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 77
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 181
    requestCount: 319
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 77
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.10
      requestCount: 128
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 77
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 174
    requestCount: 310
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 71
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 79
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 121
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.9
      requestCount: 181
    requestCount: 319
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 70
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 79
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 119
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 69
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 78
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 175
    requestCount: 310
  requestCount: 7057
