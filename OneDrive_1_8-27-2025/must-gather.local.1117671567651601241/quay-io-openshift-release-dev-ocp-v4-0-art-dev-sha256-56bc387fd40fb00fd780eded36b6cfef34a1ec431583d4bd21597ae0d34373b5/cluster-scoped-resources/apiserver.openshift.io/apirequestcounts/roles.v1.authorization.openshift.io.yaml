---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-08-21T16:59:10Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-08-21T16:59:10Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:04:18Z"
  name: roles.v1.authorization.openshift.io
  resourceVersion: "33947912"
  uid: 064e13de-7f19-428b-8d0f-46b15ef6d2c0
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  last24h:
  - byNode:
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 2
    requestCount: 2
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 2
    requestCount: 2
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 2
    requestCount: 2
  - byNode:
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  - byNode:
    - nodeName: 10.9.212.9
      requestCount: 0
    requestCount: 0
  requestCount: 6
