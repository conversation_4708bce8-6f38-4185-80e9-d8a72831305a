---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T14:58:09Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:58:09Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:41:36Z"
  name: persistentvolumes.v1
  resourceVersion: "33963851"
  uid: 02b0a5d3-48ad-4940-a619-a18abc399a4a
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: get
        requestCount: 33
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 32
          verb: get
        requestCount: 32
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 31
          verb: get
        requestCount: 31
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 31
          verb: get
        requestCount: 31
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 31
          verb: get
        requestCount: 31
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.10
      requestCount: 188
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 60
    requestCount: 256
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 46
          verb: get
        requestCount: 46
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 275
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 99
    requestCount: 390
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 46
          verb: get
        requestCount: 46
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.10
      requestCount: 277
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 101
    requestCount: 394
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 46
          verb: get
        requestCount: 46
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 46
          verb: get
        requestCount: 46
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 276
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 100
    requestCount: 392
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 50
          verb: get
        requestCount: 50
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 280
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      nodeName: 10.9.212.9
      requestCount: 98
    requestCount: 393
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 50
          verb: get
        requestCount: 50
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 49
          verb: get
        requestCount: 49
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 282
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: watch
        requestCount: 36
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 103
    requestCount: 401
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 49
          verb: get
        requestCount: 49
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 46
          verb: get
        requestCount: 46
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 277
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 105
    requestCount: 398
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 49
          verb: get
        requestCount: 49
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 279
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.9
      requestCount: 98
    requestCount: 393
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 46
          verb: get
        requestCount: 46
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 273
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 101
    requestCount: 389
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 281
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 15
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 101
    requestCount: 397
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 49
          verb: get
        requestCount: 49
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 46
          verb: get
        requestCount: 46
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.10
      requestCount: 276
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 98
    requestCount: 391
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 49
          verb: get
        requestCount: 49
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 278
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.9
      requestCount: 102
    requestCount: 397
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 49
          verb: get
        requestCount: 49
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 281
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 101
    requestCount: 398
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 49
          verb: get
        requestCount: 49
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 279
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 100
    requestCount: 395
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 50
          verb: get
        requestCount: 50
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 46
          verb: get
        requestCount: 46
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 278
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 17
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      nodeName: 10.9.212.9
      requestCount: 102
    requestCount: 397
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 33
          verb: get
        requestCount: 33
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 32
          verb: get
        requestCount: 32
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 31
          verb: get
        requestCount: 31
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 31
          verb: get
        requestCount: 31
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 31
          verb: get
        requestCount: 31
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.10
      requestCount: 188
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 60
    requestCount: 256
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 49
          verb: get
        requestCount: 49
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 49
          verb: get
        requestCount: 49
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.10
      requestCount: 287
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 99
    requestCount: 395
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 288
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 29
          verb: watch
        requestCount: 29
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 96
    requestCount: 392
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 49
          verb: get
        requestCount: 49
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 49
          verb: get
        requestCount: 49
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 289
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      nodeName: 10.9.212.9
      requestCount: 99
    requestCount: 396
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 287
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 103
    requestCount: 398
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 49
          verb: get
        requestCount: 49
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 46
          verb: get
        requestCount: 46
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 46
          verb: get
        requestCount: 46
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.10
      requestCount: 283
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 97
    requestCount: 388
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 49
          verb: get
        requestCount: 49
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 48
          verb: get
        requestCount: 48
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 288
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 95
    requestCount: 391
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 44
          verb: get
        requestCount: 44
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 43
          verb: get
        requestCount: 43
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 43
          verb: get
        requestCount: 43
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 42
          verb: get
        requestCount: 42
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 42
          verb: get
        requestCount: 42
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 254
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 18
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 96
    requestCount: 368
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 49
          verb: get
        requestCount: 49
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 47
          verb: get
        requestCount: 47
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 46
          verb: get
        requestCount: 46
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: csi-attacher/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.10
      requestCount: 275
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 16
    - byUser:
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-resizer/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-controller-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: metrics-agent/v0.0.0
        username: system:serviceaccount:cloudability:metrics-agent
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: csi-provisioner/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-controller-sa
      nodeName: 10.9.212.9
      requestCount: 103
    requestCount: 394
  requestCount: 8903
