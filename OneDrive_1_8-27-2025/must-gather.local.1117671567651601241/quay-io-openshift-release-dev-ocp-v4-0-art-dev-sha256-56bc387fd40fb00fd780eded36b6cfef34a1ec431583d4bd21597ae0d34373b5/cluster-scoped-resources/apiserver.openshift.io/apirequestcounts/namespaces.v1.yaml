---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T14:58:34Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:58:34Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:41:44Z"
  name: namespaces.v1
  resourceVersion: "********"
  uid: 2eb4f937-0f2f-4d1d-8ee8-742ac6d71ab6
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 549
          verb: get
        requestCount: 549
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 486
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 493
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 11
          verb: watch
        requestCount: 155
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 134
          verb: get
        - requestCount: 11
          verb: watch
        requestCount: 145
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 88
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 93
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 83
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 88
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 52
          verb: watch
        requestCount: 52
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 40
          verb: get
        requestCount: 40
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 1699
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: list
        requestCount: 30
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 22
          verb: get
        requestCount: 22
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 22
          verb: get
        requestCount: 22
        userAgent: oc/4.17.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 1
          verb: patch
        requestCount: 1
        userAgent: cluster-policy-controller/v0.0.0
        username: system:serviceaccount:openshift-infra:namespace-security-allocation-controller
      nodeName: 10.9.212.8
      requestCount: 100
    - byUser:
      - byVerb:
        - requestCount: 395
          verb: get
        - requestCount: 11
          verb: watch
        requestCount: 406
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 62
          verb: get
        - requestCount: 11
          verb: watch
        requestCount: 73
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 68
          verb: get
        - requestCount: 4
          verb: watch
        requestCount: 72
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 46
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 51
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: list
        requestCount: 18
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 17
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 4
          verb: update
        - requestCount: 5
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      nodeName: 10.9.212.9
      requestCount: 702
    requestCount: 2501
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: get
        requestCount: 860
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 724
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 732
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 212
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 227
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 200
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 216
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 132
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 140
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 128
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 135
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 81
          verb: watch
        requestCount: 81
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 47
          verb: watch
        requestCount: 47
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 2534
    - byUser:
      - byVerb:
        - requestCount: 48
          verb: list
        requestCount: 48
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 79
    - byUser:
      - byVerb:
        - requestCount: 625
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 640
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 106
          verb: get
        - requestCount: 1
          verb: list
        - requestCount: 6
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 120
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 101
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 116
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 81
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 28
          verb: get
        requestCount: 28
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 27
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.9
      requestCount: 1106
    requestCount: 3719
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 832
          verb: get
        requestCount: 832
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 664
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 672
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 224
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 190
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 205
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 132
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 141
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 123
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 131
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 79
          verb: watch
        requestCount: 79
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 49
          verb: watch
        requestCount: 49
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 34
          verb: list
        requestCount: 34
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 2427
    - byUser:
      - byVerb:
        - requestCount: 42
          verb: list
        requestCount: 42
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 2
          verb: watch
        requestCount: 2
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: 10.9.212.8
      requestCount: 73
    - byUser:
      - byVerb:
        - requestCount: 624
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 639
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 97
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 112
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 102
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 109
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 75
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 84
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 44
          verb: list
        requestCount: 44
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 26
          verb: get
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 25
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 4
          verb: update
        - requestCount: 5
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1097
    requestCount: 3597
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 845
          verb: get
        requestCount: 845
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 720
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 727
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 224
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 194
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 208
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 132
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 140
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 128
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 135
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 81
          verb: watch
        requestCount: 81
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 49
          verb: watch
        requestCount: 49
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 46
          verb: list
        requestCount: 46
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 2515
    - byUser:
      - byVerb:
        - requestCount: 34
          verb: list
        requestCount: 34
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: 10.9.212.8
      requestCount: 74
    - byUser:
      - byVerb:
        - requestCount: 625
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 642
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 99
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 115
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 104
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 113
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 82
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 40
          verb: list
        requestCount: 40
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: get
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 13
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1106
    requestCount: 3695
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: get
        requestCount: 860
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 670
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 678
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 225
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 196
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 212
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 132
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 141
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 128
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 137
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 78
          verb: watch
        requestCount: 78
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 49
          verb: watch
        requestCount: 49
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 34
          verb: list
        requestCount: 34
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 2474
    - byUser:
      - byVerb:
        - requestCount: 50
          verb: list
        requestCount: 50
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 91
    - byUser:
      - byVerb:
        - requestCount: 593
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 608
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 95
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 110
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 100
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 107
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 71
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 79
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 32
          verb: list
        requestCount: 32
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 29
          verb: watch
        requestCount: 29
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 26
          verb: get
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 16
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 24
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 4
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 12
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 1039
    requestCount: 3604
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 817
          verb: get
        requestCount: 817
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 712
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 720
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 223
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 188
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 203
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 132
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 140
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 122
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 129
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 86
          verb: watch
        requestCount: 86
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 47
          verb: watch
        requestCount: 47
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 2463
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      nodeName: 10.9.212.8
      requestCount: 75
    - byUser:
      - byVerb:
        - requestCount: 625
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 640
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 102
          verb: get
        - requestCount: 1
          verb: list
        - requestCount: 6
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 117
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 116
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 81
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 46
          verb: list
        requestCount: 46
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: get
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 14
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1114
    requestCount: 3652
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: get
        requestCount: 860
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 708
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 716
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 223
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 194
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 211
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 132
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 140
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 128
          verb: patch
        - requestCount: 10
          verb: watch
        requestCount: 138
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 75
          verb: watch
        requestCount: 75
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 49
          verb: watch
        requestCount: 49
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 2510
    - byUser:
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      nodeName: 10.9.212.8
      requestCount: 79
    - byUser:
      - byVerb:
        - requestCount: 625
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 641
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 114
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 102
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 110
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 83
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 44
          verb: list
        requestCount: 44
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 26
          verb: get
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 10
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 19
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      nodeName: 10.9.212.9
      requestCount: 1111
    requestCount: 3700
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 825
          verb: get
        requestCount: 825
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 536
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 544
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 224
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 196
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 210
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 132
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 139
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 123
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 131
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 79
          verb: watch
        requestCount: 79
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 47
          verb: watch
        requestCount: 47
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 28
          verb: list
        requestCount: 28
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 2287
    - byUser:
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 77
    - byUser:
      - byVerb:
        - requestCount: 626
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 641
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 99
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 115
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 104
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 111
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 82
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 54
          verb: list
        requestCount: 54
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: get
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 14
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1115
    requestCount: 3479
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 852
          verb: get
        requestCount: 852
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 670
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 677
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 212
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 230
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 194
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 211
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 132
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 140
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 127
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 135
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 82
          verb: watch
        requestCount: 82
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 52
          verb: list
        requestCount: 52
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 50
          verb: watch
        requestCount: 50
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      nodeName: 10.9.212.10
      requestCount: 2489
    - byUser:
      - byVerb:
        - requestCount: 34
          verb: list
        requestCount: 34
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      nodeName: 10.9.212.8
      requestCount: 74
    - byUser:
      - byVerb:
        - requestCount: 625
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 641
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 100
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 117
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 105
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 113
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 81
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 34
          verb: list
        requestCount: 34
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 29
          verb: get
        requestCount: 29
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 4
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 12
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1103
    requestCount: 3666
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 858
          verb: get
        requestCount: 858
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 684
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 692
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 224
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 196
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 213
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 132
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 140
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 128
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 135
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 79
          verb: watch
        requestCount: 79
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 49
          verb: watch
        requestCount: 49
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 48
          verb: list
        requestCount: 48
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 2498
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 75
    - byUser:
      - byVerb:
        - requestCount: 622
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 637
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 103
          verb: get
        - requestCount: 1
          verb: list
        - requestCount: 6
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 119
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 115
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 82
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 26
          verb: get
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 25
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 6
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1095
    requestCount: 3668
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 819
          verb: get
        requestCount: 819
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 638
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 646
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 222
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 194
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 211
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 132
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 140
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 122
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 130
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 77
          verb: watch
        requestCount: 77
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 46
          verb: watch
        requestCount: 46
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 32
          verb: list
        requestCount: 32
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 2383
    - byUser:
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      nodeName: 10.9.212.8
      requestCount: 78
    - byUser:
      - byVerb:
        - requestCount: 625
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 642
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 100
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 116
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 104
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 112
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 81
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 48
          verb: list
        requestCount: 48
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: get
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 4
          verb: update
        - requestCount: 6
          verb: watch
        requestCount: 10
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1110
    requestCount: 3571
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: get
        requestCount: 860
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 642
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 649
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 224
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 194
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 209
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 133
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 141
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 128
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 136
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 80
          verb: watch
        requestCount: 80
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 61
          verb: get
        requestCount: 61
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 48
          verb: watch
        requestCount: 48
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 40
          verb: list
        requestCount: 40
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 2448
    - byUser:
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: 10.9.212.8
      requestCount: 78
    - byUser:
      - byVerb:
        - requestCount: 621
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 638
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 99
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 115
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 105
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 112
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 82
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 42
          verb: list
        requestCount: 42
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 26
          verb: get
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 1099
    requestCount: 3625
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 817
          verb: get
        requestCount: 817
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 712
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 720
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 206
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 222
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 196
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 211
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 132
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 140
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 129
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 138
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 82
          verb: watch
        requestCount: 82
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 46
          verb: watch
        requestCount: 46
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 28
          verb: list
        requestCount: 28
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 2464
    - byUser:
      - byVerb:
        - requestCount: 44
          verb: list
        requestCount: 44
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 85
    - byUser:
      - byVerb:
        - requestCount: 589
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 604
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 93
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 108
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 97
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 105
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 71
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 79
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 44
          verb: list
        requestCount: 44
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 24
          verb: get
        requestCount: 24
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 16
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 23
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 4
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 12
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1043
    requestCount: 3592
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: get
        requestCount: 860
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 676
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 684
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 222
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 194
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 209
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 132
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 140
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 122
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 131
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 80
          verb: watch
        requestCount: 80
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 47
          verb: watch
        requestCount: 47
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 34
          verb: list
        requestCount: 34
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 2467
    - byUser:
      - byVerb:
        - requestCount: 44
          verb: list
        requestCount: 44
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: 10.9.212.8
      requestCount: 86
    - byUser:
      - byVerb:
        - requestCount: 625
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 639
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 104
          verb: get
        - requestCount: 1
          verb: list
        - requestCount: 6
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 118
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 114
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 75
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 83
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 42
          verb: list
        requestCount: 42
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 27
          verb: get
        requestCount: 27
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 14
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1112
    requestCount: 3665
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 847
          verb: get
        requestCount: 847
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 686
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 693
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 226
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 194
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 212
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 132
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 141
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 128
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 136
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 79
          verb: watch
        requestCount: 79
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 52
          verb: list
        requestCount: 52
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 47
          verb: watch
        requestCount: 47
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      nodeName: 10.9.212.10
      requestCount: 2493
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      nodeName: 10.9.212.8
      requestCount: 75
    - byUser:
      - byVerb:
        - requestCount: 623
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 637
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 114
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 104
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 113
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 81
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 32
          verb: list
        requestCount: 32
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: get
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 14
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1089
    requestCount: 3657
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 549
          verb: get
        requestCount: 549
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 486
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 493
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 11
          verb: watch
        requestCount: 155
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 134
          verb: get
        - requestCount: 11
          verb: watch
        requestCount: 145
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 88
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 93
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 83
          verb: patch
        - requestCount: 5
          verb: watch
        requestCount: 88
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 52
          verb: watch
        requestCount: 52
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 40
          verb: get
        requestCount: 40
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 26
          verb: list
        requestCount: 26
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 1699
    - byUser:
      - byVerb:
        - requestCount: 30
          verb: list
        requestCount: 30
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 22
          verb: get
        requestCount: 22
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 22
          verb: get
        requestCount: 22
        userAgent: oc/4.17.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 1
          verb: patch
        requestCount: 1
        userAgent: cluster-policy-controller/v0.0.0
        username: system:serviceaccount:openshift-infra:namespace-security-allocation-controller
      nodeName: 10.9.212.8
      requestCount: 100
    - byUser:
      - byVerb:
        - requestCount: 395
          verb: get
        - requestCount: 11
          verb: watch
        requestCount: 406
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 62
          verb: get
        - requestCount: 11
          verb: watch
        requestCount: 73
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 68
          verb: get
        - requestCount: 4
          verb: watch
        requestCount: 72
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 46
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 51
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: list
        requestCount: 18
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 17
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 4
          verb: update
        - requestCount: 5
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      nodeName: 10.9.212.9
      requestCount: 702
    requestCount: 2501
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 832
          verb: get
        requestCount: 832
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 648
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 656
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 218
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 233
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 204
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 221
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 130
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 138
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 128
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 136
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 82
          verb: watch
        requestCount: 82
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 47
          verb: watch
        requestCount: 47
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 2443
    - byUser:
      - byVerb:
        - requestCount: 42
          verb: list
        requestCount: 42
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 1
          verb: patch
        requestCount: 1
        userAgent: cluster-policy-controller/v0.0.0
        username: system:serviceaccount:openshift-infra:namespace-security-allocation-controller
      - byVerb:
        - requestCount: 1
          verb: delete
        requestCount: 1
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 61
    - byUser:
      - byVerb:
        - requestCount: 623
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 639
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 103
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 120
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 104
          verb: get
        - requestCount: 1
          verb: list
        - requestCount: 6
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 120
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 74
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 82
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 40
          verb: list
        requestCount: 40
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 32
          verb: get
        requestCount: 32
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: update
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1121
    requestCount: 3625
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: get
        requestCount: 860
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 676
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 684
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 216
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 231
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 198
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 214
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 131
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 140
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 122
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 130
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 81
          verb: watch
        requestCount: 81
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 49
          verb: watch
        requestCount: 49
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 2487
    - byUser:
      - byVerb:
        - requestCount: 40
          verb: list
        requestCount: 40
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 1
          verb: delete
        requestCount: 1
        userAgent: Mozilla/5.0
        username: M0681
      - byVerb:
        - requestCount: 1
          verb: patch
        requestCount: 1
        userAgent: cluster-policy-controller/v0.0.0
        username: system:serviceaccount:openshift-infra:namespace-security-allocation-controller
      nodeName: 10.9.212.8
      requestCount: 58
    - byUser:
      - byVerb:
        - requestCount: 619
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 635
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 104
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 120
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 103
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 110
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 81
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 42
          verb: list
        requestCount: 42
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 30
          verb: get
        requestCount: 30
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 4
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 12
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1102
    requestCount: 3647
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 817
          verb: get
        requestCount: 817
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 682
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 689
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 216
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 231
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 200
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 216
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 132
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 140
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 128
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 137
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 78
          verb: watch
        requestCount: 78
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 49
          verb: watch
        requestCount: 49
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 44
          verb: list
        requestCount: 44
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 2461
    - byUser:
      - byVerb:
        - requestCount: 36
          verb: list
        requestCount: 36
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: patch
        requestCount: 1
        userAgent: cluster-policy-controller/v0.0.0
        username: system:serviceaccount:openshift-infra:namespace-security-allocation-controller
      nodeName: 10.9.212.8
      requestCount: 53
    - byUser:
      - byVerb:
        - requestCount: 624
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 640
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 102
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 118
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 102
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 110
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 40
          verb: list
        requestCount: 40
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 30
          verb: get
        requestCount: 30
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 25
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 14
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1105
    requestCount: 3619
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: get
        requestCount: 860
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 580
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 589
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 206
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 224
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 192
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 206
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 132
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 140
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 127
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 135
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 84
          verb: watch
        requestCount: 84
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 48
          verb: watch
        requestCount: 48
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 32
          verb: list
        requestCount: 32
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 2378
    - byUser:
      - byVerb:
        - requestCount: 42
          verb: list
        requestCount: 42
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 3
          verb: watch
        requestCount: 3
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      nodeName: 10.9.212.8
      requestCount: 61
    - byUser:
      - byVerb:
        - requestCount: 626
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 642
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 112
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 97
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 106
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 82
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 46
          verb: list
        requestCount: 46
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 26
          verb: get
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 14
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1101
    requestCount: 3540
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 837
          verb: get
        requestCount: 837
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 602
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 610
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 222
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 192
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 208
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 130
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 138
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 123
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 130
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 79
          verb: watch
        requestCount: 79
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 46
          verb: watch
        requestCount: 46
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 32
          verb: list
        requestCount: 32
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 2362
    - byUser:
      - byVerb:
        - requestCount: 54
          verb: list
        requestCount: 54
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 78
    - byUser:
      - byVerb:
        - requestCount: 621
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 637
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 103
          verb: get
        - requestCount: 1
          verb: list
        - requestCount: 6
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 118
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 99
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 114
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 82
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 34
          verb: list
        requestCount: 34
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 27
          verb: get
        requestCount: 27
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 25
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 6
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 14
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1098
    requestCount: 3538
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 840
          verb: get
        requestCount: 840
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 604
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 612
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 224
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 196
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 213
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 132
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 139
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 128
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 136
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 79
          verb: watch
        requestCount: 79
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 48
          verb: watch
        requestCount: 48
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 2389
    - byUser:
      - byVerb:
        - requestCount: 38
          verb: list
        requestCount: 38
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      nodeName: 10.9.212.8
      requestCount: 62
    - byUser:
      - byVerb:
        - requestCount: 623
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 640
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 115
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 100
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 109
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 72
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 80
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 44
          verb: list
        requestCount: 44
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 26
          verb: get
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 10
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 18
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      nodeName: 10.9.212.9
      requestCount: 1104
    requestCount: 3555
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 860
          verb: get
        requestCount: 860
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 672
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 680
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 224
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 194
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 211
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 133
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 141
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 122
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 129
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 81
          verb: watch
        requestCount: 81
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 61
          verb: get
        requestCount: 61
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 50
          verb: list
        requestCount: 50
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 48
          verb: watch
        requestCount: 48
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      nodeName: 10.9.212.10
      requestCount: 2485
    - byUser:
      - byVerb:
        - requestCount: 28
          verb: list
        requestCount: 28
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 61
    - byUser:
      - byVerb:
        - requestCount: 627
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 642
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 113
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 101
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 109
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 80
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 42
          verb: list
        requestCount: 42
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: get
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus2-kx2t4/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus2-kx2t4
      nodeName: 10.9.212.9
      requestCount: 1093
    requestCount: 3639
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 817
          verb: get
        requestCount: 817
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 650
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 657
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 225
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 192
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 209
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 131
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 139
        userAgent: cluster-openshift-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-apiserver-operator:openshift-apiserver-operator
      - byVerb:
        - requestCount: 128
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 136
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 78
          verb: watch
        requestCount: 78
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 60
          verb: get
        requestCount: 60
        userAgent: cluster-kube-storage-version-migrator-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-storage-version-migrator-operator:kube-storage-version-migrator-operator
      - byVerb:
        - requestCount: 50
          verb: watch
        requestCount: 50
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 42
          verb: list
        requestCount: 42
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.10
      requestCount: 2413
    - byUser:
      - byVerb:
        - requestCount: 48
          verb: list
        requestCount: 48
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 78
    - byUser:
      - byVerb:
        - requestCount: 625
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 642
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 114
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 100
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 109
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 73
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 81
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 30
          verb: list
        requestCount: 30
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 26
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 26
          verb: get
        requestCount: 26
        userAgent: cluster-image-registry-operator/v0.0.0
        username: system:serviceaccount:openshift-image-registry:cluster-image-registry-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: coredns/v0.0.0
        username: system:serviceaccount:openshift-dns:dns
      - byVerb:
        - requestCount: 4
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 11
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1086
    requestCount: 3577
  requestCount: 82131
