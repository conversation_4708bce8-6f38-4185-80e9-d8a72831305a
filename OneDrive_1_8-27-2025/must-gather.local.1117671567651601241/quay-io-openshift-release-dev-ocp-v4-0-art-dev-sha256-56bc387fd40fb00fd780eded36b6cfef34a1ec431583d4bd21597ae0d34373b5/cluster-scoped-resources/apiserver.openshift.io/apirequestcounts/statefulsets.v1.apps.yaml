---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T15:00:54Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T15:00:54Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:42:07Z"
  name: statefulsets.v1.apps
  resourceVersion: "********"
  uid: afb3108b-9f02-48ea-87cf-4fa6539f6a8c
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 872
    - byUser:
      - byVerb:
        - requestCount: 649
          verb: list
        requestCount: 649
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 654
    - byUser:
      - byVerb:
        - requestCount: 649
          verb: list
        requestCount: 649
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 4
          verb: watch
        requestCount: 6
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 699
    requestCount: 2225
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1180
          verb: list
        requestCount: 1180
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: oc/4.17.0
        username: system:serviceaccount:openshift-image-registry:pruner
      nodeName: 10.9.212.10
      requestCount: 1245
    - byUser:
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 833
    - byUser:
      - byVerb:
        - requestCount: 1475
          verb: list
        requestCount: 1475
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 29
          verb: watch
        requestCount: 29
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 1548
    requestCount: 3626
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1357
          verb: list
        requestCount: 1357
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 1420
    - byUser:
      - byVerb:
        - requestCount: 885
          verb: list
        requestCount: 885
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 892
    - byUser:
      - byVerb:
        - requestCount: 1239
          verb: list
        requestCount: 1239
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1319
    requestCount: 3631
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1357
          verb: list
        requestCount: 1357
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 1419
    - byUser:
      - byVerb:
        - requestCount: 1239
          verb: list
        requestCount: 1239
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 1249
    - byUser:
      - byVerb:
        - requestCount: 944
          verb: list
        requestCount: 944
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 11
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1022
    requestCount: 3690
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1180
          verb: list
        requestCount: 1180
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 1241
    - byUser:
      - byVerb:
        - requestCount: 944
          verb: list
        requestCount: 944
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 952
    - byUser:
      - byVerb:
        - requestCount: 1416
          verb: list
        requestCount: 1416
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1492
    requestCount: 3685
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1416
          verb: list
        requestCount: 1416
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.10
      requestCount: 1479
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 1128
    - byUser:
      - byVerb:
        - requestCount: 1003
          verb: list
        requestCount: 1003
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 12
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1082
    requestCount: 3689
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1180
          verb: list
        requestCount: 1180
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 1245
    - byUser:
      - byVerb:
        - requestCount: 1180
          verb: list
        requestCount: 1180
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 1189
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 5
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 13
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1197
    requestCount: 3631
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1475
          verb: list
        requestCount: 1475
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.10
      requestCount: 1537
    - byUser:
      - byVerb:
        - requestCount: 944
          verb: list
        requestCount: 944
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 952
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 11
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1198
    requestCount: 3687
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 1186
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 1306
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1199
    requestCount: 3691
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 767
          verb: list
        requestCount: 767
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 833
    - byUser:
      - byVerb:
        - requestCount: 1357
          verb: list
        requestCount: 1357
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 1365
    - byUser:
      - byVerb:
        - requestCount: 1416
          verb: list
        requestCount: 1416
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 1
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 10
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 1493
    requestCount: 3691
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 26
          verb: watch
        requestCount: 26
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 1186
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 1306
    - byUser:
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1139
    requestCount: 3631
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 26
          verb: watch
        requestCount: 26
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 1187
    - byUser:
      - byVerb:
        - requestCount: 1239
          verb: list
        requestCount: 1239
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 1247
    - byUser:
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1136
    requestCount: 3570
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1239
          verb: list
        requestCount: 1239
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 22
          verb: watch
        requestCount: 22
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 1298
    - byUser:
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 1070
    - byUser:
      - byVerb:
        - requestCount: 1239
          verb: list
        requestCount: 1239
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 11
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1319
    requestCount: 3687
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 944
          verb: list
        requestCount: 944
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 26
          verb: watch
        requestCount: 26
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.10
      requestCount: 1012
    - byUser:
      - byVerb:
        - requestCount: 1534
          verb: list
        requestCount: 1534
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 1541
    - byUser:
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 10
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1141
    requestCount: 3694
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.10
      requestCount: 1364
    - byUser:
      - byVerb:
        - requestCount: 1416
          verb: list
        requestCount: 1416
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 1423
    - byUser:
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 12
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 908
    requestCount: 3695
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 5
          verb: list
        requestCount: 5
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 872
    - byUser:
      - byVerb:
        - requestCount: 649
          verb: list
        requestCount: 649
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 654
    - byUser:
      - byVerb:
        - requestCount: 649
          verb: list
        requestCount: 649
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 20
          verb: watch
        requestCount: 20
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 4
          verb: watch
        requestCount: 6
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 699
    requestCount: 2225
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1357
          verb: list
        requestCount: 1357
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 1430
    - byUser:
      - byVerb:
        - requestCount: 1239
          verb: list
        requestCount: 1239
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1239
    - byUser:
      - byVerb:
        - requestCount: 944
          verb: list
        requestCount: 944
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 11
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 1025
    requestCount: 3694
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      nodeName: 10.9.212.10
      requestCount: 1194
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1298
    - byUser:
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 1140
    requestCount: 3632
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1180
          verb: list
        requestCount: 1180
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 1254
    - byUser:
      - byVerb:
        - requestCount: 1416
          verb: list
        requestCount: 1416
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1416
    - byUser:
      - byVerb:
        - requestCount: 944
          verb: list
        requestCount: 944
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 11
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 1
          verb: deletecollection
        - requestCount: 1
          verb: list
        requestCount: 2
        userAgent: kube-controller-manager/v1.30.12
        username: system:serviceaccount:kube-system:namespace-controller
      nodeName: 10.9.212.9
      requestCount: 1024
    requestCount: 3694
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 1373
    - byUser:
      - byVerb:
        - requestCount: 767
          verb: list
        requestCount: 767
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 767
    - byUser:
      - byVerb:
        - requestCount: 1475
          verb: list
        requestCount: 1475
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 11
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1555
    requestCount: 3695
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 944
          verb: list
        requestCount: 944
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 25
          verb: watch
        requestCount: 25
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 1017
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1298
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 10
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 1377
    requestCount: 3692
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1003
          verb: list
        requestCount: 1003
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 26
          verb: watch
        requestCount: 26
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      nodeName: 10.9.212.10
      requestCount: 1079
    - byUser:
      - byVerb:
        - requestCount: 1475
          verb: list
        requestCount: 1475
        userAgent: aro/v0.0.0
        username: system:aro-service
      nodeName: 10.9.212.8
      requestCount: 1475
    - byUser:
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 5
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 13
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.9
      requestCount: 1142
    requestCount: 3696
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 24
          verb: watch
        requestCount: 24
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      nodeName: 10.9.212.10
      requestCount: 1127
    - byUser:
      - byVerb:
        - requestCount: 1711
          verb: list
        requestCount: 1711
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 1720
    - byUser:
      - byVerb:
        - requestCount: 767
          verb: list
        requestCount: 767
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 842
    requestCount: 3689
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1180
          verb: list
        requestCount: 1180
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 23
          verb: watch
        requestCount: 23
        userAgent: PrometheusOperator/0.75.2
        username: system:serviceaccount:openshift-monitoring:prometheus-operator
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-state-metrics/v
        username: system:serviceaccount:openshift-monitoring:kube-state-metrics
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-controller-manager/v0.0.0
        username: system:serviceaccount:openshift-controller-manager:openshift-controller-manager-sa
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-autoscaler/v0.0.0
        username: system:serviceaccount:openshift-machine-api:cluster-autoscaler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      nodeName: 10.9.212.10
      requestCount: 1241
    - byUser:
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 1069
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 11
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1377
    requestCount: 3687
  requestCount: 83002
