---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T14:59:26Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T14:59:26Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:41:37Z"
  name: pods.v1
  resourceVersion: "********"
  uid: 97266c2f-891b-4044-ba94-c866c8d09790
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 816
          verb: get
        - requestCount: 48
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 869
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 494
          verb: watch
        requestCount: 494
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 15
          verb: delete
        - requestCount: 178
          verb: get
        - requestCount: 132
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 331
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 13
          verb: create
        - requestCount: 115
          verb: get
        - requestCount: 8
          verb: list
        requestCount: 136
        userAgent: oc/4.17.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 17
          verb: update
        - requestCount: 39
          verb: watch
        requestCount: 73
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 15
          verb: create
        - requestCount: 29
          verb: delete
        - requestCount: 8
          verb: watch
        requestCount: 52
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 40
          verb: watch
        requestCount: 48
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 1
          verb: update
        - requestCount: 40
          verb: watch
        requestCount: 42
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 1
          verb: update
        - requestCount: 39
          verb: watch
        requestCount: 41
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus2-dgjh4
      nodeName: 10.9.212.10
      requestCount: 3201
    - byUser:
      - byVerb:
        - requestCount: 58
          verb: create
        - requestCount: 638
          verb: get
        - requestCount: 25
          verb: list
        requestCount: 721
        userAgent: oc/4.17.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 590
          verb: list
        requestCount: 590
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: update
        - requestCount: 42
          verb: watch
        requestCount: 45
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: update
        - requestCount: 41
          verb: watch
        requestCount: 44
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 14
          verb: get
        - requestCount: 10
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 30
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 21
          verb: list
        requestCount: 21
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 11
          verb: create
        requestCount: 11
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: update
        - requestCount: 6
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 1
          verb: update
        - requestCount: 5
          verb: watch
        requestCount: 6
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: 10.9.212.8
      requestCount: 1501
    - byUser:
      - byVerb:
        - requestCount: 885
          verb: list
        requestCount: 885
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 804
          verb: get
        - requestCount: 54
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 863
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 786
          verb: get
        - requestCount: 46
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 838
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 786
          verb: get
        - requestCount: 46
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 837
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 474
          verb: watch
        requestCount: 491
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 99
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 108
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 38
          verb: watch
        requestCount: 38
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 38
          verb: watch
        requestCount: 38
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 38
          verb: watch
        requestCount: 38
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 38
          verb: watch
        requestCount: 38
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-c4ssz
      nodeName: 10.9.212.9
      requestCount: 4200
    requestCount: 8902
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1357
          verb: list
        requestCount: 1357
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1218
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 1297
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 748
          verb: watch
        requestCount: 748
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 21
          verb: delete
        - requestCount: 228
          verb: get
        - requestCount: 164
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 421
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 21
          verb: get
        - requestCount: 21
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 102
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 21
          verb: create
        - requestCount: 41
          verb: delete
        - requestCount: 17
          verb: watch
        requestCount: 79
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 68
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 49
          verb: watch
        requestCount: 49
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 25
          verb: get
        - requestCount: 9
          verb: list
        requestCount: 34
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 4215
    - byUser:
      - byVerb:
        - requestCount: 767
          verb: list
        requestCount: 767
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 20
          verb: create
        requestCount: 20
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 4
          verb: get
        requestCount: 4
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 898
    - byUser:
      - byVerb:
        - requestCount: 1416
          verb: list
        requestCount: 1416
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1263
          verb: get
        - requestCount: 84
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1355
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1218
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1298
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 1218
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1298
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 25
          verb: get
        - requestCount: 750
          verb: watch
        requestCount: 775
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 156
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 171
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 2
          verb: list
        - requestCount: 62
          verb: watch
        requestCount: 64
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 61
          verb: watch
        requestCount: 62
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 1
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 62
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: 10.9.212.9
      requestCount: 6561
    requestCount: 11674
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1357
          verb: list
        requestCount: 1357
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1230
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1311
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 758
          verb: watch
        requestCount: 758
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 22
          verb: delete
        - requestCount: 233
          verb: get
        - requestCount: 168
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 430
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 22
          verb: get
        - requestCount: 22
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 104
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 22
          verb: create
        - requestCount: 38
          verb: delete
        - requestCount: 15
          verb: watch
        requestCount: 75
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 68
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 58
          verb: watch
        requestCount: 58
        userAgent: network-metrics/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 50
          verb: watch
        requestCount: 50
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      nodeName: 10.9.212.10
      requestCount: 4271
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 49
          verb: watch
        requestCount: 49
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 21
          verb: create
        requestCount: 21
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      nodeName: 10.9.212.8
      requestCount: 1477
    - byUser:
      - byVerb:
        - requestCount: 1260
          verb: get
        - requestCount: 84
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 1351
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1236
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1317
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1227
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1307
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 885
          verb: list
        requestCount: 885
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 27
          verb: get
        - requestCount: 750
          verb: watch
        requestCount: 777
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 156
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 171
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 58
          verb: watch
        requestCount: 59
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-1
      nodeName: 10.9.212.9
      requestCount: 6047
    requestCount: 11795
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1242
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1322
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 749
          verb: watch
        requestCount: 749
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 649
          verb: list
        requestCount: 649
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 21
          verb: delete
        - requestCount: 225
          verb: get
        - requestCount: 161
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 416
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 21
          verb: get
        - requestCount: 21
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 102
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 21
          verb: create
        - requestCount: 41
          verb: delete
        - requestCount: 16
          verb: watch
        requestCount: 78
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 68
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 58
          verb: watch
        requestCount: 58
        userAgent: network-metrics/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 25
          verb: get
        - requestCount: 9
          verb: list
        requestCount: 34
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 3536
    - byUser:
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 59
          verb: watch
        requestCount: 59
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 18
          verb: create
        requestCount: 18
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 1481
    - byUser:
      - byVerb:
        - requestCount: 1534
          verb: list
        requestCount: 1534
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1272
          verb: get
        - requestCount: 84
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1364
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1242
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1322
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 1242
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 1321
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 25
          verb: get
        - requestCount: 753
          verb: watch
        requestCount: 778
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 156
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 171
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 61
          verb: watch
        requestCount: 62
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 61
          verb: watch
        requestCount: 62
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-kx2t4
      nodeName: 10.9.212.9
      requestCount: 6734
    requestCount: 11751
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1357
          verb: list
        requestCount: 1357
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1248
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1329
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 750
          verb: watch
        requestCount: 750
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 22
          verb: delete
        - requestCount: 240
          verb: get
        - requestCount: 173
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 443
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 22
          verb: get
        - requestCount: 22
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 104
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 22
          verb: create
        - requestCount: 37
          verb: delete
        - requestCount: 16
          verb: watch
        requestCount: 75
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 68
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 57
          verb: watch
        requestCount: 57
        userAgent: network-metrics/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 46
          verb: watch
        requestCount: 46
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      nodeName: 10.9.212.10
      requestCount: 4289
    - byUser:
      - byVerb:
        - requestCount: 1003
          verb: list
        requestCount: 1003
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 59
          verb: watch
        requestCount: 59
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 22
          verb: create
        requestCount: 22
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 5
          verb: get
        - requestCount: 2
          verb: watch
        requestCount: 7
        userAgent: Mozilla/5.0
        username: M0681
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      nodeName: 10.9.212.8
      requestCount: 1189
    - byUser:
      - byVerb:
        - requestCount: 1284
          verb: get
        - requestCount: 84
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 1375
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1254
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1335
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1245
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 1324
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 28
          verb: get
        - requestCount: 751
          verb: watch
        requestCount: 779
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 156
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 172
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 2
          verb: list
        - requestCount: 62
          verb: watch
        requestCount: 64
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.9
      requestCount: 6350
    requestCount: 11828
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1416
          verb: list
        requestCount: 1416
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1218
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1298
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 754
          verb: watch
        requestCount: 754
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 21
          verb: delete
        - requestCount: 229
          verb: get
        - requestCount: 166
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 424
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 21
          verb: get
        - requestCount: 21
          verb: update
        - requestCount: 61
          verb: watch
        requestCount: 103
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 21
          verb: create
        - requestCount: 41
          verb: delete
        - requestCount: 15
          verb: watch
        requestCount: 77
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 68
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 56
          verb: watch
        requestCount: 56
        userAgent: network-metrics/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 25
          verb: get
        - requestCount: 9
          verb: list
        requestCount: 34
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 4290
    - byUser:
      - byVerb:
        - requestCount: 1003
          verb: list
        requestCount: 1003
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 59
          verb: watch
        requestCount: 59
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 25
          verb: create
        requestCount: 25
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      nodeName: 10.9.212.8
      requestCount: 1195
    - byUser:
      - byVerb:
        - requestCount: 1248
          verb: get
        - requestCount: 84
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1340
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1218
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1298
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1215
          verb: get
        - requestCount: 71
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1295
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 25
          verb: get
        - requestCount: 751
          verb: watch
        requestCount: 776
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 153
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 170
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 3
          verb: list
        - requestCount: 63
          verb: watch
        requestCount: 66
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: 10.9.212.9
      requestCount: 6246
    requestCount: 11731
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1475
          verb: list
        requestCount: 1475
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1230
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1310
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 756
          verb: watch
        requestCount: 756
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 21
          verb: delete
        - requestCount: 224
          verb: get
        - requestCount: 160
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 413
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 21
          verb: get
        - requestCount: 21
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 102
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 21
          verb: create
        - requestCount: 40
          verb: delete
        - requestCount: 2
          verb: get
        - requestCount: 2
          verb: patch
        - requestCount: 15
          verb: watch
        requestCount: 80
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 68
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 56
          verb: watch
        requestCount: 56
        userAgent: network-metrics/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 48
          verb: watch
        requestCount: 48
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      nodeName: 10.9.212.10
      requestCount: 4368
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 14
          verb: create
        requestCount: 14
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      nodeName: 10.9.212.8
      requestCount: 1300
    - byUser:
      - byVerb:
        - requestCount: 1260
          verb: get
        - requestCount: 84
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1352
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1230
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1311
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1224
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1305
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 944
          verb: list
        requestCount: 944
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 27
          verb: get
        - requestCount: 751
          verb: watch
        requestCount: 778
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 156
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 174
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-kx2t4
      nodeName: 10.9.212.9
      requestCount: 6104
    requestCount: 11772
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1236
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1316
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 767
          verb: list
        requestCount: 767
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 749
          verb: watch
        requestCount: 749
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 18
          verb: delete
        - requestCount: 187
          verb: get
        - requestCount: 132
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 344
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 18
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 96
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 68
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 18
          verb: create
        - requestCount: 32
          verb: delete
        - requestCount: 16
          verb: watch
        requestCount: 66
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 56
          verb: watch
        requestCount: 56
        userAgent: network-metrics/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 25
          verb: get
        - requestCount: 9
          verb: list
        requestCount: 34
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 3556
    - byUser:
      - byVerb:
        - requestCount: 1534
          verb: list
        requestCount: 1534
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 17
          verb: create
        requestCount: 17
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      nodeName: 10.9.212.8
      requestCount: 1720
    - byUser:
      - byVerb:
        - requestCount: 1272
          verb: get
        - requestCount: 84
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1364
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1242
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1322
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1242
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1322
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 1239
          verb: list
        requestCount: 1239
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 23
          verb: get
        - requestCount: 752
          verb: watch
        requestCount: 775
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 156
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 172
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 61
          verb: watch
        requestCount: 61
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-kx2t4
      nodeName: 10.9.212.9
      requestCount: 6435
    requestCount: 11711
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1475
          verb: list
        requestCount: 1475
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1260
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1341
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 764
          verb: watch
        requestCount: 764
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 19
          verb: delete
        - requestCount: 201
          verb: get
        - requestCount: 144
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 19
          verb: get
        - requestCount: 19
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 98
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 19
          verb: create
        - requestCount: 35
          verb: delete
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: patch
        - requestCount: 16
          verb: watch
        requestCount: 78
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 5
          verb: get
        - requestCount: 5
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 70
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 56
          verb: watch
        requestCount: 56
        userAgent: network-metrics/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 18
          verb: get
        - requestCount: 14
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 38
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-worker-centralus1-l4mv5
      nodeName: 10.9.212.10
      requestCount: 4352
    - byUser:
      - byVerb:
        - requestCount: 1003
          verb: list
        requestCount: 1003
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 59
          verb: watch
        requestCount: 59
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 22
          verb: create
        requestCount: 22
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      nodeName: 10.9.212.8
      requestCount: 1189
    - byUser:
      - byVerb:
        - requestCount: 1296
          verb: get
        - requestCount: 84
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1388
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1260
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 1339
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 1260
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 1339
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 752
          verb: watch
        requestCount: 776
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 180
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 196
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 61
          verb: watch
        requestCount: 62
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-w7f8z
      nodeName: 10.9.212.9
      requestCount: 6342
    requestCount: 11883
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1212
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1293
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 885
          verb: list
        requestCount: 885
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 762
          verb: watch
        requestCount: 762
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 23
          verb: delete
        - requestCount: 242
          verb: get
        - requestCount: 173
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 446
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 23
          verb: get
        - requestCount: 23
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 106
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 23
          verb: create
        - requestCount: 40
          verb: delete
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: patch
        - requestCount: 16
          verb: watch
        requestCount: 87
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 68
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 55
          verb: watch
        requestCount: 55
        userAgent: network-metrics/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 25
          verb: get
        - requestCount: 10
          verb: list
        requestCount: 35
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 3797
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 19
          verb: create
        requestCount: 19
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: 10.9.212.8
      requestCount: 1311
    - byUser:
      - byVerb:
        - requestCount: 1534
          verb: list
        requestCount: 1534
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1248
          verb: get
        - requestCount: 84
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1340
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1206
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1287
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 1206
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 1285
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 27
          verb: get
        - requestCount: 751
          verb: watch
        requestCount: 778
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 156
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 173
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-1
      nodeName: 10.9.212.9
      requestCount: 6637
    requestCount: 11745
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1475
          verb: list
        requestCount: 1475
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1230
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1311
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 754
          verb: watch
        requestCount: 754
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 20
          verb: delete
        - requestCount: 213
          verb: get
        - requestCount: 152
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 391
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 20
          verb: get
        - requestCount: 20
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 100
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 20
          verb: create
        - requestCount: 36
          verb: delete
        - requestCount: 2
          verb: get
        - requestCount: 2
          verb: patch
        - requestCount: 15
          verb: watch
        requestCount: 75
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 68
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 49
          verb: watch
        requestCount: 49
        userAgent: network-metrics/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 47
          verb: watch
        requestCount: 47
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      nodeName: 10.9.212.10
      requestCount: 4330
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 2
          verb: list
        - requestCount: 62
          verb: watch
        requestCount: 64
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 18
          verb: create
        requestCount: 18
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      nodeName: 10.9.212.8
      requestCount: 1311
    - byUser:
      - byVerb:
        - requestCount: 1260
          verb: get
        - requestCount: 84
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1353
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1236
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 1315
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1230
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1310
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 885
          verb: list
        requestCount: 885
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 753
          verb: watch
        requestCount: 777
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 156
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 172
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.9
      requestCount: 6052
    requestCount: 11693
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1242
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1322
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 944
          verb: list
        requestCount: 944
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 754
          verb: watch
        requestCount: 754
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 19
          verb: delete
        - requestCount: 200
          verb: get
        - requestCount: 144
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 370
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 19
          verb: get
        - requestCount: 19
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 98
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 19
          verb: create
        - requestCount: 37
          verb: delete
        - requestCount: 15
          verb: watch
        requestCount: 71
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 68
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 56
          verb: watch
        requestCount: 56
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 50
          verb: watch
        requestCount: 50
        userAgent: network-metrics/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      nodeName: 10.9.212.10
      requestCount: 3793
    - byUser:
      - byVerb:
        - requestCount: 1239
          verb: list
        requestCount: 1239
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 16
          verb: create
        requestCount: 16
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      nodeName: 10.9.212.8
      requestCount: 1423
    - byUser:
      - byVerb:
        - requestCount: 1275
          verb: get
        - requestCount: 84
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1367
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1357
          verb: list
        requestCount: 1357
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1242
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1323
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 1242
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1323
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 22
          verb: get
        - requestCount: 752
          verb: watch
        requestCount: 774
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 153
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 168
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-1
      nodeName: 10.9.212.9
      requestCount: 6552
    requestCount: 11768
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1357
          verb: list
        requestCount: 1357
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1260
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1340
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 747
          verb: watch
        requestCount: 747
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 22
          verb: delete
        - requestCount: 234
          verb: get
        - requestCount: 168
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 433
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 22
          verb: get
        - requestCount: 22
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 104
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 22
          verb: create
        - requestCount: 41
          verb: delete
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: patch
        - requestCount: 16
          verb: watch
        requestCount: 87
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 68
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 56
          verb: watch
        requestCount: 56
        userAgent: network-metrics/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      nodeName: 10.9.212.10
      requestCount: 4312
    - byUser:
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 17
          verb: create
        requestCount: 17
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: 10.9.212.8
      requestCount: 1013
    - byUser:
      - byVerb:
        - requestCount: 1296
          verb: get
        - requestCount: 84
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1388
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1357
          verb: list
        requestCount: 1357
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1260
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1341
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 1260
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1340
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 25
          verb: get
        - requestCount: 751
          verb: watch
        requestCount: 776
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 156
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 173
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-c4ssz
      nodeName: 10.9.212.9
      requestCount: 6615
    requestCount: 11940
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1230
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1310
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 745
          verb: watch
        requestCount: 745
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 21
          verb: delete
        - requestCount: 222
          verb: get
        - requestCount: 159
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 410
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 21
          verb: get
        - requestCount: 21
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 102
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 21
          verb: create
        - requestCount: 38
          verb: delete
        - requestCount: 16
          verb: watch
        requestCount: 75
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 68
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 55
          verb: watch
        requestCount: 55
        userAgent: network-metrics/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      nodeName: 10.9.212.10
      requestCount: 3947
    - byUser:
      - byVerb:
        - requestCount: 1180
          verb: list
        requestCount: 1180
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 20
          verb: create
        requestCount: 20
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus2-dgjh4
      nodeName: 10.9.212.8
      requestCount: 1340
    - byUser:
      - byVerb:
        - requestCount: 1272
          verb: get
        - requestCount: 84
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1364
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1236
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1316
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1230
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 1309
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 25
          verb: get
        - requestCount: 752
          verb: watch
        requestCount: 777
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 156
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 172
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.9
      requestCount: 6476
    requestCount: 11763
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1218
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1299
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 1239
          verb: list
        requestCount: 1239
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 751
          verb: watch
        requestCount: 751
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 20
          verb: delete
        - requestCount: 212
          verb: get
        - requestCount: 151
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 390
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 20
          verb: get
        - requestCount: 20
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 100
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 20
          verb: create
        - requestCount: 38
          verb: delete
        - requestCount: 2
          verb: get
        - requestCount: 2
          verb: patch
        - requestCount: 17
          verb: watch
        requestCount: 79
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 68
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 61
          verb: watch
        requestCount: 62
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 54
          verb: watch
        requestCount: 54
        userAgent: network-metrics/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 46
          verb: watch
        requestCount: 46
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus2-dgjh4
      nodeName: 10.9.212.10
      requestCount: 4088
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 29
          verb: watch
        requestCount: 30
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 28
          verb: create
        requestCount: 28
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: 10.9.212.8
      requestCount: 1286
    - byUser:
      - byVerb:
        - requestCount: 1227
          verb: get
        - requestCount: 81
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 1315
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1197
          verb: get
        - requestCount: 68
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 1272
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 1191
          verb: get
        - requestCount: 69
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1268
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1180
          verb: list
        requestCount: 1180
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 22
          verb: get
        - requestCount: 718
          verb: watch
        requestCount: 740
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 160
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 58
          verb: watch
        requestCount: 59
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 57
          verb: watch
        requestCount: 57
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.9
      requestCount: 6171
    requestCount: 11545
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 816
          verb: get
        - requestCount: 48
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 869
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 826
          verb: list
        requestCount: 826
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 494
          verb: watch
        requestCount: 494
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 15
          verb: delete
        - requestCount: 178
          verb: get
        - requestCount: 132
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 331
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 13
          verb: create
        - requestCount: 115
          verb: get
        - requestCount: 8
          verb: list
        requestCount: 136
        userAgent: oc/4.17.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 17
          verb: update
        - requestCount: 39
          verb: watch
        requestCount: 73
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 15
          verb: create
        - requestCount: 29
          verb: delete
        - requestCount: 8
          verb: watch
        requestCount: 52
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 40
          verb: watch
        requestCount: 48
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 1
          verb: update
        - requestCount: 40
          verb: watch
        requestCount: 42
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 1
          verb: update
        - requestCount: 39
          verb: watch
        requestCount: 41
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus2-dgjh4
      nodeName: 10.9.212.10
      requestCount: 3201
    - byUser:
      - byVerb:
        - requestCount: 58
          verb: create
        - requestCount: 638
          verb: get
        - requestCount: 25
          verb: list
        requestCount: 721
        userAgent: oc/4.17.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 590
          verb: list
        requestCount: 590
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: update
        - requestCount: 42
          verb: watch
        requestCount: 45
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 1
          verb: list
        - requestCount: 1
          verb: update
        - requestCount: 41
          verb: watch
        requestCount: 44
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 14
          verb: get
        - requestCount: 10
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 30
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 21
          verb: list
        requestCount: 21
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      - byVerb:
        - requestCount: 11
          verb: create
        requestCount: 11
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: update
        - requestCount: 6
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 1
          verb: update
        - requestCount: 5
          verb: watch
        requestCount: 6
        userAgent: ocpazt003-k6zjs-worker-centralus3-84pdr/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus3-84pdr
      nodeName: 10.9.212.8
      requestCount: 1501
    - byUser:
      - byVerb:
        - requestCount: 885
          verb: list
        requestCount: 885
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 804
          verb: get
        - requestCount: 54
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 863
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 786
          verb: get
        - requestCount: 46
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 838
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 786
          verb: get
        - requestCount: 46
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 837
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 17
          verb: get
        - requestCount: 474
          verb: watch
        requestCount: 491
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 99
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 108
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 38
          verb: watch
        requestCount: 38
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 38
          verb: watch
        requestCount: 38
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 38
          verb: watch
        requestCount: 38
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 38
          verb: watch
        requestCount: 38
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-c4ssz
      nodeName: 10.9.212.9
      requestCount: 4200
    requestCount: 8902
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1218
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1298
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 1180
          verb: list
        requestCount: 1180
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 744
          verb: watch
        requestCount: 744
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 20
          verb: delete
        - requestCount: 210
          verb: get
        - requestCount: 150
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 387
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 20
          verb: get
        - requestCount: 20
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 100
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 20
          verb: create
        - requestCount: 33
          verb: delete
        - requestCount: 8
          verb: get
        - requestCount: 8
          verb: patch
        - requestCount: 15
          verb: watch
        requestCount: 84
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 68
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 57
          verb: watch
        requestCount: 57
        userAgent: network-metrics/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      nodeName: 10.9.212.10
      requestCount: 4038
    - byUser:
      - byVerb:
        - requestCount: 944
          verb: list
        requestCount: 944
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: create
        - requestCount: 36
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 38
        userAgent: Mozilla/5.0
        username: M0681
      - byVerb:
        - requestCount: 16
          verb: create
        requestCount: 16
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: multus-daemon/v0.0.0
        username: system:node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      nodeName: 10.9.212.8
      requestCount: 1023
    - byUser:
      - byVerb:
        - requestCount: 1416
          verb: list
        requestCount: 1416
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1266
          verb: get
        - requestCount: 84
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1359
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1230
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1311
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1218
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1298
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 25
          verb: get
        - requestCount: 751
          verb: watch
        requestCount: 776
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 153
          verb: get
        - requestCount: 19
          verb: watch
        requestCount: 172
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-1
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      nodeName: 10.9.212.9
      requestCount: 6572
    requestCount: 11633
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1416
          verb: list
        requestCount: 1416
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1224
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1305
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 752
          verb: watch
        requestCount: 752
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 21
          verb: delete
        - requestCount: 235
          verb: get
        - requestCount: 171
          verb: patch
        - requestCount: 8
          verb: watch
        requestCount: 435
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 21
          verb: get
        - requestCount: 21
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 102
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 21
          verb: create
        - requestCount: 39
          verb: delete
        - requestCount: 15
          verb: watch
        requestCount: 75
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 68
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 55
          verb: watch
        requestCount: 55
        userAgent: network-metrics/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 43
          verb: watch
        requestCount: 43
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      nodeName: 10.9.212.10
      requestCount: 4311
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 59
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 19
          verb: create
        requestCount: 19
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 12
          verb: get
        - requestCount: 1
          verb: list
        requestCount: 13
        userAgent: Mozilla/5.0
        username: M0681
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 2
          verb: list
        requestCount: 2
        userAgent: oc/4.15.0
        username: V8131
      nodeName: 10.9.212.8
      requestCount: 1238
    - byUser:
      - byVerb:
        - requestCount: 1272
          verb: get
        - requestCount: 84
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1365
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1224
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 1303
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 1218
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 1296
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 944
          verb: list
        requestCount: 944
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 26
          verb: get
        - requestCount: 750
          verb: watch
        requestCount: 776
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 153
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 168
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 61
          verb: watch
        requestCount: 62
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 61
          verb: watch
        requestCount: 61
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus1-xdbjj
      nodeName: 10.9.212.9
      requestCount: 6095
    requestCount: 11644
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1248
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1328
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 752
          verb: watch
        requestCount: 752
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 22
          verb: delete
        - requestCount: 237
          verb: get
        - requestCount: 171
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 439
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 22
          verb: get
        - requestCount: 22
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 104
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 22
          verb: create
        - requestCount: 41
          verb: delete
        - requestCount: 17
          verb: watch
        requestCount: 80
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 68
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 55
          verb: watch
        requestCount: 55
        userAgent: network-metrics/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 25
          verb: get
        - requestCount: 10
          verb: list
        requestCount: 35
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 4219
    - byUser:
      - byVerb:
        - requestCount: 1180
          verb: list
        requestCount: 1180
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 61
          verb: watch
        requestCount: 62
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 23
          verb: create
        requestCount: 23
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 5
          verb: get
        - requestCount: 2
          verb: watch
        requestCount: 7
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 1297
    - byUser:
      - byVerb:
        - requestCount: 1290
          verb: get
        - requestCount: 83
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1381
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1254
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1334
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1248
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1329
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 27
          verb: get
        - requestCount: 752
          verb: watch
        requestCount: 779
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 159
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 174
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-1
      nodeName: 10.9.212.9
      requestCount: 6299
    requestCount: 11815
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1248
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1329
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 769
          verb: watch
        requestCount: 769
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 708
          verb: list
        requestCount: 708
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 19
          verb: delete
        - requestCount: 203
          verb: get
        - requestCount: 143
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 372
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 19
          verb: get
        - requestCount: 19
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 98
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 19
          verb: create
        - requestCount: 31
          verb: delete
        - requestCount: 18
          verb: watch
        requestCount: 68
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 68
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 62
          verb: watch
        requestCount: 63
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 59
          verb: watch
        requestCount: 59
        userAgent: network-metrics/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 45
          verb: watch
        requestCount: 45
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      nodeName: 10.9.212.10
      requestCount: 3579
    - byUser:
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 17
          verb: create
        requestCount: 17
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 3
          verb: watch
        requestCount: 3
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 3
          verb: watch
        requestCount: 3
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 3
          verb: get
        requestCount: 3
        userAgent: Mozilla/5.0
        username: M0681
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: multus-daemon/v0.0.0
        username: system:node:ocpazt003-k6zjs-worker-centralus1-9qcdl
      nodeName: 10.9.212.8
      requestCount: 1230
    - byUser:
      - byVerb:
        - requestCount: 1711
          verb: list
        requestCount: 1711
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1284
          verb: get
        - requestCount: 84
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1377
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1239
          verb: get
        - requestCount: 71
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1318
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 1236
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1316
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 23
          verb: get
        - requestCount: 753
          verb: watch
        requestCount: 776
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 156
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 174
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 61
          verb: watch
        requestCount: 62
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 61
          verb: watch
        requestCount: 62
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-2
      nodeName: 10.9.212.9
      requestCount: 6916
    requestCount: 11725
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1212
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1292
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 1180
          verb: list
        requestCount: 1180
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 754
          verb: watch
        requestCount: 754
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 20
          verb: delete
        - requestCount: 209
          verb: get
        - requestCount: 150
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 388
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 20
          verb: get
        - requestCount: 20
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 100
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 20
          verb: create
        - requestCount: 36
          verb: delete
        - requestCount: 16
          verb: watch
        requestCount: 72
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 68
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 57
          verb: watch
        requestCount: 57
        userAgent: network-metrics/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 25
          verb: get
        - requestCount: 9
          verb: list
        requestCount: 34
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 4005
    - byUser:
      - byVerb:
        - requestCount: 1239
          verb: list
        requestCount: 1239
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 20
          verb: create
        requestCount: 20
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 3
          verb: watch
        requestCount: 3
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 3
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 1362
    - byUser:
      - byVerb:
        - requestCount: 1242
          verb: get
        - requestCount: 84
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1334
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1212
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 1291
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 1206
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1286
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1121
          verb: list
        requestCount: 1121
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 25
          verb: get
        - requestCount: 752
          verb: watch
        requestCount: 777
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 153
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 168
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 61
          verb: watch
        requestCount: 62
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-kx2t4
      nodeName: 10.9.212.9
      requestCount: 6219
    requestCount: 11586
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1230
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1310
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 1239
          verb: list
        requestCount: 1239
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 759
          verb: watch
        requestCount: 759
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 22
          verb: delete
        - requestCount: 233
          verb: get
        - requestCount: 166
          verb: patch
        - requestCount: 6
          verb: watch
        requestCount: 427
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 22
          verb: get
        - requestCount: 22
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 104
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 22
          verb: create
        - requestCount: 34
          verb: delete
        - requestCount: 17
          verb: watch
        requestCount: 73
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 68
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 56
          verb: watch
        requestCount: 56
        userAgent: network-metrics/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 47
          verb: watch
        requestCount: 47
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      nodeName: 10.9.212.10
      requestCount: 4143
    - byUser:
      - byVerb:
        - requestCount: 1239
          verb: list
        requestCount: 1239
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 18
          verb: create
        requestCount: 18
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 6
          verb: get
        requestCount: 6
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 1361
    - byUser:
      - byVerb:
        - requestCount: 1260
          verb: get
        - requestCount: 84
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1352
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1236
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1317
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1236
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1316
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 1062
          verb: list
        requestCount: 1062
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 25
          verb: get
        - requestCount: 751
          verb: watch
        requestCount: 776
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 156
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 171
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 61
          verb: watch
        requestCount: 62
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-1
      nodeName: 10.9.212.9
      requestCount: 6236
    requestCount: 11740
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1248
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 1327
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 1239
          verb: list
        requestCount: 1239
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 762
          verb: watch
        requestCount: 762
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 21
          verb: delete
        - requestCount: 219
          verb: get
        - requestCount: 156
          verb: patch
        - requestCount: 9
          verb: watch
        requestCount: 405
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 21
          verb: get
        - requestCount: 21
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 102
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 21
          verb: create
        - requestCount: 39
          verb: delete
        - requestCount: 17
          verb: watch
        requestCount: 77
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 68
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 57
          verb: watch
        requestCount: 58
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 55
          verb: watch
        requestCount: 55
        userAgent: network-metrics/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 25
          verb: get
        - requestCount: 9
          verb: list
        requestCount: 34
        userAgent: insights-operator/v0.0.0
        username: system:serviceaccount:openshift-insights:gather
      nodeName: 10.9.212.10
      requestCount: 4127
    - byUser:
      - byVerb:
        - requestCount: 1003
          verb: list
        requestCount: 1003
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 21
          verb: create
        requestCount: 21
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 10
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 1
          verb: watch
        requestCount: 4
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 1139
    - byUser:
      - byVerb:
        - requestCount: 1293
          verb: get
        - requestCount: 84
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1385
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1254
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1335
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1248
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1328
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 1298
          verb: list
        requestCount: 1298
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 24
          verb: get
        - requestCount: 754
          verb: watch
        requestCount: 778
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 156
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 174
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-kx2t4
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus3-84pdr
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus2-dgjh4
      nodeName: 10.9.212.9
      requestCount: 6538
    requestCount: 11804
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 1416
          verb: list
        requestCount: 1416
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 1242
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 9
          verb: watch
        requestCount: 1323
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 745
          verb: watch
        requestCount: 745
        userAgent: Prometheus/2.53.1
        username: system:serviceaccount:openshift-monitoring:prometheus-k8s
      - byVerb:
        - requestCount: 21
          verb: delete
        - requestCount: 221
          verb: get
        - requestCount: 158
          verb: patch
        - requestCount: 7
          verb: watch
        requestCount: 407
        userAgent: kubelet/v1.30.12
        username: system:node:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 21
          verb: get
        - requestCount: 21
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 102
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-0
      - byVerb:
        - requestCount: 21
          verb: create
        - requestCount: 39
          verb: delete
        - requestCount: 15
          verb: watch
        requestCount: 75
        userAgent: catalog/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 4
          verb: get
        - requestCount: 4
          verb: update
        - requestCount: 60
          verb: watch
        requestCount: 68
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-l4mv5
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-9qcdl
      - byVerb:
        - requestCount: 53
          verb: watch
        requestCount: 53
        userAgent: network-metrics/v0.0.0
        username: system:serviceaccount:openshift-multus:metrics-daemon-sa
      - byVerb:
        - requestCount: 47
          verb: watch
        requestCount: 47
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      nodeName: 10.9.212.10
      requestCount: 4296
    - byUser:
      - byVerb:
        - requestCount: 944
          verb: list
        requestCount: 944
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 22
          verb: create
        requestCount: 22
        userAgent: Go-http-client/1.1
        username: system:aro-service
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus2-w7f8z
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: ocpazt003-k6zjs-infra-centralus3-t8psc/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-infra-centralus3-t8psc
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: oc/4.17.0
        username: system:ovn-node:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-scheduler/v1.30.12
        username: system:kube-scheduler
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: ocpazt003-k6zjs-master-2/ovnkube@e1c068581f7f
        username: system:ovn-node:ocpazt003-k6zjs-master-2
      - byVerb:
        - requestCount: 2
          verb: get
        requestCount: 2
        userAgent: Mozilla/5.0
        username: M0681
      nodeName: 10.9.212.8
      requestCount: 1079
    - byUser:
      - byVerb:
        - requestCount: 1278
          verb: get
        - requestCount: 84
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1370
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 1242
          verb: get
        - requestCount: 72
          verb: list
        - requestCount: 8
          verb: watch
        requestCount: 1322
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 1242
          verb: get
        - requestCount: 71
          verb: list
        - requestCount: 7
          verb: watch
        requestCount: 1320
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 1180
          verb: list
        requestCount: 1180
        userAgent: aro/v0.0.0
        username: system:aro-service
      - byVerb:
        - requestCount: 23
          verb: get
        - requestCount: 752
          verb: watch
        requestCount: 775
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 153
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 168
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 1
          verb: list
        - requestCount: 61
          verb: watch
        requestCount: 62
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-worker-centralus1-c4ssz
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus1-xdbjj
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-infra-centralus2-dgjh4
      - byVerb:
        - requestCount: 60
          verb: watch
        requestCount: 60
        userAgent: multus-daemon/v0.0.0
        username: system:multus:ocpazt003-k6zjs-master-1
      nodeName: 10.9.212.9
      requestCount: 6377
    requestCount: 11752
  requestCount: 267200
