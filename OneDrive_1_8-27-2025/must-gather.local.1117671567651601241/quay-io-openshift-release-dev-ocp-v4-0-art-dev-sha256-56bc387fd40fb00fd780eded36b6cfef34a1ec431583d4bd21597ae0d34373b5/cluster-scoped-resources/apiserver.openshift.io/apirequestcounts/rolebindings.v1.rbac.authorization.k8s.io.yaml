---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T15:00:14Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T15:00:14Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:41:00Z"
  name: rolebindings.v1.rbac.authorization.k8s.io
  resourceVersion: "********"
  uid: fa49384d-e0a9-407b-80a6-ecac96043c4d
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 2804
          verb: get
        requestCount: 2804
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1153
          verb: get
        requestCount: 1153
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 72
          verb: delete
        - requestCount: 504
          verb: get
        - requestCount: 21
          verb: watch
        requestCount: 597
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 160
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 174
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 153
          verb: patch
        requestCount: 153
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 134
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 144
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 116
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 126
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 28
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 11
          verb: watch
        requestCount: 11
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 5214
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 5
    - byUser:
      - byVerb:
        - requestCount: 204
          verb: get
        - requestCount: 12
          verb: watch
        requestCount: 216
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 132
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 138
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 109
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 119
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 92
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 102
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 62
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 67
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 24
          verb: delete
        - requestCount: 42
          verb: get
        requestCount: 66
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 12
          verb: watch
        requestCount: 12
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 5
          verb: watch
        requestCount: 7
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 750
    requestCount: 5969
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4312
          verb: get
        requestCount: 4312
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1800
          verb: get
        requestCount: 1800
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 106
          verb: delete
        - requestCount: 742
          verb: get
        - requestCount: 30
          verb: watch
        requestCount: 878
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 235
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 259
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 234
          verb: patch
        requestCount: 234
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 200
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 215
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 171
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 186
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 42
          verb: watch
        requestCount: 42
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 7974
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 318
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 335
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 210
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 217
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 171
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 187
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 148
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 162
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 98
          verb: get
        requestCount: 154
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 101
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 110
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1221
    requestCount: 9203
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4270
          verb: get
        requestCount: 4270
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1742
          verb: get
        requestCount: 1742
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 104
          verb: delete
        - requestCount: 728
          verb: get
        - requestCount: 34
          verb: watch
        requestCount: 866
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 234
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 260
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 221
          verb: patch
        requestCount: 221
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 190
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 208
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 188
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 39
          verb: watch
        requestCount: 39
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 7842
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 306
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 322
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 216
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 185
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 150
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 166
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 60
          verb: delete
        - requestCount: 105
          verb: get
        requestCount: 165
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 97
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 105
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.9
      requestCount: 1210
    requestCount: 9060
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4190
          verb: get
        requestCount: 4190
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1768
          verb: get
        requestCount: 1768
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 104
          verb: delete
        - requestCount: 728
          verb: get
        - requestCount: 32
          verb: watch
        requestCount: 864
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 232
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 255
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 234
          verb: patch
        requestCount: 234
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 194
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 210
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 185
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 41
          verb: watch
        requestCount: 41
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 7794
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 329
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 207
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 217
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 187
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 148
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 164
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 52
          verb: delete
        - requestCount: 91
          verb: get
        requestCount: 143
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 99
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 108
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 10
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1203
    requestCount: 9005
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4194
          verb: get
        requestCount: 4194
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1800
          verb: get
        requestCount: 1800
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 104
          verb: delete
        - requestCount: 728
          verb: get
        - requestCount: 33
          verb: watch
        requestCount: 865
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 233
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 259
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 234
          verb: patch
        requestCount: 234
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 196
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 212
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 186
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 42
          verb: watch
        requestCount: 42
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 7841
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 309
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 323
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 207
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 214
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 186
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 148
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 164
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 98
          verb: get
        requestCount: 154
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 105
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1200
    requestCount: 9048
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4370
          verb: get
        requestCount: 4370
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1710
          verb: get
        requestCount: 1710
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 104
          verb: delete
        - requestCount: 728
          verb: get
        - requestCount: 31
          verb: watch
        requestCount: 863
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 233
          verb: get
        - requestCount: 21
          verb: watch
        requestCount: 254
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 221
          verb: patch
        requestCount: 221
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 188
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 205
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 184
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 37
          verb: watch
        requestCount: 38
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 7893
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 306
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 322
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 210
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 218
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 186
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 146
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 163
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 98
          verb: get
        requestCount: 154
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 107
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 11
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1208
    requestCount: 9109
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4354
          verb: get
        requestCount: 4354
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1800
          verb: get
        requestCount: 1800
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 104
          verb: delete
        - requestCount: 728
          verb: get
        - requestCount: 34
          verb: watch
        requestCount: 866
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 233
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 258
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 234
          verb: patch
        requestCount: 234
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 194
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 209
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 186
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 42
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 7995
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 306
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 322
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 210
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 218
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 184
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: delete
        - requestCount: 105
          verb: get
        requestCount: 165
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 148
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 163
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 108
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 5
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 14
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 1220
    requestCount: 9224
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4232
          verb: get
        requestCount: 4232
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1728
          verb: get
        requestCount: 1728
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 104
          verb: delete
        - requestCount: 728
          verb: get
        - requestCount: 33
          verb: watch
        requestCount: 865
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 235
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 259
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 224
          verb: patch
        requestCount: 224
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 196
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 213
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 185
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 41
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 7795
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 329
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 216
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 184
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 148
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 164
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 48
          verb: delete
        - requestCount: 84
          verb: get
        requestCount: 132
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 99
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 107
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1187
    requestCount: 8990
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4494
          verb: get
        requestCount: 4494
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1782
          verb: get
        requestCount: 1782
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 106
          verb: delete
        - requestCount: 742
          verb: get
        - requestCount: 32
          verb: watch
        requestCount: 880
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 243
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 268
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 231
          verb: patch
        requestCount: 231
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 194
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 209
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 172
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 187
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 44
          verb: watch
        requestCount: 44
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 8142
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 315
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 332
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 207
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 214
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 172
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 187
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: delete
        - requestCount: 105
          verb: get
        requestCount: 165
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 148
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 164
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 100
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 108
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 6
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1225
    requestCount: 9375
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4364
          verb: get
        requestCount: 4364
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1798
          verb: get
        requestCount: 1798
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 104
          verb: delete
        - requestCount: 728
          verb: get
        - requestCount: 32
          verb: watch
        requestCount: 864
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 236
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 260
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 234
          verb: patch
        requestCount: 234
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 196
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 210
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 185
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 41
          verb: watch
        requestCount: 43
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 8006
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 309
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 326
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 215
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 186
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 148
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 165
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 52
          verb: delete
        - requestCount: 91
          verb: get
        requestCount: 143
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 106
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1194
    requestCount: 9208
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4430
          verb: get
        requestCount: 4430
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1712
          verb: get
        requestCount: 1712
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 104
          verb: delete
        - requestCount: 728
          verb: get
        - requestCount: 30
          verb: watch
        requestCount: 862
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 235
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 260
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 221
          verb: patch
        requestCount: 221
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 194
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 210
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 186
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 38
          verb: watch
        requestCount: 39
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 7968
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 328
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 216
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 185
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 146
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 161
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 98
          verb: get
        requestCount: 154
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 100
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 108
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1208
    requestCount: 9185
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4230
          verb: get
        requestCount: 4230
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1800
          verb: get
        requestCount: 1800
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 104
          verb: delete
        - requestCount: 728
          verb: get
        - requestCount: 34
          verb: watch
        requestCount: 866
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 233
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 256
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 234
          verb: patch
        requestCount: 234
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 194
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 210
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 173
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 189
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 43
          verb: watch
        requestCount: 43
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 7877
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 315
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 330
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 205
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 213
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 174
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 192
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 146
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 161
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 98
          verb: get
        requestCount: 154
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 99
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 108
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.9
      requestCount: 1212
    requestCount: 9096
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4280
          verb: get
        requestCount: 4280
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1710
          verb: get
        requestCount: 1710
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 103
          verb: delete
        - requestCount: 721
          verb: get
        - requestCount: 33
          verb: watch
        requestCount: 857
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 235
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 258
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 234
          verb: patch
        requestCount: 234
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 196
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 213
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 184
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 42
          verb: watch
        requestCount: 42
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 32
          verb: watch
        requestCount: 32
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 7827
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 291
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 305
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 195
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 203
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 157
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 172
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 142
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 157
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 52
          verb: delete
        - requestCount: 91
          verb: get
        requestCount: 143
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 93
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 101
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 16
          verb: get
        requestCount: 16
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 7
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1128
    requestCount: 8963
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4206
          verb: get
        requestCount: 4206
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1800
          verb: get
        requestCount: 1800
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 104
          verb: delete
        - requestCount: 728
          verb: get
        - requestCount: 33
          verb: watch
        requestCount: 865
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 234
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 259
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 221
          verb: patch
        requestCount: 221
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 194
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 209
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 186
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 40
          verb: watch
        requestCount: 40
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 7835
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 327
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 209
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 218
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 168
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 183
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 150
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 165
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 60
          verb: delete
        - requestCount: 105
          verb: get
        requestCount: 165
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 106
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 5
          verb: watch
        requestCount: 8
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1219
    requestCount: 9062
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4316
          verb: get
        requestCount: 4316
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1781
          verb: get
        requestCount: 1781
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 104
          verb: delete
        - requestCount: 728
          verb: get
        - requestCount: 31
          verb: watch
        requestCount: 863
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 233
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 258
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 234
          verb: patch
        requestCount: 234
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 194
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 209
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 184
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 42
          verb: watch
        requestCount: 42
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 7932
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 327
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 207
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 215
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 186
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 146
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 162
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 98
          verb: get
        requestCount: 154
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 105
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 11
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1206
    requestCount: 9146
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 2804
          verb: get
        requestCount: 2804
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1153
          verb: get
        requestCount: 1153
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 72
          verb: delete
        - requestCount: 504
          verb: get
        - requestCount: 21
          verb: watch
        requestCount: 597
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 160
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 174
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 153
          verb: patch
        requestCount: 153
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 134
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 144
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 116
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 126
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 2
          verb: get
        - requestCount: 26
          verb: watch
        requestCount: 28
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 11
          verb: watch
        requestCount: 11
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 5214
    - byUser:
      - byVerb:
        - requestCount: 5
          verb: watch
        requestCount: 5
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 5
    - byUser:
      - byVerb:
        - requestCount: 204
          verb: get
        - requestCount: 12
          verb: watch
        requestCount: 216
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 132
          verb: get
        - requestCount: 6
          verb: watch
        requestCount: 138
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 109
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 119
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 92
          verb: get
        - requestCount: 10
          verb: watch
        requestCount: 102
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 62
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 67
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 24
          verb: delete
        - requestCount: 42
          verb: get
        requestCount: 66
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 12
          verb: watch
        requestCount: 12
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 5
          verb: watch
        requestCount: 7
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 750
    requestCount: 5969
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4358
          verb: get
        requestCount: 4358
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1733
          verb: get
        requestCount: 1733
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 109
          verb: delete
        - requestCount: 763
          verb: get
        - requestCount: 33
          verb: watch
        requestCount: 905
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 252
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 275
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 234
          verb: patch
        requestCount: 234
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 204
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 220
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 175
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 190
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 40
          verb: watch
        requestCount: 40
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 8005
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 312
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 328
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 215
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 174
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 190
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 148
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 165
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 98
          verb: get
        requestCount: 154
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 103
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 110
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 6
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1217
    requestCount: 9229
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4422
          verb: get
        requestCount: 4422
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1800
          verb: get
        requestCount: 1800
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 108
          verb: delete
        - requestCount: 756
          verb: get
        - requestCount: 32
          verb: watch
        requestCount: 896
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 244
          verb: get
        - requestCount: 25
          verb: watch
        requestCount: 269
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 221
          verb: patch
        requestCount: 221
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 198
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 212
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 173
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 190
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 40
          verb: watch
        requestCount: 40
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 8099
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 309
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 325
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 207
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 216
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 174
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 191
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 146
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 161
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 98
          verb: get
        requestCount: 154
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 104
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 112
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 2
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 10
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1216
    requestCount: 9323
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4290
          verb: get
        requestCount: 4290
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1710
          verb: get
        requestCount: 1710
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 108
          verb: delete
        - requestCount: 756
          verb: get
        - requestCount: 33
          verb: watch
        requestCount: 897
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 234
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 257
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 234
          verb: patch
        requestCount: 234
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 200
          verb: get
        - requestCount: 18
          verb: watch
        requestCount: 218
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 185
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 42
          verb: watch
        requestCount: 42
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 7882
    - byUser:
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 9
    - byUser:
      - byVerb:
        - requestCount: 306
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 322
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 207
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 216
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 174
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 191
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 160
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 98
          verb: get
        requestCount: 154
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 102
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 110
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1206
    requestCount: 9097
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4250
          verb: get
        requestCount: 4250
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1800
          verb: get
        requestCount: 1800
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 103
          verb: delete
        - requestCount: 721
          verb: get
        - requestCount: 30
          verb: watch
        requestCount: 854
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 237
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 260
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 234
          verb: patch
        requestCount: 234
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 192
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 208
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 187
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 3
          verb: get
        - requestCount: 40
          verb: watch
        requestCount: 43
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 7886
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 291
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 307
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 209
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 216
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 185
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 146
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 161
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 52
          verb: delete
        - requestCount: 91
          verb: get
        requestCount: 143
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 105
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 3
          verb: update
        - requestCount: 6
          verb: watch
        requestCount: 9
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1172
    requestCount: 9066
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4360
          verb: get
        requestCount: 4360
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1764
          verb: get
        requestCount: 1764
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 104
          verb: delete
        - requestCount: 728
          verb: get
        - requestCount: 33
          verb: watch
        requestCount: 865
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 235
          verb: get
        - requestCount: 24
          verb: watch
        requestCount: 259
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 221
          verb: patch
        requestCount: 221
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 192
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 207
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 184
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 41
          verb: watch
        requestCount: 42
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 33
          verb: watch
        requestCount: 33
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 7950
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: watch
        requestCount: 6
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 6
    - byUser:
      - byVerb:
        - requestCount: 309
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 324
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 208
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 217
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 170
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 187
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 60
          verb: delete
        - requestCount: 105
          verb: get
        requestCount: 165
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 146
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 161
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 99
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 107
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: cluster-policy-controller/v0.0.0
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 1216
    requestCount: 9172
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4140
          verb: get
        requestCount: 4140
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1746
          verb: get
        requestCount: 1746
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 104
          verb: delete
        - requestCount: 728
          verb: get
        - requestCount: 34
          verb: watch
        requestCount: 866
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 232
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 255
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 234
          verb: patch
        requestCount: 234
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 196
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 212
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 184
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 41
          verb: watch
        requestCount: 42
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 30
          verb: watch
        requestCount: 30
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 7725
    - byUser:
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 7
    - byUser:
      - byVerb:
        - requestCount: 300
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 316
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 210
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 217
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 184
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 161
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 98
          verb: get
        requestCount: 154
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 106
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 5
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 14
        userAgent: manager/v0.0.0
        username: system:serviceaccount:openshift-gitops-operator:openshift-gitops-operator-controller-manager
      - byVerb:
        - requestCount: 5
          verb: list
        - requestCount: 5
          verb: watch
        requestCount: 10
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      nodeName: 10.9.212.9
      requestCount: 1195
    requestCount: 8927
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4308
          verb: get
        requestCount: 4308
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1800
          verb: get
        requestCount: 1800
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 104
          verb: delete
        - requestCount: 728
          verb: get
        - requestCount: 31
          verb: watch
        requestCount: 863
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 232
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 255
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 221
          verb: patch
        requestCount: 221
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 194
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 211
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 174
          verb: get
        - requestCount: 17
          verb: watch
        requestCount: 191
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 41
          verb: watch
        requestCount: 42
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 31
          verb: watch
        requestCount: 31
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 7938
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 303
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 318
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 210
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 218
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 174
          verb: get
        - requestCount: 15
          verb: watch
        requestCount: 189
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 146
          verb: get
        - requestCount: 14
          verb: watch
        requestCount: 160
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 52
          verb: delete
        - requestCount: 91
          verb: get
        requestCount: 143
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 105
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.9
      requestCount: 1188
    requestCount: 9134
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 4060
          verb: get
        requestCount: 4060
        userAgent: machine-config-operator/v0.0.0
        username: system:serviceaccount:openshift-machine-config-operator:machine-config-operator
      - byVerb:
        - requestCount: 1710
          verb: get
        requestCount: 1710
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 104
          verb: delete
        - requestCount: 728
          verb: get
        - requestCount: 32
          verb: watch
        requestCount: 864
        userAgent: cluster-openshift-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-controller-manager-operator:openshift-controller-manager-operator
      - byVerb:
        - requestCount: 237
          verb: get
        - requestCount: 23
          verb: watch
        requestCount: 260
        userAgent: cluster-storage-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-storage-operator:cluster-storage-operator
      - byVerb:
        - requestCount: 234
          verb: patch
        requestCount: 234
        userAgent: network-operator/4.17.0-************.p0.g82191d8.assembly.stream.el9-82191d8
        username: system:serviceaccount:openshift-network-operator:cluster-network-operator
      - byVerb:
        - requestCount: 192
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 208
        userAgent: cluster-kube-controller-manager-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-controller-manager-operator:kube-controller-manager-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 185
        userAgent: azure-file-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-file-csi-driver-operator
      - byVerb:
        - requestCount: 1
          verb: get
        - requestCount: 39
          verb: watch
        requestCount: 40
        userAgent: ingress-operator/v0.0.0
        username: system:serviceaccount:openshift-ingress-operator:ingress-operator
      - byVerb:
        - requestCount: 34
          verb: watch
        requestCount: 34
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.10
      requestCount: 7610
    - byUser:
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-apiserver/v1.30.12
        username: system:apiserver
      nodeName: 10.9.212.8
      requestCount: 8
    - byUser:
      - byVerb:
        - requestCount: 300
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 316
        userAgent: cluster-kube-apiserver-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-apiserver-operator:kube-apiserver-operator
      - byVerb:
        - requestCount: 209
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 217
        userAgent: authentication-operator/v0.0.0
        username: system:serviceaccount:openshift-authentication-operator:authentication-operator
      - byVerb:
        - requestCount: 169
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 185
        userAgent: azure-disk-csi-driver-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-csi-drivers:azure-disk-csi-driver-operator
      - byVerb:
        - requestCount: 146
          verb: get
        - requestCount: 16
          verb: watch
        requestCount: 162
        userAgent: cluster-kube-scheduler-operator/v0.0.0
        username: system:serviceaccount:openshift-kube-scheduler-operator:openshift-kube-scheduler-operator
      - byVerb:
        - requestCount: 56
          verb: delete
        - requestCount: 98
          verb: get
        requestCount: 154
        userAgent: operator/v0.0.0
        username: system:serviceaccount:openshift-monitoring:cluster-monitoring-operator
      - byVerb:
        - requestCount: 98
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 106
        userAgent: cluster-etcd-operator/v0.0.0
        username: system:serviceaccount:openshift-etcd-operator:etcd-operator
      - byVerb:
        - requestCount: 18
          verb: get
        requestCount: 18
        userAgent: service-ca-operator/v0.0.0
        username: system:serviceaccount:openshift-service-ca-operator:service-ca-operator
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: datadog-cluster-agent/v0.0.0
        username: system:serviceaccount:datadog:datadog-agent-cluster-agent
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: openshift-apiserver/v0.0.0
        username: system:serviceaccount:openshift-apiserver:openshift-apiserver-sa
      nodeName: 10.9.212.9
      requestCount: 1194
    requestCount: 8812
  requestCount: 206403
