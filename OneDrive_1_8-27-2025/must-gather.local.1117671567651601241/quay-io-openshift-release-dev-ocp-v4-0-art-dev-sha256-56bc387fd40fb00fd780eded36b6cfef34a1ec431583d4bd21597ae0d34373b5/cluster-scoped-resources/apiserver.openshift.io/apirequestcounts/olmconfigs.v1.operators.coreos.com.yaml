---
apiVersion: apiserver.openshift.io/v1
kind: APIRequestCount
metadata:
  creationTimestamp: "2025-06-25T15:00:13Z"
  generation: 1
  managedFields:
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:spec:
        .: {}
        f:numberOfUsersToReport: {}
    manager: kube-apiserver
    operation: Update
    time: "2025-06-25T15:00:13Z"
  - apiVersion: apiserver.openshift.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        .: {}
        f:currentHour:
          .: {}
          f:byNode: {}
          f:requestCount: {}
        f:last24h: {}
        f:requestCount: {}
    manager: kube-apiserver
    operation: Update
    subresource: status
    time: "2025-08-22T14:41:04Z"
  name: olmconfigs.v1.operators.coreos.com
  resourceVersion: "********"
  uid: 2c9ad22d-dc85-4855-a237-f3a9b9e196dc
spec:
  numberOfUsersToReport: 10
status:
  currentHour:
    byNode:
    - byUser:
      - byVerb:
        - requestCount: 153
          verb: get
        - requestCount: 4
          verb: watch
        requestCount: 157
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 86
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 91
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 269
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      nodeName: 10.9.212.8
      requestCount: 1
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 12
    requestCount: 282
  last24h:
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 269
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 277
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 148
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 156
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 470
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 19
    requestCount: 489
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 268
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 276
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 156
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 164
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 475
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 19
    requestCount: 494
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 270
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 278
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 132
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 140
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 453
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 473
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 269
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 276
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 150
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 157
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 470
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 19
    requestCount: 489
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 266
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 274
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 146
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 154
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 461
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 21
    requestCount: 482
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 266
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 274
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 142
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 150
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 462
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 19
    requestCount: 481
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 270
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 278
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 142
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 150
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 461
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 22
    requestCount: 483
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 269
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 276
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 146
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 153
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 466
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 486
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 265
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 273
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 150
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 159
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 466
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 486
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 270
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 277
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 154
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 162
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 476
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 21
    requestCount: 497
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 269
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 277
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 136
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 144
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 458
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 478
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 272
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 280
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 150
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 158
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 472
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 492
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 267
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 275
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 150
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 159
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 17
          verb: watch
        requestCount: 17
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 471
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 22
    requestCount: 493
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 268
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 277
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 146
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 155
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 466
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 19
    requestCount: 485
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 153
          verb: get
        - requestCount: 4
          verb: watch
        requestCount: 157
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 86
          verb: get
        - requestCount: 5
          verb: watch
        requestCount: 91
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 12
          verb: get
        requestCount: 12
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 269
    - byUser:
      - byVerb:
        - requestCount: 1
          verb: list
        requestCount: 1
        userAgent: Go-http-client/2.0
        username: system:serviceaccount:openshift-must-gather-xwm28:default
      nodeName: 10.9.212.8
      requestCount: 1
    - byUser:
      - byVerb:
        - requestCount: 4
          verb: list
        - requestCount: 4
          verb: watch
        requestCount: 8
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 4
          verb: watch
        requestCount: 4
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 12
    requestCount: 282
  - requestCount: 0
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 266
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 274
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 148
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 156
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 466
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 10
          verb: watch
        requestCount: 10
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 22
    requestCount: 488
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 268
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 275
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 150
          verb: get
        - requestCount: 2
          verb: update
        - requestCount: 8
          verb: watch
        requestCount: 160
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 470
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 490
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 271
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 278
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 138
          verb: get
        - requestCount: 2
          verb: update
        - requestCount: 9
          verb: watch
        requestCount: 149
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 461
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 481
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 269
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 278
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 152
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 18
          verb: watch
        requestCount: 18
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 468
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 488
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 266
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 274
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 152
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 159
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 467
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 8
          verb: watch
        requestCount: 8
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 20
    requestCount: 487
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 268
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 276
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 136
          verb: get
        - requestCount: 9
          verb: watch
        requestCount: 145
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 15
          verb: watch
        requestCount: 15
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 456
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 21
    requestCount: 477
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 268
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 276
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 144
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 152
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 20
          verb: get
        requestCount: 20
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 16
          verb: watch
        requestCount: 16
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 464
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 7
          verb: watch
        requestCount: 7
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 19
    requestCount: 483
  - byNode:
    - byUser:
      - byVerb:
        - requestCount: 270
          verb: get
        - requestCount: 7
          verb: watch
        requestCount: 277
        userAgent: console/v0.0.0
        username: system:serviceaccount:openshift-console-operator:console-operator
      - byVerb:
        - requestCount: 142
          verb: get
        - requestCount: 8
          verb: watch
        requestCount: 150
        userAgent: olm/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      - byVerb:
        - requestCount: 19
          verb: get
        requestCount: 19
        userAgent: cluster-version-operator/v0.0.0
        username: system:serviceaccount:openshift-cluster-version:default
      - byVerb:
        - requestCount: 14
          verb: watch
        requestCount: 14
        userAgent: package-server/v0.0.0
        username: system:serviceaccount:openshift-operator-lifecycle-manager:olm-operator-serviceaccount
      nodeName: 10.9.212.10
      requestCount: 460
    - nodeName: 10.9.212.8
      requestCount: 0
    - byUser:
      - byVerb:
        - requestCount: 6
          verb: list
        - requestCount: 6
          verb: watch
        requestCount: 12
        userAgent: argocd-application-controller/v0.0.0
        username: system:serviceaccount:openshift-gitops:openshift-gitops-argocd-application-controller
      - byVerb:
        - requestCount: 9
          verb: watch
        requestCount: 9
        userAgent: kube-controller-manager/v1.30.12
        username: system:kube-controller-manager
      nodeName: 10.9.212.9
      requestCount: 21
    requestCount: 481
  requestCount: 10965
