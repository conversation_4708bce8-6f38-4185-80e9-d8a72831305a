---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  annotations:
    service.beta.openshift.io/inject-cabundle: "true"
  creationTimestamp: "2025-06-25T14:58:48Z"
  generation: 16
  labels:
    k8s-app: cluster-autoscaler-operator
  managedFields:
  - apiVersion: admissionregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:service.beta.openshift.io/inject-cabundle: {}
        f:labels:
          .: {}
          f:k8s-app: {}
      f:webhooks:
        .: {}
        k:{"name":"clusterautoscalers.autoscaling.openshift.io"}:
          .: {}
          f:admissionReviewVersions: {}
          f:clientConfig:
            .: {}
            f:service:
              .: {}
              f:name: {}
              f:namespace: {}
              f:path: {}
              f:port: {}
          f:failurePolicy: {}
          f:matchPolicy: {}
          f:name: {}
          f:namespaceSelector: {}
          f:objectSelector: {}
          f:rules: {}
          f:sideEffects: {}
          f:timeoutSeconds: {}
        k:{"name":"machineautoscalers.autoscaling.openshift.io"}:
          .: {}
          f:admissionReviewVersions: {}
          f:clientConfig:
            .: {}
            f:service:
              .: {}
              f:name: {}
              f:namespace: {}
              f:path: {}
              f:port: {}
          f:failurePolicy: {}
          f:matchPolicy: {}
          f:name: {}
          f:namespaceSelector: {}
          f:objectSelector: {}
          f:rules: {}
          f:sideEffects: {}
          f:timeoutSeconds: {}
    manager: cluster-autoscaler-operator
    operation: Update
    time: "2025-06-25T14:58:48Z"
  - apiVersion: admissionregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:webhooks:
        k:{"name":"clusterautoscalers.autoscaling.openshift.io"}:
          f:clientConfig:
            f:caBundle: {}
        k:{"name":"machineautoscalers.autoscaling.openshift.io"}:
          f:clientConfig:
            f:caBundle: {}
    manager: service-ca-operator
    operation: Update
    time: "2025-08-01T12:41:39Z"
  name: autoscaling.openshift.io
  resourceVersion: "21375681"
  uid: 7ffa6c4b-bca5-43b7-92dd-006bc02908c2
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURVVENDQWptZ0F3SUJBZ0lJZHpKblVydFl3TVV3RFFZSktvWklodmNOQVFFTEJRQXdOakUwTURJR0ExVUUKQXd3cmIzQmxibk5vYVdaMExYTmxjblpwWTJVdGMyVnlkbWx1WnkxemFXZHVaWEpBTVRjMU1EZzJNelE1TkRBZQpGdzB5TlRBMk1qVXhORFU0TVROYUZ3MHlOekE0TWpReE5EVTRNVFJhTURZeE5EQXlCZ05WQkFNTUsyOXdaVzV6CmFHbG1kQzF6WlhKMmFXTmxMWE5sY25acGJtY3RjMmxuYm1WeVFERTNOVEE0TmpNME9UUXdnZ0VpTUEwR0NTcUcKU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRRFNsSjQwa1FtblRTK245U1lCd2QwYW8zRDlUVXlPK1BFQgp0R3FjbGh0L3pmemk5SVRtRmVZeHNOcVZMUDlLQ1hNY0J5L0I2Nk9qV1l5SzJlYU1QbGhCUXp2cmhmU3lCaU5DClpwSFVkYXEzSjZrVVlSUGpGWHRNa1o5YUNVa016UTNqYm42Sm9zbTBxcXFvd0F2Vk1YL21jaWFJWXJNVTFVMEkKbWNhd0VBK3BqeS9ZKzEzSWtlRm9FbitUK0dwUjB6RVFLRnJaK05OM2dTOHBIQ3dpbW9odS9NVHA0WjdEQXI3RApJQzVqcklzU0JoTXQzdVFMU1dhb0VmcHoxemRsSmVFamNwbVdiWEIzbTB6d3FDekNPQm9YZTh3RlZYVGdTK0ZFCjEwNXp4cTBsYzIxS21BRVRDSml4cEQzK1p6NXA5dk1Pa3VMTmsxdXF5c0xhMU5lWlExaVJBZ01CQUFHall6QmgKTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQQmdOVkhSTUJBZjhFQlRBREFRSC9NQjBHQTFVZERnUVdCQlQrU081SwpBTkxudVZ6eG5CZStMS3hyL3dMb2JUQWZCZ05WSFNNRUdEQVdnQlQrU081S0FOTG51Vnp4bkJlK0xLeHIvd0xvCmJUQU5CZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUFTcWc0cG5LdWQrUmN3NUFVcjdVQXBPZTVaOXcyTUhKS3R6MmoKK3BuUHg3dituNWZJdDZoV3BxYlNzREovenZaeDhjSW9mcWgrNTFUOTQ3bmdHQjhxdThtOEprOHFnMncxcENJYgpqWDhNRThBTnVwM3diYVZEYXdGQXJrTE5HbmwzTFB0akV5amkyZGpTUjlUK3pZWHVUa3JvSVQ1Mmg3NUY1bFI1CmFvelJmVjE3bmQvcGpEcHhSNkYycUpMVVh4eGt2elZnQjdBMGs0M1RYSitCTkdrSmNHQXVuQjBPd2JTN2E1T2QKRFRBSGk3NVM5QmN5akhLVk1iNVg0L3FLS25yNlVqSHgxZHp1bC9xWHBYWmFhWndJRHFURHZSZzczRStrTC9xdgpyMUFCcWdxZ0RNb0J6bUpLd1lVa29lbzNmYS9EaThid2NWVnJEekJSeG8rclZWYlc5QT09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
    service:
      name: cluster-autoscaler-operator
      namespace: openshift-machine-api
      path: /validate-clusterautoscalers
      port: 443
  failurePolicy: Ignore
  matchPolicy: Equivalent
  name: clusterautoscalers.autoscaling.openshift.io
  namespaceSelector: {}
  objectSelector: {}
  rules:
  - apiGroups:
    - autoscaling.openshift.io
    apiVersions:
    - v1
    operations:
    - CREATE
    - UPDATE
    resources:
    - clusterautoscalers
    scope: '*'
  sideEffects: None
  timeoutSeconds: 10
- admissionReviewVersions:
  - v1
  clientConfig:
    caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURVVENDQWptZ0F3SUJBZ0lJZHpKblVydFl3TVV3RFFZSktvWklodmNOQVFFTEJRQXdOakUwTURJR0ExVUUKQXd3cmIzQmxibk5vYVdaMExYTmxjblpwWTJVdGMyVnlkbWx1WnkxemFXZHVaWEpBTVRjMU1EZzJNelE1TkRBZQpGdzB5TlRBMk1qVXhORFU0TVROYUZ3MHlOekE0TWpReE5EVTRNVFJhTURZeE5EQXlCZ05WQkFNTUsyOXdaVzV6CmFHbG1kQzF6WlhKMmFXTmxMWE5sY25acGJtY3RjMmxuYm1WeVFERTNOVEE0TmpNME9UUXdnZ0VpTUEwR0NTcUcKU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRRFNsSjQwa1FtblRTK245U1lCd2QwYW8zRDlUVXlPK1BFQgp0R3FjbGh0L3pmemk5SVRtRmVZeHNOcVZMUDlLQ1hNY0J5L0I2Nk9qV1l5SzJlYU1QbGhCUXp2cmhmU3lCaU5DClpwSFVkYXEzSjZrVVlSUGpGWHRNa1o5YUNVa016UTNqYm42Sm9zbTBxcXFvd0F2Vk1YL21jaWFJWXJNVTFVMEkKbWNhd0VBK3BqeS9ZKzEzSWtlRm9FbitUK0dwUjB6RVFLRnJaK05OM2dTOHBIQ3dpbW9odS9NVHA0WjdEQXI3RApJQzVqcklzU0JoTXQzdVFMU1dhb0VmcHoxemRsSmVFamNwbVdiWEIzbTB6d3FDekNPQm9YZTh3RlZYVGdTK0ZFCjEwNXp4cTBsYzIxS21BRVRDSml4cEQzK1p6NXA5dk1Pa3VMTmsxdXF5c0xhMU5lWlExaVJBZ01CQUFHall6QmgKTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQQmdOVkhSTUJBZjhFQlRBREFRSC9NQjBHQTFVZERnUVdCQlQrU081SwpBTkxudVZ6eG5CZStMS3hyL3dMb2JUQWZCZ05WSFNNRUdEQVdnQlQrU081S0FOTG51Vnp4bkJlK0xLeHIvd0xvCmJUQU5CZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUFTcWc0cG5LdWQrUmN3NUFVcjdVQXBPZTVaOXcyTUhKS3R6MmoKK3BuUHg3dituNWZJdDZoV3BxYlNzREovenZaeDhjSW9mcWgrNTFUOTQ3bmdHQjhxdThtOEprOHFnMncxcENJYgpqWDhNRThBTnVwM3diYVZEYXdGQXJrTE5HbmwzTFB0akV5amkyZGpTUjlUK3pZWHVUa3JvSVQ1Mmg3NUY1bFI1CmFvelJmVjE3bmQvcGpEcHhSNkYycUpMVVh4eGt2elZnQjdBMGs0M1RYSitCTkdrSmNHQXVuQjBPd2JTN2E1T2QKRFRBSGk3NVM5QmN5akhLVk1iNVg0L3FLS25yNlVqSHgxZHp1bC9xWHBYWmFhWndJRHFURHZSZzczRStrTC9xdgpyMUFCcWdxZ0RNb0J6bUpLd1lVa29lbzNmYS9EaThid2NWVnJEekJSeG8rclZWYlc5QT09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
    service:
      name: cluster-autoscaler-operator
      namespace: openshift-machine-api
      path: /validate-machineautoscalers
      port: 443
  failurePolicy: Ignore
  matchPolicy: Equivalent
  name: machineautoscalers.autoscaling.openshift.io
  namespaceSelector: {}
  objectSelector: {}
  rules:
  - apiGroups:
    - autoscaling.openshift.io
    apiVersions:
    - v1beta1
    operations:
    - CREATE
    - UPDATE
    resources:
    - machineautoscalers
    scope: '*'
  sideEffects: None
  timeoutSeconds: 10
