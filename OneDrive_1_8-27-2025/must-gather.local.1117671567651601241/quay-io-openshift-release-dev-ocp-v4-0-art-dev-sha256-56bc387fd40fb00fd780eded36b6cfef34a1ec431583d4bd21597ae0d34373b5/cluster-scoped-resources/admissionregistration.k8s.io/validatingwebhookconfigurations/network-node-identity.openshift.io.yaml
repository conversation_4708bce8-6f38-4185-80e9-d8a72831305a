---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  creationTimestamp: "2025-06-25T15:05:32Z"
  generation: 1
  managedFields:
  - apiVersion: admissionregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:ownerReferences:
          k:{"uid":"e8b7620a-d5d2-448e-b2bf-6f6768b9b1dc"}: {}
      f:webhooks:
        k:{"name":"node.network-node-identity.openshift.io"}:
          .: {}
          f:admissionReviewVersions: {}
          f:clientConfig:
            f:caBundle: {}
            f:url: {}
          f:name: {}
          f:rules: {}
          f:sideEffects: {}
        k:{"name":"pod.network-node-identity.openshift.io"}:
          .: {}
          f:admissionReviewVersions: {}
          f:clientConfig:
            f:caBundle: {}
            f:url: {}
          f:name: {}
          f:rules: {}
          f:sideEffects: {}
    manager: cluster-network-operator/operconfig
    operation: Apply
    time: "2025-06-25T15:05:32Z"
  name: network-node-identity.openshift.io
  ownerReferences:
  - apiVersion: operator.openshift.io/v1
    blockOwnerDeletion: true
    controller: true
    kind: Network
    name: cluster
    uid: e8b7620a-d5d2-448e-b2bf-6f6768b9b1dc
  resourceVersion: "27061"
  uid: 729ac6a7-d18c-48b9-a673-3d41f59d8b4d
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURnVENDQW1tZ0F3SUJBZ0lJTytwNjdyRnJnZHN3RFFZSktvWklodmNOQVFFTEJRQXdUakZNTUVvR0ExVUUKQXd4RGIzQmxibk5vYVdaMExXNWxkSGR2Y21zdGJtOWtaUzFwWkdWdWRHbDBlVjl1WlhSM2IzSnJMVzV2WkdVdAphV1JsYm5ScGRIa3RZMkZBTVRjMU1EZzJNelEyTWpBZUZ3MHlOVEEyTWpVeE5EVTNOREZhRncwek5UQTJNak14Ck5EVTNOREphTUU0eFREQktCZ05WQkFNTVEyOXdaVzV6YUdsbWRDMXVaWFIzYjNKckxXNXZaR1V0YVdSbGJuUnAKZEhsZmJtVjBkMjl5YXkxdWIyUmxMV2xrWlc1MGFYUjVMV05oUURFM05UQTROak0wTmpJd2dnRWlNQTBHQ1NxRwpTSWIzRFFFQkFRVUFBNElCRHdBd2dnRUtBb0lCQVFDOVcwd2lMV3pEb2MvRWNSMEhvNGJvcWVzZm9nT2xEei9zCi9ZUjNWYXN4a29hVXpiYTFNcjFodmhiZktPL21XYU5Tak5vV3hPL1pvWGIxdjA4dHgxOUwvUnBQSjFlbFprZ00KRkcyS010RWZhU0M3bWk3dWx6YjQ4bWYrRnBKZ0ZJNjE1TGJpMW1SM1ZxQ0VZS1d4Q1E2cUFlOERQc1FpZVViSAplUHJtMUdzSnZDa1lUd1BNNCtjSmZ4UUY1RlpWeWVERXVjVWJhbnZ4L0lwY1lIM0ZTUlcwR2pRVnZXeTV4cVZMCmYwbzFVT1lRcFBjL2MxYjA1WU9WdUszdWxGYWl5bXRGYklPU0VXNW9hSm1WUUVWYW85N20wUkM2U1AxV2UzdGgKMHBkRjZ3ci9OSUVrOFU2d1pMc3M3dFVFaDNJNDRaL2hBUnlmRWxkZmw3S2RUN2dLYmhpcEFnTUJBQUdqWXpCaApNQTRHQTFVZER3RUIvd1FFQXdJQ3BEQVBCZ05WSFJNQkFmOEVCVEFEQVFIL01CMEdBMVVkRGdRV0JCUThySkUrCkdGS1c2TzJSeW5lN0ZDYkZqYm5UeVRBZkJnTlZIU01FR0RBV2dCUThySkUrR0ZLVzZPMlJ5bmU3RkNiRmpiblQKeVRBTkJna3Foa2lHOXcwQkFRc0ZBQU9DQVFFQUs2VkVEWkVvbGNmZ1VpTEI0Uzh2bXVHWUVJUlMvQS9HaDlJZApPWnZ5QkxiSU5UM3lZTmN2T1dSNHZES0JEMk1Od0dRWHA2VDJrWEFCQVJPd1Zla29ISDI0K1NWUVVGZUw0OFFlCktzNHZoRHN6aUV1Q2NYb0hXZHZFMzBKOUtJd3hSZ3dqd21HNnZxMnJsc3lOamhlOW4wc3IwandJYlhMTmVZeU4KYS9oMzRNaUZmTHdMajk3TEhUZkRjKzdKVm1NQ0dDUHFkSC9XN2hPSlplUTFaLzdDVm9Nc2FNdVVVbEtjUmREVgp3M2F4cWhJRWU3U1hoS0dtVUk5bW5WY2w2dEZ6eUVtcGV1TTZpQlZHYW55UGJxZzd1YzZGVG5PQXpLMmxrVHNXCldOSjlGNDkyZ0NSSC8xTXRYUVNrbGxkeWdJZnFWUkg0WVFpV01HMTkxK25mOHlYMnhnPT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
    url: https://127.0.0.1:9743/node
  failurePolicy: Fail
  matchPolicy: Equivalent
  name: node.network-node-identity.openshift.io
  namespaceSelector: {}
  objectSelector: {}
  rules:
  - apiGroups:
    - '*'
    apiVersions:
    - '*'
    operations:
    - UPDATE
    resources:
    - nodes/status
    scope: '*'
  sideEffects: None
  timeoutSeconds: 10
- admissionReviewVersions:
  - v1
  clientConfig:
    caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURnVENDQW1tZ0F3SUJBZ0lJTytwNjdyRnJnZHN3RFFZSktvWklodmNOQVFFTEJRQXdUakZNTUVvR0ExVUUKQXd4RGIzQmxibk5vYVdaMExXNWxkSGR2Y21zdGJtOWtaUzFwWkdWdWRHbDBlVjl1WlhSM2IzSnJMVzV2WkdVdAphV1JsYm5ScGRIa3RZMkZBTVRjMU1EZzJNelEyTWpBZUZ3MHlOVEEyTWpVeE5EVTNOREZhRncwek5UQTJNak14Ck5EVTNOREphTUU0eFREQktCZ05WQkFNTVEyOXdaVzV6YUdsbWRDMXVaWFIzYjNKckxXNXZaR1V0YVdSbGJuUnAKZEhsZmJtVjBkMjl5YXkxdWIyUmxMV2xrWlc1MGFYUjVMV05oUURFM05UQTROak0wTmpJd2dnRWlNQTBHQ1NxRwpTSWIzRFFFQkFRVUFBNElCRHdBd2dnRUtBb0lCQVFDOVcwd2lMV3pEb2MvRWNSMEhvNGJvcWVzZm9nT2xEei9zCi9ZUjNWYXN4a29hVXpiYTFNcjFodmhiZktPL21XYU5Tak5vV3hPL1pvWGIxdjA4dHgxOUwvUnBQSjFlbFprZ00KRkcyS010RWZhU0M3bWk3dWx6YjQ4bWYrRnBKZ0ZJNjE1TGJpMW1SM1ZxQ0VZS1d4Q1E2cUFlOERQc1FpZVViSAplUHJtMUdzSnZDa1lUd1BNNCtjSmZ4UUY1RlpWeWVERXVjVWJhbnZ4L0lwY1lIM0ZTUlcwR2pRVnZXeTV4cVZMCmYwbzFVT1lRcFBjL2MxYjA1WU9WdUszdWxGYWl5bXRGYklPU0VXNW9hSm1WUUVWYW85N20wUkM2U1AxV2UzdGgKMHBkRjZ3ci9OSUVrOFU2d1pMc3M3dFVFaDNJNDRaL2hBUnlmRWxkZmw3S2RUN2dLYmhpcEFnTUJBQUdqWXpCaApNQTRHQTFVZER3RUIvd1FFQXdJQ3BEQVBCZ05WSFJNQkFmOEVCVEFEQVFIL01CMEdBMVVkRGdRV0JCUThySkUrCkdGS1c2TzJSeW5lN0ZDYkZqYm5UeVRBZkJnTlZIU01FR0RBV2dCUThySkUrR0ZLVzZPMlJ5bmU3RkNiRmpiblQKeVRBTkJna3Foa2lHOXcwQkFRc0ZBQU9DQVFFQUs2VkVEWkVvbGNmZ1VpTEI0Uzh2bXVHWUVJUlMvQS9HaDlJZApPWnZ5QkxiSU5UM3lZTmN2T1dSNHZES0JEMk1Od0dRWHA2VDJrWEFCQVJPd1Zla29ISDI0K1NWUVVGZUw0OFFlCktzNHZoRHN6aUV1Q2NYb0hXZHZFMzBKOUtJd3hSZ3dqd21HNnZxMnJsc3lOamhlOW4wc3IwandJYlhMTmVZeU4KYS9oMzRNaUZmTHdMajk3TEhUZkRjKzdKVm1NQ0dDUHFkSC9XN2hPSlplUTFaLzdDVm9Nc2FNdVVVbEtjUmREVgp3M2F4cWhJRWU3U1hoS0dtVUk5bW5WY2w2dEZ6eUVtcGV1TTZpQlZHYW55UGJxZzd1YzZGVG5PQXpLMmxrVHNXCldOSjlGNDkyZ0NSSC8xTXRYUVNrbGxkeWdJZnFWUkg0WVFpV01HMTkxK25mOHlYMnhnPT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
    url: https://127.0.0.1:9743/pod
  failurePolicy: Fail
  matchPolicy: Equivalent
  name: pod.network-node-identity.openshift.io
  namespaceSelector: {}
  objectSelector: {}
  rules:
  - apiGroups:
    - '*'
    apiVersions:
    - '*'
    operations:
    - UPDATE
    resources:
    - pods/status
    scope: '*'
  sideEffects: None
  timeoutSeconds: 10
