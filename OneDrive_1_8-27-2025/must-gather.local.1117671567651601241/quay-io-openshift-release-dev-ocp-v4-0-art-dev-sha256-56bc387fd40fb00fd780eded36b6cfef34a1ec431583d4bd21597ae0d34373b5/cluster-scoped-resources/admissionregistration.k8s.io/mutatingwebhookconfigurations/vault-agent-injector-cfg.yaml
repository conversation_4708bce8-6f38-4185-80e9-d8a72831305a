---
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  annotations:
    argocd.argoproj.io/tracking-id: vault:admissionregistration.k8s.io/MutatingWebhookConfiguration:vault/vault-agent-injector-cfg
    kubectl.kubernetes.io/last-applied-configuration: |
      {"apiVersion":"admissionregistration.k8s.io/v1","kind":"MutatingWebhookConfiguration","metadata":{"annotations":{"argocd.argoproj.io/tracking-id":"vault:admissionregistration.k8s.io/MutatingWebhookConfiguration:vault/vault-agent-injector-cfg"},"labels":{"app.kubernetes.io/instance":"vault","app.kubernetes.io/managed-by":"Helm","app.kubernetes.io/name":"vault-agent-injector","csx/appname":"vault"},"name":"vault-agent-injector-cfg"},"webhooks":[{"admissionReviewVersions":["v1","v1beta1"],"clientConfig":{"caBundle":"","service":{"name":"vault-agent-injector-svc","namespace":"vault","path":"/mutate"}},"failurePolicy":"Ignore","matchPolicy":"Exact","name":"vault.hashicorp.com","objectSelector":{"matchExpressions":[{"key":"app.kubernetes.io/name","operator":"NotIn","values":["vault-agent-injector"]}]},"rules":[{"apiGroups":[""],"apiVersions":["v1"],"operations":["CREATE"],"resources":["pods"],"scope":"Namespaced"}],"sideEffects":"None","timeoutSeconds":30}]}
  creationTimestamp: "2025-07-01T19:23:06Z"
  generation: 43
  labels:
    app.kubernetes.io/instance: vault
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vault-agent-injector
    csx/appname: vault
  managedFields:
  - apiVersion: admissionregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:argocd.argoproj.io/tracking-id: {}
          f:kubectl.kubernetes.io/last-applied-configuration: {}
        f:labels:
          .: {}
          f:app.kubernetes.io/instance: {}
          f:app.kubernetes.io/managed-by: {}
          f:app.kubernetes.io/name: {}
          f:csx/appname: {}
      f:webhooks:
        .: {}
        k:{"name":"vault.hashicorp.com"}:
          .: {}
          f:admissionReviewVersions: {}
          f:clientConfig:
            .: {}
            f:service:
              .: {}
              f:name: {}
              f:namespace: {}
              f:path: {}
              f:port: {}
          f:failurePolicy: {}
          f:matchPolicy: {}
          f:name: {}
          f:namespaceSelector: {}
          f:objectSelector: {}
          f:reinvocationPolicy: {}
          f:rules: {}
          f:sideEffects: {}
          f:timeoutSeconds: {}
    manager: argocd-controller
    operation: Update
    time: "2025-07-01T19:23:06Z"
  - apiVersion: admissionregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:webhooks:
        k:{"name":"vault.hashicorp.com"}:
          f:clientConfig:
            f:caBundle: {}
    manager: vault-k8s
    operation: Update
    time: "2025-08-11T18:13:25Z"
  name: vault-agent-injector-cfg
  resourceVersion: "27466351"
  uid: 731b8b5c-779d-485e-9dec-1955424baa9a
webhooks:
- admissionReviewVersions:
  - v1
  - v1beta1
  clientConfig:
    caBundle: 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
    service:
      name: vault-agent-injector-svc
      namespace: vault
      path: /mutate
      port: 443
  failurePolicy: Ignore
  matchPolicy: Exact
  name: vault.hashicorp.com
  namespaceSelector: {}
  objectSelector:
    matchExpressions:
    - key: app.kubernetes.io/name
      operator: NotIn
      values:
      - vault-agent-injector
  reinvocationPolicy: Never
  rules:
  - apiGroups:
    - ""
    apiVersions:
    - v1
    operations:
    - CREATE
    resources:
    - pods
    scope: Namespaced
  sideEffects: None
  timeoutSeconds: 30
