---
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  creationTimestamp: "2025-06-26T15:48:22Z"
  generation: 1
  managedFields:
  - apiVersion: admissionregistration.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:webhooks:
        .: {}
        k:{"name":"datadog.webhook.agent.config"}:
          .: {}
          f:admissionReviewVersions: {}
          f:clientConfig:
            .: {}
            f:caBundle: {}
            f:service:
              .: {}
              f:name: {}
              f:namespace: {}
              f:path: {}
              f:port: {}
          f:failurePolicy: {}
          f:matchPolicy: {}
          f:name: {}
          f:namespaceSelector: {}
          f:objectSelector: {}
          f:reinvocationPolicy: {}
          f:rules: {}
          f:sideEffects: {}
          f:timeoutSeconds: {}
        k:{"name":"datadog.webhook.lib.injection"}:
          .: {}
          f:admissionReviewVersions: {}
          f:clientConfig:
            .: {}
            f:caBundle: {}
            f:service:
              .: {}
              f:name: {}
              f:namespace: {}
              f:path: {}
              f:port: {}
          f:failurePolicy: {}
          f:matchPolicy: {}
          f:name: {}
          f:namespaceSelector: {}
          f:objectSelector: {}
          f:reinvocationPolicy: {}
          f:rules: {}
          f:sideEffects: {}
          f:timeoutSeconds: {}
        k:{"name":"datadog.webhook.standard.tags"}:
          .: {}
          f:admissionReviewVersions: {}
          f:clientConfig:
            .: {}
            f:caBundle: {}
            f:service:
              .: {}
              f:name: {}
              f:namespace: {}
              f:path: {}
              f:port: {}
          f:failurePolicy: {}
          f:matchPolicy: {}
          f:name: {}
          f:namespaceSelector: {}
          f:objectSelector: {}
          f:reinvocationPolicy: {}
          f:rules: {}
          f:sideEffects: {}
          f:timeoutSeconds: {}
    manager: datadog-cluster-agent
    operation: Update
    time: "2025-06-26T15:48:22Z"
  name: datadog-webhook
  resourceVersion: "608725"
  uid: c9622621-3c42-468c-ae8f-4b393bca570d
webhooks:
- admissionReviewVersions:
  - v1
  - v1beta1
  clientConfig:
    caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUVEekNDQXZlZ0F3SUJBZ0lSQU1na3FETWhETnJFbkl4NkwvcE1qemd3RFFZSktvWklodmNOQVFFTEJRQXcKRWpFUU1BNEdBMVVFQ2hNSFJHRjBZV1J2WnpBZUZ3MHlOVEEyTWpZeE5UUXpNakphRncweU5qQTJNall4TlRRNApNakphTUJJeEVEQU9CZ05WQkFvVEIwUmhkR0ZrYjJjd2dnRWlNQTBHQ1NxR1NJYjNEUUVCQVFVQUE0SUJEd0F3CmdnRUtBb0lCQVFEbXNHNldPcWpYWnhveWFFemJJUGpBTHAxanJENEN4L0lXc0JGWWFRcjlSNk44SWNKanJIQ3oKMmYvT2txZGZxUHRVQ2Q0aFhKWW9iQVQ2aVJpMEtTZDMyUk5kL0p5L1RJWjBHbmNjenJJUlp6dFQ1QXFxYS9tRgppUU5vRk5VWWQyS3JBSmxMamFTa0tTNVppekZCeWY2cmVLbmhiWEZ5VngxcGpPc3BHRkJBNUtIeTFUeXE3UUd5CngyVEduNTJtT0lZMDkyQUZBSTFGcWtQZHR6ZDhTNHlyY0V2NHRPcE5OdXA1K1ZUdWtSaC91NzhCK01EQVhpSU4KWFRCaVZNNUNBVU5SbWhvVXF4Q1k1L3Y5M2lyanlLbjJDcldmR1djZTN1ZlVsTTIwYUk4OU4zai9WaUlDQ3JJQQo5akxsK2JrVGhGd3ZjMUt5ckdLaWNOMElNS3RHQ29WZkFnTUJBQUdqZ2dGZU1JSUJXakFPQmdOVkhROEJBZjhFCkJBTUNBcVF3RXdZRFZSMGxCQXd3Q2dZSUt3WUJCUVVIQXdFd0R3WURWUjBUQVFIL0JBVXdBd0VCL3pBZEJnTlYKSFE0RUZnUVVSOEFlVDVZM3Z5U1lMUnZ1UEwvQ1E0TWRzOXd3Z2dFQkJnTlZIUkVFZ2Zrd2dmYUNNR1JoZEdGawpiMmN0WVdkbGJuUXRZMngxYzNSbGNpMWhaMlZ1ZEMxaFpHMXBjM05wYjI0dFkyOXVkSEp2Ykd4bGNvSTRaR0YwCllXUnZaeTFoWjJWdWRDMWpiSFZ6ZEdWeUxXRm5aVzUwTFdGa2JXbHpjMmx2YmkxamIyNTBjbTlzYkdWeUxtUmgKZEdGa2IyZUNQR1JoZEdGa2IyY3RZV2RsYm5RdFkyeDFjM1JsY2kxaFoyVnVkQzFoWkcxcGMzTnBiMjR0WTI5dQpkSEp2Ykd4bGNpNWtZWFJoWkc5bkxuTjJZNEpLWkdGMFlXUnZaeTFoWjJWdWRDMWpiSFZ6ZEdWeUxXRm5aVzUwCkxXRmtiV2x6YzJsdmJpMWpiMjUwY205c2JHVnlMbVJoZEdGa2IyY3VjM1pqTG1Oc2RYTjBaWEl1Ykc5allXd3cKRFFZSktvWklodmNOQVFFTEJRQURnZ0VCQU1xckdhVTcrWk5SZU5pWWtuMTJZVUZBalVnYnpXclFHdU9Cc3ZSNQp4OE0yTm1jTWNrMURmUm9IWDhHcmMwMzJqYkVaWXZXQ3NCVDNTOTFqcm1CNjdpdG9sM3hELzltWGpreVBXMkZuCnUyQWFxL0U2MHl1YVV3VklTTzhNZHBZdWt4dUN3SDFUNVRGUytWMUZqWWZwQjBzSDhvWnRvTEQ2bzFYcGZRUWMKTlBIUHJoYkJBVkpKcEM3UkhDZ2dRMEtLazE0MHp0Qjc2OUVJSlNTM0NRZjd6VmdDZlFsZFJDeTI2MWNlOGFoVwpWQjFObEdhbWdKb2VvTzN5WWZIclBoMmNnQmtUSEQ1dllhVUNRT1Rta3lZclpjOVpDV1F1WFVXZXBXU2tnbkFkCkFqdGhXUzVTYjlEV3hBUVNiSGlubmM1MVpHc0NVRHVGVncwcXRHcTY5Si9yQ000PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
    service:
      name: datadog-agent-cluster-agent-admission-controller
      namespace: datadog
      path: /injectconfig
      port: 443
  failurePolicy: Ignore
  matchPolicy: Exact
  name: datadog.webhook.agent.config
  namespaceSelector: {}
  objectSelector:
    matchExpressions:
    - key: admission.datadoghq.com/enabled
      operator: NotIn
      values:
      - "false"
  reinvocationPolicy: IfNeeded
  rules:
  - apiGroups:
    - ""
    apiVersions:
    - v1
    operations:
    - CREATE
    resources:
    - pods
    scope: '*'
  sideEffects: None
  timeoutSeconds: 10
- admissionReviewVersions:
  - v1
  - v1beta1
  clientConfig:
    caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUVEekNDQXZlZ0F3SUJBZ0lSQU1na3FETWhETnJFbkl4NkwvcE1qemd3RFFZSktvWklodmNOQVFFTEJRQXcKRWpFUU1BNEdBMVVFQ2hNSFJHRjBZV1J2WnpBZUZ3MHlOVEEyTWpZeE5UUXpNakphRncweU5qQTJNall4TlRRNApNakphTUJJeEVEQU9CZ05WQkFvVEIwUmhkR0ZrYjJjd2dnRWlNQTBHQ1NxR1NJYjNEUUVCQVFVQUE0SUJEd0F3CmdnRUtBb0lCQVFEbXNHNldPcWpYWnhveWFFemJJUGpBTHAxanJENEN4L0lXc0JGWWFRcjlSNk44SWNKanJIQ3oKMmYvT2txZGZxUHRVQ2Q0aFhKWW9iQVQ2aVJpMEtTZDMyUk5kL0p5L1RJWjBHbmNjenJJUlp6dFQ1QXFxYS9tRgppUU5vRk5VWWQyS3JBSmxMamFTa0tTNVppekZCeWY2cmVLbmhiWEZ5VngxcGpPc3BHRkJBNUtIeTFUeXE3UUd5CngyVEduNTJtT0lZMDkyQUZBSTFGcWtQZHR6ZDhTNHlyY0V2NHRPcE5OdXA1K1ZUdWtSaC91NzhCK01EQVhpSU4KWFRCaVZNNUNBVU5SbWhvVXF4Q1k1L3Y5M2lyanlLbjJDcldmR1djZTN1ZlVsTTIwYUk4OU4zai9WaUlDQ3JJQQo5akxsK2JrVGhGd3ZjMUt5ckdLaWNOMElNS3RHQ29WZkFnTUJBQUdqZ2dGZU1JSUJXakFPQmdOVkhROEJBZjhFCkJBTUNBcVF3RXdZRFZSMGxCQXd3Q2dZSUt3WUJCUVVIQXdFd0R3WURWUjBUQVFIL0JBVXdBd0VCL3pBZEJnTlYKSFE0RUZnUVVSOEFlVDVZM3Z5U1lMUnZ1UEwvQ1E0TWRzOXd3Z2dFQkJnTlZIUkVFZ2Zrd2dmYUNNR1JoZEdGawpiMmN0WVdkbGJuUXRZMngxYzNSbGNpMWhaMlZ1ZEMxaFpHMXBjM05wYjI0dFkyOXVkSEp2Ykd4bGNvSTRaR0YwCllXUnZaeTFoWjJWdWRDMWpiSFZ6ZEdWeUxXRm5aVzUwTFdGa2JXbHpjMmx2YmkxamIyNTBjbTlzYkdWeUxtUmgKZEdGa2IyZUNQR1JoZEdGa2IyY3RZV2RsYm5RdFkyeDFjM1JsY2kxaFoyVnVkQzFoWkcxcGMzTnBiMjR0WTI5dQpkSEp2Ykd4bGNpNWtZWFJoWkc5bkxuTjJZNEpLWkdGMFlXUnZaeTFoWjJWdWRDMWpiSFZ6ZEdWeUxXRm5aVzUwCkxXRmtiV2x6YzJsdmJpMWpiMjUwY205c2JHVnlMbVJoZEdGa2IyY3VjM1pqTG1Oc2RYTjBaWEl1Ykc5allXd3cKRFFZSktvWklodmNOQVFFTEJRQURnZ0VCQU1xckdhVTcrWk5SZU5pWWtuMTJZVUZBalVnYnpXclFHdU9Cc3ZSNQp4OE0yTm1jTWNrMURmUm9IWDhHcmMwMzJqYkVaWXZXQ3NCVDNTOTFqcm1CNjdpdG9sM3hELzltWGpreVBXMkZuCnUyQWFxL0U2MHl1YVV3VklTTzhNZHBZdWt4dUN3SDFUNVRGUytWMUZqWWZwQjBzSDhvWnRvTEQ2bzFYcGZRUWMKTlBIUHJoYkJBVkpKcEM3UkhDZ2dRMEtLazE0MHp0Qjc2OUVJSlNTM0NRZjd6VmdDZlFsZFJDeTI2MWNlOGFoVwpWQjFObEdhbWdKb2VvTzN5WWZIclBoMmNnQmtUSEQ1dllhVUNRT1Rta3lZclpjOVpDV1F1WFVXZXBXU2tnbkFkCkFqdGhXUzVTYjlEV3hBUVNiSGlubmM1MVpHc0NVRHVGVncwcXRHcTY5Si9yQ000PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
    service:
      name: datadog-agent-cluster-agent-admission-controller
      namespace: datadog
      path: /injecttags
      port: 443
  failurePolicy: Ignore
  matchPolicy: Exact
  name: datadog.webhook.standard.tags
  namespaceSelector: {}
  objectSelector:
    matchExpressions:
    - key: admission.datadoghq.com/enabled
      operator: NotIn
      values:
      - "false"
  reinvocationPolicy: IfNeeded
  rules:
  - apiGroups:
    - ""
    apiVersions:
    - v1
    operations:
    - CREATE
    resources:
    - pods
    scope: '*'
  sideEffects: None
  timeoutSeconds: 10
- admissionReviewVersions:
  - v1
  - v1beta1
  clientConfig:
    caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUVEekNDQXZlZ0F3SUJBZ0lSQU1na3FETWhETnJFbkl4NkwvcE1qemd3RFFZSktvWklodmNOQVFFTEJRQXcKRWpFUU1BNEdBMVVFQ2hNSFJHRjBZV1J2WnpBZUZ3MHlOVEEyTWpZeE5UUXpNakphRncweU5qQTJNall4TlRRNApNakphTUJJeEVEQU9CZ05WQkFvVEIwUmhkR0ZrYjJjd2dnRWlNQTBHQ1NxR1NJYjNEUUVCQVFVQUE0SUJEd0F3CmdnRUtBb0lCQVFEbXNHNldPcWpYWnhveWFFemJJUGpBTHAxanJENEN4L0lXc0JGWWFRcjlSNk44SWNKanJIQ3oKMmYvT2txZGZxUHRVQ2Q0aFhKWW9iQVQ2aVJpMEtTZDMyUk5kL0p5L1RJWjBHbmNjenJJUlp6dFQ1QXFxYS9tRgppUU5vRk5VWWQyS3JBSmxMamFTa0tTNVppekZCeWY2cmVLbmhiWEZ5VngxcGpPc3BHRkJBNUtIeTFUeXE3UUd5CngyVEduNTJtT0lZMDkyQUZBSTFGcWtQZHR6ZDhTNHlyY0V2NHRPcE5OdXA1K1ZUdWtSaC91NzhCK01EQVhpSU4KWFRCaVZNNUNBVU5SbWhvVXF4Q1k1L3Y5M2lyanlLbjJDcldmR1djZTN1ZlVsTTIwYUk4OU4zai9WaUlDQ3JJQQo5akxsK2JrVGhGd3ZjMUt5ckdLaWNOMElNS3RHQ29WZkFnTUJBQUdqZ2dGZU1JSUJXakFPQmdOVkhROEJBZjhFCkJBTUNBcVF3RXdZRFZSMGxCQXd3Q2dZSUt3WUJCUVVIQXdFd0R3WURWUjBUQVFIL0JBVXdBd0VCL3pBZEJnTlYKSFE0RUZnUVVSOEFlVDVZM3Z5U1lMUnZ1UEwvQ1E0TWRzOXd3Z2dFQkJnTlZIUkVFZ2Zrd2dmYUNNR1JoZEdGawpiMmN0WVdkbGJuUXRZMngxYzNSbGNpMWhaMlZ1ZEMxaFpHMXBjM05wYjI0dFkyOXVkSEp2Ykd4bGNvSTRaR0YwCllXUnZaeTFoWjJWdWRDMWpiSFZ6ZEdWeUxXRm5aVzUwTFdGa2JXbHpjMmx2YmkxamIyNTBjbTlzYkdWeUxtUmgKZEdGa2IyZUNQR1JoZEdGa2IyY3RZV2RsYm5RdFkyeDFjM1JsY2kxaFoyVnVkQzFoWkcxcGMzTnBiMjR0WTI5dQpkSEp2Ykd4bGNpNWtZWFJoWkc5bkxuTjJZNEpLWkdGMFlXUnZaeTFoWjJWdWRDMWpiSFZ6ZEdWeUxXRm5aVzUwCkxXRmtiV2x6YzJsdmJpMWpiMjUwY205c2JHVnlMbVJoZEdGa2IyY3VjM1pqTG1Oc2RYTjBaWEl1Ykc5allXd3cKRFFZSktvWklodmNOQVFFTEJRQURnZ0VCQU1xckdhVTcrWk5SZU5pWWtuMTJZVUZBalVnYnpXclFHdU9Cc3ZSNQp4OE0yTm1jTWNrMURmUm9IWDhHcmMwMzJqYkVaWXZXQ3NCVDNTOTFqcm1CNjdpdG9sM3hELzltWGpreVBXMkZuCnUyQWFxL0U2MHl1YVV3VklTTzhNZHBZdWt4dUN3SDFUNVRGUytWMUZqWWZwQjBzSDhvWnRvTEQ2bzFYcGZRUWMKTlBIUHJoYkJBVkpKcEM3UkhDZ2dRMEtLazE0MHp0Qjc2OUVJSlNTM0NRZjd6VmdDZlFsZFJDeTI2MWNlOGFoVwpWQjFObEdhbWdKb2VvTzN5WWZIclBoMmNnQmtUSEQ1dllhVUNRT1Rta3lZclpjOVpDV1F1WFVXZXBXU2tnbkFkCkFqdGhXUzVTYjlEV3hBUVNiSGlubmM1MVpHc0NVRHVGVncwcXRHcTY5Si9yQ000PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
    service:
      name: datadog-agent-cluster-agent-admission-controller
      namespace: datadog
      path: /injectlib
      port: 443
  failurePolicy: Ignore
  matchPolicy: Exact
  name: datadog.webhook.lib.injection
  namespaceSelector: {}
  objectSelector:
    matchExpressions:
    - key: admission.datadoghq.com/enabled
      operator: NotIn
      values:
      - "false"
  reinvocationPolicy: IfNeeded
  rules:
  - apiGroups:
    - ""
    apiVersions:
    - v1
    operations:
    - CREATE
    resources:
    - pods
    scope: '*'
  sideEffects: None
  timeoutSeconds: 10
