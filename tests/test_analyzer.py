#!/usr/bin/env python3
"""
Comprehensive test suite for the OpenShift Must-Gather Analyzer.
Tests core functionality, edge cases, and integration scenarios.
"""

import os
import sys
import unittest
import tempfile
import shutil
import json
import yaml
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from core.analyzer import MustGatherAnalyzer
from core.models import AnalysisContext, AnalysisResult, AnalysisConfig
from core.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorSeverity, ErrorCategory
from parsers.pod_parser import PodParser
from parsers.node_parser import NodeParser
from analyzers.health_analyzer import HealthAnalyzer
from analyzers.security_analyzer import SecurityAnalyzer
from web.app import create_app, WebAnalyzer


class TestMustGatherAnalyzer(unittest.TestCase):
    """Test cases for the main MustGatherAnalyzer class."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = tempfile.mkdtemp()
        self.test_data_dir = Path(self.test_dir) / 'test_data'
        self.test_data_dir.mkdir()
        
        # Create sample must-gather structure
        self.create_sample_must_gather_data()
        
        # Initialize analyzer
        self.analyzer = MustGatherAnalyzer()
        
        # Mock error handler
        self.error_handler = Mock(spec=ErrorHandler)
    
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.test_dir)
    
    def create_sample_must_gather_data(self):
        """Create sample must-gather data for testing."""
        # Create directory structure
        namespaces_dir = self.test_data_dir / 'namespaces'
        default_ns = namespaces_dir / 'default'
        pods_dir = default_ns / 'pods'
        pods_dir.mkdir(parents=True)
        
        # Create sample pod YAML
        pod_data = {
            'apiVersion': 'v1',
            'kind': 'Pod',
            'metadata': {
                'name': 'test-pod',
                'namespace': 'default',
                'labels': {'app': 'test-app'},
                'creationTimestamp': '2024-01-15T10:00:00Z'
            },
            'spec': {
                'containers': [{
                    'name': 'test-container',
                    'image': 'nginx:latest',
                    'resources': {
                        'requests': {'cpu': '100m', 'memory': '128Mi'},
                        'limits': {'cpu': '200m', 'memory': '256Mi'}
                    }
                }]
            },
            'status': {
                'phase': 'Running',
                'conditions': [{
                    'type': 'Ready',
                    'status': 'True',
                    'lastTransitionTime': '2024-01-15T10:01:00Z'
                }],
                'containerStatuses': [{
                    'name': 'test-container',
                    'ready': True,
                    'restartCount': 0,
                    'state': {'running': {'startedAt': '2024-01-15T10:01:00Z'}}
                }]
            }
        }
        
        with open(pods_dir / 'test-pod.yaml', 'w') as f:
            yaml.dump(pod_data, f)
        
        # Create sample node data
        nodes_dir = self.test_data_dir / 'cluster-scoped-resources' / 'core' / 'nodes'
        nodes_dir.mkdir(parents=True)
        
        node_data = {
            'apiVersion': 'v1',
            'kind': 'Node',
            'metadata': {
                'name': 'worker-node-1',
                'labels': {'node-role.kubernetes.io/worker': ''}
            },
            'status': {
                'conditions': [{
                    'type': 'Ready',
                    'status': 'True',
                    'lastHeartbeatTime': '2024-01-15T10:00:00Z'
                }],
                'capacity': {'cpu': '4', 'memory': '8Gi'},
                'allocatable': {'cpu': '3800m', 'memory': '7Gi'}
            }
        }
        
        with open(nodes_dir / 'worker-node-1.yaml', 'w') as f:
            yaml.dump(node_data, f)
        
        # Create sample logs
        logs_dir = self.test_data_dir / 'namespaces' / 'default' / 'pods' / 'test-pod' / 'logs'
        logs_dir.mkdir(parents=True)
        
        log_content = """
2024-01-15T10:01:00.123Z INFO Starting application
2024-01-15T10:01:01.456Z INFO Server listening on port 8080
2024-01-15T10:01:05.789Z WARN High memory usage detected
2024-01-15T10:01:10.012Z ERROR Failed to connect to database
2024-01-15T10:01:15.345Z INFO Retrying database connection
"""
        
        with open(logs_dir / 'test-container.log', 'w') as f:
            f.write(log_content)
    
    def test_analyze_must_gather_basic(self):
        """Test basic must-gather analysis."""
        config = AnalysisConfig(
            must_gather_path=str(self.test_data_dir),
            output_path=str(self.test_dir / 'output'),
            include_security_analysis=True,
            include_performance_analysis=True
        )
        
        result = self.analyzer.analyze(config)
        
        self.assertIsInstance(result, AnalysisResult)
        self.assertGreater(len(result.findings), 0)
        self.assertIsNotNone(result.summary)
    
    def test_analyze_invalid_path(self):
        """Test analysis with invalid must-gather path."""
        config = AnalysisConfig(
            must_gather_path='/nonexistent/path',
            output_path=str(self.test_dir / 'output')
        )
        
        with self.assertRaises(FileNotFoundError):
            self.analyzer.analyze(config)
    
    def test_analyze_empty_directory(self):
        """Test analysis with empty directory."""
        empty_dir = self.test_dir / 'empty'
        empty_dir.mkdir()
        
        config = AnalysisConfig(
            must_gather_path=str(empty_dir),
            output_path=str(self.test_dir / 'output')
        )
        
        result = self.analyzer.analyze(config)
        
        self.assertIsInstance(result, AnalysisResult)
        self.assertEqual(len(result.findings), 0)
    
    def test_analyze_with_filters(self):
        """Test analysis with namespace and resource filters."""
        config = AnalysisConfig(
            must_gather_path=str(self.test_data_dir),
            output_path=str(self.test_dir / 'output'),
            namespace_filter=['default'],
            resource_types=['pods']
        )
        
        result = self.analyzer.analyze(config)
        
        self.assertIsInstance(result, AnalysisResult)
        # Should only analyze pods in default namespace
        pod_findings = [f for f in result.findings if 'pod' in f.title.lower()]
        self.assertGreater(len(pod_findings), 0)


class TestParsers(unittest.TestCase):
    """Test cases for resource parsers."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = tempfile.mkdtemp()
        self.pod_parser = PodParser()
        self.node_parser = NodeParser()
    
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.test_dir)
    
    def test_pod_parser_valid_yaml(self):
        """Test pod parser with valid YAML."""
        pod_yaml = """
apiVersion: v1
kind: Pod
metadata:
  name: test-pod
  namespace: default
spec:
  containers:
  - name: test-container
    image: nginx:latest
status:
  phase: Running
"""
        
        pod_file = Path(self.test_dir) / 'test-pod.yaml'
        with open(pod_file, 'w') as f:
            f.write(pod_yaml)
        
        pods = self.pod_parser.parse_file(pod_file)
        
        self.assertEqual(len(pods), 1)
        self.assertEqual(pods[0].name, 'test-pod')
        self.assertEqual(pods[0].namespace, 'default')
        self.assertEqual(pods[0].status, 'Running')
    
    def test_pod_parser_invalid_yaml(self):
        """Test pod parser with invalid YAML."""
        invalid_yaml = "invalid: yaml: content: ["
        
        pod_file = Path(self.test_dir) / 'invalid.yaml'
        with open(pod_file, 'w') as f:
            f.write(invalid_yaml)
        
        pods = self.pod_parser.parse_file(pod_file)
        
        self.assertEqual(len(pods), 0)
    
    def test_node_parser_valid_yaml(self):
        """Test node parser with valid YAML."""
        node_yaml = """
apiVersion: v1
kind: Node
metadata:
  name: worker-node-1
status:
  conditions:
  - type: Ready
    status: "True"
  capacity:
    cpu: "4"
    memory: "8Gi"
"""
        
        node_file = Path(self.test_dir) / 'test-node.yaml'
        with open(node_file, 'w') as f:
            f.write(node_yaml)
        
        nodes = self.node_parser.parse_file(node_file)
        
        self.assertEqual(len(nodes), 1)
        self.assertEqual(nodes[0].name, 'worker-node-1')
        self.assertTrue(nodes[0].is_ready)


class TestAnalyzers(unittest.TestCase):
    """Test cases for analysis components."""
    
    def setUp(self):
        """Set up test environment."""
        self.health_analyzer = HealthAnalyzer()
        self.security_analyzer = SecurityAnalyzer()
        
        # Create mock context
        self.context = Mock(spec=AnalysisContext)
        self.context.pods = []
        self.context.nodes = []
        self.context.services = []
    
    def test_health_analyzer_healthy_cluster(self):
        """Test health analyzer with healthy cluster."""
        # Mock healthy pods and nodes
        healthy_pod = Mock()
        healthy_pod.name = 'healthy-pod'
        healthy_pod.status = 'Running'
        healthy_pod.is_ready = True
        healthy_pod.restart_count = 0
        
        healthy_node = Mock()
        healthy_node.name = 'healthy-node'
        healthy_node.is_ready = True
        healthy_node.conditions = [{'type': 'Ready', 'status': 'True'}]
        
        self.context.pods = [healthy_pod]
        self.context.nodes = [healthy_node]
        
        findings = self.health_analyzer.analyze(self.context)
        
        # Should have minimal findings for healthy cluster
        critical_findings = [f for f in findings if f.severity == 'Critical']
        self.assertEqual(len(critical_findings), 0)
    
    def test_health_analyzer_unhealthy_cluster(self):
        """Test health analyzer with unhealthy cluster."""
        # Mock unhealthy pods and nodes
        unhealthy_pod = Mock()
        unhealthy_pod.name = 'unhealthy-pod'
        unhealthy_pod.status = 'Failed'
        unhealthy_pod.is_ready = False
        unhealthy_pod.restart_count = 5
        
        unhealthy_node = Mock()
        unhealthy_node.name = 'unhealthy-node'
        unhealthy_node.is_ready = False
        unhealthy_node.conditions = [{'type': 'Ready', 'status': 'False'}]
        
        self.context.pods = [unhealthy_pod]
        self.context.nodes = [unhealthy_node]
        
        findings = self.health_analyzer.analyze(self.context)
        
        # Should have findings for unhealthy cluster
        self.assertGreater(len(findings), 0)
        
        # Should have critical findings
        critical_findings = [f for f in findings if f.severity == 'Critical']
        self.assertGreater(len(critical_findings), 0)
    
    def test_security_analyzer_insecure_pods(self):
        """Test security analyzer with insecure pod configurations."""
        # Mock insecure pod
        insecure_pod = Mock()
        insecure_pod.name = 'insecure-pod'
        insecure_pod.namespace = 'default'
        insecure_pod.security_context = None
        insecure_pod.containers = [Mock()]
        insecure_pod.containers[0].security_context = None
        insecure_pod.containers[0].resources = None
        
        self.context.pods = [insecure_pod]
        
        findings = self.security_analyzer.analyze(self.context)
        
        # Should have security findings
        self.assertGreater(len(findings), 0)
        
        # Should have findings about missing security context
        security_findings = [f for f in findings if 'security' in f.title.lower()]
        self.assertGreater(len(security_findings), 0)


class TestWebInterface(unittest.TestCase):
    """Test cases for the web interface."""
    
    def setUp(self):
        """Set up test environment."""
        self.app = create_app({'TESTING': True})
        self.client = self.app.test_client()
        self.test_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.test_dir)
    
    def test_index_page(self):
        """Test main index page."""
        response = self.client.get('/')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'OpenShift Must-Gather Analyzer', response.data)
    
    def test_upload_page(self):
        """Test upload page."""
        response = self.client.get('/upload')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Upload', response.data)
    
    def test_jobs_api(self):
        """Test jobs API endpoint."""
        response = self.client.get('/api/jobs')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertIsInstance(data, dict)
    
    def test_system_health_api(self):
        """Test system health API endpoint."""
        response = self.client.get('/api/system/health')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertIn('status', data)
        self.assertEqual(data['status'], 'healthy')
    
    def test_upload_file_missing(self):
        """Test file upload with missing file."""
        response = self.client.post('/api/upload')
        self.assertEqual(response.status_code, 400)
    
    def test_upload_file_invalid_type(self):
        """Test file upload with invalid file type."""
        data = {
            'file': (open(__file__, 'rb'), 'test.txt')
        }
        
        response = self.client.post('/api/upload', data=data)
        self.assertEqual(response.status_code, 400)


class TestErrorHandling(unittest.TestCase):
    """Test cases for error handling and logging."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = tempfile.mkdtemp()
        self.error_handler = ErrorHandler(log_dir=self.test_dir)
    
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.test_dir)
    
    def test_handle_file_not_found_error(self):
        """Test handling of FileNotFoundError."""
        error = FileNotFoundError("Test file not found")
        
        structured_error = self.error_handler.handle_error(error)
        
        self.assertEqual(structured_error.category, ErrorCategory.FILE_IO)
        self.assertEqual(structured_error.severity, ErrorSeverity.HIGH)
        self.assertIn("Test file not found", structured_error.message)
    
    def test_handle_value_error(self):
        """Test handling of ValueError."""
        error = ValueError("Invalid value provided")
        
        structured_error = self.error_handler.handle_error(error)
        
        self.assertEqual(structured_error.category, ErrorCategory.VALIDATION)
        self.assertEqual(structured_error.severity, ErrorSeverity.MEDIUM)
    
    def test_error_summary(self):
        """Test error summary generation."""
        # Generate some test errors
        errors = [
            FileNotFoundError("File 1 not found"),
            ValueError("Invalid value 1"),
            FileNotFoundError("File 2 not found"),
            ConnectionError("Network error")
        ]
        
        for error in errors:
            self.error_handler.handle_error(error)
        
        summary = self.error_handler.get_error_summary(hours=1)
        
        self.assertEqual(summary['total_errors'], 4)
        self.assertIn('by_severity', summary)
        self.assertIn('by_category', summary)
    
    def test_error_export(self):
        """Test error export functionality."""
        # Generate test error
        error = ValueError("Test error for export")
        self.error_handler.handle_error(error)
        
        # Export to JSON
        export_file = Path(self.test_dir) / 'errors_export.json'
        self.error_handler.export_errors(str(export_file), format='json')
        
        self.assertTrue(export_file.exists())
        
        # Verify export content
        with open(export_file, 'r') as f:
            exported_data = json.load(f)
        
        self.assertIsInstance(exported_data, list)
        self.assertGreater(len(exported_data), 0)


class TestIntegration(unittest.TestCase):
    """Integration test cases."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = tempfile.mkdtemp()
        self.create_comprehensive_test_data()
    
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.test_dir)
    
    def create_comprehensive_test_data(self):
        """Create comprehensive test data for integration testing."""
        # This would create a more complete must-gather structure
        # with multiple namespaces, various resource types, etc.
        pass
    
    def test_end_to_end_analysis(self):
        """Test complete end-to-end analysis workflow."""
        # This would test the complete workflow from
        # data ingestion to report generation
        pass
    
    def test_web_interface_integration(self):
        """Test web interface integration with analysis engine."""
        # This would test the integration between web interface
        # and the analysis engine
        pass


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestMustGatherAnalyzer,
        TestParsers,
        TestAnalyzers,
        TestWebInterface,
        TestErrorHandling,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Exit with appropriate code
    sys.exit(0 if result.wasSuccessful() else 1)
