#!/usr/bin/env python3
"""
Validation test suite for OpenShift Must-Gather Analyzer.
Tests data validation, schema compliance, and edge cases.
"""

import os
import sys
import unittest
import tempfile
import shutil
import json
import yaml
from pathlib import Path
from unittest.mock import Mock, patch
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from core.models import AnalysisConfig, AnalysisResult, Finding
from utils.validators import DataValidator, SchemaValidator
from utils.config import ConfigurationManager


class TestDataValidation(unittest.TestCase):
    """Test cases for data validation."""
    
    def setUp(self):
        """Set up test environment."""
        self.validator = DataValidator()
        self.test_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.test_dir)
    
    def test_validate_kubernetes_yaml_valid(self):
        """Test validation of valid Kubernetes YAML."""
        valid_yaml = {
            'apiVersion': 'v1',
            'kind': 'Pod',
            'metadata': {
                'name': 'test-pod',
                'namespace': 'default'
            },
            'spec': {
                'containers': [{
                    'name': 'test-container',
                    'image': 'nginx:latest'
                }]
            }
        }
        
        is_valid, errors = self.validator.validate_kubernetes_resource(valid_yaml)
        
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
    
    def test_validate_kubernetes_yaml_invalid(self):
        """Test validation of invalid Kubernetes YAML."""
        invalid_yaml = {
            'apiVersion': 'v1',
            'kind': 'Pod',
            'metadata': {
                'name': '',  # Invalid: empty name
                'namespace': 'default'
            },
            'spec': {
                'containers': []  # Invalid: no containers
            }
        }
        
        is_valid, errors = self.validator.validate_kubernetes_resource(invalid_yaml)
        
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)
    
    def test_validate_must_gather_structure(self):
        """Test validation of must-gather directory structure."""
        # Create valid structure
        valid_structure = self.test_dir / 'valid'
        (valid_structure / 'namespaces' / 'default' / 'pods').mkdir(parents=True)
        (valid_structure / 'cluster-scoped-resources' / 'core' / 'nodes').mkdir(parents=True)
        
        is_valid, errors = self.validator.validate_must_gather_structure(valid_structure)
        
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
    
    def test_validate_must_gather_structure_invalid(self):
        """Test validation of invalid must-gather directory structure."""
        # Create invalid structure (missing required directories)
        invalid_structure = self.test_dir / 'invalid'
        invalid_structure.mkdir()
        
        is_valid, errors = self.validator.validate_must_gather_structure(invalid_structure)
        
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)
    
    def test_validate_analysis_config(self):
        """Test validation of analysis configuration."""
        valid_config = AnalysisConfig(
            must_gather_path=str(self.test_dir),
            output_path=str(self.test_dir / 'output'),
            namespace_filter=['default', 'kube-system'],
            resource_types=['pods', 'services'],
            include_security_analysis=True,
            include_performance_analysis=True
        )
        
        is_valid, errors = self.validator.validate_analysis_config(valid_config)
        
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
    
    def test_validate_analysis_config_invalid_path(self):
        """Test validation of analysis configuration with invalid path."""
        invalid_config = AnalysisConfig(
            must_gather_path='/nonexistent/path',
            output_path=str(self.test_dir / 'output')
        )
        
        is_valid, errors = self.validator.validate_analysis_config(invalid_config)
        
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)


class TestSchemaValidation(unittest.TestCase):
    """Test cases for schema validation."""
    
    def setUp(self):
        """Set up test environment."""
        self.schema_validator = SchemaValidator()
    
    def test_validate_finding_schema(self):
        """Test validation of finding schema."""
        valid_finding = {
            'id': 'finding-001',
            'title': 'Test Finding',
            'description': 'This is a test finding',
            'severity': 'Medium',
            'category': 'Configuration',
            'affected_resources': ['pod/test-pod'],
            'recommendations': ['Fix the configuration'],
            'timestamp': datetime.now().isoformat()
        }
        
        is_valid, errors = self.schema_validator.validate_finding(valid_finding)
        
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
    
    def test_validate_finding_schema_invalid(self):
        """Test validation of invalid finding schema."""
        invalid_finding = {
            'id': '',  # Invalid: empty ID
            'title': 'Test Finding',
            'severity': 'Invalid',  # Invalid: not in allowed values
            'category': 'Configuration'
            # Missing required fields
        }
        
        is_valid, errors = self.schema_validator.validate_finding(invalid_finding)
        
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)
    
    def test_validate_analysis_result_schema(self):
        """Test validation of analysis result schema."""
        valid_result = {
            'analysis_id': 'analysis-001',
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_resources': 100,
                'total_findings': 5,
                'critical_findings': 1,
                'high_findings': 2,
                'medium_findings': 2,
                'low_findings': 0
            },
            'findings': [],
            'recommendations': [],
            'metadata': {
                'analyzer_version': '1.0.0',
                'analysis_duration': 120.5
            }
        }
        
        is_valid, errors = self.schema_validator.validate_analysis_result(valid_result)
        
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)


class TestEdgeCases(unittest.TestCase):
    """Test cases for edge cases and error conditions."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.test_dir)
    
    def test_large_must_gather_data(self):
        """Test handling of large must-gather data sets."""
        # Create a large number of test files
        large_data_dir = self.test_dir / 'large_data'
        large_data_dir.mkdir()
        
        # Create 1000 pod files
        pods_dir = large_data_dir / 'namespaces' / 'default' / 'pods'
        pods_dir.mkdir(parents=True)
        
        for i in range(1000):
            pod_data = {
                'apiVersion': 'v1',
                'kind': 'Pod',
                'metadata': {
                    'name': f'test-pod-{i}',
                    'namespace': 'default'
                },
                'spec': {
                    'containers': [{
                        'name': 'test-container',
                        'image': 'nginx:latest'
                    }]
                },
                'status': {'phase': 'Running'}
            }
            
            with open(pods_dir / f'test-pod-{i}.yaml', 'w') as f:
                yaml.dump(pod_data, f)
        
        # Test that the system can handle this amount of data
        from core.analyzer import MustGatherAnalyzer
        analyzer = MustGatherAnalyzer()
        
        config = AnalysisConfig(
            must_gather_path=str(large_data_dir),
            output_path=str(self.test_dir / 'output')
        )
        
        # This should complete without errors
        result = analyzer.analyze(config)
        self.assertIsInstance(result, AnalysisResult)
    
    def test_corrupted_yaml_files(self):
        """Test handling of corrupted YAML files."""
        corrupted_dir = self.test_dir / 'corrupted'
        pods_dir = corrupted_dir / 'namespaces' / 'default' / 'pods'
        pods_dir.mkdir(parents=True)
        
        # Create corrupted YAML files
        corrupted_files = [
            'invalid: yaml: [',
            '{{invalid json}}',
            '',  # Empty file
            'valid_start:\n  but_invalid: [unclosed',
            '\x00\x01\x02binary_data'  # Binary data
        ]
        
        for i, content in enumerate(corrupted_files):
            with open(pods_dir / f'corrupted-{i}.yaml', 'w') as f:
                f.write(content)
        
        # Test that the system handles corrupted files gracefully
        from core.analyzer import MustGatherAnalyzer
        analyzer = MustGatherAnalyzer()
        
        config = AnalysisConfig(
            must_gather_path=str(corrupted_dir),
            output_path=str(self.test_dir / 'output')
        )
        
        # Should complete without crashing
        result = analyzer.analyze(config)
        self.assertIsInstance(result, AnalysisResult)
    
    def test_permission_denied_files(self):
        """Test handling of files with permission issues."""
        if os.name == 'nt':  # Skip on Windows
            self.skipTest("Permission test not applicable on Windows")
        
        restricted_dir = self.test_dir / 'restricted'
        pods_dir = restricted_dir / 'namespaces' / 'default' / 'pods'
        pods_dir.mkdir(parents=True)
        
        # Create a file and remove read permissions
        restricted_file = pods_dir / 'restricted.yaml'
        with open(restricted_file, 'w') as f:
            f.write('apiVersion: v1\nkind: Pod\n')
        
        os.chmod(restricted_file, 0o000)  # No permissions
        
        try:
            from core.analyzer import MustGatherAnalyzer
            analyzer = MustGatherAnalyzer()
            
            config = AnalysisConfig(
                must_gather_path=str(restricted_dir),
                output_path=str(self.test_dir / 'output')
            )
            
            # Should handle permission errors gracefully
            result = analyzer.analyze(config)
            self.assertIsInstance(result, AnalysisResult)
        
        finally:
            # Restore permissions for cleanup
            os.chmod(restricted_file, 0o644)
    
    def test_extremely_nested_resources(self):
        """Test handling of extremely nested resource structures."""
        nested_dir = self.test_dir / 'nested'
        
        # Create deeply nested directory structure
        current_dir = nested_dir
        for i in range(50):  # 50 levels deep
            current_dir = current_dir / f'level-{i}'
        
        current_dir.mkdir(parents=True)
        
        # Create a resource file at the deepest level
        resource_file = current_dir / 'deep-resource.yaml'
        with open(resource_file, 'w') as f:
            yaml.dump({
                'apiVersion': 'v1',
                'kind': 'ConfigMap',
                'metadata': {'name': 'deep-config'}
            }, f)
        
        from core.analyzer import MustGatherAnalyzer
        analyzer = MustGatherAnalyzer()
        
        config = AnalysisConfig(
            must_gather_path=str(nested_dir),
            output_path=str(self.test_dir / 'output')
        )
        
        # Should handle deep nesting
        result = analyzer.analyze(config)
        self.assertIsInstance(result, AnalysisResult)


class TestPerformance(unittest.TestCase):
    """Test cases for performance validation."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.test_dir)
    
    def test_analysis_performance(self):
        """Test that analysis completes within reasonable time."""
        # Create moderate-sized test data
        test_data_dir = self.test_dir / 'perf_test'
        
        # Create 100 pods across 10 namespaces
        for ns in range(10):
            ns_name = f'namespace-{ns}'
            pods_dir = test_data_dir / 'namespaces' / ns_name / 'pods'
            pods_dir.mkdir(parents=True)
            
            for pod in range(10):
                pod_data = {
                    'apiVersion': 'v1',
                    'kind': 'Pod',
                    'metadata': {
                        'name': f'pod-{pod}',
                        'namespace': ns_name
                    },
                    'spec': {
                        'containers': [{
                            'name': 'container',
                            'image': 'nginx:latest'
                        }]
                    },
                    'status': {'phase': 'Running'}
                }
                
                with open(pods_dir / f'pod-{pod}.yaml', 'w') as f:
                    yaml.dump(pod_data, f)
        
        from core.analyzer import MustGatherAnalyzer
        analyzer = MustGatherAnalyzer()
        
        config = AnalysisConfig(
            must_gather_path=str(test_data_dir),
            output_path=str(self.test_dir / 'output')
        )
        
        # Measure analysis time
        start_time = datetime.now()
        result = analyzer.analyze(config)
        end_time = datetime.now()
        
        duration = (end_time - start_time).total_seconds()
        
        # Should complete within 30 seconds for this dataset
        self.assertLess(duration, 30.0)
        self.assertIsInstance(result, AnalysisResult)
    
    def test_memory_usage(self):
        """Test memory usage during analysis."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # Create test data
        test_data_dir = self.test_dir / 'memory_test'
        pods_dir = test_data_dir / 'namespaces' / 'default' / 'pods'
        pods_dir.mkdir(parents=True)
        
        # Create 500 pods
        for i in range(500):
            pod_data = {
                'apiVersion': 'v1',
                'kind': 'Pod',
                'metadata': {
                    'name': f'memory-test-pod-{i}',
                    'namespace': 'default'
                },
                'spec': {
                    'containers': [{
                        'name': 'container',
                        'image': 'nginx:latest'
                    }]
                },
                'status': {'phase': 'Running'}
            }
            
            with open(pods_dir / f'pod-{i}.yaml', 'w') as f:
                yaml.dump(pod_data, f)
        
        from core.analyzer import MustGatherAnalyzer
        analyzer = MustGatherAnalyzer()
        
        config = AnalysisConfig(
            must_gather_path=str(test_data_dir),
            output_path=str(self.test_dir / 'output')
        )
        
        # Run analysis
        result = analyzer.analyze(config)
        
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 500MB)
        self.assertLess(memory_increase, 500 * 1024 * 1024)
        self.assertIsInstance(result, AnalysisResult)


class TestConfigurationValidation(unittest.TestCase):
    """Test cases for configuration validation."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = tempfile.mkdtemp()
        self.config_manager = ConfigurationManager()
    
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.test_dir)
    
    def test_valid_configuration_file(self):
        """Test loading valid configuration file."""
        config_data = {
            'analysis': {
                'include_security_analysis': True,
                'include_performance_analysis': True,
                'namespace_filter': ['default', 'kube-system'],
                'resource_types': ['pods', 'services', 'deployments']
            },
            'output': {
                'format': 'json',
                'include_raw_data': False,
                'compress_output': True
            },
            'logging': {
                'level': 'INFO',
                'file_output': True
            }
        }
        
        config_file = self.test_dir / 'valid_config.yaml'
        with open(config_file, 'w') as f:
            yaml.dump(config_data, f)
        
        config = self.config_manager.load_config(str(config_file))
        
        self.assertIsNotNone(config)
        self.assertTrue(config.include_security_analysis)
        self.assertEqual(len(config.namespace_filter), 2)
    
    def test_invalid_configuration_file(self):
        """Test loading invalid configuration file."""
        invalid_config = {
            'analysis': {
                'include_security_analysis': 'invalid_boolean',  # Should be boolean
                'namespace_filter': 'not_a_list',  # Should be list
                'invalid_option': 'value'  # Unknown option
            }
        }
        
        config_file = self.test_dir / 'invalid_config.yaml'
        with open(config_file, 'w') as f:
            yaml.dump(invalid_config, f)
        
        with self.assertRaises(ValueError):
            self.config_manager.load_config(str(config_file))
    
    def test_missing_configuration_file(self):
        """Test handling of missing configuration file."""
        nonexistent_file = self.test_dir / 'nonexistent.yaml'
        
        with self.assertRaises(FileNotFoundError):
            self.config_manager.load_config(str(nonexistent_file))


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestDataValidation,
        TestSchemaValidation,
        TestEdgeCases,
        TestPerformance,
        TestConfigurationValidation
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Exit with appropriate code
    sys.exit(0 if result.wasSuccessful() else 1)
