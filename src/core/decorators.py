#!/usr/bin/env python3
"""
Decorators for error handling, logging, and monitoring in OpenShift Must-Gather Analyzer.
Provides reusable decorators for common cross-cutting concerns.
"""

import time
import functools
from typing import Any, Callable, Optional, Dict, Union
from datetime import datetime

from .error_handler import get_error_handler, ErrorContext, ErrorSeverity, ErrorCategory


def handle_errors(severity: Optional[ErrorSeverity] = None,
                 category: Optional[ErrorCategory] = None,
                 reraise: bool = True,
                 default_return: Any = None,
                 context_factory: Optional[Callable] = None):
    """
    Decorator for automatic error handling and logging.
    
    Args:
        severity: Override error severity
        category: Override error category
        reraise: Whether to reraise the exception after handling
        default_return: Default return value if exception is caught and not reraised
        context_factory: Function to create error context
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            error_handler = get_error_handler()
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Create context
                context = None
                if context_factory:
                    try:
                        context = context_factory(*args, **kwargs)
                    except:
                        pass
                
                if context is None:
                    context = ErrorContext(
                        operation=func.__name__,
                        component=func.__module__
                    )
                
                # Handle the error
                structured_error = error_handler.handle_error(
                    e, 
                    context=context,
                    severity=severity,
                    category=category
                )
                
                if reraise:
                    raise
                else:
                    return default_return
        
        return wrapper
    return decorator


def monitor_performance(operation_name: Optional[str] = None,
                       log_threshold_ms: float = 1000.0,
                       context_factory: Optional[Callable] = None):
    """
    Decorator for performance monitoring and logging.
    
    Args:
        operation_name: Custom operation name (defaults to function name)
        log_threshold_ms: Log if operation takes longer than this threshold
        context_factory: Function to create performance context
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            error_handler = get_error_handler()
            operation = operation_name or f"{func.__module__}.{func.__name__}"
            
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                duration_ms = duration * 1000
                
                # Create context
                context = {}
                if context_factory:
                    try:
                        context = context_factory(*args, **kwargs)
                    except:
                        pass
                
                # Add function metadata to context
                context.update({
                    'function': func.__name__,
                    'module': func.__module__,
                    'args_count': len(args),
                    'kwargs_count': len(kwargs)
                })
                
                # Log performance if above threshold
                if duration_ms >= log_threshold_ms:
                    error_handler.log_performance(operation, duration, context)
        
        return wrapper
    return decorator


def audit_action(action: Optional[str] = None,
                resource_factory: Optional[Callable] = None,
                user_id_factory: Optional[Callable] = None):
    """
    Decorator for audit logging.
    
    Args:
        action: Custom action name (defaults to function name)
        resource_factory: Function to extract resource from function args
        user_id_factory: Function to extract user ID from function args
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            error_handler = get_error_handler()
            action_name = action or func.__name__
            
            # Extract resource and user ID
            resource = None
            user_id = None
            
            if resource_factory:
                try:
                    resource = resource_factory(*args, **kwargs)
                except:
                    pass
            
            if user_id_factory:
                try:
                    user_id = user_id_factory(*args, **kwargs)
                except:
                    pass
            
            # Execute function and determine result
            result = "success"
            details = {}
            
            try:
                return_value = func(*args, **kwargs)
                details['return_type'] = type(return_value).__name__
                return return_value
            except Exception as e:
                result = "failure"
                details['error'] = str(e)
                details['error_type'] = type(e).__name__
                raise
            finally:
                # Log audit event
                error_handler.log_audit(
                    action=action_name,
                    user_id=user_id,
                    resource=resource,
                    result=result,
                    details=details
                )
        
        return wrapper
    return decorator


def retry_on_failure(max_attempts: int = 3,
                    delay: float = 1.0,
                    backoff_factor: float = 2.0,
                    exceptions: tuple = (Exception,),
                    on_retry: Optional[Callable] = None):
    """
    Decorator for automatic retry on failure.
    
    Args:
        max_attempts: Maximum number of retry attempts
        delay: Initial delay between retries (seconds)
        backoff_factor: Multiplier for delay on each retry
        exceptions: Tuple of exceptions to retry on
        on_retry: Callback function called on each retry
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            error_handler = get_error_handler()
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_attempts - 1:
                        # Last attempt failed, handle error and reraise
                        context = ErrorContext(
                            operation=func.__name__,
                            component=func.__module__,
                            additional_data={
                                'attempt': attempt + 1,
                                'max_attempts': max_attempts
                            }
                        )
                        
                        error_handler.handle_error(e, context=context)
                        raise
                    
                    # Log retry attempt
                    error_handler.logger.warning(
                        f"Attempt {attempt + 1}/{max_attempts} failed for {func.__name__}: {e}. "
                        f"Retrying in {current_delay:.2f} seconds..."
                    )
                    
                    # Call retry callback if provided
                    if on_retry:
                        try:
                            on_retry(attempt + 1, e, *args, **kwargs)
                        except:
                            pass
                    
                    # Wait before retry
                    time.sleep(current_delay)
                    current_delay *= backoff_factor
            
            # This should never be reached, but just in case
            raise last_exception
        
        return wrapper
    return decorator


def validate_input(validation_func: Callable,
                  error_message: str = "Input validation failed"):
    """
    Decorator for input validation.
    
    Args:
        validation_func: Function that takes (*args, **kwargs) and returns bool
        error_message: Error message for validation failure
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if not validation_func(*args, **kwargs):
                raise ValueError(error_message)
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


def cache_result(ttl_seconds: int = 300,
                key_factory: Optional[Callable] = None):
    """
    Decorator for caching function results.
    
    Args:
        ttl_seconds: Time to live for cached results
        key_factory: Function to generate cache key from args/kwargs
    """
    cache = {}
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            if key_factory:
                cache_key = key_factory(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # Check cache
            now = time.time()
            if cache_key in cache:
                result, timestamp = cache[cache_key]
                if now - timestamp < ttl_seconds:
                    return result
                else:
                    # Expired, remove from cache
                    del cache[cache_key]
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache[cache_key] = (result, now)
            
            return result
        
        return wrapper
    return decorator


def rate_limit(calls_per_second: float = 1.0):
    """
    Decorator for rate limiting function calls.
    
    Args:
        calls_per_second: Maximum calls per second allowed
    """
    min_interval = 1.0 / calls_per_second
    last_called = [0.0]
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            now = time.time()
            elapsed = now - last_called[0]
            
            if elapsed < min_interval:
                sleep_time = min_interval - elapsed
                time.sleep(sleep_time)
            
            last_called[0] = time.time()
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


def require_auth(auth_checker: Callable):
    """
    Decorator for authentication requirement.
    
    Args:
        auth_checker: Function that checks authentication from args/kwargs
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if not auth_checker(*args, **kwargs):
                error_handler = get_error_handler()
                context = ErrorContext(
                    operation=func.__name__,
                    component=func.__module__
                )
                
                auth_error = PermissionError("Authentication required")
                error_handler.handle_error(
                    auth_error,
                    context=context,
                    category=ErrorCategory.AUTHENTICATION
                )
                raise auth_error
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


# Convenience decorators for common use cases
def handle_file_errors(func: Callable) -> Callable:
    """Convenience decorator for file operation error handling."""
    return handle_errors(
        category=ErrorCategory.FILE_IO,
        severity=ErrorSeverity.MEDIUM
    )(func)


def handle_network_errors(func: Callable) -> Callable:
    """Convenience decorator for network operation error handling."""
    return handle_errors(
        category=ErrorCategory.NETWORK,
        severity=ErrorSeverity.HIGH
    )(func)


def handle_parsing_errors(func: Callable) -> Callable:
    """Convenience decorator for parsing operation error handling."""
    return handle_errors(
        category=ErrorCategory.PARSING,
        severity=ErrorSeverity.MEDIUM
    )(func)


def monitor_critical_operation(func: Callable) -> Callable:
    """Convenience decorator for monitoring critical operations."""
    return monitor_performance(
        log_threshold_ms=500.0
    )(handle_errors(
        severity=ErrorSeverity.HIGH
    )(func))


# Class decorator for automatic error handling on all methods
def handle_class_errors(severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                       category: ErrorCategory = ErrorCategory.UNKNOWN):
    """
    Class decorator to add error handling to all methods.
    
    Args:
        severity: Default error severity for all methods
        category: Default error category for all methods
    """
    def decorator(cls):
        for attr_name in dir(cls):
            attr = getattr(cls, attr_name)
            if callable(attr) and not attr_name.startswith('_'):
                wrapped = handle_errors(
                    severity=severity,
                    category=category
                )(attr)
                setattr(cls, attr_name, wrapped)
        return cls
    
    return decorator
