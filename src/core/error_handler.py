#!/usr/bin/env python3
"""
Comprehensive error handling and logging system for OpenShift Must-Gather Analyzer.
Provides structured error handling, logging, monitoring, and recovery mechanisms.
"""

import os
import sys
import json
import traceback
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from enum import Enum
from dataclasses import dataclass, asdict
from contextlib import contextmanager


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for classification."""
    SYSTEM = "system"
    NETWORK = "network"
    FILE_IO = "file_io"
    PARSING = "parsing"
    VALIDATION = "validation"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    RESOURCE = "resource"
    CONFIGURATION = "configuration"
    USER_INPUT = "user_input"
    EXTERNAL_SERVICE = "external_service"
    UNKNOWN = "unknown"


@dataclass
class ErrorContext:
    """Context information for errors."""
    job_id: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    file_path: Optional[str] = None
    operation: Optional[str] = None
    component: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None


@dataclass
class StructuredError:
    """Structured error representation."""
    error_id: str
    timestamp: datetime
    severity: ErrorSeverity
    category: ErrorCategory
    message: str
    details: str
    context: ErrorContext
    stack_trace: Optional[str] = None
    resolution_steps: Optional[List[str]] = None
    related_errors: Optional[List[str]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'error_id': self.error_id,
            'timestamp': self.timestamp.isoformat(),
            'severity': self.severity.value,
            'category': self.category.value,
            'message': self.message,
            'details': self.details,
            'context': asdict(self.context),
            'stack_trace': self.stack_trace,
            'resolution_steps': self.resolution_steps,
            'related_errors': self.related_errors
        }


class ErrorHandler:
    """Comprehensive error handling and logging system."""
    
    def __init__(self, log_dir: str = "logs", log_level: str = "INFO"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # Error storage
        self.errors: Dict[str, StructuredError] = {}
        self.error_counts: Dict[str, int] = {}
        
        # Setup logging
        self.setup_logging(log_level)
        
        # Error patterns for automatic categorization
        self.error_patterns = self._load_error_patterns()
        
        # Recovery strategies
        self.recovery_strategies = self._setup_recovery_strategies()
    
    def setup_logging(self, log_level: str):
        """Setup comprehensive logging configuration."""
        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
        )
        
        json_formatter = JsonFormatter()
        
        # Setup main logger
        self.logger = logging.getLogger('must_gather_analyzer')
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(detailed_formatter)
        self.logger.addHandler(console_handler)
        
        # File handler for general logs
        file_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / 'analyzer.log',
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)
        self.logger.addHandler(file_handler)
        
        # JSON file handler for structured logs
        json_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / 'analyzer.json.log',
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        json_handler.setLevel(logging.INFO)
        json_handler.setFormatter(json_formatter)
        self.logger.addHandler(json_handler)
        
        # Error-specific handler
        error_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / 'errors.log',
            maxBytes=5*1024*1024,  # 5MB
            backupCount=10
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)
        self.logger.addHandler(error_handler)
        
        # Performance handler
        perf_handler = logging.FileHandler(self.log_dir / 'performance.log')
        perf_handler.setLevel(logging.INFO)
        perf_handler.setFormatter(detailed_formatter)
        
        self.perf_logger = logging.getLogger('performance')
        self.perf_logger.addHandler(perf_handler)
        self.perf_logger.setLevel(logging.INFO)
    
    def handle_error(self, 
                    exception: Exception,
                    context: Optional[ErrorContext] = None,
                    severity: Optional[ErrorSeverity] = None,
                    category: Optional[ErrorCategory] = None,
                    custom_message: Optional[str] = None) -> StructuredError:
        """Handle and log an error with full context."""
        
        # Generate unique error ID
        error_id = self._generate_error_id()
        
        # Determine severity and category if not provided
        if severity is None:
            severity = self._determine_severity(exception)
        
        if category is None:
            category = self._categorize_error(exception)
        
        # Create structured error
        structured_error = StructuredError(
            error_id=error_id,
            timestamp=datetime.now(),
            severity=severity,
            category=category,
            message=custom_message or str(exception),
            details=self._extract_error_details(exception),
            context=context or ErrorContext(),
            stack_trace=traceback.format_exc(),
            resolution_steps=self._get_resolution_steps(exception, category),
            related_errors=self._find_related_errors(exception)
        )
        
        # Store error
        self.errors[error_id] = structured_error
        
        # Update error counts
        error_key = f"{category.value}:{type(exception).__name__}"
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        
        # Log error
        self._log_structured_error(structured_error)
        
        # Attempt recovery if applicable
        if severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            self._attempt_recovery(structured_error)
        
        # Send alerts for critical errors
        if severity == ErrorSeverity.CRITICAL:
            self._send_alert(structured_error)
        
        return structured_error
    
    def log_performance(self, operation: str, duration: float, context: Optional[Dict] = None):
        """Log performance metrics."""
        perf_data = {
            'operation': operation,
            'duration_ms': duration * 1000,
            'timestamp': datetime.now().isoformat(),
            'context': context or {}
        }
        
        self.perf_logger.info(f"PERFORMANCE: {json.dumps(perf_data)}")
    
    def log_audit(self, action: str, user_id: Optional[str] = None, 
                  resource: Optional[str] = None, result: str = "success",
                  details: Optional[Dict] = None):
        """Log audit events."""
        audit_data = {
            'action': action,
            'user_id': user_id,
            'resource': resource,
            'result': result,
            'timestamp': datetime.now().isoformat(),
            'details': details or {}
        }
        
        audit_logger = logging.getLogger('audit')
        if not audit_logger.handlers:
            handler = logging.FileHandler(self.log_dir / 'audit.log')
            handler.setFormatter(JsonFormatter())
            audit_logger.addHandler(handler)
            audit_logger.setLevel(logging.INFO)
        
        audit_logger.info(json.dumps(audit_data))
    
    @contextmanager
    def error_context(self, **context_kwargs):
        """Context manager for error handling with automatic context."""
        context = ErrorContext(**context_kwargs)
        try:
            yield context
        except Exception as e:
            self.handle_error(e, context=context)
            raise
    
    @contextmanager
    def performance_monitor(self, operation: str, context: Optional[Dict] = None):
        """Context manager for performance monitoring."""
        start_time = datetime.now()
        try:
            yield
        finally:
            duration = (datetime.now() - start_time).total_seconds()
            self.log_performance(operation, duration, context)
    
    def get_error_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get error summary for the specified time period."""
        cutoff_time = datetime.now().timestamp() - (hours * 3600)
        
        recent_errors = [
            error for error in self.errors.values()
            if error.timestamp.timestamp() > cutoff_time
        ]
        
        summary = {
            'total_errors': len(recent_errors),
            'by_severity': {},
            'by_category': {},
            'top_errors': {},
            'error_rate': len(recent_errors) / hours if hours > 0 else 0
        }
        
        # Group by severity
        for error in recent_errors:
            severity = error.severity.value
            summary['by_severity'][severity] = summary['by_severity'].get(severity, 0) + 1
        
        # Group by category
        for error in recent_errors:
            category = error.category.value
            summary['by_category'][category] = summary['by_category'].get(category, 0) + 1
        
        # Top error types
        error_types = {}
        for error in recent_errors:
            error_type = error.message.split(':')[0] if ':' in error.message else error.message
            error_types[error_type] = error_types.get(error_type, 0) + 1
        
        summary['top_errors'] = dict(sorted(error_types.items(), key=lambda x: x[1], reverse=True)[:10])
        
        return summary
    
    def export_errors(self, output_file: str, format: str = 'json', hours: int = 24):
        """Export errors to file."""
        cutoff_time = datetime.now().timestamp() - (hours * 3600)
        
        recent_errors = [
            error.to_dict() for error in self.errors.values()
            if error.timestamp.timestamp() > cutoff_time
        ]
        
        output_path = Path(output_file)
        
        if format.lower() == 'json':
            with open(output_path, 'w') as f:
                json.dump(recent_errors, f, indent=2, default=str)
        elif format.lower() == 'csv':
            import csv
            with open(output_path, 'w', newline='') as f:
                if recent_errors:
                    writer = csv.DictWriter(f, fieldnames=recent_errors[0].keys())
                    writer.writeheader()
                    writer.writerows(recent_errors)
    
    # Private methods
    def _generate_error_id(self) -> str:
        """Generate unique error ID."""
        import uuid
        return f"ERR_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
    
    def _determine_severity(self, exception: Exception) -> ErrorSeverity:
        """Determine error severity based on exception type."""
        critical_exceptions = (SystemExit, KeyboardInterrupt, MemoryError)
        high_exceptions = (FileNotFoundError, PermissionError, ConnectionError)
        medium_exceptions = (ValueError, TypeError, AttributeError)
        
        if isinstance(exception, critical_exceptions):
            return ErrorSeverity.CRITICAL
        elif isinstance(exception, high_exceptions):
            return ErrorSeverity.HIGH
        elif isinstance(exception, medium_exceptions):
            return ErrorSeverity.MEDIUM
        else:
            return ErrorSeverity.LOW
    
    def _categorize_error(self, exception: Exception) -> ErrorCategory:
        """Categorize error based on exception type and message."""
        exception_categories = {
            FileNotFoundError: ErrorCategory.FILE_IO,
            PermissionError: ErrorCategory.FILE_IO,
            ConnectionError: ErrorCategory.NETWORK,
            TimeoutError: ErrorCategory.NETWORK,
            ValueError: ErrorCategory.VALIDATION,
            TypeError: ErrorCategory.VALIDATION,
            KeyError: ErrorCategory.CONFIGURATION,
            AttributeError: ErrorCategory.CONFIGURATION,
            ImportError: ErrorCategory.SYSTEM,
            ModuleNotFoundError: ErrorCategory.SYSTEM,
            MemoryError: ErrorCategory.RESOURCE,
            OSError: ErrorCategory.SYSTEM
        }
        
        return exception_categories.get(type(exception), ErrorCategory.UNKNOWN)
    
    def _extract_error_details(self, exception: Exception) -> str:
        """Extract detailed error information."""
        details = {
            'exception_type': type(exception).__name__,
            'exception_module': type(exception).__module__,
            'args': exception.args,
        }
        
        # Add specific details for certain exception types
        if hasattr(exception, 'errno'):
            details['errno'] = exception.errno
        
        if hasattr(exception, 'filename'):
            details['filename'] = exception.filename
        
        if hasattr(exception, 'response'):
            details['response_status'] = getattr(exception.response, 'status_code', None)
        
        return json.dumps(details, default=str)
    
    def _get_resolution_steps(self, exception: Exception, category: ErrorCategory) -> List[str]:
        """Get resolution steps based on error type and category."""
        resolution_map = {
            ErrorCategory.FILE_IO: [
                "Check if the file path exists and is accessible",
                "Verify file permissions",
                "Ensure sufficient disk space",
                "Check if the file is locked by another process"
            ],
            ErrorCategory.NETWORK: [
                "Check network connectivity",
                "Verify service endpoints are accessible",
                "Check firewall and proxy settings",
                "Retry the operation after a brief delay"
            ],
            ErrorCategory.VALIDATION: [
                "Validate input data format and types",
                "Check for required fields",
                "Verify data constraints and limits",
                "Review input validation rules"
            ],
            ErrorCategory.RESOURCE: [
                "Check available system resources (CPU, memory, disk)",
                "Close unnecessary processes",
                "Increase resource limits if possible",
                "Consider processing data in smaller chunks"
            ]
        }
        
        return resolution_map.get(category, ["Review error details and consult documentation"])
    
    def _find_related_errors(self, exception: Exception) -> List[str]:
        """Find related errors that might be connected."""
        # This would implement logic to find patterns in recent errors
        return []
    
    def _log_structured_error(self, error: StructuredError):
        """Log structured error with appropriate level."""
        log_level_map = {
            ErrorSeverity.LOW: logging.INFO,
            ErrorSeverity.MEDIUM: logging.WARNING,
            ErrorSeverity.HIGH: logging.ERROR,
            ErrorSeverity.CRITICAL: logging.CRITICAL
        }
        
        level = log_level_map[error.severity]
        
        self.logger.log(level, f"[{error.error_id}] {error.message}", extra={
            'error_id': error.error_id,
            'severity': error.severity.value,
            'category': error.category.value,
            'context': asdict(error.context)
        })
    
    def _attempt_recovery(self, error: StructuredError):
        """Attempt automatic recovery for certain error types."""
        recovery_strategy = self.recovery_strategies.get(error.category)
        if recovery_strategy:
            try:
                recovery_strategy(error)
                self.logger.info(f"Recovery attempted for error {error.error_id}")
            except Exception as e:
                self.logger.error(f"Recovery failed for error {error.error_id}: {e}")
    
    def _send_alert(self, error: StructuredError):
        """Send alert for critical errors."""
        # This would integrate with alerting systems (email, Slack, etc.)
        self.logger.critical(f"CRITICAL ERROR ALERT: {error.error_id} - {error.message}")
    
    def _load_error_patterns(self) -> Dict:
        """Load error patterns for automatic categorization."""
        return {}
    
    def _setup_recovery_strategies(self) -> Dict:
        """Setup recovery strategies for different error categories."""
        return {}


class JsonFormatter(logging.Formatter):
    """JSON formatter for structured logging."""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add extra fields if present
        if hasattr(record, 'error_id'):
            log_entry['error_id'] = record.error_id
        
        if hasattr(record, 'severity'):
            log_entry['severity'] = record.severity
        
        if hasattr(record, 'category'):
            log_entry['category'] = record.category
        
        if hasattr(record, 'context'):
            log_entry['context'] = record.context
        
        return json.dumps(log_entry)


# Global error handler instance
_global_error_handler: Optional[ErrorHandler] = None


def get_error_handler() -> ErrorHandler:
    """Get global error handler instance."""
    global _global_error_handler
    if _global_error_handler is None:
        _global_error_handler = ErrorHandler()
    return _global_error_handler


def setup_global_error_handler(log_dir: str = "logs", log_level: str = "INFO"):
    """Setup global error handler."""
    global _global_error_handler
    _global_error_handler = ErrorHandler(log_dir, log_level)
    return _global_error_handler
