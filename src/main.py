"""
Main entry point for the OpenShift Must-Gather Analyzer.
Enhanced with comprehensive error handling and logging.
"""

import sys
import argparse
from pathlib import Path
from typing import List, Optional

from utils.config import ConfigurationManager
from core.models import AnalysisContext, AnalysisResult
from src.core.error_handler import setup_global_error_handler, get_error_handler, ErrorContext
from src.core.decorators import handle_errors, monitor_performance, ErrorCategory, ErrorSeverity


class MustGatherAnalyzer:
    """
    Main analyzer orchestrator that coordinates parsing, analysis, and reporting.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the must-gather analyzer.
        
        Args:
            config_path: Optional path to configuration file
        """
        self.config_manager = ConfigurationManager(config_path)
        self.parsers = []
        self.analyzers = []
        self.reporters = []
    
    def analyze(self, must_gather_path: str, output_path: str = "reports") -> bool:
        """
        Perform complete analysis of must-gather data.
        
        Args:
            must_gather_path: Path to must-gather directory
            output_path: Path for output reports
            
        Returns:
            True if analysis completed successfully
        """
        try:
            print(f"Starting analysis of must-gather data: {must_gather_path}")
            
            # Step 1: Parse and validate must-gather data
            print("Step 1: Parsing must-gather data...")
            context = self._parse_must_gather_data(must_gather_path)
            
            if not context:
                print("Error: Failed to parse must-gather data")
                return False
            
            # Step 2: Run all analyzers
            print("Step 2: Running analysis...")
            analysis_results = self._run_analyzers(context)
            
            if not analysis_results:
                print("Warning: No analysis results generated")
                return False
            
            # Step 3: Generate reports
            print("Step 3: Generating reports...")
            success = self._generate_reports(analysis_results, output_path)
            
            if success:
                print(f"Analysis completed successfully. Reports saved to: {output_path}")
            else:
                print("Error: Failed to generate reports")
            
            return success
            
        except Exception as e:
            print(f"Error during analysis: {e}")
            return False
    
    def _parse_must_gather_data(self, must_gather_path: str) -> Optional[AnalysisContext]:
        """
        Parse must-gather data using available parsers.
        
        Args:
            must_gather_path: Path to must-gather directory
            
        Returns:
            AnalysisContext with parsed data or None if parsing failed
        """
        # For now, create a basic context
        # This will be expanded when parsers are implemented
        context = AnalysisContext(
            must_gather_path=must_gather_path,
            configuration=self.config_manager.get_config()
        )
        
        return context
    
    def _run_analyzers(self, context: AnalysisContext) -> List[AnalysisResult]:
        """
        Run all registered analyzers on the context data.
        
        Args:
            context: Analysis context with parsed data
            
        Returns:
            List of analysis results
        """
        results = []
        
        # For now, return empty results
        # This will be expanded when analyzers are implemented
        print("Note: No analyzers registered yet")
        
        return results
    
    def _generate_reports(self, analysis_results: List[AnalysisResult], 
                         output_path: str) -> bool:
        """
        Generate reports using all registered reporters.
        
        Args:
            analysis_results: Results from analyzers
            output_path: Base path for output files
            
        Returns:
            True if at least one report was generated successfully
        """
        # For now, just create the output directory
        Path(output_path).mkdir(parents=True, exist_ok=True)
        print(f"Created output directory: {output_path}")
        
        # This will be expanded when reporters are implemented
        print("Note: No reporters registered yet")
        
        return True
    
    def register_parser(self, parser):
        """Register a must-gather parser."""
        self.parsers.append(parser)
    
    def register_analyzer(self, analyzer):
        """Register an analyzer."""
        self.analyzers.append(analyzer)
    
    def register_reporter(self, reporter):
        """Register a report generator."""
        self.reporters.append(reporter)


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="OpenShift Must-Gather Analyzer",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python -m src.main /path/to/must-gather
  python -m src.main /path/to/must-gather --output ./reports
  python -m src.main /path/to/must-gather --config custom-config.yaml
        """
    )
    
    parser.add_argument(
        "must_gather_path",
        help="Path to must-gather directory or archive"
    )
    
    parser.add_argument(
        "--output", "-o",
        default="reports",
        help="Output directory for reports (default: reports)"
    )
    
    parser.add_argument(
        "--config", "-c",
        help="Path to configuration file"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose output"
    )
    
    args = parser.parse_args()
    
    # Validate must-gather path
    if not Path(args.must_gather_path).exists():
        print(f"Error: Must-gather path does not exist: {args.must_gather_path}")
        sys.exit(1)
    
    # Initialize analyzer
    analyzer = MustGatherAnalyzer(args.config)
    
    # Run analysis
    success = analyzer.analyze(args.must_gather_path, args.output)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()