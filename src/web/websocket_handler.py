#!/usr/bin/env python3
"""
WebSocket handler for real-time data streaming in the OpenShift Must-Gather Analyzer.
Provides real-time updates for analysis progress, log streaming, and live data refresh.
"""

import json
import time
import threading
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

try:
    from flask_socketio import Socket<PERSON>, emit, join_room, leave_room, disconnect
    SOCKETIO_AVAILABLE = True
except ImportError:
    SOCKETIO_AVAILABLE = False
    # Fallback for when SocketIO is not available
    class MockSocketIO:
        def __init__(self, *args, **kwargs):
            pass
        def on(self, *args, **kwargs):
            def decorator(f):
                return f
            return decorator
        def emit(self, *args, **kwargs):
            pass
    SocketIO = MockSocketIO
    emit = lambda *args, **kwargs: None
    join_room = lambda *args, **kwargs: None
    leave_room = lambda *args, **kwargs: None
    disconnect = lambda *args, **kwargs: None

from core.models import AnalysisProgress, AnalysisStatus


class WebSocketManager:
    """Manages WebSocket connections and real-time data streaming."""
    
    def __init__(self, socketio: SocketIO):
        self.socketio = socketio
        self.active_connections: Dict[str, List[str]] = {}  # job_id -> [session_ids]
        self.log_streams: Dict[str, Dict] = {}  # session_id -> stream_info
        self.progress_monitors: Dict[str, bool] = {}  # job_id -> monitoring_active
        
        # Register event handlers
        self.register_handlers()
    
    def register_handlers(self):
        """Register WebSocket event handlers."""
        
        @self.socketio.on('connect')
        def handle_connect():
            """Handle client connection."""
            print(f"Client connected: {request.sid}")
            emit('connection_status', {'status': 'connected', 'timestamp': datetime.now().isoformat()})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """Handle client disconnection."""
            print(f"Client disconnected: {request.sid}")
            self.cleanup_client_connections(request.sid)
        
        @self.socketio.on('join_job_room')
        def handle_join_job_room(data):
            """Join a room for job-specific updates."""
            job_id = data.get('job_id')
            if not job_id:
                emit('error', {'message': 'Job ID is required'})
                return
            
            join_room(f"job_{job_id}")
            
            # Track connection
            if job_id not in self.active_connections:
                self.active_connections[job_id] = []
            if request.sid not in self.active_connections[job_id]:
                self.active_connections[job_id].append(request.sid)
            
            emit('joined_room', {'job_id': job_id, 'room': f"job_{job_id}"})
            print(f"Client {request.sid} joined room for job {job_id}")
        
        @self.socketio.on('leave_job_room')
        def handle_leave_job_room(data):
            """Leave a job-specific room."""
            job_id = data.get('job_id')
            if not job_id:
                return
            
            leave_room(f"job_{job_id}")
            
            # Remove from tracking
            if job_id in self.active_connections:
                if request.sid in self.active_connections[job_id]:
                    self.active_connections[job_id].remove(request.sid)
                if not self.active_connections[job_id]:
                    del self.active_connections[job_id]
            
            emit('left_room', {'job_id': job_id})
        
        @self.socketio.on('start_progress_monitoring')
        def handle_start_progress_monitoring(data):
            """Start monitoring job progress."""
            job_id = data.get('job_id')
            if not job_id:
                emit('error', {'message': 'Job ID is required'})
                return
            
            self.start_progress_monitoring(job_id)
            emit('progress_monitoring_started', {'job_id': job_id})
        
        @self.socketio.on('stop_progress_monitoring')
        def handle_stop_progress_monitoring(data):
            """Stop monitoring job progress."""
            job_id = data.get('job_id')
            if not job_id:
                return
            
            self.stop_progress_monitoring(job_id)
            emit('progress_monitoring_stopped', {'job_id': job_id})
        
        @self.socketio.on('start_log_stream')
        def handle_start_log_stream(data):
            """Start streaming log file content."""
            job_id = data.get('job_id')
            file_path = data.get('file_path')
            
            if not job_id or not file_path:
                emit('error', {'message': 'Job ID and file path are required'})
                return
            
            self.start_log_stream(request.sid, job_id, file_path)
        
        @self.socketio.on('stop_log_stream')
        def handle_stop_log_stream():
            """Stop streaming log content."""
            self.stop_log_stream(request.sid)
        
        @self.socketio.on('request_data_refresh')
        def handle_data_refresh(data):
            """Handle request for data refresh."""
            data_type = data.get('type', 'all')
            job_id = data.get('job_id')
            
            if job_id:
                self.refresh_job_data(job_id, data_type)
            else:
                self.refresh_global_data(data_type)
    
    def cleanup_client_connections(self, session_id: str):
        """Clean up connections for a disconnected client."""
        # Remove from job connections
        for job_id, sessions in list(self.active_connections.items()):
            if session_id in sessions:
                sessions.remove(session_id)
                if not sessions:
                    del self.active_connections[job_id]
        
        # Stop log streaming
        if session_id in self.log_streams:
            self.stop_log_stream(session_id)
    
    def broadcast_progress_update(self, job_id: str, progress: AnalysisProgress):
        """Broadcast progress update to all clients monitoring the job."""
        if job_id not in self.active_connections:
            return
        
        progress_data = {
            'job_id': job_id,
            'status': progress.status.value,
            'progress': progress.progress,
            'current_step': progress.current_step,
            'start_time': progress.start_time.isoformat() if progress.start_time else None,
            'end_time': progress.end_time.isoformat() if progress.end_time else None,
            'timestamp': datetime.now().isoformat()
        }
        
        self.socketio.emit('progress_update', progress_data, room=f"job_{job_id}")
        print(f"Broadcasted progress update for job {job_id}: {progress.progress}%")
    
    def broadcast_job_completion(self, job_id: str, success: bool, results: Optional[Dict] = None):
        """Broadcast job completion notification."""
        completion_data = {
            'job_id': job_id,
            'success': success,
            'timestamp': datetime.now().isoformat(),
            'results': results or {}
        }
        
        self.socketio.emit('job_completed', completion_data, room=f"job_{job_id}")
        
        # Stop progress monitoring
        self.stop_progress_monitoring(job_id)
        
        print(f"Broadcasted job completion for {job_id}: {'success' if success else 'failure'}")
    
    def start_progress_monitoring(self, job_id: str):
        """Start monitoring progress for a specific job."""
        if job_id in self.progress_monitors and self.progress_monitors[job_id]:
            return  # Already monitoring
        
        self.progress_monitors[job_id] = True
        
        def monitor_progress():
            """Monitor job progress in a separate thread."""
            while self.progress_monitors.get(job_id, False):
                try:
                    # This would integrate with the actual analyzer
                    # For now, simulate progress updates
                    progress = self.get_job_progress(job_id)
                    if progress:
                        self.broadcast_progress_update(job_id, progress)
                        
                        if progress.status in [AnalysisStatus.COMPLETED, AnalysisStatus.FAILED]:
                            self.broadcast_job_completion(
                                job_id, 
                                progress.status == AnalysisStatus.COMPLETED,
                                {'message': 'Analysis completed successfully' if progress.status == AnalysisStatus.COMPLETED else 'Analysis failed'}
                            )
                            break
                    
                    time.sleep(2)  # Update every 2 seconds
                    
                except Exception as e:
                    print(f"Error monitoring progress for job {job_id}: {e}")
                    break
            
            # Clean up
            if job_id in self.progress_monitors:
                del self.progress_monitors[job_id]
        
        # Start monitoring in background thread
        thread = threading.Thread(target=monitor_progress, daemon=True)
        thread.start()
    
    def stop_progress_monitoring(self, job_id: str):
        """Stop monitoring progress for a specific job."""
        if job_id in self.progress_monitors:
            self.progress_monitors[job_id] = False
    
    def start_log_stream(self, session_id: str, job_id: str, file_path: str):
        """Start streaming log file content to a client."""
        # Stop any existing stream for this session
        self.stop_log_stream(session_id)
        
        # Get the actual file path
        actual_file_path = self.get_job_file_path(job_id, file_path)
        if not actual_file_path or not actual_file_path.exists():
            self.socketio.emit('log_stream_error', 
                             {'message': f'File not found: {file_path}'}, 
                             room=session_id)
            return
        
        # Store stream info
        self.log_streams[session_id] = {
            'job_id': job_id,
            'file_path': file_path,
            'actual_path': actual_file_path,
            'position': 0,
            'active': True
        }
        
        def stream_logs():
            """Stream log content in a separate thread."""
            stream_info = self.log_streams.get(session_id)
            if not stream_info:
                return
            
            try:
                with open(stream_info['actual_path'], 'r', encoding='utf-8') as f:
                    # Seek to last known position
                    f.seek(stream_info['position'])
                    
                    while stream_info.get('active', False):
                        line = f.readline()
                        if line:
                            # Send new line to client
                            self.socketio.emit('log_stream_data', {
                                'job_id': job_id,
                                'file_path': file_path,
                                'line': line.rstrip(),
                                'timestamp': datetime.now().isoformat()
                            }, room=session_id)
                            
                            # Update position
                            stream_info['position'] = f.tell()
                        else:
                            # No new data, wait a bit
                            time.sleep(1)
                        
                        # Check if stream is still active
                        stream_info = self.log_streams.get(session_id)
                        if not stream_info:
                            break
                            
            except Exception as e:
                self.socketio.emit('log_stream_error', 
                                 {'message': f'Error reading file: {str(e)}'}, 
                                 room=session_id)
            finally:
                # Clean up
                if session_id in self.log_streams:
                    del self.log_streams[session_id]
        
        # Start streaming in background thread
        thread = threading.Thread(target=stream_logs, daemon=True)
        thread.start()
        
        # Notify client that streaming started
        self.socketio.emit('log_stream_started', {
            'job_id': job_id,
            'file_path': file_path
        }, room=session_id)
    
    def stop_log_stream(self, session_id: str):
        """Stop log streaming for a client."""
        if session_id in self.log_streams:
            self.log_streams[session_id]['active'] = False
            del self.log_streams[session_id]
            
            self.socketio.emit('log_stream_stopped', {}, room=session_id)
    
    def refresh_job_data(self, job_id: str, data_type: str = 'all'):
        """Refresh and broadcast job-specific data."""
        try:
            refresh_data = {
                'job_id': job_id,
                'type': data_type,
                'timestamp': datetime.now().isoformat()
            }
            
            if data_type in ['all', 'files']:
                refresh_data['files'] = self.get_job_files(job_id)
            
            if data_type in ['all', 'resources']:
                refresh_data['resources'] = self.get_job_resources(job_id)
            
            if data_type in ['all', 'metrics']:
                refresh_data['metrics'] = self.get_job_metrics(job_id)
            
            self.socketio.emit('data_refreshed', refresh_data, room=f"job_{job_id}")
            
        except Exception as e:
            self.socketio.emit('refresh_error', 
                             {'message': f'Error refreshing data: {str(e)}'}, 
                             room=f"job_{job_id}")
    
    def refresh_global_data(self, data_type: str = 'all'):
        """Refresh and broadcast global data."""
        try:
            refresh_data = {
                'type': data_type,
                'timestamp': datetime.now().isoformat()
            }
            
            if data_type in ['all', 'jobs']:
                refresh_data['jobs'] = self.get_all_jobs()
            
            if data_type in ['all', 'system']:
                refresh_data['system'] = self.get_system_status()
            
            self.socketio.emit('global_data_refreshed', refresh_data)
            
        except Exception as e:
            self.socketio.emit('refresh_error', {'message': f'Error refreshing global data: {str(e)}'})
    
    # Helper methods (these would integrate with the actual analyzer)
    def get_job_progress(self, job_id: str) -> Optional[AnalysisProgress]:
        """Get current progress for a job."""
        # This would integrate with the actual WebAnalyzer
        return None
    
    def get_job_file_path(self, job_id: str, file_path: str) -> Optional[Path]:
        """Get the actual file path for a job file."""
        # This would integrate with the actual job data
        return None
    
    def get_job_files(self, job_id: str) -> List[Dict]:
        """Get file list for a job."""
        return []
    
    def get_job_resources(self, job_id: str) -> List[Dict]:
        """Get resources for a job."""
        return []
    
    def get_job_metrics(self, job_id: str) -> Dict:
        """Get metrics for a job."""
        return {}
    
    def get_all_jobs(self) -> List[Dict]:
        """Get all jobs."""
        return []
    
    def get_system_status(self) -> Dict:
        """Get system status."""
        return {'status': 'healthy', 'timestamp': datetime.now().isoformat()}


def create_websocket_manager(socketio: SocketIO) -> WebSocketManager:
    """Create and configure WebSocket manager."""
    return WebSocketManager(socketio)
