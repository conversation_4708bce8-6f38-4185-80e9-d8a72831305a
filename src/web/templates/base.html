<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}OpenShift Must-Gather Analyzer{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #0d6efd;
            --success-color: #198754;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #0dcaf0;
            --dark-color: #212529;
            --light-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.1rem;
        }
        
        .status-badge {
            font-size: 0.8em;
        }
        
        .progress-container {
            margin: 1rem 0;
        }
        
        .job-card {
            margin-bottom: 1rem;
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        
        .job-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .upload-area {
            border: 3px dashed #dee2e6;
            border-radius: 0.5rem;
            padding: 3rem 2rem;
            text-align: center;
            transition: all 0.3s ease-in-out;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .upload-area:hover {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            transform: scale(1.02);
        }
        
        .upload-area.dragover {
            border-color: var(--success-color);
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            transform: scale(1.05);
        }
        
        .footer {
            margin-top: 3rem;
            padding: 2rem 0;
            border-top: 1px solid #dee2e6;
            background-color: var(--light-color);
        }
        
        .card {
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: box-shadow 0.3s ease-in-out;
        }
        
        .card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .btn {
            border-radius: 0.375rem;
            font-weight: 500;
            transition: all 0.2s ease-in-out;
        }
        
        .btn:hover {
            transform: translateY(-1px);
        }
        
        .log-entry {
            padding: 0.25rem 0;
            border-left: 3px solid transparent;
            padding-left: 0.5rem;
            margin-left: -0.5rem;
        }
        
        .log-entry.text-info {
            border-left-color: var(--info-color);
        }
        
        .log-entry.text-success {
            border-left-color: var(--success-color);
        }
        
        .log-entry.text-warning {
            border-left-color: var(--warning-color);
        }
        
        .log-entry.text-danger {
            border-left-color: var(--danger-color);
        }
        
        .log-entry.text-primary {
            border-left-color: var(--primary-color);
        }
        
        .progress {
            height: 0.5rem;
            border-radius: 0.25rem;
        }
        
        .progress-bar {
            border-radius: 0.25rem;
        }
        
        .alert {
            border: none;
            border-radius: 0.5rem;
        }
        
        .modal-content {
            border: none;
            border-radius: 0.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .table {
            border-radius: 0.375rem;
            overflow: hidden;
        }
        
        /* Responsive enhancements */
        @media (max-width: 768px) {
            .upload-area {
                padding: 2rem 1rem;
                min-height: 250px;
            }
            
            .navbar-brand {
                font-size: 1rem;
            }
            
            .btn-group .btn {
                padding: 0.375rem 0.5rem;
            }
            
            .card-body {
                padding: 1rem;
            }
        }
        
        @media (max-width: 576px) {
            .upload-area {
                padding: 1.5rem 0.5rem;
                min-height: 200px;
            }
            
            .btn-lg {
                padding: 0.5rem 1rem;
                font-size: 1rem;
            }
        }
        
        /* Animation classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        /* Loading states */
        .loading {
            position: relative;
            overflow: hidden;
        }
        
        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: loading 1.5s infinite;
        }
        
        @keyframes loading {
            0% { left: -100%; }
            100% { left: 100%; }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-chart-line me-2"></i>
                OpenShift Must-Gather Analyzer
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('upload_file') }}">
                            <i class="fas fa-upload me-1"></i>Upload
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('list_jobs') }}">
                            <i class="fas fa-list me-1"></i>Jobs
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="dataDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-database me-1"></i>Data Explorer
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('data_browser') }}">
                                <i class="fas fa-folder-tree me-2"></i>Browse Data
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('log_viewer') }}">
                                <i class="fas fa-file-alt me-2"></i>Log Viewer
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('resource_explorer') }}">
                                <i class="fas fa-cubes me-2"></i>Resources
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('search_interface') }}">
                                <i class="fas fa-search me-2"></i>Advanced Search
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('interactive_report') }}">
                            <i class="fas fa-chart-line me-1"></i>Interactive Report
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer bg-light">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="text-muted mb-0">
                        &copy; 2024 OpenShift Must-Gather Analyzer
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="text-muted mb-0">
                        <i class="fas fa-code me-1"></i>
                        Built for OpenShift cluster analysis
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Socket.IO for real-time features -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>

    <!-- Real-time client -->
    <script src="{{ url_for('static', filename='js/realtime.js') }}"></script>

    <!-- Connection status indicator -->
    <div id="connection-status" class="badge bg-secondary position-fixed" style="top: 10px; left: 10px; z-index: 9999;">
        <i class="fas fa-wifi-slash me-1"></i>Connecting...
    </div>

    {% block extra_js %}{% endblock %}
</body>
</html>