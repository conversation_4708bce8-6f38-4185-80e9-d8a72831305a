{% extends "base.html" %}

{% block title %}Advanced Search - OpenShift Must-Gather Analyzer{% endblock %}

{% block extra_css %}
<style>
    .search-form {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .search-results {
        max-height: 600px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
    }
    
    .result-item {
        padding: 1rem;
        border-bottom: 1px solid #e9ecef;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    
    .result-item:hover {
        background-color: #f8f9fa;
    }
    
    .result-item:last-child {
        border-bottom: none;
    }
    
    .result-header {
        display: flex;
        justify-content-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }
    
    .result-title {
        font-weight: 600;
        color: #495057;
    }
    
    .result-type {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 1rem;
        background-color: #e9ecef;
        color: #495057;
    }
    
    .result-path {
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 0.5rem;
    }
    
    .result-context {
        font-size: 0.875rem;
        color: #495057;
        background-color: #f8f9fa;
        padding: 0.5rem;
        border-radius: 0.25rem;
        border-left: 3px solid #007bff;
    }
    
    .search-highlight {
        background-color: #fff3cd;
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
        font-weight: 600;
    }
    
    .search-stats {
        background-color: #e3f2fd;
        padding: 1rem;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
    }
    
    .filter-section {
        background-color: white;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .search-operator {
        font-size: 0.875rem;
        color: #6c757d;
        margin: 0 0.5rem;
    }
    
    .advanced-query {
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 0.875rem;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.75rem;
        resize: vertical;
    }
    
    .query-builder {
        background-color: #f8f9fa;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .query-row {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
        padding: 0.5rem;
        background-color: white;
        border-radius: 0.25rem;
        border: 1px solid #dee2e6;
    }
    
    .query-field {
        flex: 1;
        margin-right: 0.5rem;
    }
    
    .query-operator {
        width: 100px;
        margin-right: 0.5rem;
    }
    
    .query-value {
        flex: 2;
        margin-right: 0.5rem;
    }
    
    .saved-searches {
        background-color: white;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1rem;
    }
    
    .saved-search-item {
        display: flex;
        justify-content-between;
        align-items: center;
        padding: 0.5rem;
        border-bottom: 1px solid #f8f9fa;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    
    .saved-search-item:hover {
        background-color: #f8f9fa;
    }
    
    .saved-search-item:last-child {
        border-bottom: none;
    }
    
    .export-options {
        position: absolute;
        top: 100%;
        right: 0;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        z-index: 1000;
        min-width: 200px;
        display: none;
    }
    
    .export-options.show {
        display: block;
    }
    
    .export-option {
        padding: 0.5rem 1rem;
        cursor: pointer;
        border-bottom: 1px solid #f8f9fa;
        transition: background-color 0.2s;
    }
    
    .export-option:hover {
        background-color: #f8f9fa;
    }
    
    .export-option:last-child {
        border-bottom: none;
    }
    
    .search-progress {
        display: none;
        text-align: center;
        padding: 2rem;
        color: #6c757d;
    }
    
    .no-results {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-search me-2"></i>
                Advanced Search
            </h1>
            <div class="btn-group" role="group">
                <button class="btn btn-outline-primary" onclick="clearSearch()">
                    <i class="fas fa-eraser me-1"></i>Clear
                </button>
                <button class="btn btn-outline-secondary" onclick="toggleQueryBuilder()">
                    <i class="fas fa-tools me-1"></i>Query Builder
                </button>
                <div class="position-relative">
                    <button class="btn btn-outline-success" onclick="toggleExportOptions()">
                        <i class="fas fa-download me-1"></i>Export Results
                    </button>
                    <div class="export-options" id="export-options">
                        <div class="export-option" onclick="exportResults('json')">
                            <i class="fas fa-file-code me-2"></i>Export as JSON
                        </div>
                        <div class="export-option" onclick="exportResults('csv')">
                            <i class="fas fa-file-csv me-2"></i>Export as CSV
                        </div>
                        <div class="export-option" onclick="exportResults('txt')">
                            <i class="fas fa-file-alt me-2"></i>Export as Text
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Job Selection -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-folder-open me-2"></i>
                    Search Scope
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">Analysis Job</label>
                        <select class="form-select" id="job-selector" onchange="loadJobForSearch()">
                            <option value="">Search across all completed jobs...</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Search Scope</label>
                        <select class="form-select" id="scope-selector">
                            <option value="all">All data types</option>
                            <option value="logs">Logs only</option>
                            <option value="configs">Configurations only</option>
                            <option value="resources">Kubernetes resources only</option>
                            <option value="events">Events only</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search Form -->
<div class="row mb-4">
    <div class="col-12">
        <div class="search-form">
            <div class="row">
                <div class="col-md-8">
                    <label class="form-label">Search Query</label>
                    <div class="input-group input-group-lg">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="search-query" 
                               placeholder="Enter search terms, regex patterns, or use advanced syntax..." 
                               onkeyup="handleSearchInput(event)">
                        <button class="btn btn-primary" onclick="performSearch()">
                            <i class="fas fa-search me-1"></i>Search
                        </button>
                    </div>
                    <div class="form-text">
                        Use quotes for exact phrases, * for wildcards, regex:/pattern/ for regular expressions
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Search Options</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="case-sensitive">
                        <label class="form-check-label" for="case-sensitive">
                            Case sensitive
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="regex-mode">
                        <label class="form-check-label" for="regex-mode">
                            Regular expressions
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="whole-words">
                        <label class="form-check-label" for="whole-words">
                            Whole words only
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Query Builder (Hidden by default) -->
<div class="row mb-4" id="query-builder" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>
                    Query Builder
                </h6>
            </div>
            <div class="card-body">
                <div class="query-builder">
                    <div id="query-rows">
                        <!-- Query rows will be added dynamically -->
                    </div>
                    <button class="btn btn-outline-primary btn-sm" onclick="addQueryRow()">
                        <i class="fas fa-plus me-1"></i>Add Condition
                    </button>
                </div>
                <div class="mt-3">
                    <label class="form-label">Generated Query</label>
                    <textarea class="form-control advanced-query" id="generated-query" rows="3" readonly></textarea>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search Results -->
<div class="row">
    <div class="col-lg-9">
        <!-- Search Stats -->
        <div class="search-stats" id="search-stats" style="display: none;">
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <div class="h4 mb-0" id="total-results">0</div>
                        <small>Total Results</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <div class="h4 mb-0" id="files-searched">0</div>
                        <small>Files Searched</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <div class="h4 mb-0" id="search-time">0ms</div>
                        <small>Search Time</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <div class="h4 mb-0" id="matches-count">0</div>
                        <small>Unique Matches</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Container -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    Search Results
                </h6>
                <div class="btn-group btn-group-sm" role="group">
                    <button class="btn btn-outline-secondary" onclick="sortResults('relevance')">
                        <i class="fas fa-sort-amount-down me-1"></i>Relevance
                    </button>
                    <button class="btn btn-outline-secondary" onclick="sortResults('filename')">
                        <i class="fas fa-sort-alpha-down me-1"></i>Filename
                    </button>
                    <button class="btn btn-outline-secondary" onclick="sortResults('type')">
                        <i class="fas fa-sort me-1"></i>Type
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="search-progress" id="search-progress">
                    <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                    <p>Searching through must-gather data...</p>
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             style="width: 0%" id="search-progress-bar"></div>
                    </div>
                </div>
                
                <div class="search-results" id="search-results">
                    <div class="no-results">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <p>Enter a search query to find content across must-gather data</p>
                        <small>Search through logs, configurations, resources, and events</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Saved Searches -->
    <div class="col-lg-3">
        <!-- Filters -->
        <div class="filter-section">
            <h6 class="mb-3">
                <i class="fas fa-filter me-2"></i>
                Filters
            </h6>
            
            <div class="mb-3">
                <label class="form-label">File Type</label>
                <div id="file-type-filters">
                    <!-- Will be populated dynamically -->
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Namespace</label>
                <select class="form-select form-select-sm" id="namespace-filter" onchange="applyFilters()">
                    <option value="">All Namespaces</option>
                </select>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Date Range</label>
                <input type="date" class="form-control form-control-sm mb-2" id="date-from" onchange="applyFilters()">
                <input type="date" class="form-control form-control-sm" id="date-to" onchange="applyFilters()">
            </div>
        </div>

        <!-- Saved Searches -->
        <div class="saved-searches">
            <h6 class="mb-3">
                <i class="fas fa-bookmark me-2"></i>
                Saved Searches
            </h6>
            <div id="saved-searches-list">
                <div class="text-center text-muted">
                    <small>No saved searches</small>
                </div>
            </div>
            <button class="btn btn-outline-primary btn-sm w-100 mt-2" onclick="saveCurrentSearch()">
                <i class="fas fa-save me-1"></i>Save Current Search
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentSearchResults = [];
let searchInProgress = false;
let queryRows = [];
let savedSearches = JSON.parse(localStorage.getItem('savedSearches') || '[]');

// Initialize search interface
document.addEventListener('DOMContentLoaded', function() {
    loadAvailableJobs();
    loadSavedSearches();
    addQueryRow(); // Add initial query row
    
    // Close export menu when clicking outside
    document.addEventListener('click', function(event) {
        const exportOptions = document.getElementById('export-options');
        const exportBtn = event.target.closest('[onclick="toggleExportOptions()"]');

        if (!exportBtn && !exportOptions.contains(event.target)) {
            exportOptions.classList.remove('show');
        }
    });
});

function loadAvailableJobs() {
    fetch('/api/jobs')
        .then(response => response.json())
        .then(data => {
            const selector = document.getElementById('job-selector');
            const completedJobs = Object.values(data).filter(job => job.status === 'completed');

            selector.innerHTML = '<option value="">Search across all completed jobs...</option>';

            completedJobs.forEach(job => {
                const option = document.createElement('option');
                option.value = job.job_id;
                option.textContent = `Job ${job.job_id.substring(0, 8)} - ${new Date(job.start_time).toLocaleString()}`;
                selector.appendChild(option);
            });

            if (completedJobs.length === 0) {
                selector.innerHTML = '<option value="">No completed jobs available</option>';
            }
        })
        .catch(error => {
            console.error('Error loading jobs:', error);
            showAlert('Error loading available jobs', 'danger');
        });
}

function loadJobForSearch() {
    const jobId = document.getElementById('job-selector').value;
    // Could load job-specific filters here if needed
}

function handleSearchInput(event) {
    if (event.key === 'Enter') {
        performSearch();
    }
}

function performSearch() {
    if (searchInProgress) return;

    const query = document.getElementById('search-query').value.trim();
    if (!query) {
        showAlert('Please enter a search query', 'warning');
        return;
    }

    searchInProgress = true;
    showSearchProgress(true);

    const searchParams = {
        query: query,
        jobId: document.getElementById('job-selector').value,
        scope: document.getElementById('scope-selector').value,
        caseSensitive: document.getElementById('case-sensitive').checked,
        regexMode: document.getElementById('regex-mode').checked,
        wholeWords: document.getElementById('whole-words').checked,
        namespaceFilter: document.getElementById('namespace-filter').value,
        dateFrom: document.getElementById('date-from').value,
        dateTo: document.getElementById('date-to').value
    };

    const startTime = Date.now();

    fetch('/api/search', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(searchParams)
    })
    .then(response => response.json())
    .then(data => {
        const searchTime = Date.now() - startTime;
        currentSearchResults = data.results || [];

        displaySearchResults(currentSearchResults);
        updateSearchStats(currentSearchResults, data.filesSearched || 0, searchTime);
        populateFilters(currentSearchResults);

        showAlert(`Found ${currentSearchResults.length} results in ${searchTime}ms`, 'success');
    })
    .catch(error => {
        console.error('Search error:', error);
        showAlert('Search failed. Please try again.', 'danger');
    })
    .finally(() => {
        searchInProgress = false;
        showSearchProgress(false);
    });
}

function displaySearchResults(results) {
    const container = document.getElementById('search-results');

    if (results.length === 0) {
        container.innerHTML = `
            <div class="no-results">
                <i class="fas fa-search fa-3x mb-3"></i>
                <p>No results found for your search query</p>
                <small>Try adjusting your search terms or filters</small>
            </div>
        `;
        return;
    }

    let html = '';
    results.forEach((result, index) => {
        html += `
            <div class="result-item" onclick="openResult('${result.id}')">
                <div class="result-header">
                    <div class="result-title">${result.filename}</div>
                    <div class="result-type">${result.type}</div>
                </div>
                <div class="result-path">${result.path}</div>
                <div class="result-context">${highlightMatches(result.context, result.matches)}</div>
                ${result.lineNumber ? `<small class="text-muted">Line ${result.lineNumber}</small>` : ''}
            </div>
        `;
    });

    container.innerHTML = html;
}

function highlightMatches(context, matches) {
    if (!matches || matches.length === 0) return context;

    let highlighted = context;
    matches.forEach(match => {
        const regex = new RegExp(`(${escapeRegex(match)})`, 'gi');
        highlighted = highlighted.replace(regex, '<span class="search-highlight">$1</span>');
    });

    return highlighted;
}

function updateSearchStats(results, filesSearched, searchTime) {
    document.getElementById('total-results').textContent = results.length;
    document.getElementById('files-searched').textContent = filesSearched;
    document.getElementById('search-time').textContent = `${searchTime}ms`;
    document.getElementById('matches-count').textContent = new Set(results.map(r => r.filename)).size;

    document.getElementById('search-stats').style.display = 'block';
}

function populateFilters(results) {
    // Populate file type filters
    const fileTypes = [...new Set(results.map(r => r.type))];
    const fileTypeContainer = document.getElementById('file-type-filters');

    fileTypeContainer.innerHTML = '';
    fileTypes.forEach(type => {
        const checkbox = document.createElement('div');
        checkbox.className = 'form-check form-check-sm';
        checkbox.innerHTML = `
            <input class="form-check-input" type="checkbox" id="filter-${type}" checked onchange="applyFilters()">
            <label class="form-check-label" for="filter-${type}">
                ${type} (${results.filter(r => r.type === type).length})
            </label>
        `;
        fileTypeContainer.appendChild(checkbox);
    });

    // Populate namespace filters
    const namespaces = [...new Set(results.map(r => r.namespace).filter(Boolean))];
    const namespaceSelect = document.getElementById('namespace-filter');

    namespaceSelect.innerHTML = '<option value="">All Namespaces</option>';
    namespaces.forEach(ns => {
        const option = document.createElement('option');
        option.value = ns;
        option.textContent = `${ns} (${results.filter(r => r.namespace === ns).length})`;
        namespaceSelect.appendChild(option);
    });
}

function applyFilters() {
    if (currentSearchResults.length === 0) return;

    // Get active file type filters
    const activeFileTypes = [];
    document.querySelectorAll('#file-type-filters input[type="checkbox"]:checked').forEach(checkbox => {
        const type = checkbox.id.replace('filter-', '');
        activeFileTypes.push(type);
    });

    const namespaceFilter = document.getElementById('namespace-filter').value;
    const dateFrom = document.getElementById('date-from').value;
    const dateTo = document.getElementById('date-to').value;

    const filteredResults = currentSearchResults.filter(result => {
        // File type filter
        if (activeFileTypes.length > 0 && !activeFileTypes.includes(result.type)) {
            return false;
        }

        // Namespace filter
        if (namespaceFilter && result.namespace !== namespaceFilter) {
            return false;
        }

        // Date filter
        if (dateFrom || dateTo) {
            const resultDate = new Date(result.timestamp || result.lastModified);
            if (dateFrom && resultDate < new Date(dateFrom)) return false;
            if (dateTo && resultDate > new Date(dateTo)) return false;
        }

        return true;
    });

    displaySearchResults(filteredResults);
    updateSearchStats(filteredResults, 0, 0);
}

// Query Builder Functions
function toggleQueryBuilder() {
    const builder = document.getElementById('query-builder');
    const isVisible = builder.style.display !== 'none';
    builder.style.display = isVisible ? 'none' : 'block';
}

function addQueryRow() {
    const container = document.getElementById('query-rows');
    const rowId = `query-row-${Date.now()}`;

    const row = document.createElement('div');
    row.className = 'query-row';
    row.id = rowId;
    row.innerHTML = `
        <select class="form-select query-field">
            <option value="content">Content</option>
            <option value="filename">Filename</option>
            <option value="path">Path</option>
            <option value="namespace">Namespace</option>
            <option value="kind">Resource Kind</option>
            <option value="labels">Labels</option>
        </select>
        <select class="form-select query-operator">
            <option value="contains">Contains</option>
            <option value="equals">Equals</option>
            <option value="starts">Starts with</option>
            <option value="ends">Ends with</option>
            <option value="regex">Regex</option>
        </select>
        <input type="text" class="form-control query-value" placeholder="Enter value...">
        <button class="btn btn-outline-danger btn-sm" onclick="removeQueryRow('${rowId}')">
            <i class="fas fa-times"></i>
        </button>
    `;

    container.appendChild(row);
    queryRows.push(rowId);
    updateGeneratedQuery();

    // Add event listeners
    row.querySelectorAll('select, input').forEach(element => {
        element.addEventListener('change', updateGeneratedQuery);
        element.addEventListener('input', updateGeneratedQuery);
    });
}

function removeQueryRow(rowId) {
    const row = document.getElementById(rowId);
    if (row) {
        row.remove();
        queryRows = queryRows.filter(id => id !== rowId);
        updateGeneratedQuery();
    }
}

function updateGeneratedQuery() {
    const rows = document.querySelectorAll('.query-row');
    const conditions = [];

    rows.forEach(row => {
        const field = row.querySelector('.query-field').value;
        const operator = row.querySelector('.query-operator').value;
        const value = row.querySelector('.query-value').value;

        if (value.trim()) {
            let condition = '';
            switch (operator) {
                case 'contains':
                    condition = `${field}:*${value}*`;
                    break;
                case 'equals':
                    condition = `${field}:"${value}"`;
                    break;
                case 'starts':
                    condition = `${field}:${value}*`;
                    break;
                case 'ends':
                    condition = `${field}:*${value}`;
                    break;
                case 'regex':
                    condition = `${field}:/${value}/`;
                    break;
            }
            conditions.push(condition);
        }
    });

    const query = conditions.join(' AND ');
    document.getElementById('generated-query').value = query;
    document.getElementById('search-query').value = query;
}

// Saved Searches Functions
function loadSavedSearches() {
    const container = document.getElementById('saved-searches-list');

    if (savedSearches.length === 0) {
        container.innerHTML = '<div class="text-center text-muted"><small>No saved searches</small></div>';
        return;
    }

    let html = '';
    savedSearches.forEach((search, index) => {
        html += `
            <div class="saved-search-item" onclick="loadSavedSearch(${index})">
                <div>
                    <div class="fw-bold">${search.name}</div>
                    <small class="text-muted">${search.query}</small>
                </div>
                <button class="btn btn-outline-danger btn-sm" onclick="deleteSavedSearch(${index}); event.stopPropagation();">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
    });

    container.innerHTML = html;
}

function saveCurrentSearch() {
    const query = document.getElementById('search-query').value.trim();
    if (!query) {
        showAlert('No search query to save', 'warning');
        return;
    }

    const name = prompt('Enter a name for this search:');
    if (!name) return;

    const search = {
        name: name,
        query: query,
        scope: document.getElementById('scope-selector').value,
        caseSensitive: document.getElementById('case-sensitive').checked,
        regexMode: document.getElementById('regex-mode').checked,
        wholeWords: document.getElementById('whole-words').checked,
        timestamp: new Date().toISOString()
    };

    savedSearches.push(search);
    localStorage.setItem('savedSearches', JSON.stringify(savedSearches));
    loadSavedSearches();

    showAlert('Search saved successfully', 'success');
}

function loadSavedSearch(index) {
    const search = savedSearches[index];
    if (!search) return;

    document.getElementById('search-query').value = search.query;
    document.getElementById('scope-selector').value = search.scope || 'all';
    document.getElementById('case-sensitive').checked = search.caseSensitive || false;
    document.getElementById('regex-mode').checked = search.regexMode || false;
    document.getElementById('whole-words').checked = search.wholeWords || false;

    showAlert(`Loaded search: ${search.name}`, 'info');
}

function deleteSavedSearch(index) {
    if (confirm('Are you sure you want to delete this saved search?')) {
        savedSearches.splice(index, 1);
        localStorage.setItem('savedSearches', JSON.stringify(savedSearches));
        loadSavedSearches();
        showAlert('Search deleted', 'success');
    }
}

// Utility Functions
function clearSearch() {
    document.getElementById('search-query').value = '';
    document.getElementById('generated-query').value = '';
    document.getElementById('case-sensitive').checked = false;
    document.getElementById('regex-mode').checked = false;
    document.getElementById('whole-words').checked = false;
    document.getElementById('namespace-filter').value = '';
    document.getElementById('date-from').value = '';
    document.getElementById('date-to').value = '';

    currentSearchResults = [];

    document.getElementById('search-results').innerHTML = `
        <div class="no-results">
            <i class="fas fa-search fa-3x mb-3"></i>
            <p>Enter a search query to find content across must-gather data</p>
            <small>Search through logs, configurations, resources, and events</small>
        </div>
    `;

    document.getElementById('search-stats').style.display = 'none';
    document.getElementById('file-type-filters').innerHTML = '';
}

function sortResults(method) {
    if (currentSearchResults.length === 0) return;

    let sortedResults = [...currentSearchResults];

    switch (method) {
        case 'relevance':
            // Sort by number of matches (descending)
            sortedResults.sort((a, b) => (b.matches?.length || 0) - (a.matches?.length || 0));
            break;
        case 'filename':
            sortedResults.sort((a, b) => a.filename.localeCompare(b.filename));
            break;
        case 'type':
            sortedResults.sort((a, b) => a.type.localeCompare(b.type));
            break;
    }

    displaySearchResults(sortedResults);
}

function openResult(resultId) {
    const result = currentSearchResults.find(r => r.id === resultId);
    if (!result) return;

    // Open the result in the appropriate viewer
    if (result.type === 'log') {
        window.open(`/log-viewer?job=${result.jobId}&file=${encodeURIComponent(result.path)}`, '_blank');
    } else if (result.type === 'resource') {
        window.open(`/resource-explorer?job=${result.jobId}&resource=${result.resourceId}`, '_blank');
    } else {
        window.open(`/data-browser?job=${result.jobId}&file=${encodeURIComponent(result.path)}`, '_blank');
    }
}

function showSearchProgress(show) {
    document.getElementById('search-progress').style.display = show ? 'block' : 'none';

    if (show) {
        // Simulate progress
        let progress = 0;
        const progressBar = document.getElementById('search-progress-bar');
        const interval = setInterval(() => {
            progress += Math.random() * 20;
            if (progress >= 90) {
                progress = 90;
                clearInterval(interval);
            }
            progressBar.style.width = `${progress}%`;
        }, 200);
    }
}

function toggleExportOptions() {
    const menu = document.getElementById('export-options');
    menu.classList.toggle('show');
}

function exportResults(format) {
    if (currentSearchResults.length === 0) {
        showAlert('No results to export', 'warning');
        return;
    }

    let content = '';
    let filename = `search_results_${new Date().toISOString().split('T')[0]}`;
    let mimeType = 'text/plain';

    switch (format) {
        case 'json':
            content = JSON.stringify(currentSearchResults, null, 2);
            filename += '.json';
            mimeType = 'application/json';
            break;

        case 'csv':
            const headers = ['Filename', 'Type', 'Path', 'Context', 'Line Number'];
            content = headers.join(',') + '\n';
            content += currentSearchResults.map(result =>
                [
                    `"${result.filename}"`,
                    `"${result.type}"`,
                    `"${result.path}"`,
                    `"${result.context.replace(/"/g, '""')}"`,
                    result.lineNumber || ''
                ].join(',')
            ).join('\n');
            filename += '.csv';
            mimeType = 'text/csv';
            break;

        case 'txt':
            content = currentSearchResults.map(result =>
                `File: ${result.filename}\nType: ${result.type}\nPath: ${result.path}\nContext: ${result.context}\n${result.lineNumber ? `Line: ${result.lineNumber}\n` : ''}\n---\n`
            ).join('\n');
            filename += '.txt';
            break;
    }

    downloadContent(content, filename, mimeType);
    document.getElementById('export-options').classList.remove('show');
}

function downloadContent(content, filename, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

function escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function showAlert(message, type = 'info') {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alert);

    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
</script>
{% endblock %}
