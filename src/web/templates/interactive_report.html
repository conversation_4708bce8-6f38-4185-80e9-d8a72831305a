{% extends "base.html" %}

{% block title %}Interactive Report - OpenShift Must-Gather Analyzer{% endblock %}

{% block extra_css %}
<!-- Chart.js for interactive charts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

<style>
    .report-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 0.5rem;
        margin-bottom: 2rem;
    }
    
    .metric-card {
        background: white;
        border-radius: 0.5rem;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: transform 0.2s, box-shadow 0.2s;
        cursor: pointer;
        border-left: 4px solid transparent;
    }
    
    .metric-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    .metric-card.critical {
        border-left-color: #dc3545;
    }
    
    .metric-card.warning {
        border-left-color: #ffc107;
    }
    
    .metric-card.success {
        border-left-color: #28a745;
    }
    
    .metric-card.info {
        border-left-color: #17a2b8;
    }
    
    .metric-value {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .metric-label {
        color: #6c757d;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .metric-change {
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }
    
    .metric-change.positive {
        color: #28a745;
    }
    
    .metric-change.negative {
        color: #dc3545;
    }
    
    .chart-container {
        position: relative;
        height: 400px;
        margin-bottom: 2rem;
    }
    
    .chart-controls {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
    }
    
    .findings-grid {
        display: grid;
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .finding-card {
        background: white;
        border-radius: 0.5rem;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #dee2e6;
        transition: all 0.2s;
        cursor: pointer;
    }
    
    .finding-card:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    .finding-card.severity-critical {
        border-left-color: #dc3545;
    }
    
    .finding-card.severity-high {
        border-left-color: #fd7e14;
    }
    
    .finding-card.severity-medium {
        border-left-color: #ffc107;
    }
    
    .finding-card.severity-low {
        border-left-color: #28a745;
    }
    
    .finding-header {
        display: flex;
        justify-content-between;
        align-items-start;
        margin-bottom: 1rem;
    }
    
    .finding-title {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.25rem;
    }
    
    .finding-category {
        font-size: 0.75rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .severity-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .severity-critical {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .severity-high {
        background-color: #ffeaa7;
        color: #856404;
    }
    
    .severity-medium {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .severity-low {
        background-color: #d4edda;
        color: #155724;
    }
    
    .finding-description {
        color: #6c757d;
        margin-bottom: 1rem;
        line-height: 1.5;
    }
    
    .finding-resources {
        margin-bottom: 1rem;
    }
    
    .resource-tag {
        display: inline-block;
        background-color: #e9ecef;
        color: #495057;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        margin-right: 0.5rem;
        margin-bottom: 0.25rem;
    }
    
    .finding-actions {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .timeline-container {
        background: white;
        border-radius: 0.5rem;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .timeline-item {
        position: relative;
        padding-left: 2rem;
        padding-bottom: 1.5rem;
        border-left: 2px solid #dee2e6;
    }
    
    .timeline-item:last-child {
        border-left: none;
        padding-bottom: 0;
    }
    
    .timeline-marker {
        position: absolute;
        left: -6px;
        top: 0;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #007bff;
        border: 2px solid white;
        box-shadow: 0 0 0 2px #007bff;
    }
    
    .timeline-marker.critical {
        background-color: #dc3545;
        box-shadow: 0 0 0 2px #dc3545;
    }
    
    .timeline-marker.warning {
        background-color: #ffc107;
        box-shadow: 0 0 0 2px #ffc107;
    }
    
    .timeline-time {
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
    }
    
    .timeline-content {
        color: #495057;
    }
    
    .filter-panel {
        background: white;
        border-radius: 0.5rem;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: sticky;
        top: 2rem;
    }
    
    .filter-group {
        margin-bottom: 1.5rem;
    }
    
    .filter-group:last-child {
        margin-bottom: 0;
    }
    
    .filter-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
        display: block;
    }
    
    .filter-checkbox {
        margin-bottom: 0.5rem;
    }
    
    .recommendations-panel {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .recommendation-item {
        background: white;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-bottom: 1rem;
        border-left: 4px solid #17a2b8;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .recommendation-item:last-child {
        margin-bottom: 0;
    }
    
    .recommendation-priority {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 1rem;
        font-weight: 600;
        text-transform: uppercase;
        margin-bottom: 0.5rem;
        display: inline-block;
    }
    
    .priority-high {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .priority-medium {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .priority-low {
        background-color: #d4edda;
        color: #155724;
    }
    
    .drill-down-modal .modal-dialog {
        max-width: 90%;
    }
    
    .drill-down-content {
        max-height: 70vh;
        overflow-y: auto;
    }
    
    .export-panel {
        background: white;
        border-radius: 0.5rem;
        padding: 1rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }
    
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        display: none;
    }
    
    .loading-spinner {
        text-align: center;
        color: #6c757d;
    }
    
    @media (max-width: 768px) {
        .metric-value {
            font-size: 2rem;
        }
        
        .chart-container {
            height: 300px;
        }
        
        .filter-panel {
            position: static;
            margin-bottom: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Loading Overlay -->
<div class="loading-overlay" id="loading-overlay">
    <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin fa-3x mb-3"></i>
        <p>Loading report data...</p>
    </div>
</div>

<!-- Report Header -->
<div class="report-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="mb-2">
                <i class="fas fa-chart-line me-2"></i>
                Interactive Analysis Report
            </h1>
            <p class="mb-0 opacity-75" id="report-subtitle">
                Comprehensive analysis of OpenShift cluster health and performance
            </p>
        </div>
        <div class="col-md-4 text-end">
            <div class="d-flex flex-column align-items-end">
                <div class="mb-2">
                    <span class="badge bg-light text-dark fs-6" id="report-timestamp">
                        Generated: --
                    </span>
                </div>
                <div class="btn-group" role="group">
                    <button class="btn btn-light btn-sm" onclick="refreshReport()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                    <button class="btn btn-light btn-sm" onclick="exportReport()">
                        <i class="fas fa-download me-1"></i>Export
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Job Selection -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <label class="form-label">Analysis Job</label>
                        <select class="form-select" id="job-selector" onchange="loadReportData()">
                            <option value="">Select a completed analysis job...</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <div id="job-info" class="mt-3 mt-md-0">
                            <small class="text-muted">Select a job to view its interactive report</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Key Metrics Dashboard -->
<div class="row mb-4" id="metrics-dashboard" style="display: none;">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="metric-card critical" onclick="drillDownMetric('health')">
            <div class="metric-value" id="health-score">--</div>
            <div class="metric-label">Cluster Health Score</div>
            <div class="metric-change" id="health-change"></div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="metric-card warning" onclick="drillDownMetric('issues')">
            <div class="metric-value" id="total-issues">--</div>
            <div class="metric-label">Total Issues</div>
            <div class="metric-change" id="issues-change"></div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="metric-card info" onclick="drillDownMetric('resources')">
            <div class="metric-value" id="resources-analyzed">--</div>
            <div class="metric-label">Resources Analyzed</div>
            <div class="metric-change" id="resources-change"></div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="metric-card success" onclick="drillDownMetric('recommendations')">
            <div class="metric-value" id="recommendations-count">--</div>
            <div class="metric-label">Recommendations</div>
            <div class="metric-change" id="recommendations-change"></div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="row" id="main-content" style="display: none;">
    <!-- Charts and Analysis -->
    <div class="col-lg-9">
        <!-- Chart Controls -->
        <div class="chart-controls">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <label class="form-label mb-0">Chart Type:</label>
                    <select class="form-select form-select-sm d-inline-block w-auto ms-2" id="chart-type" onchange="updateChart()">
                        <option value="severity">Issues by Severity</option>
                        <option value="category">Issues by Category</option>
                        <option value="timeline">Timeline Analysis</option>
                        <option value="resources">Resource Utilization</option>
                    </select>
                </div>
                <div class="col-md-6 text-end">
                    <div class="btn-group btn-group-sm" role="group">
                        <button class="btn btn-outline-secondary" onclick="toggleChartAnimation()">
                            <i class="fas fa-play me-1"></i>Animate
                        </button>
                        <button class="btn btn-outline-secondary" onclick="exportChart()">
                            <i class="fas fa-image me-1"></i>Export Chart
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chart Container -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="main-chart"></canvas>
                </div>
            </div>
        </div>

        <!-- Top Recommendations -->
        <div class="recommendations-panel">
            <h5 class="mb-3">
                <i class="fas fa-lightbulb me-2"></i>
                Top Recommendations
            </h5>
            <div id="recommendations-list">
                <!-- Recommendations will be populated here -->
            </div>
        </div>

        <!-- Findings Grid -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Detailed Findings
                </h6>
                <div class="btn-group btn-group-sm" role="group">
                    <button class="btn btn-outline-secondary" onclick="sortFindings('severity')">
                        <i class="fas fa-sort-amount-down me-1"></i>Severity
                    </button>
                    <button class="btn btn-outline-secondary" onclick="sortFindings('category')">
                        <i class="fas fa-sort-alpha-down me-1"></i>Category
                    </button>
                    <button class="btn btn-outline-secondary" onclick="sortFindings('timestamp')">
                        <i class="fas fa-clock me-1"></i>Time
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="findings-grid" id="findings-grid">
                    <!-- Findings will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Timeline -->
    <div class="col-lg-3">
        <!-- Filters Panel -->
        <div class="filter-panel">
            <h6 class="mb-3">
                <i class="fas fa-filter me-2"></i>
                Filters
            </h6>
            
            <div class="filter-group">
                <label class="filter-label">Severity</label>
                <div class="filter-checkbox">
                    <input type="checkbox" class="form-check-input" id="filter-critical" checked onchange="applyFilters()">
                    <label class="form-check-label ms-2" for="filter-critical">Critical</label>
                </div>
                <div class="filter-checkbox">
                    <input type="checkbox" class="form-check-input" id="filter-high" checked onchange="applyFilters()">
                    <label class="form-check-label ms-2" for="filter-high">High</label>
                </div>
                <div class="filter-checkbox">
                    <input type="checkbox" class="form-check-input" id="filter-medium" checked onchange="applyFilters()">
                    <label class="form-check-label ms-2" for="filter-medium">Medium</label>
                </div>
                <div class="filter-checkbox">
                    <input type="checkbox" class="form-check-input" id="filter-low" onchange="applyFilters()">
                    <label class="form-check-label ms-2" for="filter-low">Low</label>
                </div>
            </div>
            
            <div class="filter-group">
                <label class="filter-label">Category</label>
                <div id="category-filters">
                    <!-- Category filters will be populated dynamically -->
                </div>
            </div>
            
            <div class="filter-group">
                <label class="filter-label">Time Range</label>
                <input type="date" class="form-control form-control-sm mb-2" id="date-from" onchange="applyFilters()">
                <input type="date" class="form-control form-control-sm" id="date-to" onchange="applyFilters()">
            </div>
            
            <button class="btn btn-outline-secondary btn-sm w-100" onclick="clearFilters()">
                <i class="fas fa-eraser me-1"></i>Clear Filters
            </button>
        </div>

        <!-- Timeline -->
        <div class="timeline-container mt-4">
            <h6 class="mb-3">
                <i class="fas fa-clock me-2"></i>
                Event Timeline
            </h6>
            <div id="timeline-content">
                <!-- Timeline items will be populated here -->
            </div>
        </div>
    </div>
</div>

<!-- Drill-down Modal -->
<div class="modal fade" id="drillDownModal" tabindex="-1">
    <div class="modal-dialog drill-down-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="drillDownTitle">Detailed Analysis</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="drill-down-content" id="drillDownContent">
                    <!-- Drill-down content will be populated here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="exportDrillDown()">
                    <i class="fas fa-download me-1"></i>Export Details
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentJobId = null;
let reportData = null;
let currentChart = null;
let filteredFindings = [];
let allFindings = [];

// Initialize the interactive report
document.addEventListener('DOMContentLoaded', function() {
    loadAvailableJobs();
    initializeChart();
});

function loadAvailableJobs() {
    fetch('/api/jobs')
        .then(response => response.json())
        .then(data => {
            const selector = document.getElementById('job-selector');
            const completedJobs = Object.values(data).filter(job => job.status === 'completed');

            selector.innerHTML = '<option value="">Select a completed analysis job...</option>';

            completedJobs.forEach(job => {
                const option = document.createElement('option');
                option.value = job.job_id;
                option.textContent = `Job ${job.job_id.substring(0, 8)} - ${new Date(job.start_time).toLocaleString()}`;
                selector.appendChild(option);
            });

            if (completedJobs.length === 0) {
                selector.innerHTML = '<option value="">No completed jobs available</option>';
            }
        })
        .catch(error => {
            console.error('Error loading jobs:', error);
            showAlert('Error loading available jobs', 'danger');
        });
}

function loadReportData() {
    const selector = document.getElementById('job-selector');
    const jobId = selector.value;

    if (!jobId) {
        hideReportContent();
        return;
    }

    currentJobId = jobId;
    showLoading(true);

    // Update job info
    const jobInfo = document.getElementById('job-info');
    jobInfo.innerHTML = `<small class="text-primary">Loading report for job ${jobId.substring(0, 8)}...</small>`;

    // Load report data
    fetch(`/api/jobs/${jobId}/report`)
        .then(response => response.json())
        .then(data => {
            reportData = data;
            allFindings = data.findings || [];
            filteredFindings = [...allFindings];

            updateReportHeader(data);
            updateMetricsDashboard(data);
            updateChart(data);
            updateRecommendations(data);
            updateFindings(filteredFindings);
            updateTimeline(data);
            populateCategoryFilters(data);

            showReportContent();
            jobInfo.innerHTML = `<small class="text-success">Report loaded successfully</small>`;
        })
        .catch(error => {
            console.error('Error loading report:', error);
            showAlert('Error loading report data', 'danger');
            jobInfo.innerHTML = `<small class="text-danger">Error loading report</small>`;
        })
        .finally(() => {
            showLoading(false);
        });
}

function updateReportHeader(data) {
    document.getElementById('report-subtitle').textContent =
        `Analysis of ${data.clusterName || 'OpenShift Cluster'} - ${data.totalResources || 0} resources analyzed`;

    document.getElementById('report-timestamp').textContent =
        `Generated: ${new Date(data.timestamp || Date.now()).toLocaleString()}`;
}

function updateMetricsDashboard(data) {
    const metrics = data.metrics || {};

    // Health Score
    const healthScore = metrics.healthScore || 0;
    document.getElementById('health-score').textContent = `${healthScore}%`;
    document.getElementById('health-change').innerHTML =
        getChangeIndicator(metrics.healthChange || 0, 'positive');

    // Total Issues
    const totalIssues = (data.findings || []).length;
    document.getElementById('total-issues').textContent = totalIssues;
    document.getElementById('issues-change').innerHTML =
        getChangeIndicator(metrics.issuesChange || 0, 'negative');

    // Resources Analyzed
    document.getElementById('resources-analyzed').textContent = data.totalResources || 0;
    document.getElementById('resources-change').innerHTML =
        getChangeIndicator(metrics.resourcesChange || 0, 'positive');

    // Recommendations
    const recommendationsCount = (data.recommendations || []).length;
    document.getElementById('recommendations-count').textContent = recommendationsCount;
    document.getElementById('recommendations-change').innerHTML =
        getChangeIndicator(metrics.recommendationsChange || 0, 'positive');
}

function getChangeIndicator(change, positiveDirection) {
    if (change === 0) return '<span class="text-muted">No change</span>';

    const isPositive = (positiveDirection === 'positive' && change > 0) ||
                      (positiveDirection === 'negative' && change < 0);

    const icon = change > 0 ? 'fa-arrow-up' : 'fa-arrow-down';
    const colorClass = isPositive ? 'positive' : 'negative';

    return `<span class="metric-change ${colorClass}">
        <i class="fas ${icon} me-1"></i>${Math.abs(change)}%
    </span>`;
}

function initializeChart() {
    const ctx = document.getElementById('main-chart').getContext('2d');

    currentChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#dc3545', // Critical - Red
                    '#fd7e14', // High - Orange
                    '#ffc107', // Medium - Yellow
                    '#28a745'  // Low - Green
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            },
            onClick: (event, elements) => {
                if (elements.length > 0) {
                    const index = elements[0].index;
                    const label = currentChart.data.labels[index];
                    drillDownChart(label);
                }
            }
        }
    });
}

function updateChart(data) {
    const chartType = document.getElementById('chart-type').value;

    switch (chartType) {
        case 'severity':
            updateSeverityChart(data);
            break;
        case 'category':
            updateCategoryChart(data);
            break;
        case 'timeline':
            updateTimelineChart(data);
            break;
        case 'resources':
            updateResourceChart(data);
            break;
    }
}

function updateSeverityChart(data) {
    const findings = data.findings || [];
    const severityCounts = {
        'Critical': 0,
        'High': 0,
        'Medium': 0,
        'Low': 0
    };

    findings.forEach(finding => {
        const severity = finding.severity || 'Low';
        severityCounts[severity] = (severityCounts[severity] || 0) + 1;
    });

    currentChart.data.labels = Object.keys(severityCounts);
    currentChart.data.datasets[0].data = Object.values(severityCounts);
    currentChart.update();
}

function updateCategoryChart(data) {
    const findings = data.findings || [];
    const categoryCounts = {};

    findings.forEach(finding => {
        const category = finding.category || 'Other';
        categoryCounts[category] = (categoryCounts[category] || 0) + 1;
    });

    currentChart.data.labels = Object.keys(categoryCounts);
    currentChart.data.datasets[0].data = Object.values(categoryCounts);

    // Use different colors for categories
    const colors = [
        '#007bff', '#28a745', '#ffc107', '#dc3545',
        '#6f42c1', '#fd7e14', '#20c997', '#6c757d'
    ];
    currentChart.data.datasets[0].backgroundColor = colors.slice(0, Object.keys(categoryCounts).length);

    currentChart.update();
}

function updateTimelineChart(data) {
    // Convert to line chart for timeline
    currentChart.destroy();

    const ctx = document.getElementById('main-chart').getContext('2d');
    const findings = data.findings || [];

    // Group findings by date
    const timelineData = {};
    findings.forEach(finding => {
        const date = new Date(finding.timestamp || Date.now()).toDateString();
        timelineData[date] = (timelineData[date] || 0) + 1;
    });

    const sortedDates = Object.keys(timelineData).sort((a, b) => new Date(a) - new Date(b));

    currentChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: sortedDates,
            datasets: [{
                label: 'Issues Found',
                data: sortedDates.map(date => timelineData[date]),
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
}

function updateResourceChart(data) {
    // Convert to bar chart for resources
    currentChart.destroy();

    const ctx = document.getElementById('main-chart').getContext('2d');
    const resources = data.resourceMetrics || {};

    currentChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: Object.keys(resources),
            datasets: [{
                label: 'Resource Count',
                data: Object.values(resources),
                backgroundColor: '#007bff',
                borderColor: '#0056b3',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
}

function updateRecommendations(data) {
    const recommendations = data.recommendations || [];
    const container = document.getElementById('recommendations-list');

    if (recommendations.length === 0) {
        container.innerHTML = '<p class="text-muted">No recommendations available.</p>';
        return;
    }

    let html = '';
    recommendations.slice(0, 5).forEach(rec => {
        html += `
            <div class="recommendation-item">
                <span class="recommendation-priority priority-${rec.priority || 'medium'}">${rec.priority || 'Medium'} Priority</span>
                <h6 class="mt-2 mb-1">${rec.title}</h6>
                <p class="mb-2">${rec.description}</p>
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">Impact: ${rec.impact || 'Medium'}</small>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewRecommendationDetails('${rec.id}')">
                        View Details
                    </button>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

function updateFindings(findings) {
    const container = document.getElementById('findings-grid');

    if (findings.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">No findings match the current filters.</p>';
        return;
    }

    let html = '';
    findings.forEach(finding => {
        const severity = finding.severity || 'Low';
        const resources = finding.affectedResources || [];

        html += `
            <div class="finding-card severity-${severity.toLowerCase()}" onclick="viewFindingDetails('${finding.id}')">
                <div class="finding-header">
                    <div>
                        <div class="finding-title">${finding.title}</div>
                        <div class="finding-category">${finding.category || 'General'}</div>
                    </div>
                    <span class="severity-badge severity-${severity.toLowerCase()}">${severity}</span>
                </div>
                <div class="finding-description">${finding.description}</div>
                <div class="finding-resources">
                    ${resources.slice(0, 3).map(resource =>
                        `<span class="resource-tag">${resource}</span>`
                    ).join('')}
                    ${resources.length > 3 ? `<span class="resource-tag">+${resources.length - 3} more</span>` : ''}
                </div>
                <div class="finding-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); viewFindingDetails('${finding.id}')">
                        <i class="fas fa-eye me-1"></i>Details
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="event.stopPropagation(); exportFinding('${finding.id}')">
                        <i class="fas fa-download me-1"></i>Export
                    </button>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

function updateTimeline(data) {
    const events = data.timeline || [];
    const container = document.getElementById('timeline-content');

    if (events.length === 0) {
        container.innerHTML = '<p class="text-muted">No timeline events available.</p>';
        return;
    }

    let html = '';
    events.slice(0, 10).forEach(event => {
        const severity = event.severity || 'info';
        const markerClass = severity === 'critical' ? 'critical' :
                           severity === 'warning' ? 'warning' : '';

        html += `
            <div class="timeline-item">
                <div class="timeline-marker ${markerClass}"></div>
                <div class="timeline-time">${new Date(event.timestamp).toLocaleString()}</div>
                <div class="timeline-content">${event.description}</div>
            </div>
        `;
    });

    container.innerHTML = html;
}

function populateCategoryFilters(data) {
    const findings = data.findings || [];
    const categories = [...new Set(findings.map(f => f.category || 'Other'))];
    const container = document.getElementById('category-filters');

    let html = '';
    categories.forEach(category => {
        html += `
            <div class="filter-checkbox">
                <input type="checkbox" class="form-check-input" id="filter-cat-${category}" checked onchange="applyFilters()">
                <label class="form-check-label ms-2" for="filter-cat-${category}">${category}</label>
            </div>
        `;
    });

    container.innerHTML = html;
}

// Filter and interaction functions
function applyFilters() {
    if (!allFindings.length) return;

    const severityFilters = {
        critical: document.getElementById('filter-critical').checked,
        high: document.getElementById('filter-high').checked,
        medium: document.getElementById('filter-medium').checked,
        low: document.getElementById('filter-low').checked
    };

    const dateFrom = document.getElementById('date-from').value;
    const dateTo = document.getElementById('date-to').value;

    // Get category filters
    const categoryFilters = {};
    document.querySelectorAll('#category-filters input[type="checkbox"]').forEach(checkbox => {
        const category = checkbox.id.replace('filter-cat-', '');
        categoryFilters[category] = checkbox.checked;
    });

    filteredFindings = allFindings.filter(finding => {
        // Severity filter
        const severity = (finding.severity || 'Low').toLowerCase();
        if (!severityFilters[severity]) return false;

        // Category filter
        const category = finding.category || 'Other';
        if (!categoryFilters[category]) return false;

        // Date filter
        if (dateFrom || dateTo) {
            const findingDate = new Date(finding.timestamp || Date.now());
            if (dateFrom && findingDate < new Date(dateFrom)) return false;
            if (dateTo && findingDate > new Date(dateTo)) return false;
        }

        return true;
    });

    updateFindings(filteredFindings);
    updateChart({ ...reportData, findings: filteredFindings });
}

function clearFilters() {
    // Reset all filters
    document.getElementById('filter-critical').checked = true;
    document.getElementById('filter-high').checked = true;
    document.getElementById('filter-medium').checked = true;
    document.getElementById('filter-low').checked = false;
    document.getElementById('date-from').value = '';
    document.getElementById('date-to').value = '';

    // Reset category filters
    document.querySelectorAll('#category-filters input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = true;
    });

    applyFilters();
}

function sortFindings(method) {
    let sortedFindings = [...filteredFindings];

    switch (method) {
        case 'severity':
            const severityOrder = { 'Critical': 0, 'High': 1, 'Medium': 2, 'Low': 3 };
            sortedFindings.sort((a, b) => {
                const severityA = severityOrder[a.severity || 'Low'];
                const severityB = severityOrder[b.severity || 'Low'];
                return severityA - severityB;
            });
            break;
        case 'category':
            sortedFindings.sort((a, b) => (a.category || 'Other').localeCompare(b.category || 'Other'));
            break;
        case 'timestamp':
            sortedFindings.sort((a, b) => new Date(b.timestamp || 0) - new Date(a.timestamp || 0));
            break;
    }

    updateFindings(sortedFindings);
}

// Drill-down functions
function drillDownMetric(metric) {
    const modal = new bootstrap.Modal(document.getElementById('drillDownModal'));
    const title = document.getElementById('drillDownTitle');
    const content = document.getElementById('drillDownContent');

    switch (metric) {
        case 'health':
            title.textContent = 'Cluster Health Details';
            content.innerHTML = generateHealthDrillDown();
            break;
        case 'issues':
            title.textContent = 'Issues Breakdown';
            content.innerHTML = generateIssuesDrillDown();
            break;
        case 'resources':
            title.textContent = 'Resources Analysis';
            content.innerHTML = generateResourcesDrillDown();
            break;
        case 'recommendations':
            title.textContent = 'All Recommendations';
            content.innerHTML = generateRecommendationsDrillDown();
            break;
    }

    modal.show();
}

function generateHealthDrillDown() {
    const metrics = reportData?.metrics || {};

    return `
        <div class="row">
            <div class="col-md-6">
                <h6>Health Components</h6>
                <ul class="list-group">
                    <li class="list-group-item d-flex justify-content-between">
                        Node Health <span class="badge bg-success">${metrics.nodeHealth || 85}%</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between">
                        Pod Health <span class="badge bg-warning">${metrics.podHealth || 72}%</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between">
                        Service Health <span class="badge bg-success">${metrics.serviceHealth || 95}%</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between">
                        Storage Health <span class="badge bg-danger">${metrics.storageHealth || 60}%</span>
                    </li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>Health Trends</h6>
                <p class="text-muted">Health score has ${metrics.healthChange > 0 ? 'improved' : 'declined'} by ${Math.abs(metrics.healthChange || 0)}% since last analysis.</p>
                <canvas id="health-trend-chart" width="300" height="200"></canvas>
            </div>
        </div>
    `;
}

function generateIssuesDrillDown() {
    const findings = reportData?.findings || [];
    const severityBreakdown = {
        'Critical': findings.filter(f => f.severity === 'Critical').length,
        'High': findings.filter(f => f.severity === 'High').length,
        'Medium': findings.filter(f => f.severity === 'Medium').length,
        'Low': findings.filter(f => f.severity === 'Low').length
    };

    return `
        <div class="row">
            <div class="col-md-6">
                <h6>Issues by Severity</h6>
                <div class="list-group">
                    ${Object.entries(severityBreakdown).map(([severity, count]) => `
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <span class="severity-badge severity-${severity.toLowerCase()}">${severity}</span>
                            <span class="badge bg-secondary">${count}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
            <div class="col-md-6">
                <h6>Top Categories</h6>
                <div class="list-group">
                    ${getTopCategories(findings).map(([category, count]) => `
                        <div class="list-group-item d-flex justify-content-between">
                            ${category} <span class="badge bg-primary">${count}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;
}

function generateResourcesDrillDown() {
    const resources = reportData?.resourceMetrics || {};

    return `
        <div class="row">
            <div class="col-12">
                <h6>Resource Distribution</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Resource Type</th>
                                <th>Count</th>
                                <th>Health Status</th>
                                <th>Issues Found</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${Object.entries(resources).map(([type, count]) => `
                                <tr>
                                    <td>${type}</td>
                                    <td>${count}</td>
                                    <td><span class="badge bg-success">Healthy</span></td>
                                    <td>${Math.floor(Math.random() * 5)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;
}

function generateRecommendationsDrillDown() {
    const recommendations = reportData?.recommendations || [];

    return `
        <div class="row">
            <div class="col-12">
                ${recommendations.map(rec => `
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="card-title">${rec.title}</h6>
                                <span class="recommendation-priority priority-${rec.priority || 'medium'}">${rec.priority || 'Medium'}</span>
                            </div>
                            <p class="card-text">${rec.description}</p>
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">Impact: ${rec.impact || 'Medium'}</small>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted">Effort: ${rec.effort || 'Medium'}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

function getTopCategories(findings) {
    const categoryCounts = {};
    findings.forEach(finding => {
        const category = finding.category || 'Other';
        categoryCounts[category] = (categoryCounts[category] || 0) + 1;
    });

    return Object.entries(categoryCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5);
}

// Chart interaction functions
function drillDownChart(label) {
    const modal = new bootstrap.Modal(document.getElementById('drillDownModal'));
    const title = document.getElementById('drillDownTitle');
    const content = document.getElementById('drillDownContent');

    title.textContent = `${label} Details`;

    const relatedFindings = filteredFindings.filter(finding => {
        const chartType = document.getElementById('chart-type').value;
        if (chartType === 'severity') {
            return finding.severity === label;
        } else if (chartType === 'category') {
            return finding.category === label;
        }
        return false;
    });

    content.innerHTML = `
        <div class="row">
            <div class="col-12">
                <h6>${relatedFindings.length} findings in ${label}</h6>
                <div class="list-group">
                    ${relatedFindings.map(finding => `
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">${finding.title}</h6>
                                    <p class="mb-1">${finding.description}</p>
                                    <small class="text-muted">${finding.category || 'General'}</small>
                                </div>
                                <span class="severity-badge severity-${(finding.severity || 'Low').toLowerCase()}">${finding.severity || 'Low'}</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;

    modal.show();
}

// Utility functions
function viewFindingDetails(findingId) {
    const finding = allFindings.find(f => f.id === findingId);
    if (!finding) return;

    const modal = new bootstrap.Modal(document.getElementById('drillDownModal'));
    const title = document.getElementById('drillDownTitle');
    const content = document.getElementById('drillDownContent');

    title.textContent = finding.title;
    content.innerHTML = `
        <div class="row">
            <div class="col-md-8">
                <h6>Description</h6>
                <p>${finding.description}</p>

                <h6>Affected Resources</h6>
                <div class="mb-3">
                    ${(finding.affectedResources || []).map(resource =>
                        `<span class="resource-tag">${resource}</span>`
                    ).join('')}
                </div>

                ${finding.remediation ? `
                    <h6>Remediation</h6>
                    <p>${finding.remediation}</p>
                ` : ''}
            </div>
            <div class="col-md-4">
                <h6>Details</h6>
                <table class="table table-sm">
                    <tr>
                        <th>Severity</th>
                        <td><span class="severity-badge severity-${(finding.severity || 'Low').toLowerCase()}">${finding.severity || 'Low'}</span></td>
                    </tr>
                    <tr>
                        <th>Category</th>
                        <td>${finding.category || 'General'}</td>
                    </tr>
                    <tr>
                        <th>Found</th>
                        <td>${new Date(finding.timestamp || Date.now()).toLocaleString()}</td>
                    </tr>
                    ${finding.namespace ? `
                        <tr>
                            <th>Namespace</th>
                            <td>${finding.namespace}</td>
                        </tr>
                    ` : ''}
                </table>
            </div>
        </div>
    `;

    modal.show();
}

function viewRecommendationDetails(recId) {
    const recommendation = (reportData?.recommendations || []).find(r => r.id === recId);
    if (!recommendation) return;

    const modal = new bootstrap.Modal(document.getElementById('drillDownModal'));
    const title = document.getElementById('drillDownTitle');
    const content = document.getElementById('drillDownContent');

    title.textContent = recommendation.title;
    content.innerHTML = `
        <div class="row">
            <div class="col-12">
                <div class="mb-3">
                    <span class="recommendation-priority priority-${recommendation.priority || 'medium'}">${recommendation.priority || 'Medium'} Priority</span>
                </div>

                <h6>Description</h6>
                <p>${recommendation.description}</p>

                ${recommendation.steps ? `
                    <h6>Implementation Steps</h6>
                    <ol>
                        ${recommendation.steps.map(step => `<li>${step}</li>`).join('')}
                    </ol>
                ` : ''}

                <div class="row mt-3">
                    <div class="col-md-4">
                        <strong>Impact:</strong> ${recommendation.impact || 'Medium'}
                    </div>
                    <div class="col-md-4">
                        <strong>Effort:</strong> ${recommendation.effort || 'Medium'}
                    </div>
                    <div class="col-md-4">
                        <strong>Risk:</strong> ${recommendation.risk || 'Low'}
                    </div>
                </div>
            </div>
        </div>
    `;

    modal.show();
}

// Export functions
function exportReport() {
    if (!reportData) {
        showAlert('No report data to export', 'warning');
        return;
    }

    const exportData = {
        metadata: {
            jobId: currentJobId,
            timestamp: new Date().toISOString(),
            clusterName: reportData.clusterName || 'Unknown'
        },
        metrics: reportData.metrics || {},
        findings: filteredFindings,
        recommendations: reportData.recommendations || [],
        timeline: reportData.timeline || []
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `openshift-analysis-report-${currentJobId?.substring(0, 8) || 'export'}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    showAlert('Report exported successfully', 'success');
}

function exportChart() {
    if (!currentChart) return;

    const link = document.createElement('a');
    link.href = currentChart.toBase64Image();
    link.download = `chart-${document.getElementById('chart-type').value}-${Date.now()}.png`;
    link.click();

    showAlert('Chart exported successfully', 'success');
}

function exportFinding(findingId) {
    const finding = allFindings.find(f => f.id === findingId);
    if (!finding) return;

    const blob = new Blob([JSON.stringify(finding, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `finding-${findingId}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

function exportDrillDown() {
    const content = document.getElementById('drillDownContent').textContent;
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `drill-down-details-${Date.now()}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

// UI state functions
function showReportContent() {
    document.getElementById('metrics-dashboard').style.display = 'block';
    document.getElementById('main-content').style.display = 'block';
}

function hideReportContent() {
    document.getElementById('metrics-dashboard').style.display = 'none';
    document.getElementById('main-content').style.display = 'none';
}

function showLoading(show) {
    document.getElementById('loading-overlay').style.display = show ? 'flex' : 'none';
}

function refreshReport() {
    if (currentJobId) {
        loadReportData();
    }
}

function toggleChartAnimation() {
    if (currentChart) {
        currentChart.options.animation = !currentChart.options.animation;
        currentChart.update();
    }
}

function showAlert(message, type = 'info') {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alert);

    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
