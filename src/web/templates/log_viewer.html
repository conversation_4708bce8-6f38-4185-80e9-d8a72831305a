{% extends "base.html" %}

{% block title %}Log Viewer - OpenShift Must-Gather Analyzer{% endblock %}

{% block extra_css %}
<style>
    .log-container {
        background-color: #1e1e1e;
        color: #d4d4d4;
        font-family: '<PERSON>sol<PERSON>', 'Monaco', 'Courier New', monospace;
        font-size: 0.875rem;
        line-height: 1.4;
        max-height: 600px;
        overflow-y: auto;
        border-radius: 0.375rem;
        position: relative;
    }
    
    .log-line {
        padding: 0.25rem 1rem;
        border-left: 3px solid transparent;
        white-space: pre-wrap;
        word-break: break-word;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    
    .log-line:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    .log-line.selected {
        background-color: rgba(0, 123, 255, 0.2);
        border-left-color: #007bff;
    }
    
    .log-line.error {
        border-left-color: #dc3545;
        background-color: rgba(220, 53, 69, 0.1);
    }
    
    .log-line.warning {
        border-left-color: #ffc107;
        background-color: rgba(255, 193, 7, 0.1);
    }
    
    .log-line.info {
        border-left-color: #17a2b8;
        background-color: rgba(23, 162, 184, 0.1);
    }
    
    .log-line.debug {
        border-left-color: #6c757d;
        color: #adb5bd;
    }
    
    .log-timestamp {
        color: #569cd6;
        font-weight: 500;
    }
    
    .log-level {
        font-weight: bold;
        padding: 0.125rem 0.375rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        margin-right: 0.5rem;
    }
    
    .log-level.ERROR {
        background-color: #dc3545;
        color: white;
    }
    
    .log-level.WARN {
        background-color: #ffc107;
        color: #212529;
    }
    
    .log-level.INFO {
        background-color: #17a2b8;
        color: white;
    }
    
    .log-level.DEBUG {
        background-color: #6c757d;
        color: white;
    }
    
    .log-search-highlight {
        background-color: #ffeb3b;
        color: #212529;
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
    }
    
    .log-controls {
        position: sticky;
        top: 0;
        background-color: white;
        z-index: 10;
        border-bottom: 1px solid #dee2e6;
        padding: 1rem;
        margin: -1rem -1rem 1rem -1rem;
    }
    
    .log-stats {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .filter-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        margin: 0.125rem;
        border-radius: 1rem;
        font-size: 0.75rem;
        cursor: pointer;
        transition: all 0.2s;
        border: 1px solid #dee2e6;
    }
    
    .filter-badge.active {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }
    
    .filter-badge.error {
        border-color: #dc3545;
        color: #dc3545;
    }
    
    .filter-badge.error.active {
        background-color: #dc3545;
        color: white;
    }
    
    .filter-badge.warning {
        border-color: #ffc107;
        color: #ffc107;
    }
    
    .filter-badge.warning.active {
        background-color: #ffc107;
        color: #212529;
    }
    
    .filter-badge.info {
        border-color: #17a2b8;
        color: #17a2b8;
    }
    
    .filter-badge.info.active {
        background-color: #17a2b8;
        color: white;
    }
    
    .filter-badge.debug {
        border-color: #6c757d;
        color: #6c757d;
    }
    
    .filter-badge.debug.active {
        background-color: #6c757d;
        color: white;
    }
    
    .log-line-number {
        color: #858585;
        margin-right: 1rem;
        user-select: none;
        min-width: 4rem;
        display: inline-block;
        text-align: right;
    }
    
    .auto-scroll-indicator {
        position: absolute;
        bottom: 1rem;
        right: 1rem;
        background-color: rgba(0, 123, 255, 0.9);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 1rem;
        font-size: 0.875rem;
        display: none;
    }
    
    .log-export-menu {
        position: absolute;
        top: 100%;
        right: 0;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        z-index: 1000;
        min-width: 200px;
        display: none;
    }
    
    .log-export-menu.show {
        display: block;
    }
    
    .log-export-item {
        padding: 0.5rem 1rem;
        cursor: pointer;
        border-bottom: 1px solid #f8f9fa;
        transition: background-color 0.2s;
    }
    
    .log-export-item:hover {
        background-color: #f8f9fa;
    }
    
    .log-export-item:last-child {
        border-bottom: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-file-alt me-2"></i>
                Log Viewer
            </h1>
            <div class="btn-group" role="group">
                <button class="btn btn-outline-primary" onclick="refreshLogs()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
                <button class="btn btn-outline-secondary" onclick="clearLogs()">
                    <i class="fas fa-trash me-1"></i>Clear
                </button>
                <div class="position-relative">
                    <button class="btn btn-outline-success" onclick="toggleExportMenu()">
                        <i class="fas fa-download me-1"></i>Export
                    </button>
                    <div class="log-export-menu" id="export-menu">
                        <div class="log-export-item" onclick="exportLogs('txt')">
                            <i class="fas fa-file-alt me-2"></i>Export as Text
                        </div>
                        <div class="log-export-item" onclick="exportLogs('csv')">
                            <i class="fas fa-file-csv me-2"></i>Export as CSV
                        </div>
                        <div class="log-export-item" onclick="exportLogs('json')">
                            <i class="fas fa-file-code me-2"></i>Export as JSON
                        </div>
                        <div class="log-export-item" onclick="exportFiltered()">
                            <i class="fas fa-filter me-2"></i>Export Filtered Only
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Job and Log File Selection -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-folder-open me-2"></i>
                    Log File Selection
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">Analysis Job</label>
                        <select class="form-select" id="job-selector" onchange="loadLogFiles()">
                            <option value="">Select a completed analysis job...</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Log File</label>
                        <select class="form-select" id="log-file-selector" onchange="loadLogContent()" disabled>
                            <option value="">Select a log file...</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Log Controls and Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="log-controls">
                    <div class="row align-items-center mb-3">
                        <div class="col-md-8">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="log-search" 
                                       placeholder="Search log content..." 
                                       onkeyup="searchLogs(event)">
                                <button class="btn btn-outline-secondary" onclick="clearLogSearch()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="auto-scroll" onchange="toggleAutoScroll()">
                                <label class="form-check-label" for="auto-scroll">
                                    Auto-scroll to bottom
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row align-items-center mb-3">
                        <div class="col-md-6">
                            <label class="form-label mb-1">Log Level Filters:</label>
                            <div>
                                <span class="filter-badge error" data-level="error" onclick="toggleLogFilter(this)">
                                    <i class="fas fa-exclamation-triangle me-1"></i>ERROR
                                </span>
                                <span class="filter-badge warning" data-level="warning" onclick="toggleLogFilter(this)">
                                    <i class="fas fa-exclamation-circle me-1"></i>WARN
                                </span>
                                <span class="filter-badge info" data-level="info" onclick="toggleLogFilter(this)">
                                    <i class="fas fa-info-circle me-1"></i>INFO
                                </span>
                                <span class="filter-badge debug" data-level="debug" onclick="toggleLogFilter(this)">
                                    <i class="fas fa-bug me-1"></i>DEBUG
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="log-stats" id="log-stats">
                                <div class="row text-center">
                                    <div class="col-3">
                                        <div class="fw-bold" id="total-lines">0</div>
                                        <small>Total Lines</small>
                                    </div>
                                    <div class="col-3">
                                        <div class="fw-bold text-danger" id="error-count">0</div>
                                        <small>Errors</small>
                                    </div>
                                    <div class="col-3">
                                        <div class="fw-bold text-warning" id="warning-count">0</div>
                                        <small>Warnings</small>
                                    </div>
                                    <div class="col-3">
                                        <div class="fw-bold" id="visible-lines">0</div>
                                        <small>Visible</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="input-group input-group-sm">
                                <span class="input-group-text">Time Range:</span>
                                <input type="datetime-local" class="form-control" id="start-time" onchange="applyTimeFilter()">
                                <span class="input-group-text">to</span>
                                <input type="datetime-local" class="form-control" id="end-time" onchange="applyTimeFilter()">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="btn-group btn-group-sm" role="group">
                                <button class="btn btn-outline-secondary" onclick="jumpToTop()">
                                    <i class="fas fa-angle-double-up me-1"></i>Top
                                </button>
                                <button class="btn btn-outline-secondary" onclick="jumpToBottom()">
                                    <i class="fas fa-angle-double-down me-1"></i>Bottom
                                </button>
                                <button class="btn btn-outline-secondary" onclick="toggleLineNumbers()">
                                    <i class="fas fa-list-ol me-1"></i>Line #
                                </button>
                                <button class="btn btn-outline-secondary" onclick="toggleWordWrap()">
                                    <i class="fas fa-text-width me-1"></i>Wrap
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Log Content -->
                <div class="log-container" id="log-container">
                    <div class="text-center text-muted p-5">
                        <i class="fas fa-file-alt fa-3x mb-3"></i>
                        <p>Select a job and log file to view content</p>
                        <small>Supports real-time filtering, search, and export</small>
                    </div>
                    <div class="auto-scroll-indicator" id="auto-scroll-indicator">
                        <i class="fas fa-arrow-down me-1"></i>Auto-scrolling...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentJobId = null;
let currentLogFile = null;
let logLines = [];
let filteredLines = [];
let activeLogFilters = new Set(['error', 'warning', 'info', 'debug']);
let autoScrollEnabled = false;
let showLineNumbers = false;
let wordWrapEnabled = false;
let searchQuery = '';

// Initialize log viewer
document.addEventListener('DOMContentLoaded', function() {
    loadAvailableJobs();

    // Close export menu when clicking outside
    document.addEventListener('click', function(event) {
        const exportMenu = document.getElementById('export-menu');
        const exportBtn = event.target.closest('[onclick="toggleExportMenu()"]');

        if (!exportBtn && !exportMenu.contains(event.target)) {
            exportMenu.classList.remove('show');
        }
    });
});

function loadAvailableJobs() {
    fetch('/api/jobs')
        .then(response => response.json())
        .then(data => {
            const selector = document.getElementById('job-selector');
            const completedJobs = Object.values(data).filter(job => job.status === 'completed');

            selector.innerHTML = '<option value="">Select a completed analysis job...</option>';

            completedJobs.forEach(job => {
                const option = document.createElement('option');
                option.value = job.job_id;
                option.textContent = `Job ${job.job_id.substring(0, 8)} - ${new Date(job.start_time).toLocaleString()}`;
                selector.appendChild(option);
            });

            if (completedJobs.length === 0) {
                selector.innerHTML = '<option value="">No completed jobs available</option>';
            }
        })
        .catch(error => {
            console.error('Error loading jobs:', error);
            showAlert('Error loading available jobs', 'danger');
        });
}

function loadLogFiles() {
    const selector = document.getElementById('job-selector');
    const jobId = selector.value;
    const logFileSelector = document.getElementById('log-file-selector');

    if (!jobId) {
        currentJobId = null;
        logFileSelector.disabled = true;
        logFileSelector.innerHTML = '<option value="">Select a log file...</option>';
        clearLogs();
        return;
    }

    currentJobId = jobId;
    logFileSelector.disabled = false;
    logFileSelector.innerHTML = '<option value="">Loading log files...</option>';

    fetch(`/api/jobs/${jobId}/files`)
        .then(response => response.json())
        .then(data => {
            const logFiles = Object.keys(data).filter(path =>
                path.toLowerCase().includes('log') ||
                path.endsWith('.log') ||
                path.includes('/logs/')
            );

            logFileSelector.innerHTML = '<option value="">Select a log file...</option>';

            logFiles.forEach(logFile => {
                const option = document.createElement('option');
                option.value = logFile;
                option.textContent = logFile;
                logFileSelector.appendChild(option);
            });

            if (logFiles.length === 0) {
                logFileSelector.innerHTML = '<option value="">No log files found</option>';
            }
        })
        .catch(error => {
            console.error('Error loading log files:', error);
            logFileSelector.innerHTML = '<option value="">Error loading log files</option>';
            showAlert('Error loading log files', 'danger');
        });
}

function loadLogContent() {
    const logFileSelector = document.getElementById('log-file-selector');
    const logFile = logFileSelector.value;

    if (!logFile || !currentJobId) {
        clearLogs();
        return;
    }

    currentLogFile = logFile;

    const container = document.getElementById('log-container');
    container.innerHTML = '<div class="text-center p-4"><i class="fas fa-spinner fa-spin fa-2x"></i><br>Loading log content...</div>';

    fetch(`/api/jobs/${currentJobId}/files/${encodeURIComponent(logFile)}`)
        .then(response => response.text())
        .then(content => {
            parseLogContent(content);
            renderLogLines();
            updateLogStats();
        })
        .catch(error => {
            console.error('Error loading log content:', error);
            container.innerHTML = '<div class="text-center text-danger p-4"><i class="fas fa-exclamation-triangle fa-2x"></i><br>Error loading log content</div>';
            showAlert('Error loading log content', 'danger');
        });
}

function parseLogContent(content) {
    const lines = content.split('\n');
    logLines = [];

    lines.forEach((line, index) => {
        if (line.trim()) {
            const parsed = parseLogLine(line, index + 1);
            logLines.push(parsed);
        }
    });

    filteredLines = [...logLines];
}

function parseLogLine(line, lineNumber) {
    // Try to extract timestamp, level, and message
    const timestampRegex = /^(\d{4}-\d{2}-\d{2}[T\s]\d{2}:\d{2}:\d{2}[^\s]*)/;
    const levelRegex = /\b(ERROR|FATAL|CRITICAL|WARN|WARNING|INFO|INFORMATION|DEBUG|TRACE)\b/i;

    const timestampMatch = line.match(timestampRegex);
    const levelMatch = line.match(levelRegex);

    let timestamp = null;
    let level = 'info';
    let message = line;

    if (timestampMatch) {
        timestamp = timestampMatch[1];
        message = line.substring(timestampMatch[0].length).trim();
    }

    if (levelMatch) {
        level = levelMatch[1].toLowerCase();
        if (level === 'warning') level = 'warning';
        if (level === 'fatal' || level === 'critical') level = 'error';
        if (level === 'information') level = 'info';
        if (level === 'trace') level = 'debug';
    }

    return {
        lineNumber,
        timestamp,
        level,
        message,
        originalLine: line,
        visible: true
    };
}

function renderLogLines() {
    const container = document.getElementById('log-container');

    if (filteredLines.length === 0) {
        container.innerHTML = '<div class="text-center text-muted p-4">No log lines to display</div>';
        return;
    }

    let html = '';
    filteredLines.forEach(logLine => {
        if (!logLine.visible) return;

        const levelClass = logLine.level;
        const lineNumberHtml = showLineNumbers ? `<span class="log-line-number">${logLine.lineNumber}</span>` : '';

        let content = logLine.originalLine;
        if (searchQuery) {
            const regex = new RegExp(`(${escapeRegex(searchQuery)})`, 'gi');
            content = content.replace(regex, '<span class="log-search-highlight">$1</span>');
        }

        html += `
            <div class="log-line ${levelClass}" data-line="${logLine.lineNumber}" onclick="selectLogLine(this)">
                ${lineNumberHtml}${content}
            </div>
        `;
    });

    container.innerHTML = html;

    if (autoScrollEnabled) {
        container.scrollTop = container.scrollHeight;
    }
}

function selectLogLine(element) {
    document.querySelectorAll('.log-line').forEach(line => {
        line.classList.remove('selected');
    });
    element.classList.add('selected');
}

function toggleLogFilter(badge) {
    const level = badge.dataset.level;

    if (activeLogFilters.has(level)) {
        activeLogFilters.delete(level);
        badge.classList.remove('active');
    } else {
        activeLogFilters.add(level);
        badge.classList.add('active');
    }

    applyFilters();
}

function applyFilters() {
    filteredLines = logLines.filter(line => {
        // Level filter
        if (!activeLogFilters.has(line.level)) {
            return false;
        }

        // Search filter
        if (searchQuery && !line.originalLine.toLowerCase().includes(searchQuery.toLowerCase())) {
            return false;
        }

        // Time range filter
        const startTime = document.getElementById('start-time').value;
        const endTime = document.getElementById('end-time').value;

        if (startTime || endTime) {
            if (line.timestamp) {
                const lineTime = new Date(line.timestamp);
                if (startTime && lineTime < new Date(startTime)) return false;
                if (endTime && lineTime > new Date(endTime)) return false;
            }
        }

        return true;
    });

    renderLogLines();
    updateLogStats();
}

function searchLogs(event) {
    if (event.key === 'Enter') {
        searchQuery = document.getElementById('log-search').value.trim();
        applyFilters();
    }
}

function clearLogSearch() {
    document.getElementById('log-search').value = '';
    searchQuery = '';
    applyFilters();
}

function updateLogStats() {
    const totalLines = logLines.length;
    const visibleLines = filteredLines.length;
    const errorCount = logLines.filter(line => line.level === 'error').length;
    const warningCount = logLines.filter(line => line.level === 'warning').length;

    document.getElementById('total-lines').textContent = totalLines;
    document.getElementById('visible-lines').textContent = visibleLines;
    document.getElementById('error-count').textContent = errorCount;
    document.getElementById('warning-count').textContent = warningCount;
}

function toggleAutoScroll() {
    autoScrollEnabled = document.getElementById('auto-scroll').checked;
    const indicator = document.getElementById('auto-scroll-indicator');

    if (autoScrollEnabled) {
        indicator.style.display = 'block';
        const container = document.getElementById('log-container');
        container.scrollTop = container.scrollHeight;
    } else {
        indicator.style.display = 'none';
    }
}

function applyTimeFilter() {
    applyFilters();
}

function jumpToTop() {
    document.getElementById('log-container').scrollTop = 0;
}

function jumpToBottom() {
    const container = document.getElementById('log-container');
    container.scrollTop = container.scrollHeight;
}

function toggleLineNumbers() {
    showLineNumbers = !showLineNumbers;
    renderLogLines();
}

function toggleWordWrap() {
    wordWrapEnabled = !wordWrapEnabled;
    const container = document.getElementById('log-container');

    if (wordWrapEnabled) {
        container.style.whiteSpace = 'pre-wrap';
    } else {
        container.style.whiteSpace = 'pre';
    }
}

function refreshLogs() {
    if (currentLogFile) {
        loadLogContent();
    }
}

function clearLogs() {
    logLines = [];
    filteredLines = [];
    currentLogFile = null;

    const container = document.getElementById('log-container');
    container.innerHTML = `
        <div class="text-center text-muted p-5">
            <i class="fas fa-file-alt fa-3x mb-3"></i>
            <p>Select a job and log file to view content</p>
            <small>Supports real-time filtering, search, and export</small>
        </div>
    `;

    updateLogStats();
}

function toggleExportMenu() {
    const menu = document.getElementById('export-menu');
    menu.classList.toggle('show');
}

function exportLogs(format) {
    if (filteredLines.length === 0) {
        showAlert('No log data to export', 'warning');
        return;
    }

    let content = '';
    let filename = `logs_${currentJobId?.substring(0, 8) || 'export'}_${new Date().toISOString().split('T')[0]}`;
    let mimeType = 'text/plain';

    switch (format) {
        case 'txt':
            content = filteredLines.map(line => line.originalLine).join('\n');
            filename += '.txt';
            break;

        case 'csv':
            content = 'Line Number,Timestamp,Level,Message\n';
            content += filteredLines.map(line =>
                `${line.lineNumber},"${line.timestamp || ''}","${line.level}","${line.message.replace(/"/g, '""')}"`
            ).join('\n');
            filename += '.csv';
            mimeType = 'text/csv';
            break;

        case 'json':
            content = JSON.stringify(filteredLines, null, 2);
            filename += '.json';
            mimeType = 'application/json';
            break;
    }

    downloadContent(content, filename, mimeType);
    document.getElementById('export-menu').classList.remove('show');
}

function exportFiltered() {
    exportLogs('txt');
}

function downloadContent(content, filename, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

function escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function showAlert(message, type = 'info') {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alert);

    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
</script>
{% endblock %}
