{% extends "base.html" %}

{% block title %}Data Browser - OpenShift Must-Gather Analyzer{% endblock %}

{% block extra_css %}
<style>
    .file-tree {
        max-height: 600px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
    }
    
    .tree-item {
        padding: 0.25rem 0.5rem;
        cursor: pointer;
        border-bottom: 1px solid #f8f9fa;
        transition: background-color 0.2s;
    }
    
    .tree-item:hover {
        background-color: #f8f9fa;
    }
    
    .tree-item.selected {
        background-color: #e3f2fd;
        border-left: 3px solid #2196f3;
    }
    
    .tree-item .tree-icon {
        width: 20px;
        text-align: center;
        margin-right: 0.5rem;
    }
    
    .tree-item.folder {
        font-weight: 500;
    }
    
    .tree-item.file {
        color: #6c757d;
    }
    
    .tree-item .tree-indent {
        display: inline-block;
        width: 20px;
    }
    
    .content-viewer {
        min-height: 400px;
        max-height: 600px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        background-color: #f8f9fa;
    }
    
    .content-viewer pre {
        margin: 0;
        padding: 1rem;
        background: transparent;
        border: none;
        font-size: 0.875rem;
        line-height: 1.4;
    }
    
    .search-highlight {
        background-color: #fff3cd;
        padding: 0.1rem 0.2rem;
        border-radius: 0.2rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
    }
    
    .filter-chip {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        margin: 0.125rem;
        background-color: #e9ecef;
        border-radius: 1rem;
        font-size: 0.75rem;
        cursor: pointer;
        transition: all 0.2s;
    }
    
    .filter-chip:hover {
        background-color: #dee2e6;
    }
    
    .filter-chip.active {
        background-color: #0d6efd;
        color: white;
    }
    
    .metadata-table {
        font-size: 0.875rem;
    }
    
    .metadata-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        width: 30%;
    }
    
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
    }
    
    .syntax-highlight .keyword { color: #0000ff; font-weight: bold; }
    .syntax-highlight .string { color: #008000; }
    .syntax-highlight .number { color: #ff6600; }
    .syntax-highlight .comment { color: #808080; font-style: italic; }
    .syntax-highlight .property { color: #800080; }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-database me-2"></i>
                Data Browser
            </h1>
            <div class="btn-group" role="group">
                <button class="btn btn-outline-primary" onclick="refreshDataTree()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
                <button class="btn btn-outline-secondary" onclick="expandAll()">
                    <i class="fas fa-expand-arrows-alt me-1"></i>Expand All
                </button>
                <button class="btn btn-outline-secondary" onclick="collapseAll()">
                    <i class="fas fa-compress-arrows-alt me-1"></i>Collapse All
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Job Selection -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-folder-open me-2"></i>
                    Select Analysis Job
                </h6>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <select class="form-select" id="job-selector" onchange="loadJobData()">
                            <option value="">Select a completed analysis job...</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <div id="job-info" class="text-muted">
                            <small>Select a job to browse its must-gather data</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-search me-2"></i>
                    Search and Filters
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" id="search-input" 
                                   placeholder="Search files, content, or resource names..." 
                                   onkeyup="performSearch(event)">
                            <button class="btn btn-outline-secondary" onclick="clearSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" id="file-type-filter" onchange="applyFilters()">
                            <option value="">All file types</option>
                            <option value="yaml">YAML files</option>
                            <option value="json">JSON files</option>
                            <option value="log">Log files</option>
                            <option value="txt">Text files</option>
                        </select>
                    </div>
                </div>
                <div class="mt-2">
                    <div class="d-flex flex-wrap" id="filter-chips">
                        <span class="filter-chip" data-filter="pods" onclick="toggleFilter(this)">
                            <i class="fas fa-cube me-1"></i>Pods
                        </span>
                        <span class="filter-chip" data-filter="services" onclick="toggleFilter(this)">
                            <i class="fas fa-network-wired me-1"></i>Services
                        </span>
                        <span class="filter-chip" data-filter="deployments" onclick="toggleFilter(this)">
                            <i class="fas fa-rocket me-1"></i>Deployments
                        </span>
                        <span class="filter-chip" data-filter="configmaps" onclick="toggleFilter(this)">
                            <i class="fas fa-cog me-1"></i>ConfigMaps
                        </span>
                        <span class="filter-chip" data-filter="secrets" onclick="toggleFilter(this)">
                            <i class="fas fa-key me-1"></i>Secrets
                        </span>
                        <span class="filter-chip" data-filter="events" onclick="toggleFilter(this)">
                            <i class="fas fa-calendar-alt me-1"></i>Events
                        </span>
                        <span class="filter-chip" data-filter="logs" onclick="toggleFilter(this)">
                            <i class="fas fa-file-alt me-1"></i>Logs
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Area -->
<div class="row">
    <!-- File Tree -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-folder-tree me-2"></i>
                    File Structure
                </h6>
                <div class="btn-group btn-group-sm" role="group">
                    <button class="btn btn-outline-secondary" onclick="sortTree('name')" title="Sort by name">
                        <i class="fas fa-sort-alpha-down"></i>
                    </button>
                    <button class="btn btn-outline-secondary" onclick="sortTree('type')" title="Sort by type">
                        <i class="fas fa-sort"></i>
                    </button>
                </div>
            </div>
            <div class="card-body p-0 position-relative">
                <div id="loading-tree" class="loading-overlay" style="display: none;">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                        <div>Loading file tree...</div>
                    </div>
                </div>
                <div id="file-tree" class="file-tree">
                    <div class="text-center text-muted p-4">
                        <i class="fas fa-folder-open fa-3x mb-3"></i>
                        <p>Select a job to view its file structure</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Viewer -->
    <div class="col-lg-8 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="card-title mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        Content Viewer
                    </h6>
                    <nav aria-label="breadcrumb" class="mt-1">
                        <ol class="breadcrumb breadcrumb-sm mb-0" id="file-breadcrumb">
                            <li class="breadcrumb-item text-muted">No file selected</li>
                        </ol>
                    </nav>
                </div>
                <div class="btn-group btn-group-sm" role="group">
                    <button class="btn btn-outline-secondary" onclick="downloadFile()" id="download-btn" disabled>
                        <i class="fas fa-download me-1"></i>Download
                    </button>
                    <button class="btn btn-outline-secondary" onclick="copyContent()" id="copy-btn" disabled>
                        <i class="fas fa-copy me-1"></i>Copy
                    </button>
                    <button class="btn btn-outline-secondary" onclick="toggleWrap()" id="wrap-btn">
                        <i class="fas fa-text-width me-1"></i>Wrap
                    </button>
                </div>
            </div>
            <div class="card-body p-0 position-relative">
                <div id="loading-content" class="loading-overlay" style="display: none;">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                        <div>Loading content...</div>
                    </div>
                </div>
                <div id="content-viewer" class="content-viewer">
                    <div class="text-center text-muted p-5">
                        <i class="fas fa-file-alt fa-3x mb-3"></i>
                        <p>Select a file from the tree to view its content</p>
                        <small>Supports YAML, JSON, logs, and text files with syntax highlighting</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- File Metadata Modal -->
<div class="modal fade" id="metadataModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>
                    File Metadata
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <table class="table table-borderless metadata-table" id="metadata-table">
                    <tbody>
                        <!-- Metadata will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentJobId = null;
let currentFilePath = null;
let fileTreeData = {};
let activeFilters = new Set();
let searchResults = [];
let wrapEnabled = false;

// Initialize the data browser
document.addEventListener('DOMContentLoaded', function() {
    loadAvailableJobs();
});

function loadAvailableJobs() {
    fetch('/api/jobs')
        .then(response => response.json())
        .then(data => {
            const selector = document.getElementById('job-selector');
            const completedJobs = Object.values(data).filter(job => job.status === 'completed');
            
            selector.innerHTML = '<option value="">Select a completed analysis job...</option>';
            
            completedJobs.forEach(job => {
                const option = document.createElement('option');
                option.value = job.job_id;
                option.textContent = `Job ${job.job_id.substring(0, 8)} - ${new Date(job.start_time).toLocaleString()}`;
                selector.appendChild(option);
            });
            
            if (completedJobs.length === 0) {
                selector.innerHTML = '<option value="">No completed jobs available</option>';
            }
        })
        .catch(error => {
            console.error('Error loading jobs:', error);
            showAlert('Error loading available jobs', 'danger');
        });
}

function loadJobData() {
    const selector = document.getElementById('job-selector');
    const jobId = selector.value;

    if (!jobId) {
        currentJobId = null;
        clearFileTree();
        clearContentViewer();
        return;
    }

    currentJobId = jobId;
    showTreeLoading(true);

    // Update job info
    const jobInfo = document.getElementById('job-info');
    jobInfo.innerHTML = `<small class="text-primary">Loading data for job ${jobId.substring(0, 8)}...</small>`;

    // Load file tree for this job
    fetch(`/api/jobs/${jobId}/files`)
        .then(response => response.json())
        .then(data => {
            fileTreeData = data;
            renderFileTree(data);
            jobInfo.innerHTML = `<small class="text-success">Loaded ${Object.keys(data).length} files</small>`;
        })
        .catch(error => {
            console.error('Error loading job data:', error);
            showAlert('Error loading job data', 'danger');
            jobInfo.innerHTML = `<small class="text-danger">Error loading job data</small>`;
        })
        .finally(() => {
            showTreeLoading(false);
        });
}

function renderFileTree(data) {
    const container = document.getElementById('file-tree');
    container.innerHTML = '';

    // Convert flat file list to tree structure
    const tree = buildTreeStructure(data);

    // Render tree nodes
    renderTreeNode(container, tree, 0);
}

function buildTreeStructure(files) {
    const tree = {};

    Object.keys(files).forEach(filePath => {
        const parts = filePath.split('/');
        let current = tree;

        parts.forEach((part, index) => {
            if (!current[part]) {
                current[part] = {
                    type: index === parts.length - 1 ? 'file' : 'folder',
                    path: parts.slice(0, index + 1).join('/'),
                    children: {},
                    metadata: index === parts.length - 1 ? files[filePath] : null
                };
            }
            current = current[part].children;
        });
    });

    return tree;
}

function renderTreeNode(container, node, depth) {
    Object.keys(node).sort().forEach(key => {
        const item = node[key];
        const div = document.createElement('div');
        div.className = `tree-item ${item.type}`;
        div.dataset.path = item.path;
        div.dataset.type = item.type;

        const indent = ''.repeat(depth);
        const icon = item.type === 'folder' ? 'fas fa-folder' : getFileIcon(item.path);

        div.innerHTML = `
            <span class="tree-indent">${indent}</span>
            <span class="tree-icon"><i class="${icon}"></i></span>
            <span class="tree-label">${key}</span>
        `;

        if (item.type === 'file') {
            div.addEventListener('click', () => selectFile(item.path));
        } else {
            div.addEventListener('click', () => toggleFolder(div));
        }

        container.appendChild(div);

        // Render children if folder
        if (item.type === 'folder' && Object.keys(item.children).length > 0) {
            const childContainer = document.createElement('div');
            childContainer.className = 'tree-children';
            childContainer.style.display = 'none';
            renderTreeNode(childContainer, item.children, depth + 1);
            container.appendChild(childContainer);
        }
    });
}

function getFileIcon(filePath) {
    const ext = filePath.split('.').pop().toLowerCase();
    const iconMap = {
        'yaml': 'fas fa-file-code text-primary',
        'yml': 'fas fa-file-code text-primary',
        'json': 'fas fa-file-code text-success',
        'log': 'fas fa-file-alt text-warning',
        'txt': 'fas fa-file-alt text-secondary',
        'xml': 'fas fa-file-code text-info',
        'conf': 'fas fa-cog text-secondary'
    };
    return iconMap[ext] || 'fas fa-file text-muted';
}

function toggleFolder(folderElement) {
    const childContainer = folderElement.nextElementSibling;
    if (childContainer && childContainer.classList.contains('tree-children')) {
        const isVisible = childContainer.style.display !== 'none';
        childContainer.style.display = isVisible ? 'none' : 'block';

        const icon = folderElement.querySelector('.tree-icon i');
        icon.className = isVisible ? 'fas fa-folder' : 'fas fa-folder-open';
    }
}

function selectFile(filePath) {
    // Update selection
    document.querySelectorAll('.tree-item').forEach(item => {
        item.classList.remove('selected');
    });

    const selectedItem = document.querySelector(`[data-path="${filePath}"]`);
    if (selectedItem) {
        selectedItem.classList.add('selected');
    }

    currentFilePath = filePath;
    loadFileContent(filePath);
    updateBreadcrumb(filePath);

    // Enable action buttons
    document.getElementById('download-btn').disabled = false;
    document.getElementById('copy-btn').disabled = false;
}

function loadFileContent(filePath) {
    if (!currentJobId) return;

    showContentLoading(true);

    fetch(`/api/jobs/${currentJobId}/files/${encodeURIComponent(filePath)}`)
        .then(response => response.text())
        .then(content => {
            displayFileContent(content, filePath);
        })
        .catch(error => {
            console.error('Error loading file content:', error);
            displayError('Error loading file content');
        })
        .finally(() => {
            showContentLoading(false);
        });
}

function displayFileContent(content, filePath) {
    const viewer = document.getElementById('content-viewer');
    const ext = filePath.split('.').pop().toLowerCase();

    let formattedContent = content;

    // Apply syntax highlighting based on file type
    if (['yaml', 'yml'].includes(ext)) {
        formattedContent = highlightYaml(content);
    } else if (ext === 'json') {
        try {
            const parsed = JSON.parse(content);
            formattedContent = highlightJson(JSON.stringify(parsed, null, 2));
        } catch (e) {
            formattedContent = highlightJson(content);
        }
    } else if (ext === 'log') {
        formattedContent = highlightLogs(content);
    }

    viewer.innerHTML = `<pre class="syntax-highlight">${formattedContent}</pre>`;

    // Apply word wrap if enabled
    if (wrapEnabled) {
        viewer.querySelector('pre').style.whiteSpace = 'pre-wrap';
    }
}

// Syntax highlighting functions
function highlightYaml(content) {
    return content
        .replace(/^(\s*)([\w-]+)(\s*:)/gm, '$1<span class="property">$2</span>$3')
        .replace(/:\s*([^\n\r]+)/g, ': <span class="string">$1</span>')
        .replace(/#[^\n\r]*/g, '<span class="comment">$&</span>')
        .replace(/\b(true|false|null)\b/g, '<span class="keyword">$1</span>')
        .replace(/\b\d+\b/g, '<span class="number">$&</span>');
}

function highlightJson(content) {
    return content
        .replace(/"([^"]+)"(\s*:)/g, '<span class="property">"$1"</span>$2')
        .replace(/:\s*"([^"]+)"/g, ': <span class="string">"$1"</span>')
        .replace(/\b(true|false|null)\b/g, '<span class="keyword">$1</span>')
        .replace(/\b\d+\.?\d*\b/g, '<span class="number">$&</span>');
}

function highlightLogs(content) {
    return content
        .replace(/^\d{4}-\d{2}-\d{2}[T\s]\d{2}:\d{2}:\d{2}[^\s]*/gm, '<span class="keyword">$&</span>')
        .replace(/\b(ERROR|FATAL|CRITICAL)\b/g, '<span style="color: #dc3545; font-weight: bold;">$1</span>')
        .replace(/\b(WARN|WARNING)\b/g, '<span style="color: #ffc107; font-weight: bold;">$1</span>')
        .replace(/\b(INFO|INFORMATION)\b/g, '<span style="color: #0dcaf0;">$1</span>')
        .replace(/\b(DEBUG|TRACE)\b/g, '<span style="color: #6c757d;">$1</span>');
}

// Search and filter functions
function performSearch(event) {
    if (event.key === 'Enter') {
        const query = document.getElementById('search-input').value.trim();
        if (query) {
            searchFiles(query);
        } else {
            clearSearch();
        }
    }
}

function searchFiles(query) {
    if (!currentJobId) {
        showAlert('Please select a job first', 'warning');
        return;
    }

    showTreeLoading(true);

    fetch(`/api/jobs/${currentJobId}/search?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(results => {
            searchResults = results;
            highlightSearchResults(query);
            showAlert(`Found ${results.length} matches for "${query}"`, 'success');
        })
        .catch(error => {
            console.error('Search error:', error);
            showAlert('Search failed', 'danger');
        })
        .finally(() => {
            showTreeLoading(false);
        });
}

function clearSearch() {
    document.getElementById('search-input').value = '';
    searchResults = [];

    // Remove search highlights
    document.querySelectorAll('.tree-item').forEach(item => {
        item.classList.remove('search-match');
    });

    // Clear content highlights
    const viewer = document.getElementById('content-viewer');
    const content = viewer.innerHTML;
    viewer.innerHTML = content.replace(/<mark class="search-highlight">(.*?)<\/mark>/g, '$1');
}

function highlightSearchResults(query) {
    // Highlight matching files in tree
    searchResults.forEach(result => {
        const treeItem = document.querySelector(`[data-path="${result.file}"]`);
        if (treeItem) {
            treeItem.classList.add('search-match');
        }
    });

    // If current file has matches, highlight content
    if (currentFilePath) {
        const currentFileResults = searchResults.filter(r => r.file === currentFilePath);
        if (currentFileResults.length > 0) {
            highlightContentMatches(query);
        }
    }
}

function highlightContentMatches(query) {
    const viewer = document.getElementById('content-viewer');
    const pre = viewer.querySelector('pre');
    if (pre) {
        const regex = new RegExp(`(${escapeRegex(query)})`, 'gi');
        pre.innerHTML = pre.innerHTML.replace(regex, '<mark class="search-highlight">$1</mark>');
    }
}

function escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function toggleFilter(chip) {
    const filter = chip.dataset.filter;

    if (activeFilters.has(filter)) {
        activeFilters.delete(filter);
        chip.classList.remove('active');
    } else {
        activeFilters.add(filter);
        chip.classList.add('active');
    }

    applyFilters();
}

function applyFilters() {
    const fileTypeFilter = document.getElementById('file-type-filter').value;

    document.querySelectorAll('.tree-item[data-type="file"]').forEach(item => {
        const filePath = item.dataset.path;
        const fileName = filePath.toLowerCase();

        let visible = true;

        // Apply file type filter
        if (fileTypeFilter) {
            const ext = filePath.split('.').pop().toLowerCase();
            visible = visible && ext === fileTypeFilter;
        }

        // Apply category filters
        if (activeFilters.size > 0) {
            const matchesFilter = Array.from(activeFilters).some(filter => {
                return fileName.includes(filter) || filePath.includes(`/${filter}/`);
            });
            visible = visible && matchesFilter;
        }

        item.style.display = visible ? 'block' : 'none';
    });
}

// Utility functions
function updateBreadcrumb(filePath) {
    const breadcrumb = document.getElementById('file-breadcrumb');
    const parts = filePath.split('/');

    let html = '';
    parts.forEach((part, index) => {
        if (index === parts.length - 1) {
            html += `<li class="breadcrumb-item active">${part}</li>`;
        } else {
            html += `<li class="breadcrumb-item">${part}</li>`;
        }
    });

    breadcrumb.innerHTML = html;
}

function showTreeLoading(show) {
    document.getElementById('loading-tree').style.display = show ? 'flex' : 'none';
}

function showContentLoading(show) {
    document.getElementById('loading-content').style.display = show ? 'flex' : 'none';
}

function clearFileTree() {
    document.getElementById('file-tree').innerHTML = `
        <div class="text-center text-muted p-4">
            <i class="fas fa-folder-open fa-3x mb-3"></i>
            <p>Select a job to view its file structure</p>
        </div>
    `;
}

function clearContentViewer() {
    document.getElementById('content-viewer').innerHTML = `
        <div class="text-center text-muted p-5">
            <i class="fas fa-file-alt fa-3x mb-3"></i>
            <p>Select a file from the tree to view its content</p>
            <small>Supports YAML, JSON, logs, and text files with syntax highlighting</small>
        </div>
    `;

    // Reset breadcrumb
    document.getElementById('file-breadcrumb').innerHTML = '<li class="breadcrumb-item text-muted">No file selected</li>';

    // Disable action buttons
    document.getElementById('download-btn').disabled = true;
    document.getElementById('copy-btn').disabled = true;
}

// Action functions
function downloadFile() {
    if (!currentJobId || !currentFilePath) return;

    const url = `/api/jobs/${currentJobId}/files/${encodeURIComponent(currentFilePath)}/download`;
    const link = document.createElement('a');
    link.href = url;
    link.download = currentFilePath.split('/').pop();
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function copyContent() {
    const viewer = document.getElementById('content-viewer');
    const pre = viewer.querySelector('pre');

    if (pre) {
        const text = pre.textContent || pre.innerText;
        navigator.clipboard.writeText(text).then(() => {
            showAlert('Content copied to clipboard', 'success');
        }).catch(() => {
            showAlert('Failed to copy content', 'danger');
        });
    }
}

function toggleWrap() {
    wrapEnabled = !wrapEnabled;
    const btn = document.getElementById('wrap-btn');
    const pre = document.querySelector('#content-viewer pre');

    if (pre) {
        pre.style.whiteSpace = wrapEnabled ? 'pre-wrap' : 'pre';
    }

    btn.classList.toggle('active', wrapEnabled);
    btn.innerHTML = wrapEnabled
        ? '<i class="fas fa-text-width me-1"></i>Unwrap'
        : '<i class="fas fa-text-width me-1"></i>Wrap';
}

function refreshDataTree() {
    if (currentJobId) {
        loadJobData();
    } else {
        loadAvailableJobs();
    }
}

function expandAll() {
    document.querySelectorAll('.tree-children').forEach(container => {
        container.style.display = 'block';
    });

    document.querySelectorAll('.tree-item.folder .tree-icon i').forEach(icon => {
        icon.className = 'fas fa-folder-open';
    });
}

function collapseAll() {
    document.querySelectorAll('.tree-children').forEach(container => {
        container.style.display = 'none';
    });

    document.querySelectorAll('.tree-item.folder .tree-icon i').forEach(icon => {
        icon.className = 'fas fa-folder';
    });
}

function sortTree(method) {
    // This would require rebuilding the tree with different sort order
    // Implementation depends on specific requirements
    showAlert(`Sorting by ${method} - feature coming soon`, 'info');
}

function displayError(message) {
    const viewer = document.getElementById('content-viewer');
    viewer.innerHTML = `
        <div class="text-center text-danger p-5">
            <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
            <p>${message}</p>
        </div>
    `;
}

function showAlert(message, type = 'info') {
    // Create alert element
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alert);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
</script>
{% endblock %}
