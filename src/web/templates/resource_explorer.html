{% extends "base.html" %}

{% block title %}Resource Explorer - OpenShift Must-Gather Analyzer{% endblock %}

{% block extra_css %}
<style>
    .resource-tree {
        max-height: 600px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        background-color: #f8f9fa;
    }
    
    .resource-node {
        padding: 0.5rem 1rem;
        border-bottom: 1px solid #e9ecef;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        align-items: center;
    }
    
    .resource-node:hover {
        background-color: #e9ecef;
    }
    
    .resource-node.selected {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
    }
    
    .resource-node.namespace {
        background-color: #e8f5e8;
        font-weight: 600;
        color: #2e7d32;
    }
    
    .resource-node.kind {
        background-color: #fff3e0;
        font-weight: 500;
        color: #f57c00;
        padding-left: 2rem;
    }
    
    .resource-node.resource {
        padding-left: 3rem;
        color: #424242;
    }
    
    .resource-icon {
        width: 20px;
        margin-right: 0.5rem;
        text-align: center;
    }
    
    .resource-status {
        margin-left: auto;
        font-size: 0.75rem;
        padding: 0.125rem 0.5rem;
        border-radius: 1rem;
    }
    
    .status-running { background-color: #d4edda; color: #155724; }
    .status-pending { background-color: #fff3cd; color: #856404; }
    .status-failed { background-color: #f8d7da; color: #721c24; }
    .status-succeeded { background-color: #d1ecf1; color: #0c5460; }
    .status-unknown { background-color: #e2e3e5; color: #383d41; }
    
    .resource-details {
        background-color: white;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        max-height: 600px;
        overflow-y: auto;
    }
    
    .resource-header {
        background-color: #f8f9fa;
        padding: 1rem;
        border-bottom: 1px solid #dee2e6;
        position: sticky;
        top: 0;
        z-index: 10;
    }
    
    .resource-content {
        padding: 1rem;
    }
    
    .yaml-viewer {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1rem;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 0.875rem;
        line-height: 1.4;
        white-space: pre-wrap;
        max-height: 400px;
        overflow-y: auto;
    }
    
    .resource-tabs {
        border-bottom: 1px solid #dee2e6;
        margin-bottom: 1rem;
    }
    
    .resource-tab {
        padding: 0.5rem 1rem;
        border: none;
        background: none;
        cursor: pointer;
        border-bottom: 2px solid transparent;
        transition: all 0.2s;
    }
    
    .resource-tab.active {
        border-bottom-color: #007bff;
        color: #007bff;
        font-weight: 500;
    }
    
    .resource-tab:hover {
        background-color: #f8f9fa;
    }
    
    .metadata-table {
        font-size: 0.875rem;
    }
    
    .metadata-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        width: 25%;
        border-right: 1px solid #dee2e6;
    }
    
    .condition-item {
        padding: 0.5rem;
        margin-bottom: 0.5rem;
        border-radius: 0.375rem;
        border-left: 4px solid #dee2e6;
    }
    
    .condition-true {
        background-color: #d4edda;
        border-left-color: #28a745;
    }
    
    .condition-false {
        background-color: #f8d7da;
        border-left-color: #dc3545;
    }
    
    .condition-unknown {
        background-color: #fff3cd;
        border-left-color: #ffc107;
    }
    
    .event-item {
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        border-radius: 0.375rem;
        border-left: 4px solid #dee2e6;
        background-color: #f8f9fa;
    }
    
    .event-normal {
        border-left-color: #28a745;
    }
    
    .event-warning {
        border-left-color: #ffc107;
        background-color: #fff3cd;
    }
    
    .event-error {
        border-left-color: #dc3545;
        background-color: #f8d7da;
    }
    
    .filter-section {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
    }
    
    .filter-chip {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        margin: 0.125rem;
        background-color: white;
        border: 1px solid #dee2e6;
        border-radius: 1rem;
        font-size: 0.75rem;
        cursor: pointer;
        transition: all 0.2s;
    }
    
    .filter-chip:hover {
        background-color: #e9ecef;
    }
    
    .filter-chip.active {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }
    
    .search-highlight {
        background-color: #ffeb3b;
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
    }
    
    .loading-spinner {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        color: #6c757d;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }
    
    .resource-count {
        font-size: 0.75rem;
        color: #6c757d;
        margin-left: 0.5rem;
    }
    
    .expand-toggle {
        width: 16px;
        height: 16px;
        margin-right: 0.5rem;
        cursor: pointer;
        transition: transform 0.2s;
    }
    
    .expand-toggle.expanded {
        transform: rotate(90deg);
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-cubes me-2"></i>
                Resource Explorer
            </h1>
            <div class="btn-group" role="group">
                <button class="btn btn-outline-primary" onclick="refreshResources()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
                <button class="btn btn-outline-secondary" onclick="expandAllNamespaces()">
                    <i class="fas fa-expand-arrows-alt me-1"></i>Expand All
                </button>
                <button class="btn btn-outline-secondary" onclick="collapseAllNamespaces()">
                    <i class="fas fa-compress-arrows-alt me-1"></i>Collapse All
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Job Selection -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-folder-open me-2"></i>
                    Analysis Job Selection
                </h6>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <select class="form-select" id="job-selector" onchange="loadJobResources()">
                            <option value="">Select a completed analysis job...</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <div id="job-info" class="text-muted">
                            <small>Select a job to explore its Kubernetes resources</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="filter-section">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="resource-search" 
                                       placeholder="Search resources by name, namespace, or kind..." 
                                       onkeyup="searchResources(event)">
                                <button class="btn btn-outline-secondary" onclick="clearResourceSearch()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <select class="form-select" id="namespace-filter" onchange="applyFilters()">
                                <option value="">All Namespaces</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <label class="form-label mb-2">Resource Types:</label>
                        <div id="resource-type-filters">
                            <!-- Filter chips will be populated dynamically -->
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <label class="form-label mb-2">Status Filters:</label>
                        <div>
                            <span class="filter-chip" data-status="running" onclick="toggleStatusFilter(this)">
                                <i class="fas fa-play me-1"></i>Running
                            </span>
                            <span class="filter-chip" data-status="pending" onclick="toggleStatusFilter(this)">
                                <i class="fas fa-clock me-1"></i>Pending
                            </span>
                            <span class="filter-chip" data-status="failed" onclick="toggleStatusFilter(this)">
                                <i class="fas fa-times me-1"></i>Failed
                            </span>
                            <span class="filter-chip" data-status="succeeded" onclick="toggleStatusFilter(this)">
                                <i class="fas fa-check me-1"></i>Succeeded
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <!-- Resource Tree -->
    <div class="col-lg-5 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-sitemap me-2"></i>
                    Resource Tree
                </h6>
                <div class="btn-group btn-group-sm" role="group">
                    <button class="btn btn-outline-secondary" onclick="sortResources('name')" title="Sort by name">
                        <i class="fas fa-sort-alpha-down"></i>
                    </button>
                    <button class="btn btn-outline-secondary" onclick="sortResources('status')" title="Sort by status">
                        <i class="fas fa-sort"></i>
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="resource-tree" class="resource-tree">
                    <div class="empty-state">
                        <i class="fas fa-cubes fa-3x mb-3"></i>
                        <p>Select a job to view Kubernetes resources</p>
                        <small>Resources will be organized by namespace and type</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Resource Details -->
    <div class="col-lg-7 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Resource Details
                </h6>
                <div class="btn-group btn-group-sm" role="group">
                    <button class="btn btn-outline-secondary" onclick="exportResource()" id="export-resource-btn" disabled>
                        <i class="fas fa-download me-1"></i>Export YAML
                    </button>
                    <button class="btn btn-outline-secondary" onclick="copyResourceYaml()" id="copy-resource-btn" disabled>
                        <i class="fas fa-copy me-1"></i>Copy
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="resource-details" class="resource-details">
                    <div class="empty-state">
                        <i class="fas fa-info-circle fa-3x mb-3"></i>
                        <p>Select a resource to view its details</p>
                        <small>View metadata, status, conditions, and events</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentJobId = null;
let allResources = [];
let filteredResources = [];
let selectedResource = null;
let activeResourceTypes = new Set();
let activeStatusFilters = new Set();
let searchQuery = '';
let expandedNamespaces = new Set();

// Initialize resource explorer
document.addEventListener('DOMContentLoaded', function() {
    loadAvailableJobs();
});

function loadAvailableJobs() {
    fetch('/api/jobs')
        .then(response => response.json())
        .then(data => {
            const selector = document.getElementById('job-selector');
            const completedJobs = Object.values(data).filter(job => job.status === 'completed');

            selector.innerHTML = '<option value="">Select a completed analysis job...</option>';

            completedJobs.forEach(job => {
                const option = document.createElement('option');
                option.value = job.job_id;
                option.textContent = `Job ${job.job_id.substring(0, 8)} - ${new Date(job.start_time).toLocaleString()}`;
                selector.appendChild(option);
            });

            if (completedJobs.length === 0) {
                selector.innerHTML = '<option value="">No completed jobs available</option>';
            }
        })
        .catch(error => {
            console.error('Error loading jobs:', error);
            showAlert('Error loading available jobs', 'danger');
        });
}

function loadJobResources() {
    const selector = document.getElementById('job-selector');
    const jobId = selector.value;

    if (!jobId) {
        currentJobId = null;
        clearResourceTree();
        clearResourceDetails();
        return;
    }

    currentJobId = jobId;
    showResourceLoading(true);

    // Update job info
    const jobInfo = document.getElementById('job-info');
    jobInfo.innerHTML = `<small class="text-primary">Loading resources for job ${jobId.substring(0, 8)}...</small>`;

    // Load Kubernetes resources for this job
    fetch(`/api/jobs/${jobId}/resources`)
        .then(response => response.json())
        .then(data => {
            allResources = data;
            filteredResources = [...allResources];

            populateNamespaceFilter();
            populateResourceTypeFilters();
            renderResourceTree();

            jobInfo.innerHTML = `<small class="text-success">Loaded ${allResources.length} resources</small>`;
        })
        .catch(error => {
            console.error('Error loading resources:', error);
            showAlert('Error loading resources', 'danger');
            jobInfo.innerHTML = `<small class="text-danger">Error loading resources</small>`;
        })
        .finally(() => {
            showResourceLoading(false);
        });
}

function populateNamespaceFilter() {
    const namespaces = [...new Set(allResources.map(r => r.namespace).filter(Boolean))].sort();
    const selector = document.getElementById('namespace-filter');

    selector.innerHTML = '<option value="">All Namespaces</option>';
    namespaces.forEach(ns => {
        const option = document.createElement('option');
        option.value = ns;
        option.textContent = ns;
        selector.appendChild(option);
    });
}

function populateResourceTypeFilters() {
    const kinds = [...new Set(allResources.map(r => r.kind))].sort();
    const container = document.getElementById('resource-type-filters');

    container.innerHTML = '';
    kinds.forEach(kind => {
        const chip = document.createElement('span');
        chip.className = 'filter-chip active';
        chip.dataset.kind = kind;
        chip.onclick = () => toggleResourceTypeFilter(chip);
        chip.innerHTML = `<i class="${getKindIcon(kind)} me-1"></i>${kind}`;
        container.appendChild(chip);

        activeResourceTypes.add(kind);
    });
}

function getKindIcon(kind) {
    const iconMap = {
        'Pod': 'fas fa-cube',
        'Service': 'fas fa-network-wired',
        'Deployment': 'fas fa-rocket',
        'ReplicaSet': 'fas fa-clone',
        'ConfigMap': 'fas fa-cog',
        'Secret': 'fas fa-key',
        'PersistentVolume': 'fas fa-hdd',
        'PersistentVolumeClaim': 'fas fa-database',
        'Node': 'fas fa-server',
        'Namespace': 'fas fa-folder',
        'Event': 'fas fa-calendar-alt',
        'Ingress': 'fas fa-globe',
        'ServiceAccount': 'fas fa-user-cog',
        'Role': 'fas fa-shield-alt',
        'RoleBinding': 'fas fa-link',
        'ClusterRole': 'fas fa-shield-alt',
        'ClusterRoleBinding': 'fas fa-link'
    };
    return iconMap[kind] || 'fas fa-cube';
}

function renderResourceTree() {
    const container = document.getElementById('resource-tree');

    if (filteredResources.length === 0) {
        container.innerHTML = '<div class="empty-state"><p>No resources match the current filters</p></div>';
        return;
    }

    // Group resources by namespace and kind
    const grouped = groupResourcesByNamespace(filteredResources);

    let html = '';
    Object.keys(grouped).sort().forEach(namespace => {
        const nsResources = grouped[namespace];
        const isExpanded = expandedNamespaces.has(namespace);
        const resourceCount = Object.values(nsResources).reduce((sum, resources) => sum + resources.length, 0);

        html += `
            <div class="resource-node namespace" onclick="toggleNamespace('${namespace}')">
                <i class="expand-toggle fas fa-chevron-right ${isExpanded ? 'expanded' : ''}" id="toggle-${namespace}"></i>
                <i class="resource-icon fas fa-folder"></i>
                <span>${namespace || 'cluster-scoped'}</span>
                <span class="resource-count">(${resourceCount})</span>
            </div>
        `;

        if (isExpanded) {
            Object.keys(nsResources).sort().forEach(kind => {
                const kindResources = nsResources[kind];
                html += `
                    <div class="resource-node kind">
                        <i class="resource-icon ${getKindIcon(kind)}"></i>
                        <span>${kind}</span>
                        <span class="resource-count">(${kindResources.length})</span>
                    </div>
                `;

                kindResources.forEach(resource => {
                    const status = getResourceStatus(resource);
                    const statusClass = getStatusClass(status);

                    html += `
                        <div class="resource-node resource" onclick="selectResource('${resource.id}')" data-resource-id="${resource.id}">
                            <i class="resource-icon ${getKindIcon(resource.kind)}"></i>
                            <span>${resource.name}</span>
                            <span class="resource-status ${statusClass}">${status}</span>
                        </div>
                    `;
                });
            });
        }
    });

    container.innerHTML = html;
}

function groupResourcesByNamespace(resources) {
    const grouped = {};

    resources.forEach(resource => {
        const namespace = resource.namespace || '';
        if (!grouped[namespace]) {
            grouped[namespace] = {};
        }

        if (!grouped[namespace][resource.kind]) {
            grouped[namespace][resource.kind] = [];
        }

        grouped[namespace][resource.kind].push(resource);
    });

    return grouped;
}

function getResourceStatus(resource) {
    if (resource.kind === 'Pod') {
        return resource.status?.phase || 'Unknown';
    } else if (resource.kind === 'Deployment') {
        const ready = resource.status?.readyReplicas || 0;
        const desired = resource.status?.replicas || 0;
        return ready === desired ? 'Ready' : 'Updating';
    } else if (resource.kind === 'Node') {
        const conditions = resource.status?.conditions || [];
        const readyCondition = conditions.find(c => c.type === 'Ready');
        return readyCondition?.status === 'True' ? 'Ready' : 'NotReady';
    }
    return 'Active';
}

function getStatusClass(status) {
    const statusMap = {
        'Running': 'status-running',
        'Ready': 'status-running',
        'Active': 'status-running',
        'Pending': 'status-pending',
        'Updating': 'status-pending',
        'Failed': 'status-failed',
        'Error': 'status-failed',
        'NotReady': 'status-failed',
        'Succeeded': 'status-succeeded',
        'Completed': 'status-succeeded'
    };
    return statusMap[status] || 'status-unknown';
}

function toggleNamespace(namespace) {
    if (expandedNamespaces.has(namespace)) {
        expandedNamespaces.delete(namespace);
    } else {
        expandedNamespaces.add(namespace);
    }
    renderResourceTree();
}

function selectResource(resourceId) {
    // Update selection in tree
    document.querySelectorAll('.resource-node').forEach(node => {
        node.classList.remove('selected');
    });

    const selectedNode = document.querySelector(`[data-resource-id="${resourceId}"]`);
    if (selectedNode) {
        selectedNode.classList.add('selected');
    }

    // Find and display resource details
    selectedResource = allResources.find(r => r.id === resourceId);
    if (selectedResource) {
        displayResourceDetails(selectedResource);

        // Enable action buttons
        document.getElementById('export-resource-btn').disabled = false;
        document.getElementById('copy-resource-btn').disabled = false;
    }
}

function displayResourceDetails(resource) {
    const container = document.getElementById('resource-details');

    const html = `
        <div class="resource-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">
                        <i class="${getKindIcon(resource.kind)} me-2"></i>
                        ${resource.kind}: ${resource.name}
                    </h6>
                    <small class="text-muted">
                        ${resource.namespace ? `Namespace: ${resource.namespace}` : 'Cluster-scoped'}
                    </small>
                </div>
                <span class="resource-status ${getStatusClass(getResourceStatus(resource))}">
                    ${getResourceStatus(resource)}
                </span>
            </div>
        </div>

        <div class="resource-content">
            <div class="resource-tabs">
                <button class="resource-tab active" onclick="showResourceTab('overview')">Overview</button>
                <button class="resource-tab" onclick="showResourceTab('yaml')">YAML</button>
                <button class="resource-tab" onclick="showResourceTab('events')">Events</button>
                <button class="resource-tab" onclick="showResourceTab('conditions')">Conditions</button>
            </div>

            <div id="tab-overview" class="tab-content">
                ${generateOverviewTab(resource)}
            </div>

            <div id="tab-yaml" class="tab-content" style="display: none;">
                <div class="yaml-viewer">${formatYaml(resource.yaml || 'YAML not available')}</div>
            </div>

            <div id="tab-events" class="tab-content" style="display: none;">
                ${generateEventsTab(resource)}
            </div>

            <div id="tab-conditions" class="tab-content" style="display: none;">
                ${generateConditionsTab(resource)}
            </div>
        </div>
    `;

    container.innerHTML = html;
}

function generateOverviewTab(resource) {
    const metadata = resource.metadata || {};
    const spec = resource.spec || {};
    const status = resource.status || {};

    return `
        <table class="table table-borderless metadata-table">
            <tbody>
                <tr>
                    <th>Name</th>
                    <td>${resource.name}</td>
                </tr>
                <tr>
                    <th>Namespace</th>
                    <td>${resource.namespace || 'cluster-scoped'}</td>
                </tr>
                <tr>
                    <th>Kind</th>
                    <td>${resource.kind}</td>
                </tr>
                <tr>
                    <th>API Version</th>
                    <td>${resource.apiVersion || 'N/A'}</td>
                </tr>
                <tr>
                    <th>Created</th>
                    <td>${metadata.creationTimestamp ? new Date(metadata.creationTimestamp).toLocaleString() : 'N/A'}</td>
                </tr>
                <tr>
                    <th>Labels</th>
                    <td>${formatLabels(metadata.labels)}</td>
                </tr>
                <tr>
                    <th>Annotations</th>
                    <td>${formatAnnotations(metadata.annotations)}</td>
                </tr>
                ${generateKindSpecificOverview(resource)}
            </tbody>
        </table>
    `;
}

function generateKindSpecificOverview(resource) {
    const status = resource.status || {};

    switch (resource.kind) {
        case 'Pod':
            return `
                <tr>
                    <th>Phase</th>
                    <td>${status.phase || 'Unknown'}</td>
                </tr>
                <tr>
                    <th>Node</th>
                    <td>${status.nodeName || 'Not assigned'}</td>
                </tr>
                <tr>
                    <th>Restart Count</th>
                    <td>${status.containerStatuses?.reduce((sum, c) => sum + (c.restartCount || 0), 0) || 0}</td>
                </tr>
            `;
        case 'Deployment':
            return `
                <tr>
                    <th>Replicas</th>
                    <td>${status.readyReplicas || 0} / ${status.replicas || 0}</td>
                </tr>
                <tr>
                    <th>Strategy</th>
                    <td>${resource.spec?.strategy?.type || 'RollingUpdate'}</td>
                </tr>
            `;
        case 'Service':
            return `
                <tr>
                    <th>Type</th>
                    <td>${resource.spec?.type || 'ClusterIP'}</td>
                </tr>
                <tr>
                    <th>Cluster IP</th>
                    <td>${resource.spec?.clusterIP || 'N/A'}</td>
                </tr>
            `;
        default:
            return '';
    }
}

function generateEventsTab(resource) {
    // This would need to be populated with actual events from the API
    return `
        <div class="text-center text-muted p-4">
            <i class="fas fa-calendar-alt fa-2x mb-2"></i>
            <p>Events functionality coming soon</p>
            <small>Will show related Kubernetes events</small>
        </div>
    `;
}

function generateConditionsTab(resource) {
    const conditions = resource.status?.conditions || [];

    if (conditions.length === 0) {
        return `
            <div class="text-center text-muted p-4">
                <i class="fas fa-info-circle fa-2x mb-2"></i>
                <p>No conditions available</p>
            </div>
        `;
    }

    let html = '';
    conditions.forEach(condition => {
        const conditionClass = condition.status === 'True' ? 'condition-true' :
                              condition.status === 'False' ? 'condition-false' : 'condition-unknown';

        html += `
            <div class="condition-item ${conditionClass}">
                <div class="d-flex justify-content-between align-items-center mb-1">
                    <strong>${condition.type}</strong>
                    <span class="badge bg-secondary">${condition.status}</span>
                </div>
                <div class="small text-muted mb-1">${condition.reason || 'No reason'}</div>
                <div class="small">${condition.message || 'No message'}</div>
                ${condition.lastTransitionTime ? `<div class="small text-muted mt-1">Last transition: ${new Date(condition.lastTransitionTime).toLocaleString()}</div>` : ''}
            </div>
        `;
    });

    return html;
}

function formatLabels(labels) {
    if (!labels || Object.keys(labels).length === 0) {
        return '<span class="text-muted">None</span>';
    }

    return Object.entries(labels).map(([key, value]) =>
        `<span class="badge bg-light text-dark me-1">${key}=${value}</span>`
    ).join('');
}

function formatAnnotations(annotations) {
    if (!annotations || Object.keys(annotations).length === 0) {
        return '<span class="text-muted">None</span>';
    }

    const count = Object.keys(annotations).length;
    return `<span class="text-muted">${count} annotation${count > 1 ? 's' : ''}</span>`;
}

function formatYaml(yamlContent) {
    // Basic YAML syntax highlighting
    return yamlContent
        .replace(/^(\s*)([\w-]+)(\s*:)/gm, '$1<span style="color: #800080; font-weight: bold;">$2</span>$3')
        .replace(/:\s*([^\n\r]+)/g, ': <span style="color: #008000;">$1</span>')
        .replace(/#[^\n\r]*/g, '<span style="color: #808080; font-style: italic;">$&</span>')
        .replace(/\b(true|false|null)\b/g, '<span style="color: #0000ff; font-weight: bold;">$1</span>')
        .replace(/\b\d+\b/g, '<span style="color: #ff6600;">$&</span>');
}

function showResourceTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.resource-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    event.target.classList.add('active');

    // Show/hide tab content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.style.display = 'none';
    });
    document.getElementById(`tab-${tabName}`).style.display = 'block';
}

// Filter and search functions
function searchResources(event) {
    if (event.key === 'Enter') {
        searchQuery = document.getElementById('resource-search').value.trim();
        applyFilters();
    }
}

function clearResourceSearch() {
    document.getElementById('resource-search').value = '';
    searchQuery = '';
    applyFilters();
}

function toggleResourceTypeFilter(chip) {
    const kind = chip.dataset.kind;

    if (activeResourceTypes.has(kind)) {
        activeResourceTypes.delete(kind);
        chip.classList.remove('active');
    } else {
        activeResourceTypes.add(kind);
        chip.classList.add('active');
    }

    applyFilters();
}

function toggleStatusFilter(chip) {
    const status = chip.dataset.status;

    if (activeStatusFilters.has(status)) {
        activeStatusFilters.delete(status);
        chip.classList.remove('active');
    } else {
        activeStatusFilters.add(status);
        chip.classList.add('active');
    }

    applyFilters();
}

function applyFilters() {
    const namespaceFilter = document.getElementById('namespace-filter').value;

    filteredResources = allResources.filter(resource => {
        // Namespace filter
        if (namespaceFilter && resource.namespace !== namespaceFilter) {
            return false;
        }

        // Resource type filter
        if (!activeResourceTypes.has(resource.kind)) {
            return false;
        }

        // Status filter
        if (activeStatusFilters.size > 0) {
            const status = getResourceStatus(resource).toLowerCase();
            const hasMatchingStatus = Array.from(activeStatusFilters).some(filter =>
                status.includes(filter) ||
                (filter === 'running' && ['running', 'ready', 'active'].includes(status)) ||
                (filter === 'failed' && ['failed', 'error', 'notready'].includes(status))
            );
            if (!hasMatchingStatus) {
                return false;
            }
        }

        // Search filter
        if (searchQuery) {
            const query = searchQuery.toLowerCase();
            const searchableText = `${resource.name} ${resource.namespace || ''} ${resource.kind}`.toLowerCase();
            if (!searchableText.includes(query)) {
                return false;
            }
        }

        return true;
    });

    renderResourceTree();
}

// Utility functions
function refreshResources() {
    if (currentJobId) {
        loadJobResources();
    }
}

function expandAllNamespaces() {
    const namespaces = [...new Set(filteredResources.map(r => r.namespace || ''))];
    namespaces.forEach(ns => expandedNamespaces.add(ns));
    renderResourceTree();
}

function collapseAllNamespaces() {
    expandedNamespaces.clear();
    renderResourceTree();
}

function sortResources(method) {
    // Implementation would depend on specific sorting requirements
    showAlert(`Sorting by ${method} - feature coming soon`, 'info');
}

function exportResource() {
    if (!selectedResource) return;

    const yamlContent = selectedResource.yaml || 'YAML not available';
    const filename = `${selectedResource.kind}-${selectedResource.name}.yaml`;

    downloadContent(yamlContent, filename, 'text/yaml');
}

function copyResourceYaml() {
    if (!selectedResource) return;

    const yamlContent = selectedResource.yaml || 'YAML not available';
    navigator.clipboard.writeText(yamlContent).then(() => {
        showAlert('Resource YAML copied to clipboard', 'success');
    }).catch(() => {
        showAlert('Failed to copy YAML', 'danger');
    });
}

function showResourceLoading(show) {
    const container = document.getElementById('resource-tree');
    if (show) {
        container.innerHTML = '<div class="loading-spinner"><i class="fas fa-spinner fa-spin fa-2x"></i><br>Loading resources...</div>';
    }
}

function clearResourceTree() {
    document.getElementById('resource-tree').innerHTML = `
        <div class="empty-state">
            <i class="fas fa-cubes fa-3x mb-3"></i>
            <p>Select a job to view Kubernetes resources</p>
            <small>Resources will be organized by namespace and type</small>
        </div>
    `;
}

function clearResourceDetails() {
    document.getElementById('resource-details').innerHTML = `
        <div class="empty-state">
            <i class="fas fa-info-circle fa-3x mb-3"></i>
            <p>Select a resource to view its details</p>
            <small>View metadata, status, conditions, and events</small>
        </div>
    `;

    // Disable action buttons
    document.getElementById('export-resource-btn').disabled = true;
    document.getElementById('copy-resource-btn').disabled = true;
}

function downloadContent(content, filename, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

function showAlert(message, type = 'info') {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alert);

    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
</script>
{% endblock %}
