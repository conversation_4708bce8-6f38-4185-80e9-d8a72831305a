"""
Flask web application for OpenShift Must-Gather Analyzer.
Provides web interface for uploading archives and viewing analysis results.
"""

import os
import uuid
import json
import threading
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for
from werkzeug.utils import secure_filename
from werkzeug.exceptions import RequestEntityTooLarge

try:
    from flask_socketio import SocketIO
    SOCKETIO_AVAILABLE = True
except ImportError:
    SOCKETIO_AVAILABLE = False
    SocketIO = None

from main import MustGatherAnalyzer
from core.models import AnalysisProgress, AnalysisStatus

try:
    import yaml
except ImportError:
    # If PyYAML is not installed, provide a fallback
    class MockYAML:
        @staticmethod
        def safe_load_all(content):
            return []
        @staticmethod
        def dump(data, **kwargs):
            return str(data)
    yaml = MockYAML()


class WebAnalyzer:
    """Web interface wrapper for MustGatherAnalyzer with progress tracking."""
    
    def __init__(self, upload_folder: str = "uploads", output_folder: str = "reports", websocket_manager=None):
        self.upload_folder = Path(upload_folder)
        self.output_folder = Path(output_folder)
        self.upload_folder.mkdir(exist_ok=True)
        self.output_folder.mkdir(exist_ok=True)

        # Track analysis jobs
        self.jobs: Dict[str, AnalysisProgress] = {}
        self.job_lock = threading.Lock()

        # WebSocket manager for real-time updates
        self.websocket_manager = websocket_manager
    
    def start_analysis(self, job_id: str, must_gather_path: str, 
                      config_path: Optional[str] = None) -> None:
        """
        Start analysis in background thread with progress tracking.
        
        Args:
            job_id: Unique job identifier
            must_gather_path: Path to must-gather data
            config_path: Optional configuration file path
        """
        def run_analysis():
            try:
                with self.job_lock:
                    self.jobs[job_id].status = AnalysisStatus.RUNNING
                    self.jobs[job_id].current_step = "Initializing analyzer"
                
                analyzer = MustGatherAnalyzer(config_path)
                
                # Create job-specific output directory
                job_output_path = self.output_folder / job_id
                job_output_path.mkdir(exist_ok=True)
                
                with self.job_lock:
                    self.jobs[job_id].current_step = "Parsing must-gather data"
                    self.jobs[job_id].progress = 10
                
                # Run analysis with progress updates
                success = analyzer.analyze(
                    must_gather_path=must_gather_path,
                    output_path=str(job_output_path)
                )
                
                with self.job_lock:
                    if success:
                        self.jobs[job_id].status = AnalysisStatus.COMPLETED
                        self.jobs[job_id].progress = 100
                        self.jobs[job_id].current_step = "Analysis completed"
                        self.jobs[job_id].output_path = str(job_output_path)
                    else:
                        self.jobs[job_id].status = AnalysisStatus.FAILED
                        self.jobs[job_id].error_message = "Analysis failed"
                    
                    self.jobs[job_id].end_time = datetime.now()
                
            except Exception as e:
                with self.job_lock:
                    self.jobs[job_id].status = AnalysisStatus.FAILED
                    self.jobs[job_id].error_message = str(e)
                    self.jobs[job_id].end_time = datetime.now()
        
        # Initialize job tracking
        with self.job_lock:
            self.jobs[job_id] = AnalysisProgress(
                job_id=job_id,
                status=AnalysisStatus.QUEUED,
                start_time=datetime.now(),
                progress=0,
                current_step="Queued for analysis"
            )
        
        # Start analysis in background thread
        thread = threading.Thread(target=run_analysis)
        thread.daemon = True
        thread.start()
    
    def get_job_status(self, job_id: str) -> Optional[AnalysisProgress]:
        """Get current status of analysis job."""
        with self.job_lock:
            return self.jobs.get(job_id)
    
    def get_all_jobs(self) -> Dict[str, AnalysisProgress]:
        """Get status of all analysis jobs."""
        with self.job_lock:
            return dict(self.jobs)


def create_app(config: Optional[Dict[str, Any]] = None) -> Flask:
    """
    Create and configure Flask application.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        Configured Flask application
    """
    app = Flask(__name__)
    
    # Configuration
    app.config.update({
        'SECRET_KEY': os.environ.get('SECRET_KEY', 'dev-secret-key'),
        'MAX_CONTENT_LENGTH': 2 * 1024 * 1024 * 1024,  # 2GB max file size
        'UPLOAD_FOLDER': os.environ.get('UPLOAD_FOLDER', 'uploads'),
        'OUTPUT_FOLDER': os.environ.get('OUTPUT_FOLDER', 'reports'),
    })
    
    if config:
        app.config.update(config)
    
    # Initialize web analyzer
    web_analyzer = WebAnalyzer(
        upload_folder=app.config['UPLOAD_FOLDER'],
        output_folder=app.config['OUTPUT_FOLDER']
    )
    
    @app.route('/')
    def index():
        """Main dashboard page."""
        return render_template('index.html')
    
    @app.route('/upload', methods=['GET', 'POST'])
    def upload_file():
        """Handle must-gather file upload."""
        if request.method == 'GET':
            return render_template('upload.html')
        
        try:
            if 'file' not in request.files:
                return jsonify({'error': 'No file provided'}), 400
            
            file = request.files['file']
            if file.filename == '':
                return jsonify({'error': 'No file selected'}), 400
            
            if file:
                # Generate unique job ID
                job_id = str(uuid.uuid4())
                
                # Save uploaded file
                filename = secure_filename(file.filename)
                file_path = web_analyzer.upload_folder / f"{job_id}_{filename}"
                file.save(str(file_path))
                
                # Start analysis
                web_analyzer.start_analysis(job_id, str(file_path))
                
                return jsonify({
                    'job_id': job_id,
                    'message': 'Analysis started',
                    'status_url': url_for('job_status', job_id=job_id)
                })
        
        except RequestEntityTooLarge:
            return jsonify({'error': 'File too large'}), 413
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    @app.route('/jobs')
    def list_jobs():
        """List all analysis jobs."""
        jobs = web_analyzer.get_all_jobs()
        return render_template('jobs.html', jobs=jobs)
    
    @app.route('/jobs/<job_id>')
    def job_details(job_id: str):
        """Show detailed job information."""
        job = web_analyzer.get_job_status(job_id)
        if not job:
            return "Job not found", 404
        
        return render_template('job_details.html', job=job)
    
    @app.route('/api/jobs/<job_id>/status')
    def job_status(job_id: str):
        """Get job status via API."""
        job = web_analyzer.get_job_status(job_id)
        if not job:
            return jsonify({'error': 'Job not found'}), 404
        
        return jsonify({
            'job_id': job.job_id,
            'status': job.status.value,
            'progress': job.progress,
            'current_step': job.current_step,
            'start_time': job.start_time.isoformat() if job.start_time else None,
            'end_time': job.end_time.isoformat() if job.end_time else None,
            'error_message': job.error_message,
            'output_path': job.output_path
        })
    
    @app.route('/api/jobs/<job_id>/progress')
    def job_progress_stream(job_id: str):
        """Get real-time job progress updates via Server-Sent Events."""
        def generate():
            while True:
                job = web_analyzer.get_job_status(job_id)
                if not job:
                    yield f"data: {json.dumps({'error': 'Job not found'})}\n\n"
                    break
                
                data = {
                    'job_id': job.job_id,
                    'status': job.status.value,
                    'progress': job.progress,
                    'current_step': job.current_step,
                    'timestamp': datetime.now().isoformat()
                }
                
                yield f"data: {json.dumps(data)}\n\n"
                
                # Stop streaming if job is completed or failed
                if job.status in [AnalysisStatus.COMPLETED, AnalysisStatus.FAILED, AnalysisStatus.CANCELLED]:
                    break
                
                # Wait 2 seconds before next update
                import time
                time.sleep(2)
        
        return app.response_class(
            generate(),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*'
            }
        )
    
    @app.route('/api/system/health')
    def system_health():
        """Get system health information."""
        jobs = web_analyzer.get_all_jobs()
        total_jobs = len(jobs)
        running_jobs = len([j for j in jobs.values() if j.status == AnalysisStatus.RUNNING])
        
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'jobs': {
                'total': total_jobs,
                'running': running_jobs,
                'queue_size': len([j for j in jobs.values() if j.status == AnalysisStatus.QUEUED])
            },
            'system': {
                'uptime': 'Available',  # Could be calculated from app start time
                'version': '1.0.0',
                'max_file_size': '2GB'
            }
        })
    
    @app.route('/api/jobs')
    def api_list_jobs():
        """List all jobs via API."""
        jobs = web_analyzer.get_all_jobs()
        return jsonify({
            job_id: {
                'job_id': job.job_id,
                'status': job.status.value,
                'progress': job.progress,
                'current_step': job.current_step,
                'start_time': job.start_time.isoformat() if job.start_time else None,
                'end_time': job.end_time.isoformat() if job.end_time else None,
                'error_message': job.error_message
            }
            for job_id, job in jobs.items()
        })
    
    @app.route('/reports/<job_id>')
    def view_report(job_id: str):
        """View analysis report for a job."""
        job = web_analyzer.get_job_status(job_id)
        if not job or job.status != AnalysisStatus.COMPLETED:
            return "Report not available", 404
        
        # Look for HTML report
        html_report_path = Path(job.output_path) / "report.html"
        if html_report_path.exists():
            return send_file(str(html_report_path))
        
        return "Report not found", 404
    
    @app.route('/reports/<job_id>/download')
    def download_report(job_id: str):
        """Download PDF report for a job."""
        job = web_analyzer.get_job_status(job_id)
        if not job or job.status != AnalysisStatus.COMPLETED:
            return "Report not available", 404
        
        # Look for PDF report
        pdf_report_path = Path(job.output_path) / "report.pdf"
        if pdf_report_path.exists():
            return send_file(
                str(pdf_report_path),
                as_attachment=True,
                download_name=f"must-gather-analysis-{job_id}.pdf"
            )
        
        return "PDF report not found", 404
    
    @app.errorhandler(413)
    def too_large(e):
        """Handle file too large error."""
        return jsonify({'error': 'File too large. Maximum size is 2GB.'}), 413
    
    @app.route('/data-browser')
    def data_browser():
        """Data browser interface."""
        return render_template('data_browser.html')

    @app.route('/log-viewer')
    def log_viewer():
        """Log viewer interface."""
        return render_template('log_viewer.html')

    @app.route('/resource-explorer')
    def resource_explorer():
        """Resource explorer interface."""
        return render_template('resource_explorer.html')

    @app.route('/search')
    def search_interface():
        """Advanced search interface."""
        return render_template('search.html')

    @app.route('/interactive-report')
    def interactive_report():
        """Interactive report interface."""
        return render_template('interactive_report.html')

    @app.route('/api/jobs/<job_id>/files')
    def get_job_files(job_id: str):
        """Get file structure for a job."""
        job = web_analyzer.get_job_status(job_id)
        if not job or job.status != AnalysisStatus.COMPLETED:
            return jsonify({'error': 'Job not found or not completed'}), 404

        try:
            # Get must-gather path from job
            must_gather_path = job.must_gather_path or job.output_path
            if not must_gather_path:
                return jsonify({'error': 'No data path available'}), 404

            files = {}
            must_gather_root = Path(must_gather_path)

            if must_gather_root.exists():
                for file_path in must_gather_root.rglob('*'):
                    if file_path.is_file():
                        relative_path = str(file_path.relative_to(must_gather_root))
                        files[relative_path] = {
                            'size': file_path.stat().st_size,
                            'modified': file_path.stat().st_mtime,
                            'type': _get_file_type(file_path.name)
                        }

            return jsonify(files)

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/jobs/<job_id>/files/<path:file_path>')
    def get_file_content(job_id: str, file_path: str):
        """Get content of a specific file."""
        job = web_analyzer.get_job_status(job_id)
        if not job or job.status != AnalysisStatus.COMPLETED:
            return jsonify({'error': 'Job not found or not completed'}), 404

        try:
            must_gather_path = job.must_gather_path or job.output_path
            if not must_gather_path:
                return jsonify({'error': 'No data path available'}), 404

            full_path = Path(must_gather_path) / file_path

            if not full_path.exists() or not full_path.is_file():
                return jsonify({'error': 'File not found'}), 404

            # Check if file is too large (>10MB)
            if full_path.stat().st_size > 10 * 1024 * 1024:
                return jsonify({'error': 'File too large to display'}), 413

            # Try to read as text
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return content, 200, {'Content-Type': 'text/plain; charset=utf-8'}
            except UnicodeDecodeError:
                # If not text, return error
                return jsonify({'error': 'Binary file cannot be displayed'}), 415

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/jobs/<job_id>/files/<path:file_path>/download')
    def download_file(job_id: str, file_path: str):
        """Download a specific file."""
        job = web_analyzer.get_job_status(job_id)
        if not job or job.status != AnalysisStatus.COMPLETED:
            return jsonify({'error': 'Job not found or not completed'}), 404

        try:
            must_gather_path = job.must_gather_path or job.output_path
            if not must_gather_path:
                return jsonify({'error': 'No data path available'}), 404

            full_path = Path(must_gather_path) / file_path

            if not full_path.exists() or not full_path.is_file():
                return jsonify({'error': 'File not found'}), 404

            return send_file(
                str(full_path),
                as_attachment=True,
                download_name=full_path.name
            )

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/jobs/<job_id>/search')
    def search_job_files(job_id: str):
        """Search within job files."""
        query = request.args.get('q', '').strip()
        if not query:
            return jsonify({'error': 'Query parameter required'}), 400

        job = web_analyzer.get_job_status(job_id)
        if not job or job.status != AnalysisStatus.COMPLETED:
            return jsonify({'error': 'Job not found or not completed'}), 404

        try:
            must_gather_path = job.must_gather_path or job.output_path
            if not must_gather_path:
                return jsonify({'error': 'No data path available'}), 404

            results = []
            must_gather_root = Path(must_gather_path)

            if must_gather_root.exists():
                for file_path in must_gather_root.rglob('*'):
                    if file_path.is_file():
                        relative_path = str(file_path.relative_to(must_gather_root))

                        # Search in filename
                        if query.lower() in relative_path.lower():
                            results.append({
                                'file': relative_path,
                                'type': 'filename',
                                'line': 0,
                                'context': relative_path
                            })

                        # Search in file content (for text files only)
                        if _is_text_file(file_path) and file_path.stat().st_size < 1024 * 1024:  # 1MB limit
                            try:
                                with open(file_path, 'r', encoding='utf-8') as f:
                                    for line_num, line in enumerate(f, 1):
                                        if query.lower() in line.lower():
                                            results.append({
                                                'file': relative_path,
                                                'type': 'content',
                                                'line': line_num,
                                                'context': line.strip()[:100]
                                            })

                                            # Limit results per file
                                            if len([r for r in results if r['file'] == relative_path and r['type'] == 'content']) >= 10:
                                                break
                            except (UnicodeDecodeError, PermissionError):
                                continue

            return jsonify(results[:100])  # Limit total results

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/jobs/<job_id>/resources')
    def get_job_resources(job_id: str):
        """Get Kubernetes resources for a job."""
        job = web_analyzer.get_job_status(job_id)
        if not job or job.status != AnalysisStatus.COMPLETED:
            return jsonify({'error': 'Job not found or not completed'}), 404

        try:
            must_gather_path = job.must_gather_path or job.output_path
            if not must_gather_path:
                return jsonify({'error': 'No data path available'}), 404

            resources = []
            must_gather_root = Path(must_gather_path)

            if must_gather_root.exists():
                # Look for YAML files that contain Kubernetes resources
                for file_path in must_gather_root.rglob('*.yaml'):
                    if file_path.is_file():
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()

                            # Parse YAML documents
                            import yaml
                            documents = list(yaml.safe_load_all(content))

                            for doc in documents:
                                if doc and isinstance(doc, dict) and 'kind' in doc:
                                    resource = {
                                        'id': f"{job_id}_{len(resources)}",
                                        'name': doc.get('metadata', {}).get('name', 'unknown'),
                                        'namespace': doc.get('metadata', {}).get('namespace'),
                                        'kind': doc.get('kind'),
                                        'apiVersion': doc.get('apiVersion'),
                                        'metadata': doc.get('metadata', {}),
                                        'spec': doc.get('spec', {}),
                                        'status': doc.get('status', {}),
                                        'yaml': yaml.dump(doc, default_flow_style=False),
                                        'filePath': str(file_path.relative_to(must_gather_root))
                                    }
                                    resources.append(resource)

                        except (yaml.YAMLError, UnicodeDecodeError, PermissionError):
                            continue

            return jsonify(resources)

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/search', methods=['POST'])
    def advanced_search():
        """Perform advanced search across must-gather data."""
        try:
            data = request.get_json()
            query = data.get('query', '').strip()

            if not query:
                return jsonify({'error': 'Query is required'}), 400

            job_id = data.get('jobId')
            scope = data.get('scope', 'all')
            case_sensitive = data.get('caseSensitive', False)
            regex_mode = data.get('regexMode', False)
            whole_words = data.get('wholeWords', False)
            namespace_filter = data.get('namespaceFilter')
            date_from = data.get('dateFrom')
            date_to = data.get('dateTo')

            results = []
            files_searched = 0

            # Determine which jobs to search
            jobs_to_search = []
            if job_id:
                job = web_analyzer.get_job_status(job_id)
                if job and job.status == AnalysisStatus.COMPLETED:
                    jobs_to_search.append(job)
            else:
                # Search all completed jobs
                all_jobs = web_analyzer.get_all_jobs()
                jobs_to_search = [job for job in all_jobs.values()
                                if job.status == AnalysisStatus.COMPLETED]

            for job in jobs_to_search:
                must_gather_path = job.must_gather_path or job.output_path
                if not must_gather_path:
                    continue

                must_gather_root = Path(must_gather_path)
                if not must_gather_root.exists():
                    continue

                # Search based on scope
                if scope in ['all', 'logs']:
                    results.extend(_search_logs(must_gather_root, query, job.job_id,
                                               case_sensitive, regex_mode, whole_words))

                if scope in ['all', 'configs']:
                    results.extend(_search_configs(must_gather_root, query, job.job_id,
                                                 case_sensitive, regex_mode, whole_words))

                if scope in ['all', 'resources']:
                    results.extend(_search_resources(must_gather_root, query, job.job_id,
                                                   case_sensitive, regex_mode, whole_words))

                if scope in ['all', 'events']:
                    results.extend(_search_events(must_gather_root, query, job.job_id,
                                                case_sensitive, regex_mode, whole_words))

                files_searched += len(list(must_gather_root.rglob('*')))

            # Apply additional filters
            if namespace_filter:
                results = [r for r in results if r.get('namespace') == namespace_filter]

            if date_from or date_to:
                filtered_results = []
                for result in results:
                    result_date = result.get('timestamp')
                    if result_date:
                        result_dt = datetime.fromisoformat(result_date.replace('Z', '+00:00'))
                        if date_from and result_dt < datetime.fromisoformat(date_from):
                            continue
                        if date_to and result_dt > datetime.fromisoformat(date_to):
                            continue
                    filtered_results.append(result)
                results = filtered_results

            return jsonify({
                'results': results[:1000],  # Limit results
                'filesSearched': files_searched,
                'totalResults': len(results)
            })

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/jobs/<job_id>/report')
    def get_interactive_report(job_id: str):
        """Get interactive report data for a job."""
        job = web_analyzer.get_job_status(job_id)
        if not job or job.status != AnalysisStatus.COMPLETED:
            return jsonify({'error': 'Job not found or not completed'}), 404

        try:
            # Generate comprehensive report data
            report_data = {
                'jobId': job_id,
                'timestamp': job.end_time.isoformat() if job.end_time else datetime.now().isoformat(),
                'clusterName': _extract_cluster_name(job),
                'totalResources': _count_total_resources(job),
                'metrics': _generate_health_metrics(job),
                'findings': _generate_findings(job),
                'recommendations': _generate_recommendations(job),
                'timeline': _generate_timeline(job),
                'resourceMetrics': _generate_resource_metrics(job)
            }

            return jsonify(report_data)

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.errorhandler(500)
    def internal_error(e):
        """Handle internal server errors."""
        return jsonify({'error': 'Internal server error'}), 500

    return app


def _get_file_type(filename: str) -> str:
    """Determine file type based on extension."""
    ext = filename.split('.')[-1].lower() if '.' in filename else ''

    type_map = {
        'yaml': 'yaml',
        'yml': 'yaml',
        'json': 'json',
        'log': 'log',
        'txt': 'text',
        'conf': 'config',
        'xml': 'xml',
        'html': 'html',
        'css': 'css',
        'js': 'javascript',
        'py': 'python',
        'sh': 'shell',
        'md': 'markdown'
    }

    return type_map.get(ext, 'unknown')


def _is_text_file(file_path: Path) -> bool:
    """Check if file is likely a text file."""
    text_extensions = {
        'yaml', 'yml', 'json', 'log', 'txt', 'conf', 'xml', 'html',
        'css', 'js', 'py', 'sh', 'md', 'cfg', 'ini', 'properties'
    }

    ext = file_path.suffix.lower().lstrip('.')
    return ext in text_extensions


def _search_logs(must_gather_root: Path, query: str, job_id: str,
                case_sensitive: bool, regex_mode: bool, whole_words: bool) -> List[Dict]:
    """Search through log files."""
    results = []

    # Find log files
    log_patterns = ['*.log', '**/logs/**/*', '**/log/**/*']
    log_files = set()

    for pattern in log_patterns:
        log_files.update(must_gather_root.glob(pattern))

    for log_file in log_files:
        if not log_file.is_file() or not _is_text_file(log_file):
            continue

        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if _matches_query(line, query, case_sensitive, regex_mode, whole_words):
                        results.append({
                            'id': f"{job_id}_{log_file.name}_{line_num}",
                            'jobId': job_id,
                            'type': 'log',
                            'filename': log_file.name,
                            'path': str(log_file.relative_to(must_gather_root)),
                            'lineNumber': line_num,
                            'context': line.strip()[:200],
                            'matches': _extract_matches(line, query, case_sensitive, regex_mode),
                            'timestamp': _extract_timestamp(line)
                        })

                        # Limit results per file
                        if len([r for r in results if r['path'] == str(log_file.relative_to(must_gather_root))]) >= 50:
                            break

        except (UnicodeDecodeError, PermissionError):
            continue

    return results


def _search_configs(must_gather_root: Path, query: str, job_id: str,
                   case_sensitive: bool, regex_mode: bool, whole_words: bool) -> List[Dict]:
    """Search through configuration files."""
    results = []

    # Find config files
    config_patterns = ['*.yaml', '*.yml', '*.json', '*.conf', '*.cfg', '*.ini']
    config_files = set()

    for pattern in config_patterns:
        config_files.update(must_gather_root.rglob(pattern))

    for config_file in config_files:
        if not config_file.is_file():
            continue

        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()

            if _matches_query(content, query, case_sensitive, regex_mode, whole_words):
                # Find specific lines that match
                lines = content.split('\n')
                for line_num, line in enumerate(lines, 1):
                    if _matches_query(line, query, case_sensitive, regex_mode, whole_words):
                        results.append({
                            'id': f"{job_id}_{config_file.name}_{line_num}",
                            'jobId': job_id,
                            'type': 'config',
                            'filename': config_file.name,
                            'path': str(config_file.relative_to(must_gather_root)),
                            'lineNumber': line_num,
                            'context': line.strip()[:200],
                            'matches': _extract_matches(line, query, case_sensitive, regex_mode),
                            'namespace': _extract_namespace_from_path(str(config_file))
                        })

        except (UnicodeDecodeError, PermissionError):
            continue

    return results


def _search_resources(must_gather_root: Path, query: str, job_id: str,
                     case_sensitive: bool, regex_mode: bool, whole_words: bool) -> List[Dict]:
    """Search through Kubernetes resources."""
    results = []

    # Find YAML files that likely contain Kubernetes resources
    for yaml_file in must_gather_root.rglob('*.yaml'):
        if not yaml_file.is_file():
            continue

        try:
            with open(yaml_file, 'r', encoding='utf-8') as f:
                content = f.read()

            import yaml
            documents = list(yaml.safe_load_all(content))

            for doc_idx, doc in enumerate(documents):
                if not doc or not isinstance(doc, dict) or 'kind' not in doc:
                    continue

                # Convert document back to YAML for searching
                doc_yaml = yaml.dump(doc, default_flow_style=False)

                if _matches_query(doc_yaml, query, case_sensitive, regex_mode, whole_words):
                    resource_name = doc.get('metadata', {}).get('name', 'unknown')
                    resource_kind = doc.get('kind', 'unknown')

                    results.append({
                        'id': f"{job_id}_{yaml_file.name}_{doc_idx}",
                        'jobId': job_id,
                        'type': 'resource',
                        'filename': f"{resource_kind}/{resource_name}",
                        'path': str(yaml_file.relative_to(must_gather_root)),
                        'context': f"{resource_kind}: {resource_name}",
                        'matches': _extract_matches(doc_yaml, query, case_sensitive, regex_mode),
                        'namespace': doc.get('metadata', {}).get('namespace'),
                        'resourceId': f"{resource_kind}_{resource_name}",
                        'kind': resource_kind
                    })

        except (yaml.YAMLError, UnicodeDecodeError, PermissionError):
            continue

    return results


def _search_events(must_gather_root: Path, query: str, job_id: str,
                  case_sensitive: bool, regex_mode: bool, whole_words: bool) -> List[Dict]:
    """Search through Kubernetes events."""
    results = []

    # Look for event files (usually in events directories or event YAML files)
    event_files = []
    event_files.extend(must_gather_root.rglob('**/events/**/*.yaml'))
    event_files.extend(must_gather_root.rglob('**/events.yaml'))

    for event_file in event_files:
        if not event_file.is_file():
            continue

        try:
            with open(event_file, 'r', encoding='utf-8') as f:
                content = f.read()

            if _matches_query(content, query, case_sensitive, regex_mode, whole_words):
                import yaml
                documents = list(yaml.safe_load_all(content))

                for doc_idx, doc in enumerate(documents):
                    if not doc or not isinstance(doc, dict):
                        continue

                    if doc.get('kind') == 'Event' or 'message' in doc:
                        event_message = doc.get('message', str(doc))

                        if _matches_query(event_message, query, case_sensitive, regex_mode, whole_words):
                            results.append({
                                'id': f"{job_id}_{event_file.name}_{doc_idx}",
                                'jobId': job_id,
                                'type': 'event',
                                'filename': event_file.name,
                                'path': str(event_file.relative_to(must_gather_root)),
                                'context': event_message[:200],
                                'matches': _extract_matches(event_message, query, case_sensitive, regex_mode),
                                'namespace': doc.get('namespace'),
                                'timestamp': doc.get('firstTimestamp') or doc.get('lastTimestamp')
                            })

        except (yaml.YAMLError, UnicodeDecodeError, PermissionError):
            continue

    return results


def _matches_query(text: str, query: str, case_sensitive: bool,
                  regex_mode: bool, whole_words: bool) -> bool:
    """Check if text matches the search query."""
    import re

    if not text or not query:
        return False

    search_text = text if case_sensitive else text.lower()
    search_query = query if case_sensitive else query.lower()

    if regex_mode:
        try:
            flags = 0 if case_sensitive else re.IGNORECASE
            return bool(re.search(search_query, search_text, flags))
        except re.error:
            # If regex is invalid, fall back to literal search
            return search_query in search_text

    if whole_words:
        import re
        pattern = r'\b' + re.escape(search_query) + r'\b'
        flags = 0 if case_sensitive else re.IGNORECASE
        return bool(re.search(pattern, search_text, flags))

    return search_query in search_text


def _extract_matches(text: str, query: str, case_sensitive: bool, regex_mode: bool) -> List[str]:
    """Extract matching terms from text."""
    import re

    matches = []

    if not text or not query:
        return matches

    search_text = text if case_sensitive else text.lower()
    search_query = query if case_sensitive else query.lower()

    if regex_mode:
        try:
            flags = 0 if case_sensitive else re.IGNORECASE
            found_matches = re.findall(search_query, search_text, flags)
            matches.extend(found_matches[:10])  # Limit matches
        except re.error:
            if search_query in search_text:
                matches.append(search_query)
    else:
        if search_query in search_text:
            matches.append(search_query)

    return matches


def _extract_timestamp(line: str) -> Optional[str]:
    """Extract timestamp from log line."""
    import re

    # Common timestamp patterns
    patterns = [
        r'\d{4}-\d{2}-\d{2}[T\s]\d{2}:\d{2}:\d{2}[^\s]*',
        r'\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2}:\d{2}',
        r'\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2}'
    ]

    for pattern in patterns:
        match = re.search(pattern, line)
        if match:
            return match.group(0)

    return None


def _extract_namespace_from_path(file_path: str) -> Optional[str]:
    """Extract namespace from file path."""
    import re

    # Look for namespace patterns in path
    namespace_match = re.search(r'/namespaces/([^/]+)/', file_path)
    if namespace_match:
        return namespace_match.group(1)

    # Look for namespace in filename
    namespace_match = re.search(r'namespace[_-]([^_\-\.]+)', file_path)
    if namespace_match:
        return namespace_match.group(1)

    return None


def _extract_cluster_name(job) -> str:
    """Extract cluster name from job data."""
    # Try to find cluster name from must-gather data
    must_gather_path = job.must_gather_path or job.output_path
    if must_gather_path:
        cluster_info_path = Path(must_gather_path) / 'cluster-scoped-resources' / 'config.openshift.io' / 'infrastructures.yaml'
        if cluster_info_path.exists():
            try:
                with open(cluster_info_path, 'r') as f:
                    content = f.read()
                    import yaml
                    docs = list(yaml.safe_load_all(content))
                    for doc in docs:
                        if doc and doc.get('kind') == 'Infrastructure':
                            return doc.get('metadata', {}).get('name', 'Unknown Cluster')
            except:
                pass

    return f"Cluster-{job.job_id[:8]}"


def _count_total_resources(job) -> int:
    """Count total resources analyzed in the job."""
    must_gather_path = job.must_gather_path or job.output_path
    if not must_gather_path:
        return 0

    count = 0
    must_gather_root = Path(must_gather_path)
    if must_gather_root.exists():
        for yaml_file in must_gather_root.rglob('*.yaml'):
            if yaml_file.is_file():
                try:
                    with open(yaml_file, 'r') as f:
                        content = f.read()
                        import yaml
                        docs = list(yaml.safe_load_all(content))
                        count += len([doc for doc in docs if doc and isinstance(doc, dict) and 'kind' in doc])
                except:
                    continue

    return count


def _generate_health_metrics(job) -> Dict[str, Any]:
    """Generate health metrics for the cluster."""
    # This would be enhanced with actual analysis results
    return {
        'healthScore': 75,  # Overall health score
        'healthChange': 5,  # Change from previous analysis
        'nodeHealth': 85,
        'podHealth': 72,
        'serviceHealth': 95,
        'storageHealth': 60,
        'issuesChange': -10,
        'resourcesChange': 15,
        'recommendationsChange': 3
    }


def _generate_findings(job) -> List[Dict[str, Any]]:
    """Generate findings from the analysis."""
    # This would be enhanced with actual analysis results
    sample_findings = [
        {
            'id': f'finding-{job.job_id}-1',
            'title': 'High Memory Usage Detected',
            'description': 'Several pods are consuming more than 80% of their memory limits',
            'severity': 'High',
            'category': 'Resource Management',
            'timestamp': datetime.now().isoformat(),
            'affectedResources': ['pod/app-server-1', 'pod/app-server-2'],
            'namespace': 'default',
            'remediation': 'Consider increasing memory limits or optimizing application memory usage'
        },
        {
            'id': f'finding-{job.job_id}-2',
            'title': 'Deprecated API Version in Use',
            'description': 'Some resources are using deprecated API versions',
            'severity': 'Medium',
            'category': 'API Compatibility',
            'timestamp': datetime.now().isoformat(),
            'affectedResources': ['deployment/legacy-app'],
            'namespace': 'production',
            'remediation': 'Update resource definitions to use current API versions'
        },
        {
            'id': f'finding-{job.job_id}-3',
            'title': 'Missing Resource Limits',
            'description': 'Containers without resource limits can impact cluster stability',
            'severity': 'Critical',
            'category': 'Security',
            'timestamp': datetime.now().isoformat(),
            'affectedResources': ['pod/unlimited-pod-1', 'pod/unlimited-pod-2', 'pod/unlimited-pod-3'],
            'namespace': 'development',
            'remediation': 'Add CPU and memory limits to all container specifications'
        }
    ]

    return sample_findings


def _generate_recommendations(job) -> List[Dict[str, Any]]:
    """Generate recommendations based on analysis."""
    return [
        {
            'id': f'rec-{job.job_id}-1',
            'title': 'Implement Resource Quotas',
            'description': 'Set up resource quotas to prevent resource exhaustion',
            'priority': 'high',
            'impact': 'High',
            'effort': 'Medium',
            'risk': 'Low',
            'steps': [
                'Create ResourceQuota objects for each namespace',
                'Define appropriate CPU and memory limits',
                'Monitor quota usage and adjust as needed'
            ]
        },
        {
            'id': f'rec-{job.job_id}-2',
            'title': 'Enable Pod Security Standards',
            'description': 'Implement pod security standards to improve cluster security',
            'priority': 'medium',
            'impact': 'High',
            'effort': 'High',
            'risk': 'Medium'
        },
        {
            'id': f'rec-{job.job_id}-3',
            'title': 'Set Up Monitoring Alerts',
            'description': 'Configure alerts for critical cluster metrics',
            'priority': 'medium',
            'impact': 'Medium',
            'effort': 'Low',
            'risk': 'Low'
        }
    ]


def _generate_timeline(job) -> List[Dict[str, Any]]:
    """Generate timeline of events."""
    return [
        {
            'timestamp': datetime.now().isoformat(),
            'severity': 'critical',
            'description': 'Critical finding: Missing resource limits detected'
        },
        {
            'timestamp': (datetime.now() - timedelta(minutes=30)).isoformat(),
            'severity': 'warning',
            'description': 'High memory usage detected in multiple pods'
        },
        {
            'timestamp': (datetime.now() - timedelta(hours=1)).isoformat(),
            'severity': 'info',
            'description': 'Analysis started for cluster resources'
        }
    ]


def _generate_resource_metrics(job) -> Dict[str, int]:
    """Generate resource metrics breakdown."""
    return {
        'Pods': 45,
        'Services': 12,
        'Deployments': 8,
        'ConfigMaps': 15,
        'Secrets': 6,
        'Nodes': 3,
        'PersistentVolumes': 4
    }


def main():
    """Run the web application."""
    app = create_app()
    
    # Development server configuration
    debug = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    host = os.environ.get('FLASK_HOST', '127.0.0.1')
    port = int(os.environ.get('FLASK_PORT', 5000))
    
    print(f"Starting OpenShift Must-Gather Analyzer Web Interface")
    print(f"Access the application at: http://{host}:{port}")
    
    app.run(host=host, port=port, debug=debug)


if __name__ == '__main__':
    main()