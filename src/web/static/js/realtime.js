/**
 * Real-time WebSocket client for OpenShift Must-Gather Analyzer
 * Provides real-time updates for analysis progress, log streaming, and data refresh
 */

class RealtimeClient {
    constructor() {
        this.socket = null;
        this.connected = false;
        this.currentJobId = null;
        this.logStreamActive = false;
        this.progressCallbacks = new Map();
        this.dataCallbacks = new Map();
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        
        this.init();
    }
    
    init() {
        // Check if Socket.IO is available
        if (typeof io === 'undefined') {
            console.warn('Socket.IO not available - real-time features disabled');
            return;
        }
        
        this.connect();
    }
    
    connect() {
        try {
            this.socket = io({
                transports: ['websocket', 'polling'],
                timeout: 5000,
                forceNew: true
            });
            
            this.setupEventHandlers();
            
        } catch (error) {
            console.error('Failed to initialize WebSocket connection:', error);
            this.fallbackToPolling();
        }
    }
    
    setupEventHandlers() {
        this.socket.on('connect', () => {
            console.log('WebSocket connected');
            this.connected = true;
            this.reconnectAttempts = 0;
            this.showConnectionStatus('connected');
            
            // Rejoin current job room if applicable
            if (this.currentJobId) {
                this.joinJobRoom(this.currentJobId);
            }
        });
        
        this.socket.on('disconnect', (reason) => {
            console.log('WebSocket disconnected:', reason);
            this.connected = false;
            this.showConnectionStatus('disconnected');
            
            if (reason === 'io server disconnect') {
                // Server initiated disconnect, try to reconnect
                this.attemptReconnect();
            }
        });
        
        this.socket.on('connect_error', (error) => {
            console.error('WebSocket connection error:', error);
            this.connected = false;
            this.showConnectionStatus('error');
            this.attemptReconnect();
        });
        
        // Progress updates
        this.socket.on('progress_update', (data) => {
            this.handleProgressUpdate(data);
        });
        
        // Job completion
        this.socket.on('job_completed', (data) => {
            this.handleJobCompletion(data);
        });
        
        // Log streaming
        this.socket.on('log_stream_data', (data) => {
            this.handleLogStreamData(data);
        });
        
        this.socket.on('log_stream_started', (data) => {
            this.logStreamActive = true;
            this.showLogStreamStatus('started', data);
        });
        
        this.socket.on('log_stream_stopped', (data) => {
            this.logStreamActive = false;
            this.showLogStreamStatus('stopped', data);
        });
        
        this.socket.on('log_stream_error', (data) => {
            this.logStreamActive = false;
            this.showLogStreamStatus('error', data);
        });
        
        // Data refresh
        this.socket.on('data_refreshed', (data) => {
            this.handleDataRefresh(data);
        });
        
        this.socket.on('global_data_refreshed', (data) => {
            this.handleGlobalDataRefresh(data);
        });
        
        // Error handling
        this.socket.on('error', (data) => {
            console.error('WebSocket error:', data);
            this.showAlert(data.message || 'WebSocket error occurred', 'danger');
        });
    }
    
    attemptReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('Max reconnection attempts reached, falling back to polling');
            this.fallbackToPolling();
            return;
        }
        
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        
        console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
        
        setTimeout(() => {
            if (!this.connected) {
                this.connect();
            }
        }, delay);
    }
    
    fallbackToPolling() {
        console.log('Falling back to polling mode for updates');
        this.showConnectionStatus('polling');
        
        // Start polling for updates every 5 seconds
        setInterval(() => {
            if (this.currentJobId) {
                this.pollJobProgress(this.currentJobId);
            }
        }, 5000);
    }
    
    // Public API methods
    joinJobRoom(jobId) {
        this.currentJobId = jobId;
        
        if (this.connected && this.socket) {
            this.socket.emit('join_job_room', { job_id: jobId });
        }
    }
    
    leaveJobRoom(jobId) {
        if (this.connected && this.socket) {
            this.socket.emit('leave_job_room', { job_id: jobId });
        }
        
        if (this.currentJobId === jobId) {
            this.currentJobId = null;
        }
    }
    
    startProgressMonitoring(jobId) {
        if (this.connected && this.socket) {
            this.socket.emit('start_progress_monitoring', { job_id: jobId });
        } else {
            // Fallback to polling
            this.pollJobProgress(jobId);
        }
    }
    
    stopProgressMonitoring(jobId) {
        if (this.connected && this.socket) {
            this.socket.emit('stop_progress_monitoring', { job_id: jobId });
        }
    }
    
    startLogStream(jobId, filePath) {
        if (this.connected && this.socket) {
            this.socket.emit('start_log_stream', { 
                job_id: jobId, 
                file_path: filePath 
            });
        } else {
            this.showAlert('Real-time log streaming requires WebSocket connection', 'warning');
        }
    }
    
    stopLogStream() {
        if (this.connected && this.socket) {
            this.socket.emit('stop_log_stream');
        }
        this.logStreamActive = false;
    }
    
    requestDataRefresh(jobId, dataType = 'all') {
        if (this.connected && this.socket) {
            this.socket.emit('request_data_refresh', { 
                job_id: jobId, 
                type: dataType 
            });
        } else {
            // Fallback to manual refresh
            this.manualDataRefresh(jobId, dataType);
        }
    }
    
    // Callback registration
    onProgressUpdate(callback) {
        this.progressCallbacks.set('progress', callback);
    }
    
    onJobCompletion(callback) {
        this.progressCallbacks.set('completion', callback);
    }
    
    onLogData(callback) {
        this.dataCallbacks.set('log', callback);
    }
    
    onDataRefresh(callback) {
        this.dataCallbacks.set('refresh', callback);
    }
    
    // Event handlers
    handleProgressUpdate(data) {
        const callback = this.progressCallbacks.get('progress');
        if (callback) {
            callback(data);
        }
        
        // Update progress UI elements
        this.updateProgressUI(data);
    }
    
    handleJobCompletion(data) {
        const callback = this.progressCallbacks.get('completion');
        if (callback) {
            callback(data);
        }
        
        // Show completion notification
        this.showJobCompletionNotification(data);
    }
    
    handleLogStreamData(data) {
        const callback = this.dataCallbacks.get('log');
        if (callback) {
            callback(data);
        }
        
        // Append to log viewer if present
        this.appendToLogViewer(data);
    }
    
    handleDataRefresh(data) {
        const callback = this.dataCallbacks.get('refresh');
        if (callback) {
            callback(data);
        }
        
        // Update relevant UI components
        this.updateDataUI(data);
    }
    
    handleGlobalDataRefresh(data) {
        // Update global UI elements
        if (data.jobs) {
            this.updateJobsList(data.jobs);
        }
        
        if (data.system) {
            this.updateSystemStatus(data.system);
        }
    }
    
    // UI update methods
    updateProgressUI(data) {
        const progressBar = document.getElementById(`progress-${data.job_id}`);
        if (progressBar) {
            progressBar.style.width = `${data.progress}%`;
            progressBar.setAttribute('aria-valuenow', data.progress);
        }
        
        const statusText = document.getElementById(`status-${data.job_id}`);
        if (statusText) {
            statusText.textContent = data.current_step || data.status;
        }
        
        const progressText = document.getElementById(`progress-text-${data.job_id}`);
        if (progressText) {
            progressText.textContent = `${data.progress}%`;
        }
    }
    
    showJobCompletionNotification(data) {
        const message = data.success 
            ? `Job ${data.job_id.substring(0, 8)} completed successfully`
            : `Job ${data.job_id.substring(0, 8)} failed`;
        
        const alertType = data.success ? 'success' : 'danger';
        this.showAlert(message, alertType);
        
        // Play notification sound if enabled
        this.playNotificationSound(data.success);
    }
    
    appendToLogViewer(data) {
        const logContainer = document.getElementById('log-container');
        if (!logContainer) return;
        
        const logLine = document.createElement('div');
        logLine.className = 'log-line';
        logLine.innerHTML = `
            <span class="log-timestamp">${new Date(data.timestamp).toLocaleTimeString()}</span>
            <span class="log-content">${this.escapeHtml(data.line)}</span>
        `;
        
        logContainer.appendChild(logLine);
        
        // Auto-scroll if enabled
        if (document.getElementById('auto-scroll')?.checked) {
            logContainer.scrollTop = logContainer.scrollHeight;
        }
    }
    
    showConnectionStatus(status) {
        const indicator = document.getElementById('connection-status');
        if (!indicator) return;
        
        const statusConfig = {
            connected: { text: 'Connected', class: 'bg-success', icon: 'fa-wifi' },
            disconnected: { text: 'Disconnected', class: 'bg-danger', icon: 'fa-wifi-slash' },
            error: { text: 'Connection Error', class: 'bg-danger', icon: 'fa-exclamation-triangle' },
            polling: { text: 'Polling Mode', class: 'bg-warning', icon: 'fa-clock' }
        };
        
        const config = statusConfig[status] || statusConfig.disconnected;
        
        indicator.className = `badge ${config.class}`;
        indicator.innerHTML = `<i class="fas ${config.icon} me-1"></i>${config.text}`;
    }
    
    showLogStreamStatus(status, data) {
        const statusElement = document.getElementById('log-stream-status');
        if (!statusElement) return;
        
        switch (status) {
            case 'started':
                statusElement.innerHTML = `<i class="fas fa-play text-success me-1"></i>Streaming: ${data.file_path}`;
                break;
            case 'stopped':
                statusElement.innerHTML = `<i class="fas fa-stop text-secondary me-1"></i>Stream stopped`;
                break;
            case 'error':
                statusElement.innerHTML = `<i class="fas fa-exclamation-triangle text-danger me-1"></i>Error: ${data.message}`;
                break;
        }
    }
    
    // Fallback methods
    pollJobProgress(jobId) {
        fetch(`/api/jobs/${jobId}/status`)
            .then(response => response.json())
            .then(data => {
                this.handleProgressUpdate({
                    job_id: jobId,
                    status: data.status,
                    progress: data.progress || 0,
                    current_step: data.current_step || 'Processing...'
                });
            })
            .catch(error => {
                console.error('Error polling job progress:', error);
            });
    }
    
    manualDataRefresh(jobId, dataType) {
        const endpoints = {
            files: `/api/jobs/${jobId}/files`,
            resources: `/api/jobs/${jobId}/resources`,
            metrics: `/api/jobs/${jobId}/metrics`
        };
        
        const endpoint = endpoints[dataType] || endpoints.files;
        
        fetch(endpoint)
            .then(response => response.json())
            .then(data => {
                this.handleDataRefresh({
                    job_id: jobId,
                    type: dataType,
                    [dataType]: data
                });
            })
            .catch(error => {
                console.error('Error refreshing data:', error);
            });
    }
    
    // Utility methods
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    showAlert(message, type = 'info') {
        // Create and show alert notification
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
    
    playNotificationSound(success) {
        // Play notification sound if audio is enabled
        if (localStorage.getItem('audioNotifications') === 'enabled') {
            const audio = new Audio(success ? '/static/sounds/success.mp3' : '/static/sounds/error.mp3');
            audio.volume = 0.3;
            audio.play().catch(() => {
                // Ignore audio play errors
            });
        }
    }
}

// Global instance
window.realtimeClient = new RealtimeClient();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RealtimeClient;
}
